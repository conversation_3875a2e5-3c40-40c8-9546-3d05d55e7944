{"extends": ["@simplex/eslint-config-simple", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"func-style": ["off"], "no-console": ["off"], "camelcase": ["error", {"properties": "never"}], "semi": ["error", "always"], "max-len": ["error", 300, 2, {"ignoreUrls": true, "ignoreComments": false}], "@typescript-eslint/naming-convention": ["off", {"selector": "objectLiteralProperty", "format": ["strictCamelCase", "PascalCase", "StrictPascalCase", "snake_case", "camelCase", "UPPER_CASE"], "custom": {"regex": ".+", "match": true}}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "no-else-return": "off"}, "root": true, "ignorePatterns": ["/server", "/webpack"]}