{"develop": false, "hosts": {"client": {"sirius": "https://sirius.kakamobi.cn/", "misc": "https://jiakao-misc.kakamobi.cn/", "config": "https://config.kakamobi.com/", "activity": "https://jiakao-activity.kakamobi.cn/", "swallow": "https://swallow.kakamobi.cn/", "jiakao3d": "https://jiakao-3d.kakamobi.cn/", "squirrel": "https://squirrel.kakamobi.cn/", "cheyouquan": "https://cheyouquan.kakamobi.com/", "monkey": "https://monkey.kakamobi.cn/", "jiakao": "https://api2.jiakaobaodian.com/", "tiku": "https://jk-tiku.kakamobi.cn/", "panda": "https://panda.kakamobi.cn/", "pony": "https://pony.kakamobi.cn/", "parrot": "https://parrot.kakamobi.cn/", "rights": "https://jk-rights.kakamobi.cn/", "koala": "https://koala.kakamobi.cn/", "eagle": "https://eagle.kakamobi.cn/"}, "server": {}}, "dir": {"src": "./src/", "web": "./web/", "node": "./node/", "weixin": "./weixin/"}, "router": [{"name": "index", "app": "./router/index", "route": "/index.html"}, {"name": "ke2_3d", "app": "./router/ke2_3d", "route": "/ke2_3d.html"}, {"name": "ke3_3d", "app": "./router/ke3_3d", "route": "/ke3_3d.html"}, {"name": "couponDetail", "app": "./router/couponDetail", "route": "/couponDetail.html"}, {"name": "exchangeCoupon", "app": "./router/exchangeCoupon", "route": "/exchangeCoupon.html"}, {"name": "noGetVip", "app": "./router/noGetVip", "route": "/noGetVip.html"}, {"name": "kemu2", "app": "./router/kemu2", "route": "/kemu2.html"}, {"name": "kemu3", "app": "./router/kemu3", "route": "/kemu3.html"}, {"name": "newbee", "app": "./router/newbee", "route": "/newbee.html"}, {"name": "kqmj", "app": "./router/kqmj", "route": "/kqmj.html"}, {"name": "qia<PERSON>ji", "app": "./router/qiaoji", "route": "/qiaoji.html"}, {"name": "session500", "app": "./router/session500", "route": "/session500.html"}, {"name": "khkemu3", "app": "./router/khkemu3", "route": "/khkemu3.html"}, {"name": "dgmnq", "app": "./router/dgmnq", "route": "/dgmnq.html"}, {"name": "dtjq", "app": "./router/dtjq", "route": "/dtjq.html"}, {"name": "sjkjDialog", "app": "./router/sjkjDialog", "route": "/sjkjDialog.html"}, {"name": "ioskqmj", "app": "./router/ioskqmj", "route": "/ioskqmj.html"}, {"name": "jkjt", "app": "./router/jkjt", "route": "/jkjt.html"}, {"name": "msjpk", "app": "./router/msjpk", "route": "/msjpk.html"}, {"name": "qjlx", "app": "./router/qjlx", "route": "/qjlx.html"}, {"name": "spxz", "app": "./router/spxz", "route": "/spxz.html"}, {"name": "znlt", "app": "./router/znlt", "route": "/znlt.html"}, {"name": "carkemuall", "app": "./router/carkemuall", "route": "/carkemuall.html"}, {"name": "detail", "app": "./router/detail", "route": "/detail.html"}, {"name": "mnks", "app": "./router/mnks", "route": "/mnks.html"}, {"name": "ke2route", "app": "./router/ke2route", "route": "/ke2route.html"}, {"name": "status", "app": "./router/status", "route": "/status.html"}, {"name": "jkxtInstruction", "app": "./router/jkxtInstruction", "title": "驾考讲堂介绍", "route": "/jkxtInstruction.html"}, {"name": "score12buy", "app": "./router/score12buy", "route": "/score12buy.html"}, {"name": "score12buySuccess", "app": "./router/score12buySuccess", "route": "/score12buySuccess.html"}, {"name": "ke2Exam", "app": "./router/ke2Exam", "route": "/ke2Exam.html"}, {"name": "mnksAlone", "app": "./router/mnksAlone", "route": "/mnksAlone.html"}, {"name": "qiaojiintro", "app": "./router/qiaojiintro", "route": "/qiaojiintro.html"}, {"name": "qkupgrade", "app": "./router/qkupgrade", "route": "/qkupgrade.html"}, {"name": "questionKqsjQk", "app": "./router/questionKqsjQk", "route": "/questionKqsjQk.html"}, {"name": "dgzxgk", "app": "./router/ke3_3d", "route": "/dgzxgk.html"}, {"name": "ke2intro", "app": "./router/ke2intro", "route": "/ke2intro.html", "title": "科目二介绍"}, {"name": "ke2kslc", "app": "./router/ke2kslc", "route": "/ke2kslc.html", "title": "科目二考试流程"}, {"name": "ksbz", "app": "./router/ksbz", "route": "/ksbz.html", "title": "考试标准"}, {"name": "ksgz", "app": "./router/ksgz", "route": "/ksgz.html", "title": "考试规则"}, {"name": "kslc", "app": "./router/kslc", "route": "/kslc.html", "title": "考试流程"}, {"name": "ksyd", "app": "./router/ksyd", "route": "/ksyd.html", "title": "考试要点"}, {"name": "sttx", "app": "./router/znlt", "route": "/sttx.html"}, {"name": "baseinfo", "app": "./router/baseinfo", "title": "基本信息", "route": "/baseinfo.html"}, {"name": "buchang", "app": "./router/buchang", "title": "补偿服务", "route": "/buchang.html"}, {"name": "dianping", "app": "./router/dianping", "route": "/dianping.html"}, {"name": "payguide", "app": "./router/payguide", "route": "/payguide.html"}, {"name": "payguidevideo", "app": "./router/payguidevideo", "title": "视频播放", "route": "/payguidevideo.html"}, {"name": "khkemu2", "app": "./router/khkemu2", "route": "/khkemu2.html"}, {"name": "kemu23", "app": "./router/kemu23", "route": "/kemu23.html"}, {"name": "yxxy", "app": "./router/yxxy", "route": "/yxxy.html"}, {"name": "yxxySuccess", "app": "./router/yxxySuccess", "route": "/yxxySuccess.html"}, {"name": "ssgm", "app": "./router/ssgm", "route": "/ssgm.html"}, {"name": "3dbuyed", "app": "./router/3dbuyed", "route": "/3dbuyed.html"}, {"name": "buyed", "app": "./router/buyed", "route": "/buyed.html"}, {"name": "ios500", "app": "./router/ios500", "route": "/ios500.html"}, {"name": "iosdianping", "app": "./router/iosdianping", "route": "/iosdianping.html"}, {"name": "iosindex", "app": "./router/iosindex", "route": "/iosindex.html"}, {"name": "ioske3route", "app": "./router/ioske3route", "route": "/ioske3route.html"}, {"name": "ioske3wenda", "app": "./router/ioske3wenda", "route": "/ioske3wenda.html"}, {"name": "ioskemu2", "app": "./router/ioskemu2", "route": "/ioskemu2.html"}, {"name": "ioskemu3", "app": "./router/ioskemu3", "route": "/ioskemu3.html"}, {"name": "ioskemu23", "app": "./router/ioskemu23", "route": "/ioskemu23.html"}, {"name": "ioskhindex", "app": "./router/ioskhindex", "route": "/ioskhindex.html"}, {"name": "iosmotoindex", "app": "./router/iosmotoindex", "route": "/iosmotoindex.html"}, {"name": "dialogKe2", "app": "./router/dialogKe2", "route": "/dialogKe2.html"}, {"name": "failReason", "app": "./router/failReason", "route": "/failReason.html"}, {"name": "ke2examArea", "app": "./router/ke2examArea", "route": "/ke2examArea.html"}, {"name": "ke2route", "app": "./router/ke2route", "route": "/ke2route.html"}, {"name": "ke3route", "app": "./router/ke3route", "route": "/ke3route.html"}, {"name": "ke3routeDialog", "app": "./router/ke3routeDialog", "route": "/ke3routeDialog.html"}, {"name": "ke3wenda", "app": "./router/ke3wenda", "route": "/ke3wenda.html"}, {"name": "kemu3old", "app": "./router/kemu3old", "route": "/kemu3old.html"}, {"name": "kqmjPersuade", "app": "./router/kqmjPersuade", "route": "/kqmjPersuade.html"}, {"name": "kqyt", "app": "./router/kqyt", "route": "/kqyt.html"}, {"name": "kqyt2", "app": "./router/kqyt2", "route": "/kqyt2.html"}, {"name": "qiaojiios", "app": "./router/qiaojiios", "route": "/qiaojiios.html"}, {"name": "rights", "app": "./router/rights", "route": "/rights.html"}, {"name": "routeStatus", "app": "./router/routeStatus", "route": "/routeStatus.html"}, {"name": "spstios", "app": "./router/spstios", "route": "/spstios.html"}, {"name": "buyed", "app": "./router/buyed", "route": "/buyed.html"}, {"name": "xsdtjq", "app": "./router/xsdtjq", "route": "/xsdtjq.html"}, {"name": "mnksDriver", "app": "./router/mnksDriver", "route": "/mnksDriver.html"}, {"name": "passAnalysis", "app": "./router/passAnalysis", "route": "/passAnalysis.html"}, {"name": "passLearning", "app": "./router/passLearning", "route": "/passLearning.html"}, {"name": "kqfd", "app": "./router/kqfd", "route": "/kqfd.html"}, {"name": "kqfdHistory", "app": "./router/kqfdHistory", "route": "/kqfdHistory.html"}, {"name": "zxgk", "app": "./router/zxgk", "route": "/zxgk.html"}, {"name": "zxgkHistory", "app": "./router/zxgkHistory", "route": "/zxgkHistory.html"}, {"name": "zdst", "app": "./router/zdst", "route": "/zdst.html"}, {"name": "zdstHistory", "app": "./router/zdstHistory", "route": "/zdstHistory.html"}, {"name": "kqfdDialog", "app": "./router/kqfdDialog", "route": "/kqfdDialog.html"}, {"name": "kqfdHistoryDialog", "app": "./router/kqfdHistoryDialog", "route": "/kqfdHistoryDialog.html"}, {"name": "zxgkDialog", "app": "./router/zxgkDialog", "route": "/zxgkDialog.html"}, {"name": "zxgkHistoryDialog", "app": "./router/zxgkHistoryDialog", "route": "/zxgkHistoryDialog.html"}, {"name": "zdstDialog", "app": "./router/zdstDialog", "route": "/zdstDialog.html"}, {"name": "zdstHistoryDialog", "app": "./router/zdstHistoryDialog", "route": "/zdstHistoryDialog.html"}, {"name": "kq2h", "app": "./router/kq2h", "route": "/kq2h.html"}, {"name": "outLimit", "app": "./router/outLimit", "route": "/outLimit.html"}, {"name": "mnksPage", "app": "./router/mnksPage", "route": "/mnksPage.html"}, {"name": "kqmjQuestion", "app": "./router/kqmjQuestion", "route": "/kqmjQuestion.html"}, {"name": "tyVip", "app": "./router/tyVip", "route": "/tyVip.html"}, {"name": "student", "app": "./router/student", "route": "/student.html", "title": "全科VIP学生卡"}, {"name": "videoGoods", "app": "./router/videoGoods", "route": "/videoGoods.html", "title": "VIP商品介绍视频"}, {"name": "kczyDialog", "app": "./router/kczyDialog", "route": "/kczyDialog.html"}, {"name": "dgmnPage", "app": "./router/dgmnPage", "route": "/dgmnPage.html", "title": "灯光模拟"}, {"name": "plivateTeachHomeWork", "app": "./router/plivateTeachHomeWork", "route": "/plivateTeachHomeWork.html", "title": "私教班课后作业"}, {"name": "plivateTeachSpecial", "app": "./router/plivateTeachSpecial", "route": "/plivateTeachSpecial.html", "title": " "}, {"name": "scratchTicket", "app": "./router/scratchTicket", "route": "/scratchTicket.html"}, {"name": "dtjqPage", "app": "./router/dtjqPage", "route": "/dtjqPage.html"}, {"name": "dtjqPageSuccess", "app": "./router/dtjqPageSuccess", "route": "/dtjqPageSuccess.html"}, {"name": "adv", "app": "./router/adv", "route": "/adv.html", "title": "去除广告"}, {"name": "clean", "app": "./router/clean", "route": "/clean.html", "title": "清爽模式"}, {"name": "hesitate", "app": "./router/hesitate", "route": "/hesitate.html"}, {"name": "safeDriving", "app": "./router/safeDriving", "route": "/safeDriving.html"}], "bundle": {"zip": true, "dir": "../"}, "server": {"hotDev": true, "port": 2190, "timeout": 10000, "cluster": true, "index": "./index.html", "resourcePath": "/", "log": {"kafka": {"host": "************", "port": 9092, "topic": "nodejs-log", "partition": 0}}}, "head": {"meta": [{"charset": "UTF-8"}, {"name": "viewport", "content": "width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover"}, {"http-equiv": "X-UA-Compatible", "content": "IE=Edge,chrome=1"}, {"name": "renderer", "renderer": "webkit"}, {"name": "format-detection", "content": "telephone=no, email=no"}], "script": [{"src": "https://web-resource.mc-cdn.cn/web/vAssets.js"}]}, "build": {"style": {"remUnit": 100, "baseWidth": 375, "mobileMaxWidth": 500}, "server": ["server"], "static": ["static"]}}