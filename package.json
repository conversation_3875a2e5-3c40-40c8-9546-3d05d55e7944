{"name": "vip", "version": "1.0.0", "description": "", "config": {"unsafe-perm": true}, "dependencies": {"@simplex/simple-base": "5.1.7", "@simplex/simple-core": "4.0.16", "@simplex/simple-mcprotocol": "2.2.6", "core-js": "^3.20.0", "html2canvas": "^1.4.1", "swiper": "^7.4.1"}, "devDependencies": {"@babel/core": "^7.13.8", "@babel/preset-env": "^7.13.9", "@babel/preset-typescript": "^7.16.0", "@simplex-types/simple-core": "1.1.9", "@simplex/eslint-config-simple": "4.0.0", "@simplex/simple-develop": "4.1.34", "@types/lodash": "^4.14.178", "@typescript-eslint/eslint-plugin": "^5.8.0", "@typescript-eslint/parser": "^5.8.0", "babel-loader": "^8.2.2", "eslint": "^7.24.0", "eslint-loader": "^4.0.2", "ts-loader": "^8.2.0", "typescript": "^4.5.2", "vconsole": "3.3.4", "vconsole-webpack-plugin": "^1.7.1", "webpack": "^4.46.0", "webpack-cli": "^4.9.1", "webpack-dev-server": "^4.6.0"}, "engines": {"node": ">=12.16.0", "npm": ">=7.9.0"}, "scripts": {"webdev": "npx webpack-dev-server --config ./webpack/webpack.web.config.js --mode development --progress ", "webdev:dev": "npx webpack-dev-server --config ./webpack/webpack.web.config.js --mode development --progress --env package.simple=package-simple.dev.json", "webdev:test": "npx webpack-dev-server --config ./webpack/webpack.web.config.js --mode development --progress --env package.simple=package-simple.test.json --env vconsole", "bundler": "npx webpack --config ./webpack/webpack.web.config.js --mode production", "bundler:dev": "npx webpack --config ./webpack/webpack.web.config.js --mode production --env package.simple=package-simple.dev.json --env vconsole", "bundler:test": "npx webpack --config ./webpack/webpack.web.config.js --mode production --env package.simple=package-simple.test.json --env vconsole", "postinstall": "node ./webpack/patch-css-loader.js"}, "private": true, "@simplex/simple-node": false, "simple": "./package-simple.json"}