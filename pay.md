# 支付业务

# 微信支付

## JSAPI支付（也称公众号支付）：

1.  应用场景：在微信客户端浏览器中打开的网页使用微信支付功能
    
2.  注意事项： 
    
    1.  确保商户平台配置JSAPI支付授权目录
        
    2.  页面需先进行网页授权获取openId，下单接口需要的openId参数
        
3.  官方文档：[https://pay.weixin.qq.com/doc/v3/merchant/4012062524](https://pay.weixin.qq.com/doc/v3/merchant/4012062524)
    
4.  调起支付：如下代码示例
    

```json
function onBridgeReady() {
    /*
    * timeStamp、nonceStr、package、signType、paySign等参数是由下单接口返回
    */
    WeixinJSBridge.invoke('getBrandWCPayRequest', {
        "appId": "wx2421b1c4370ec43b",     //公众号ID，由商户传入     
        "timeStamp": "1395712654",     //时间戳，自1970年以来的秒数     
        "nonceStr": "e61463f8efa94090b1f366cccfbbb444",      //随机串     
        "package": "prepay_id=wx21201855730335ac86f8c43d1889123400",
        "signType": "RSA",     //微信签名方式：     
        "paySign": "oR9d8PuhnIc+YZ8cBHFCwfgpaK9gd7vaRvkYD7rthRAZ\/X+QBhcCYL21N7cHCTUxbQ+EAt6Uy+lwSN22f5YZvI45MLko8Pfso0jm46v5hqcVwrk6uddkGuT+Cdvu4WBqDzaDjnNa5UK3GfE1Wfl2gHxIIY5lLdUgWFts17D4WuolLLkiFZV+JSHMvH7eaLdT9N5GBovBwu5yYKUR7skR8Fu+LozcSqQixnlEZUfyE55feLOQTUYzLmR9pNtPbPsu6WVhbNHMS3Ss2+AehHvz+n64GDmXxbX++IOBvm2olHu3PsOUGRwhudhVf7UcGcunXt8cqNjKNqZLhLw4jq\/xDg==" //微信签名 
    },
    function(res) {
        if (res.err_msg == "get_brand_wcpay_request:ok") {
            // 使用以上方式判断前端返回,微信团队郑重提示：
            //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠，商户需进一步调用后端查单确认支付结果。
        }
    });
}
if (typeof WeixinJSBridge == "undefined") {
    if (document.addEventListener) {
        document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
    } else if (document.attachEvent) {
        document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
        document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
    }
} else {
    onBridgeReady();
}
```

## H5支付：

1.  应用场景：在移动客户端浏览器（非微信客户端浏览器）中打开的网页使用微信支付功能
    
2.  注意事项： 
    
    1.  官方建议APP中不要使用H5支付，但是从应用场景来看，app中的webview一般是和系统浏览器一样的，所以H5支付也能跑
        
    2.  确保商户平台配置好H5支付域名
        
3.  官方文档：[https://pay.weixin.qq.com/doc/v3/merchant/4012791832](https://pay.weixin.qq.com/doc/v3/merchant/4012791832)
    
4.  调起支付：调用下单接口会返回一个安全支付链接h5\_url，打开此支付链接即可发起支付
    
    1.  正常流程用户支付完成后会返回至发起支付的页面，如需返回至指定页面，则可以在h5\_url后拼接上"redirect\_url"参数，来指定回调页面。例如：希望用户支付完成后跳转至`https://www.wechatpay.com.cn`，则拼接后的地址为h5\_url= `https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx20161110163838f231619da20804912345&package=1037687096&redirect_url=https%3A%2F%2Fwww.wechatpay.com.cn`。
        
    2.  redirect\_url的域名必须为商户配置的H5支付域名
        
    3.  需对redirect\_url进行urlencode处理。
        

## Native支付（二维码支付）：

1.  应用场景：在PC端网页浏览器中打开的网页使用微信支付功能
    
2.  注意事项： 
    
    1.  不支持通过相册识别或长按识别二维码的方式完成支付
        
3.  官方文档：[https://pay.weixin.qq.com/doc/v3/merchant/4012791874](https://pay.weixin.qq.com/doc/v3/merchant/4012791874)
    
4.  调起支付：调用下单接口会返回一个二维码code（code\_url），然后前端将二维码code转换为二维码图片展示给用户，用户微信扫描二维码拉起支付收银台
    

## 小程序支付：

1.  应用场景：在微信小程序中使用微信支付功能
    
2.  注意事项： 
    
    1.  小程序内嵌H5页面不能调用jsapi支付收款，小程序内只能使用小程序支付收款
        
3.  官方文档：[https://pay.weixin.qq.com/doc/v3/merchant/4012791894](https://pay.weixin.qq.com/doc/v3/merchant/4012791894)
    
4.  调起支付：如下代码示例
    

```json
wx.requestPayment
(
/*
 * timeStamp、nonceStr、package、signType、paySign等参数是由下单接口返回
 */
  {
    "timeStamp": "1414561699",
    "nonceStr": "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
    "package": "prepay_id=wx201410272009395522657a690389285100",
    "signType": "RSA",
    "paySign": "oR9d8PuhnIc+YZ8cBHFCwfgpaK9gd7vaRvkYD7rthRAZ\/X+QBhcCYL21N7cHCTUxbQ+EAt6Uy+lwSN22f5YZvI45MLko8Pfso0jm46v5hqcVwrk6uddkGuT+Cdvu4WBqDzaDjnNa5UK3GfE1Wfl2gHxIIY5lLdUgWFts17D4WuolLLkiFZV+JSHMvH7eaLdT9N5GBovBwu5yYKUR7skR8Fu+LozcSqQixnlEZUfyE55feLOQTUYzLmR9pNtPbPsu6WVhbNHMS3Ss2+AehHvz+n64GDmXxbX++IOBvm2olHu3PsOUGRwhudhVf7UcGcunXt8cqNjKNqZLhLw4jq\/xDg==",
    "success":function(res){},
    "fail":function(res){},
    "complete":function(res){}
  }
)
```

## APP支付：

1.  应用场景：在APP中打开的网页，通过木仓协议与客户端交互调起微信支付功能
    
2.  注意事项： 
    
    1.  \------
        
3.  官方文档：[https://pay.weixin.qq.com/doc/v3/merchant/4013070158](https://pay.weixin.qq.com/doc/v3/merchant/4013070158)
    
4.  调起支付：如下示例代码
    

```json
// 通过木仓协议调起支付
window.location.href = 'http://pay.nav.mucang.cn/pay?payType=vip&content=' + encodeURIComponent(retData.content) + '&orderNumber=' + retData.orderNumber + '&extraData=' + JSON.stringify(extraData) + '&productId=' + retData.appleGoodsId + '&payChannel=' + payChannels[getPayType()] + '&callback=';
await new Promise<void>((resolve, reject) => {
// 驾考vip业务里面定义的几个支付回道，由客户端触发
    window.buyCancel = () => {
        window.buyCancel = noop;
        reject(new Error('支付取消'));
    };
    window.buyFailed = () => {
        window.buyFailed = noop;
        reject(new Error('支付失败'));
    };
    window.buySuccess = () => {
        window.buySuccess = noop;
        resolve();
    };
});
```

# 支付宝支付

### H5支付

1.  应用场景：在APP中打开的网页，通过木仓协议与客户端交互调起支付宝支付功能
    
2.  注意事项： 
    
    1.  页面确认使用支付宝支付后，浏览器自动跳转支付宝 App（安装了支付宝APP） 或支付宝网页（没安装支付宝APP）
        
3.  官方文档：[https://opendocs.alipay.com/open/203/105288?pathHash=f2308e24](https://opendocs.alipay.com/open/203/105288?pathHash=f2308e24)
    
4.  调起支付：如下示例代码
    

```json
/*
  * 支付宝支付
  * payConfig： {
  *   content: 支付订单内容
  *   orderNumber: 订单号
  * }
  * 这里无法回调，在创建订单时会传一个url作为支付完成后的跳转地址。
  * */
  var div = document.createElement('div');

  div.innerHTML = payConfig.content;
  document.body.appendChild(div);

  setTimeout(function () {
      div.firstChild.submit()
  }, 100)

```

### 订单二维码支付

1.  应用场景：在PC端网页浏览器中打开的网页使用支付宝支付功能
    
2.  注意事项： 
    
    1.  \---------
        
3.  官方文档：[https://opendocs.alipay.com/open/05osux?pathHash=660a1801](https://opendocs.alipay.com/open/05osux?pathHash=660a1801)
    
4.  调起支付：同上述微信二维码支付
    

### APP支付

1.  应用场景：在APP中打开的网页，通过木仓协议与客户端交互调起支付宝支付功能
    
2.  注意事项： 
    
    1.  \-------
        
3.  官方文档：[https://opendocs.alipay.com/open/00dn73?pathHash=b91b9616](https://opendocs.alipay.com/open/00dn73?pathHash=b91b9616)
    
4.  调起支付：同上述微信APP支付
    

# 苹果支付

1.  应用场景：在APP中购买虚拟商品必须使用苹果支付（苹果味了收取手续费，费率30%）。如果是实物商品可选择第三方支付（支付宝、微信等）
    
2.  调起支付：同上述微信APP支付
