const { ReplaceSource } = require('webpack-sources');

/** 
 * 在所有脚本加载之前注入pag动画库，因为pag动画库初始化需要2s多，太慢了
 */
class PagScriptInjectPlugin {

    /**
     * 
     * @param {{
     *  pages: string[]
     * }} options 
     */
    constructor(options) {
        this.options = options;
    }

    apply(compiler) {
        // SimpleDevelop把插件顺序搞乱了，这里在ZipPlugin之前修改html

        let pluginIndex = compiler.options.plugins.findIndex(plugin => plugin.constructor.name === 'ZipPlugin');

        if (pluginIndex === -1) {
            pluginIndex = compiler.options.plugins.length;
        } else {
            console.log('found zip plugin')
        }

        compiler.options.plugins.splice(pluginIndex, 0, (compiler) => {
            compiler.hooks.emit.tapAsync('PagScriptInjectPlugin', (compilation, cb) => {
                const assets = compilation.assets;
                const pages = this.options.pages;
             
                for (const key in pages) {
                    for (const key1 in assets) {
                        if(/.+\.html$/.test(key1)){
                            if (pages[key] === 'all' || pages[key].includes(key1) || pages[key].includes(key1.replace('.html'))){
                                let source = assets[key1];
                                const code = source?.source();
                                if (code) {
                                    const haystick = '</title>';
                                    const pos = code.indexOf(haystick) + haystick.length;
                                    source = new ReplaceSource(source);
                                    source.insert(pos, key);
                                    assets[key1] = source;
                                }
                            }
                        }
                    }
                }

                cb();
            });
        });

    }
}

module.exports = PagScriptInjectPlugin;
