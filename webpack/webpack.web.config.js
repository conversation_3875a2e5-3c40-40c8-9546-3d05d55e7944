const SimpleDevelopPlugin = require('@simplex/simple-develop');
const vConsolePlugin = require('vconsole-webpack-plugin');
// const PagScriptInjectPlugin = require('./PagScriptInjectPlugin');

/** @return {import('webpack').Configuration} */
module.exports = function (env, argv) {
    return {
        resolve: {
            extensions: ['.ts', '.js'],
            alias: {
                ":common": ":src/common",
                ":assets": ":src/assets",
                "vconsole": require.resolve("vconsole"), // 强行用低版本(v3.1.0)的vconsole，不然在低版本安卓机上有问题
                "@simplex/simple-mcprotocol": require.resolve("@simplex/simple-mcprotocol")
            }
        },
        module: {
            rules: [
                {
                    test: /\.(js|ts)$/,
                    exclude: /(node_modules|bower_components)/,
                    use: 'eslint-loader',
                    enforce: 'pre'
                },
                {
                    test: /\.(js|ts)$/,
                    exclude: /(node_modules|bower_components)(\/|\\)(?!(dom7|swiper|ssr-window))/,
                    use: 'babel-loader'
                },
                {
                    test: /\.pag$/,
                    use: 'url-loader'
                }
            ]
        },
        plugins: [
            new vConsolePlugin({
                enable: !!argv.env.vconsole
            }),
            new SimpleDevelopPlugin(__dirname, argv, {
                hashInFileName: false
            })
            // ,
            // new PagScriptInjectPlugin({
            //     pages: {
            //         '<script src="https://web-resource.mc-cdn.cn/web/vAssets.js"></script>': 'all'
            //     }
            // })
        ],
        devServer: {
            host: '0.0.0.0',
            compress: true,
            client: {
                overlay: false
            }
        },
        optimization: {
            runtimeChunk: false,
            splitChunks: {
                chunks: 'all',
                minSize: 30000,
                maxSize: 0,
                minChunks: 2,
                maxAsyncRequests: 6,
                maxInitialRequests: 10,
                // automaticNameDelimiter: '~',
                automaticNameDelimiter: '~',
                automaticNameMaxLength: 50,
                name: true,
                cacheGroups: {
                    vendors: {
                        test: /[\\/]node_modules[\\/]/,
                        priority: -10
                    },
                    default: {
                        minChunks: 2,
                        priority: -20,
                        reuseExistingChunk: true
                    }
                }
            }
        }
    }
}