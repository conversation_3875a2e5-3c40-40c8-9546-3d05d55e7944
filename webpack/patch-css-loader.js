#!/usr/bin/env node

const path = require('path');
const fs = require('fs');

const cssLoaderMainFilePath = require.resolve('css-loader');
const indexFilePath = path.join(cssLoaderMainFilePath, '../index.js');
const utilsFilePath = path.join(cssLoaderMainFilePath, '../utils.js');

let content = fs.readFileSync(indexFilePath, 'utf-8');
content = content.replace(
  'if ((0, _utils.shouldUseModulesPlugins)(options.modules, this.resourcePath))',
  'if ((0, _utils.shouldUseModulesPlugins)(options.modules, this.resourcePath, this.resourceQuery))'
);
fs.writeFileSync(indexFilePath, content);

content = fs.readFileSync(utilsFilePath, 'utf-8');
content = content.replace(
  'function shouldUseModulesPlugins(modules, resourcePath) {',
  'function shouldUseModulesPlugins(modules, resourcePath, resourceQuery) {'
);
content = content.replace(
  'return modules.auto(resourcePath);',
  'return modules.auto(resourcePath, resourceQuery);'
);
fs.writeFileSync(utilsFilePath, content);

console.log('Done: 修复css-loader成功!✅');