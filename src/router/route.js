import Simple from '@simplex/simple-core';
import Initialize from ':initialize/main.ts';

export default class {

    constructor() {
        // 初始化
        this.runInitialize();

        this.Router = Simple.Router.create();

        this.router();

    }

    runInitialize() {
        Initialize();
    }

    router() {
        this.Router.use('*', (params, context) => {
            this.route(params, context);
        });

    }

    go() {
        this.Router.go(location.href);
    }

}