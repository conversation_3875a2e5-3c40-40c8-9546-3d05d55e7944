import { CarType, URLCommon, URLParams, LangType, Platform } from ':common/env';
import Route from './route';

let r = new Route();

r.route = (params, context) => {

    // 维语跳转
    if (URLParams._lang && URLParams._lang === LangType.UG) {
        window.location.replace('https://laofuzi.kakamobi.com/jkbd-vip-weiyu/status.html' + window.location.search);
        return false;
    }

    // 直接跳转vip已购买页
    if (URLParams._lang && URLParams._lang !== LangType.ZH) {
        window.location.replace('https://laofuzi.kakamobi.com/jiakaobaodian-vip-lang/status.html' + window.location.search);
        return false;
    }
    return import(':application/status/main').then(({ default: Page }) => new Page(params, context).render());
};

r.go();
