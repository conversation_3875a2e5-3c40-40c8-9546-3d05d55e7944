import { ABTestKey, ABTestType, CarType, URLCommon, URLParams, LangType, Platform, KemuType } from ':common/env';
import { getAbtest } from ':store/chores';
import Route from './route';

let r = new Route();

r.route = async (params, context) => {
    const { strategy } = await getAbtest(URLCommon.tiku);

    if (strategy[ABTestKey.key25] === ABTestType.B && URLCommon.tiku === CarType.CAR && URLCommon.kemu === KemuType.Ke1) {
        return import(':application/dtjqPageSuccess/main').then(({ default: Page }) => new Page(params, context).render());
    } else {
        return import(':application/qiaojiintro/main').then(({ default: Page }) => new Page(params, context).render());
    }

};

r.go();
