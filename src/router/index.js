import { ABTestKey, ABTestType, CarType, URLCommon, URLParams, LangType, KemuType, RecKey } from ':common/env';
import { getAbtest } from ':store/chores';
import Route from './route';

let r = new Route();

r.route = async (params, context) => {
    // 维语跳转
    if (URLParams._lang && URLParams._lang === LangType.UG) {
        window.location.replace('https://laofuzi.kakamobi.com/jkbd-vip-weiyu/index.html' + window.location.search);
        return false;
    }

    // 其余多语言跳转
    if (URLParams._lang && URLParams._lang !== LangType.ZH) {
        window.location.replace('https://laofuzi.kakamobi.com/jiakaobaodian-vip-lang/index.html' + window.location.search);
        return false;

    }

    if (URLCommon.isZigezheng || URLCommon.isScore12 || URLCommon.isElder) {
        return import(':application/special/main').then(({ default: Page }) => new Page(params, context).render());
    }
    let strategy = {};
    if (URLCommon.tiku === CarType.CAR) {
        strategy = (await getAbtest(URLCommon.tiku)).strategy;
    }

    switch (URLCommon.tiku) {
        case CarType.CAR:
            // 后期要改成枚举，因为入口是固定的，后台管理系统也要改成下拉框
            URLParams.recKey = RecKey.INDEX;
            if (strategy[ABTestKey.key30] === ABTestType.B && URLCommon.isNormal && URLCommon.kemu === KemuType.Ke1) {
                return import(':application/configPages/main').then(({ default: Page }) => new Page(params, context).render());
            } else {
                URLParams.statRecKey = `${URLCommon.tiku || CarType.CAR}_${URLCommon.kemu || KemuType.Ke1}_${URLParams.sceneCode}_${URLParams.patternCode}_${URLParams.recKey}`;
                return import(':application/car/main').then(({ default: Page }) => new Page(params, context).render());
            }

        case CarType.MOTO:
            return import(':application/moto/main').then(({ default: Page }) => new Page(params, context).render());
        case CarType.TRUCK:
        case CarType.BUS:
            return import(':application/khche/main').then(({ default: Page }) => new Page(params, context).render());
        case CarType.GUACHE:
            return import(':application/guache/main').then(({ default: Page }) => new Page(params, context).render());
        default:
            return import(':application/car/main').then(({ default: Page }) => new Page(params, context).render());
    }

};

r.go();
