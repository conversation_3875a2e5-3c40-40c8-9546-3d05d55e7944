import { ABTestKey, ABTestType, CarType, KemuType, URLCommon, URLParams, LangType, Platform } from ':common/env';
import { getAbtest } from ':store/chores';
import Route from './route';

let r = new Route();

r.route = async (params, context) => {
    if (URLParams._lang && URLParams._lang !== LangType.ZH) {
        window.location.replace('https://laofuzi.kakamobi.com/jiakaobaodian-vip-lang/index.html' + window.location.search);
        return false;
    }
    const { strategy } = await getAbtest(URLCommon.tiku);

    if (strategy[ABTestKey.key25] === ABTestType.B && URLCommon.tiku === CarType.CAR && URLCommon.kemu === KemuType.Ke1) {
        return import(':application/dtjqPage/main').then(({ default: Page }) => new Page(params, context).render());
    } else {
        return import(':application/qiaoji/main').then(({ default: Page }) => new Page(params, context).render());
    }
};

r.go();
