import { URLParams, LangType } from ':common/env';
import Route from './route';

let r = new Route();

r.route = async (params, context) => {

    // 维语跳转
    if (URLParams._lang && URLParams._lang === LangType.UG) {
        window.location.replace('https://laofuzi.kakamobi.com/jkbd-vip-weiyu/kemu2.html' + window.location.search);
        return false;
    }

    const { default: Page } = await import(':application/kemu2Native3D/main');
    return new Page(params, context).render();
};

r.go();
