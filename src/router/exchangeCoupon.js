import { CarType, URLCommon, URLParams, LangType, Platform } from ':common/env';
import Page from ':application/exchangeCoupon/main';
import Route from './route';

let r = new Route();

r.route = (params, context) => {
    
    if (URLParams._lang && URLParams._lang === LangType.UG) {
        window.location.replace('https://laofuzi.kakamobi.com/jkbd-vip-weiyu/exchange.html' + window.location.search);
        return false;
    }

    return new Page(params, context).render();
};

r.go();
