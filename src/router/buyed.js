import { CarType, URLCommon, URLParams, LangType, Platform } from ':common/env';
import Page from ':application/buyed/main';
import Route from './route';

let r = new Route();

r.route = (params, context) => {

    if (URLParams._lang && URLParams._lang === LangType.UG) {
        window.location.replace('https://laofuzi.kakamobi.com/jkbd-vip-weiyu/buyed.html' + window.location.search);
        return false;
    }

    // 直接跳转vip已购买页
    if (URLParams._lang && URLParams._lang !== LangType.ZH) {
        window.location.replace('https://laofuzi.kakamobi.com/jiakaobaodian-vip-lang/buyed.html' + window.location.search);
        return false;
    }

    return new Page(params, context).render();
};

r.go();