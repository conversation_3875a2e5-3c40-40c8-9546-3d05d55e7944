import { CarType, URLCommon, URLParams, LangType, Platform } from ':common/env';
import Route from './route';

let r = new Route();

r.route = (params, context) => {

    // 维语跳转
    if (URLParams._lang && URLParams._lang === LangType.UG) {
        window.location.replace('https://laofuzi.kakamobi.com/jkbd-vip-weiyu/ke23d.html' + window.location.search);
        return false;
    }

    return import(':application/ke23d/main').then(({ default: Page }) => new Page(params, context).render());
};

r.go();
