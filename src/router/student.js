import { Platform, URLCommon } from ':common/env';
import Route from './route';

let r = new Route();

r.route = (params, context) => {
    if (!Platform.isMuCang) {
        location.replace(`https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-share-vip/student.html?carStyle=${URLCommon.tiku}&kemuStyle=${URLCommon.kemu}`);
        return;
    }
    import(':application/student/main').then(({ default: Page }) => new Page(params, context).render());
};

r.go();
