import { getAbtest } from ':store/chores';
import { ABTestKey, ABTestType, LangType, Platform, URLParams } from ':common/env';
import Route from './route';

let r = new Route();

r.route = async (params, context) => {

    if (URLParams._lang && URLParams._lang === LangType.UG) {
        window.location.replace('https://laofuzi.kakamobi.com/jkbd-vip-weiyu/ke3route.html' + window.location.search);
        return false;
    }

    // 由于小包协议的原因，暂时留下这段代码，1年后删除（不考虑超过一年的版本)
    if (!Platform.isJiakao) {
        return import(':application/ke3route/main').then(({ default: Page }) => new Page(params, context).render());
    }

    return import(':application/ke3route/mainb').then(({ default: Page }) => new Page(params, context).render());

};

r.go();