import Page from ':application/ke3routeDialog/main';
import { LangType, Platform, URLParams } from ':common/env';
import Route from './route';

let r = new Route();

r.route = (params, context) => {

    if (URLParams._lang && URLParams._lang === LangType.UG) {
        window.location.replace('https://laofuzi.kakamobi.com/jkbd-vip-weiyu/ke3routeDialog.html' + window.location.search);
        return false;
    }

    return new Page(params, context).render();
};

r.go();