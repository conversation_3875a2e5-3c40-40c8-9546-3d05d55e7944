import Route from './route';
import { ABTestKey, ABTestType, CarType, URLCommon, URLParams, LangType, Platform } from ':common/env';
import { getAbtest } from ':store/chores';
let r = new Route();

r.route = async (params, context) => {

    // 维语跳转
    if (URLParams._lang && URLParams._lang === LangType.UG) {
        window.location.replace('https://laofuzi.kakamobi.com/jkbd-vip-weiyu/kqmj.html' + window.location.search);
        return false;
    }

    if (URLParams._lang && URLParams._lang !== LangType.ZH) {
        window.location.replace('https://laofuzi.kakamobi.com/jiakaobaodian-vip-lang/index.html' + window.location.search);
        return false;
    }

    const { strategy } = await getAbtest(URLCommon.tiku);
    if (strategy[ABTestKey.key21] === ABTestType.B && URLCommon.tiku === CarType.TRUCK) {
        return import(':application/kqmjQuestion/main').then(({ default: Page }) => new Page(params, context).render());
    } else {
        return import(':application/kqmj/main').then(({ default: Page }) => new Page(params, context).render());
    }

};

r.go();
