import { CarType, URLCommon, URLParams, LangType, Platform } from ':common/env';
import Page from ':application/mnks/main';
import Route from './route';

let r = new Route();

r.route = (params, context) => {
    // 维语跳转
    if (URLParams._lang && URLParams._lang === LangType.UG) {
        window.location.replace('https://laofuzi.kakamobi.com/jkbd-vip-weiyu/mnks.html' + window.location.search);
        return false;
    }
    if (URLParams._lang && URLParams._lang !== LangType.ZH) {
        window.location.replace('https://laofuzi.kakamobi.com/jiakaobaodian-vip-lang/mnks.html' + window.location.search);
        return false;
    }
    return import(':application/mnks/main').then(({ default: Page }) => new Page(params, context).render());
};

r.go();
