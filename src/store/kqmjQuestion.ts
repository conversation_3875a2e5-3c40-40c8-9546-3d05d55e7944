import { URLParams } from ':common/env';
import { request } from ':common/request';

export async function getQuestionList(data: { carType: string, kemu: string | number, practiceType: string, cityCode: string | number }): Promise<any> {
    const res = await request({
        hostName: 'tiku',
        url: 'api/open/vip/trial-vip-practice.htm',
        data: {
            ...data,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode

        }
    });
    return res.itemList;
}
