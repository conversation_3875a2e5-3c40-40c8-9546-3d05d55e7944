/*
 * ------------------------------------------------------------------
 * 已购买页相关的接口
 * ------------------------------------------------------------------
 */
import { KemuType, Platform, URLCommon, URLParams } from ':common/env';
import { request } from ':common/request';
import { calcImg, formatPrice } from ':common/utils';
import { hostApi } from './chores';
import { newgetRecommendGoods } from './newGoods';
// 徽章接口
export async function getVipBages(): Promise<any> {
    const res = await request({
        hostName: 'pony',
        url: 'api/open/vip-badge/vip-badges.htm',
        data: {
            carType: URLParams.carStyle,
            patternCode: URLParams.patternCode,
            sceneCode: URLParams.sceneCode,
            kemuAllPriority: true
        }
    });
    console.log('徽章接口返回数据', res);
    console.log('徽章接口返回数据itemList', res.itemList);
    return res.itemList || [];
}
// 补偿信息接口
export async function getPayOutInfo(): Promise<any> {
    const res = await request({
        hostName: 'pony',
        url: 'api/open/comp/get-need-sign-contract-kemus.htm',
        data: {
            tiku: URLParams.carStyle
        }
    });
    return res.itemList || [];
}
// 会员推荐教师接口 
export async function getRecommendVip(kemu: number): Promise<any> {
    const res = await request({
        hostName: 'panda',
        url: 'api/open/top-lesson/get-vip-recommend.htm',
        data: {
            carType: URLParams.carStyle,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            kemu
        }
    });
    res.itemList && res.itemList.forEach((res) => {
        res.price = formatPrice(res.price);
    });
    return res.itemList || [];
}
// 直播课入口接口
export async function getVipExclusiveLive(data: { kemu: number }): Promise<any> {
    const vipExclusiveLive = await request({
        hostName: 'monkey',
        url: 'api/open/live/get-vip-exclusive-live.htm',
        data: {
            carType: URLParams.carStyle,
            kemu: data.kemu,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            exclusiveLive: true
        }
    });
    if (vipExclusiveLive) {
        vipExclusiveLive.teacherAvatar = calcImg(vipExclusiveLive?.teacherAvatar);
    }
    return vipExclusiveLive;
}
// 获取升级商品接口,按位置推荐
export async function getRecommendGoodsRequest(data: { examRecords: string, kemu: string | number }): Promise<any> {
    const provide = await hostApi();
    if (!provide) {
        const getRecommendGoods = await request({
            hostName: 'sirius',
            url: 'api/web/sales-recommend-goods/get-recommend-goods-list.htm',
            data: {
                tiku: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                ...data
            }
        });
        return getRecommendGoods;
    }
    const getRecommendGoods = newgetRecommendGoods(data);

    return getRecommendGoods;
}
// 获取是否有补偿接口
export async function hasClaimsRight(data: { kemu: number }): Promise<any> {
    const rightsData = await request({
        hostName: 'pony',
        url: 'api/open/comp/has-comp-rights.htm',
        data: {
            tiku: URLCommon.tiku,
            ...data
        }
    });
    return rightsData || {};
}

// 获取VIP补偿活动的信息
export async function hasClaimsVipActiveInfo(data: { kemu: number }): Promise<any> {
    const res = await request({
        hostName: 'sirius',
        url: 'api/open/vip-claims/check-activity.htm',
        data: {
            tiku: URLCommon.tiku,
            ...data
        }
    });

    return res || {};
}

// 获取权益接口
export async function getRights(data: { kemu: number }): Promise<any> {
    const rightsData = await request({
        hostName: 'pony',
        url: 'api/open/ui-function/query-function-entrance.htm',
        data: {
            carType: URLCommon.tiku,
            tiku: URLCommon.tiku,
            ...data
        }
    });
    return rightsData.itemList || [];
}
export async function getVip(data: { kemu: number }): Promise<any> {
    const vipData = await request({
        hostName: 'sirius',
        url: 'api/open/vip/view.htm',
        data: {
            tiku: URLCommon.tiku,
            ...data
        }
    });
    return vipData;
}
// 判断用户是否能换购
export async function getBarterableOrders(): Promise<any> {
    const barterableOrders = await request({
        hostName: 'sirius',
        url: 'api/open/barter/get-barterable-orders.htm',
        data: {
            carType: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        }
    });
    return barterableOrders.itemList || [];
}
// 获取换购商品列表
export async function getBarterGoodsList(): Promise<any> {
    const barterGoodsList = await request({
        hostName: 'sirius',
        url: 'api/open/barter/get-barter-goods-list.htm',
        data: {
            carType: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        }
    });
    return barterGoodsList.itemList || [];
}
export async function getLatelyLesson(data: { carType: string, kemu: string | number, lessonType: string | number }): Promise<boolean> {
    const res = await request({
        hostName: 'monkey',
        url: 'api/open/vip-live/get-lately-lesson.htm',
        data: {
            ...data,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode

        }
    });
    return res || {};
}

/** 用户角色信息*/
export async function getUserIdentity(params?: { type: 3 | 4, kemu: KemuType }) {
    const res = await request({
        hostName: 'pony',
        url: 'api/open/user-member-identity/get-user-identity.htm',
        data: {
            carType: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            type: 3,
            ...params
        }
    });

    return (res.itemList || []).filter(item => item.status === 1);
}

// 直播课入口接口
export async function latestLive(data: { kemu: number }): Promise<any> {
    const vipExclusiveLive = await request({
        hostName: 'monkey',
        url: 'api/open/live/latest-live.htm',
        data: {
            carType: URLParams.carStyle,
            kemu: data.kemu,
            liveType: 'NORMAL',
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            exclusiveLive: true
        }
    });
    if (vipExclusiveLive) {
        vipExclusiveLive.teacherHeadImg = calcImg(vipExclusiveLive?.teacherHeadImg);
    }
    return vipExclusiveLive;
}

export async function userInit() {
    const res = await request({
        hostName: 'squirrel',
        url: 'api/open/user/init.htm',
        method: 'POST',
        data: {}
    });

    return res;
}

export async function getLatestCourse() {
    const res = await request({
        hostName: 'parrot',
        url: 'api/open/course/get-latest-course.htm'
    });

    return res;
}