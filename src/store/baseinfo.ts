/*
 * ------------------------------------------------------------------
 * 补偿相关接口
 * ------------------------------------------------------------------
 */
import { request } from ':common/request';
export const gethelpPage = (): Promise<any> => {
    return request({
        hostName: 'misc',
        url: 'api/web/help/help-page.htm'
    });
};
export const getQuestionList = (data: { labelId: number|string }): Promise<any> => {
    return request({
        hostName: 'misc',
        url: 'api/web/help/question-list.htm',
        data
    });
};
export const getQuestionDetail = (data: { questionId: number|string }): Promise<any> => {
    return request({
        hostName: 'misc',
        url: 'api/web/help/question-detail.htm',
        data
    });
};