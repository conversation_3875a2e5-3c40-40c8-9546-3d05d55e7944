/*
 * ------------------------------------------------------------------
 * 考前辅导相关视频
 * ------------------------------------------------------------------
 */
import { URLCommon, URLParams } from ':common/env';
import { request } from ':common/request';
export async function getLiveLessonSchedule(data: { carType?: string, kemu?: string | number, limit: number, lessonType?: 1 | 2 | 3 }): Promise<boolean> {
    const res = await request({
        hostName: 'monkey',
        url: 'api/open/vip-live/get-live-lesson-schedule.htm',
        data: {
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            ...data

        }
    });
    return res.itemList || [];
}
export async function getGoodsConfig(data: { carType: string, kemu: string | number, lessonType?: 1 | 2 | 3 }): Promise<boolean> {
    const res = await request({
        hostName: 'monkey',
        url: 'api/open/vip-live/get-goods-config.htm',
        data: {
            ...data,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode

        }
    });
    return res || {};
}
export async function getHistoryList(data: { carType: string, kemu: string | number, page: number, limit: number, lessonType?: 1 | 2 | 3 }): Promise<boolean> {
    const res = await request({
        hostName: 'monkey',
        url: 'api/open/vip-live/get-history-list-v2.htm',
        data: {
            ...data,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode

        }
    });

    return res.itemList || [];
}
export async function subscribe(data: { lessonId: string }): Promise<boolean> {
    const res = await request({
        hostName: 'monkey',
        url: 'api/open/live-subscribe/subscribe.htm',
        data
    });
    return res || {};
}

export async function getLessonList(data: { page: number, limit: number, tagKey: string }): Promise<any> {
    const res = await request({
        hostName: 'panda',
        url: 'api/open/top-lesson/get-lesson-list.htm',
        data: {
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            ...data
        }
    });
    return res || {};
}

export async function getLiveWithPriority(data: { lessonType?: 1 | 2 | 3 }): Promise<any> {
    const res = await request({
        hostName: 'monkey',
        url: 'api/open/vip-live/get-live-with-priority.htm',
        data: {
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            ...data
        }
    });
    return res || {};
}
