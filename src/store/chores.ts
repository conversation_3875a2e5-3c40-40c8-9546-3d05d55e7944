/*
 * ------------------------------------------------------------------
 * 其他接口
 * ------------------------------------------------------------------
 */

import { getCache, saveCache } from ':common/core';
import { CarType, KemuType, URLCommon, ABTestType, ABTestKey, Platform, Version, URLParams } from ':common/env';
import { constValue, memoizeParams } from ':common/functional';
import { request } from ':common/request';
import { calcImg, dateFormat } from ':common/utils';
import { MCProtocol } from '@simplex/simple-base';
import once from 'lodash/once';
import { GroupKey } from './goods';

/** 获取misc远程配置 */
export function getMiscConfig(data: { key: string }) {
    return request({
        hostName: 'misc',
        url: 'api/web/config/get-config.htm',
        data
    });
}

/** 获取swallow远程配置,已购买页获取远程配置的kemu是写死的，所以需要传递参数kemu */
export function getSwallowConfig(data: { key: string, kemu?: number }) {
    return request({
        hostName: 'swallow',
        url: 'api/open/config/get-config.htm',
        data: {
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            ...data
        }
    });
}

/** 获取远程配置 */
export const getConfig = once((): Promise<Record<string, any>> => {
    return request({
        hostName: 'config',
        url: 'api/web/v4/config/get.htm'
    });
});

/** 获取路线视频信息 */
export function getRouteVideo(data: { cityCode: string, placeId: string, abTestGroup?: ABTestType, handleCoachName?: boolean }) {
    return request({
        hostName: 'panda',
        url: 'api/web/route-video/list-data.htm',
        data
    });
}

/** 获取科三考试流程 */
export function getExamProcess(data: { cityCode: string }) {
    return request({
        hostName: 'panda',
        url: 'api/open/exam-process/get-list-by-city-code.htm',
        data
    });
}

/** 获取远程配置 */
export function getBarrageList() {
    return request({
        hostName: 'misc',
        url: 'api/open/route-video/barrage.htm'
    });
}

export async function getServerTime() {
    const res = await request({
        hostName: 'squirrel',
        url: 'api/web/time/now.htm'
    });
    return res.nowMillis as number;
}

export function getWantAskStatus(data: { qaCode: string }) {
    return request({
        hostName: 'misc',
        url: 'api/open/want-ask/get-status.htm',
        data
    }).then(data => Boolean(data && data.status === 1), constValue(false));
}

// 短时提分视频
export function getFourStep(data: { carType: CarType, kemu: KemuType, sceneCode?: 101 | 102, patternCode?: 101 | 102 }) {
    return request({
        hostName: 'panda',
        url: 'api/web/top-lesson/four-step.htm',
        data
    }).then(data => {
        data = data.itemList || data;
        for (let i = 0; i < data.length; i++) {
            try {
                data[i].imgUrl = JSON.parse(data[i].banner)[0].image;
            } catch (e) {
                data[i].imgUrl = '';
            }
        }
        return data;
    });
}
// 获取权益
export function getSessionList() {
    return request({
        hostName: 'sirius',
        url: 'api/web/goods/get-user-goods-session-list.htm',
        data: {
            tiku: URLCommon.tiku
        }
    }).then(data => {
        const sessionList = data.itemList;
        const sessionMap = {};

        for (let i = 0; i < sessionList.length; i++) {
            if (!sessionMap[sessionList[i].uniqKey]) {
                sessionMap[sessionList[i].uniqKey] = sessionList[i];
            }
        }
        return {
            sessionList,
            sessionMap
        };
    });
}

export type StrategyType = Partial<{
    [k in ABTestKey]: ABTestType
}>

// abtest
export const getAbtest = once(async (carType?): Promise<{ strategy: StrategyType, abTest: string }> => {
    const strategy: StrategyType = {} as StrategyType;
    const abTest = [];

    if (Platform.isMuCang && Version.bizVersion > 9) {
        return new Promise((resovle) => {
            MCProtocol['jiakao-global'].getAbTestConfig({
                callback: ({ data }) => {
                    for (const key in data) {
                        strategy[key] = data[key].strategy;
                        abTest.push(key + '_' + data[key].startTime + '_' + data[key].strategy);
                    }
                    console.log('通过协议获取到的,' + JSON.stringify(strategy));
                    resovle({
                        strategy,
                        abTest: abTest.join(',')
                    });
                }
            });
        });
    }

    try {
        const res = await request({
            hostName: 'swallow',
            url: 'api/open/ab-test/get-all-ab-test.htm',
            data: {
                carType: carType || URLCommon.tiku
            }
        });

        const itemList = res.itemList || [];
        itemList.forEach(item => {
            strategy[item.key] = item.strategy;
            abTest.push(item.key + '_' + item.startTime + '_' + item.strategy);
        });
        console.log('swallow请求拿到的abtest值', JSON.stringify(strategy));

        return {
            strategy,
            abTest: abTest.join(',')
        };
    } catch (e) {
        return {
            strategy,
            abTest: abTest.join(',')
        };
    }
});

export function getUserQuestions(config?) {
    return request({
        hostName: 'misc',
        url: 'api/open/faq/list.htm',
        data: {
            kemu: URLCommon.kemu,
            status: config?.status || 1
        }
    });
}

// 科二视频列表(按区域区分)
export function getSceneListByArea(data: {
    _userCity: string,
    areaCode?: string
}) {
    return request({
        hostName: 'misc',
        url: 'api/open/scene/list-page.htm',
        data
    });
}

// 科二视频列表
export function getSceneList(cc: string) {
    return request({
        hostName: 'jiakao3d',
        url: 'api/open/scene/get-real-scene-list.htm',
        data: {
            type: 1,
            _myVer: '3.2.8',
            cc
        }
    }).then(res => res.itemList);
}

// 判断是否有权益,并返回数据
export function getPermission(permission): Promise<any> {
    return request({
        hostName: 'sirius',
        url: 'api/open/permission/has-permission.htm',
        data: {
            permission
        }
    }).then(res => {
        res.expireTimeString = dateFormat(res.validEndTime, 'yyyy-MM-dd');
        res.hasPromission = res.status === 1;
        return res;
    });
}

/** 校验设备是否超限 */
export function checkRestriction(): Promise<{
    pass: boolean,
    tipText: string
}> {
    return Promise.resolve({
        pass: true,
        tipText: ''
    });
    return request({
        hostName: 'sirius',
        url: 'api/open/device/check-restriction.htm'
    });
}

// 科三视频
export function getVideoList(cityCode: string): Promise<{ image: string, videoUrl: string, city: string }> {
    return request({
        hostName: 'panda',
        url: 'api/web/route-video/get-one.htm',
        data: {
            cityCode
        }
    }).then((res: any) => res);
}

// 获取课程相关信息
export function getLessionInfo(practiceType: string): Promise<any> {
    return request({
        hostName: 'misc',
        url: 'api/open/config/get-pop-config.htm',
        data: {
            practiceType,
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            questionId: URLParams.questionId,
            sceneCode: URLParams.sceneCode
        }
    });
}

// 获取科二考场权益
export function getKemu2ScenePermission(sceneId: string) {
    return request({
        hostName: 'panda',
        url: 'api/open/real-scene/get-real-scene-permission.htm',
        data: {
            sceneId,
            carStyle: URLCommon.tiku
        }
    }).then(res => res);
}

// 科目23权益一，灯光演示
export function getKemu23VideoList(cityCode: string) {
    return request({
        hostName: 'misc',
        url: 'api/web/light-emulator/video-list.htm',
        data: {
            cityCode
        }
    }).then(res => res);
}
export interface ActivityInfo {
    startTime: number,
    endTime: number,
    serverTime: number,
    activityIng: boolean
}
export const getActivityTime = once(function (): Promise<ActivityInfo> {
    // 不再调用接口，直接返回没有活动
    return Promise.resolve({
        endTime: 1655966417000,
        serverTime: 1673427113533,
        startTime: 1655889622000,
        activityIng: false
    });
});

/**
 *  社区讨论
 */
export function getDiscuzzList(): Promise<[]> {
    return request({
        hostName: 'cheyouquan',
        url: 'api/h5/tag/list-jinghua-topic.htm?tagId=61406&_saturnVersion=h5'
    }).then(res => res.itemList);
}

/**
 *  是否最近有买过vip
 */

export const hasBoughtVip = (): Promise<boolean> => {
    return request({
        hostName: 'sirius',
        url: 'api/web/goods/check-device-has-bought.htm'
    }).then(data => {
        return data.value;
    });
};

export function getRecentNews(data: { groupKey: GroupKey }): Promise<Array<{ avatar: string; nickname: string }>> {
    return request({
        hostName: 'sirius',
        url: 'api/web/goods/get-recent-bought-user.htm',
        data
    }).then(res => res.itemList);
}

const checkTime = location.href.includes('laofuzi.ttt') ? 60 * 1000 : 3600 * 1000;

// 获取请求的接口到底是squ还是sir
export async function hostApi(): Promise<boolean> {

    if (URLParams.apiHost === 'squirrel' || location.href.indexOf('ke3route.html') > -1 || location.href.indexOf('ke3routeDialog.html') > -1) {
        return true;
    }

    const time = (new Date()).getTime();
    const hostObj = JSON.parse(await getCache('hostApiKey'));

    if (hostObj && time - hostObj.time <= checkTime) {
        return hostObj.value;
    }

    return request({
        hostName: 'squirrel',
        url: 'api/open/sys/provide.htm',
        data: {
            bizCode: URLParams.bizCode
        }
    }).then(res => {
        saveCache({
            key: 'hostApiKey',
            value: JSON.stringify({
                time: (new Date()).getTime(),
                value: res.value
            })
        });

        return res.value;
    }).catch(error => {
        // 第一次请求并且错了，没有cache就默认返回false
        // if (!hostObj) {
        //     return true;
        // }
        saveCache({
            key: 'hostApiKey',
            value: JSON.stringify({
                time: (new Date()).getTime(),
                value: true
            })
        });

        return true;
    });
}

// 考前2小时数据

export const kq2hList = function (params: any) {
    return request({
        hostName: 'tiku',
        url: 'api/open/tag-relation/sprint-item-list.htm',
        data: {
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            sceneCode: URLParams.sceneCode,
            ...params
        }
    });
};

export const getKe2PassRate = function () {
    return request({
        hostName: 'jiakao3d',
        url: 'api/open/pass-rate/max-rate.htm',
        data: {
            kemu: KemuType.Ke2
        }
    });
};

export const getKemuAllTempActive = once(function (): Promise<{
    hasActive: boolean,
    startTime: number,
    endTime: number
}> {
    return request({
        hostName: 'squirrel',
        url: 'api/open/promotion/get-kemu-all-activity-info.htm'
    }).then((data) => {
        return {
            ...data,
            hasActive: (data.serverTime > data.startTime) && (data.serverTime < data.endTime) && URLCommon.tiku === CarType.CAR
        };
    });

    // return new Promise((resolve) => {
    //     resolve({
    //         hasActive: true,
    //         startTime: 1693497600000,
    //         endTime: 1694275200000
    //     });
    // });
});

export function noPayOrderSign(params: {
    groupKeys: GroupKey[],
    tiku: CarType
}): Promise<{
    time: number
    remind: boolean
    channelCode: GroupKey
}> {
    return request({
        hostName: 'squirrel',
        url: 'api/open/sales/get-unpaid-order-notices.htm',
        data: params
    }).then((data) => {
        const info = data.itemList[0] || {
            expireTime: 0,
            serverTime: 0
        };
        const { groupKeys } = params;
        return {
            ...info,
            time: Math.floor((info.expireTime - info.serverTime) / 1000),
            remind: info.expireTime > info.serverTime && groupKeys.includes(info.channelCode)
        };
    });
}

export function getUnionJoined(data: {
    carType: CarType;
    kemu: KemuType;
}): Promise<{
    value: boolean;
}> {
    return request({
        hostName: 'misc',
        url: 'api/open/learning-union/has-joined.htm',
        data,
        noToast: true
    });
}

export function getKemuPassRateReminder(data: {
    userCity: string
}): Promise<{
    jiaxiaoStatsContent: string
    passRateContent: string
    sourceDescription: string
    sourceActionUrl: string
}> {

    // return Promise.resolve({
    //     jiaxiaoStatsContent: '武汉市近85%驾校的',
    //     passRateContent: '【科目一考试合格率低于70%】',
    //     sourceDescription: '交通安全综合服务管理平台公布的【驾校培训质量】',
    //     sourceActionUrl: ''
    // });

    return request({
        hostName: 'misc',
        url: 'api/open/operation-config/get-kemu-pass-rate-reminder.htm',
        data: {
            ...data,
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            configKey: 'kemu_pass_rate_reminder_vip_page'
        }
    });
}

export function getBrandList(): Promise<{
    arr: any[]
    num: number
}> {
    return request({
        hostName: 'misc',
        url: 'api/open/light-emulator/car-list.htm',
        data: {
            cityCode: 0,
            country: true,
            majorVersion: 12
        }
    }).then((data) => {
        const arr = [];
        let list = [];
        const num = data.itemList.length;
        data.itemList.forEach((item, index) => {
            list.push(item);
            if (index !== 0 && (index + 1) % 12 === 0) {
                arr.push(list);
                list = [];
            } else if (index === data.itemList.length - 1) {
                arr.push(list);
            }

        });

        return {
            arr,
            num
        };
    });
}

export interface VideoGoods {
    anchorItemList: Array<{
        name: string;
        second: number;
    }>;
    channelCode: GroupKey;
    imageUrl: string;
    introduce: string;
    name: string;
    videoId: string;
    videoDTO: {
        duration: number;
        encryptHighMap: { [key: string]: string };
        encryptHigherMap: { [key: string]: string };
        encryptLowMap: { [key: string]: string };
        encryptMiddleMap: { [key: string]: string };
        highUrl: string;
        higherUrl: string;
        lowUrl: string;
        middleUrl: string;
        thumbnail: {
            height: number;
            width: number;
            url: string;
        };
    };
}

export function getGoodsIntroVideo(data: { entrance: string }): Promise<{ itemList: VideoGoods[] }> {
    return request({
        hostName: 'misc',
        url: 'api/web/business-operation-config/goods-introduce-video.htm',
        data
    });
}

export const kemuParamsMap = {
    10: {
        kemu: 1,
        sceneCode: 101
    },
    20: {
        kemu: 4,
        sceneCode: 101
    },
    30: {
        kemu: 1,
        sceneCode: 102
    },
    40: {
        kemu: 1,
        sceneCode: 101
    }
};

// 私教班获取学生课后作业
export function getStudentCourseList(): Promise<any> {
    return request({
        hostName: 'parrot',
        url: 'api/web/course/get-student-course-list.htm'
    }).then(data => {
        return data;
    });
}

export const getStudentBaseInfo = function (params: {
    sno?: string
}) {
    return request({
        hostName: 'parrot',
        url: 'api/web/student/get-student-base-info.htm',
        data: params
    });
};

// 私教班获取课程视频和笔记
export function getCourseNote(params: { lessonId: string }): Promise<{
    lessonResources: string[]
    questionIds: number[]
}> {
    return request({
        hostName: 'parrot',
        url: 'api/web/lesson/get-lesson-detail.htm',
        data: params
    }).then(data => {

        return data;
    });
}

// 分割线信息
export const getDivisionInfo = memoizeParams((params: {
    type: 10 | 20
}): Promise<any> => {
    return request({
        hostName: 'rights',
        url: 'api/open/medal/get.htm',
        data: params,
        noToast: true
    }).then(data => {
        const res = data.itemList[0] || {};

        res.imageUrl = res.imageUrl && calcImg(res.imageUrl);

        return res;
    });
});

export const getArtfulGroupList = (): Promise<any> => {
    return request({
        hostName: 'tiku',
        url: 'api/open/artful/artful-group-list.htm',
        data: {
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            sceneCode: URLParams.sceneCode
        }
    });
};

export const getArtfulGroupDetail = (params: {
    artfulGroupId: number
}): Promise<any> => {
    return request({
        hostName: 'tiku',
        url: 'api/open/artful/artful-group-detail.htm',
        data: {
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            sceneCode: URLParams.sceneCode,
            ...params
        }
    });
};

export const getVipPurchaseActivityType = (data: {
    tiku: CarType;
    kemu: KemuType;
}): Promise<{
    /** 1代表犹豫人群 */
    value: 0 | 1;
}> => {
    return request({
        hostName: 'squirrel',
        url: 'api/open/sales/get-vip-purchase-activity-type.htm',
        data
    });
};

export const getVipRecommendGoods = once((data: {
    recKey: string;
}): Promise<any> => {

    return request({
        hostName: 'eagle',
        url: 'api/open/recommend/predict.htm',
        data
    });
});