/*
 * ------------------------------------------------------------------
 * 促销活动相关接口
 * ------------------------------------------------------------------
 */

import { Platform, URLCommon, URLParams } from ':common/env';
import { IOSGoodsInfo } from ':common/features/ios_pay';
import { request } from ':common/request';
import { formatPrice } from ':common/utils';
import { hostApi } from './chores';
import { GoodsInfo, GroupKey } from './goods';
import { ActivityType, globalGoodsInfo } from './newGoods';
import { newComerGlobleUiconfig, newGetPromotionSessions } from './new_promotion';

export interface PromotionGoodsInfo extends GoodsInfo {
    sceneCode: string;
    kemu: number;
    iOSGoodsInfo: IOSGoodsInfo;
    label: string;
}

/** 获取促销活动，该接口获取的是某个促销类型下所有的促销活动 */
export async function getPromotionSessions(data: {
    /** 促销类型,新人专享活动类型为2 */
    promotionType: number
}) {
    const provide = await hostApi();
    if (!provide) {

        const res: {
            itemList: Array<{
                groupKey: GroupKey,
                name: string,
                sessionIdList: number[],
                promotionDetail: {
                    promotionPriceInfo: {
                        activityType: string,
                        appleId: string,
                        applePrice: number,
                        price: number,
                        originalActivityType: string,
                        originalAppleId: string,
                        originalApplePrice: number,
                        originalPrice: number
                    },
                    /** 促销活动状态 1-未开始，2-进行中，3-已结束, -1-未知 */
                    promotionStatus: 1 | 2 | 3 | -1,
                    /** 促销开始时间，毫秒数 */
                    promotionStartTime: number,
                    /** 促销结束时间，毫秒数 */
                    promotionEndTime: number
                },
                validDays: string,
                sceneCode: string,
                kemu: number,
                bought: boolean,
                canRebuy: boolean,
                label: string,
                upgrade: boolean,
                upgradeStrategyCode: string,
            }>
        } = await request({
            url: 'api/open/sales-promotion/get-user-promotion.htm',
            data: {
                tiku: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                ...data
            }
        });
        return res.itemList.map<PromotionGoodsInfo>(res => {
            const price = Platform.isIOS ? res.promotionDetail.promotionPriceInfo.applePrice : res.promotionDetail.promotionPriceInfo.price;
            const originalPrice = Platform.isIOS ? res.promotionDetail.promotionPriceInfo.originalApplePrice : res.promotionDetail.promotionPriceInfo.originalPrice;
            return {
                appleId: res.promotionDetail.promotionPriceInfo.appleId,
                goodsName: res.name,
                description: '',
                bought: !res.canRebuy && res.bought,
                sessionIds: res.sessionIdList,
                payPrice: formatPrice(price),
                originalPrice: formatPrice(originalPrice),
                activityType: res.promotionDetail.promotionPriceInfo.activityType,
                name: res.name,
                validDays: res.validDays,
                groupKey: res.groupKey,
                sceneCode: res.sceneCode,
                kemu: res.kemu,
                label: res.label,
                upgrade: res.upgrade,
                upgradeStrategyCode: res.upgradeStrategyCode,
                iOSGoodsInfo: {
                    sessionIdList: res.sessionIdList,
                    groupKey: res.groupKey,
                    appleId: res.promotionDetail.promotionPriceInfo.appleId,
                    activityType: res.promotionDetail.promotionPriceInfo.activityType,
                    price: res.promotionDetail.promotionPriceInfo.price,
                    originalPrice: res.promotionDetail.promotionPriceInfo.originalApplePrice,
                    applePrice: res.promotionDetail.promotionPriceInfo.applePrice
                }
            };
        });
    }
    return newGetPromotionSessions({ activityType: ActivityType.newComer });
}

export interface PromotionExtra {
    img: string;
    videoCover: string;
    video: string;
    bgc?: string
}

export async function getPromitionExtra(data: {
    /** 促销类型,新人专享活动类型为2 */
    promotionType?: number;
    groupKey: GroupKey;
}): Promise<PromotionExtra> {
    const provide = await hostApi();
    if (!provide) {
        const res = await request({
            url: 'api/web/sales-promotion/query-extra-info.htm',
            data
        });
        return res.value ? JSON.parse(res.value) : {};
    }
    // 新人专享
    if (data.promotionType === 2) {
        return newComerGlobleUiconfig[data.groupKey].uiConfig;
    }

    return Promise.resolve(globalGoodsInfo[data.groupKey].headConfig);
}
