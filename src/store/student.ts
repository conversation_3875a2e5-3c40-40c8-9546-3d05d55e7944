/*
 * ------------------------------------------------------------------
 * 考前辅导相关视频
 * ------------------------------------------------------------------
 */
import { URLCommon, URLParams } from ':common/env';
import { request } from ':common/request';
export interface Authenticate {
    authEndTime: string,
    authStatus: number,
    userNameMask: string,
    idCardMask: string,
    schoolNameMask: string
}

export enum IdentifiedStatus {
    noIdentified = 0,
    identifieding = 10,
    identified = 20,
    noPass = 30
}

export async function queryAuthRes(): Promise<Authenticate> {
    const res = await request({
        hostName: 'squirrel',
        url: 'api/open/sales-stud-buy/query-auth-result.htm',
        data: {}
    });

    return res || {};
}
export async function submitAuthApply(data: { userName: string, idCardNum: string, schoolName?: string }): Promise<{
    authResult: boolean
    errorMsg: string
}> {
    const res = await request({
        hostName: 'squirrel',
        url: 'api/open/sales-stud-buy/submit-auth.htm',
        method: 'POST',
        data: {
            userName: data.userName,
            idCardNum: data.idCardNum,
            // 由于客户端会把schoolName为空，所以更换字段名
            campusName: data.schoolName
        }
    });
    return res;
}