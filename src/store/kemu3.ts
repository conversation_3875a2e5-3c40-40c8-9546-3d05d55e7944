import { URLCommon } from ':common/env';
import { request } from ':common/request';
export async function getStep1Video() {
    const res: any = await request({
        hostName: 'misc',
        url: 'api/web/route-video/get-exam-project-video.htm',
        data: {
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu
        }
    });
    return res.itemList || [];
}
export async function getStep2Video(data: { step: string | number }) {
    const res: any = await request({
        hostName: 'misc',
        url: 'api/web/route-video/get-exam-step-video.htm',
        data: {
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            ...data
        }
    });
    return res.itemList || [];
}
export async function getPractice(data: { cityCode: string | number }) {
    const res: any = await request({
        hostName: 'panda',
        url: 'api/web/route-video/has-practice.htm',
        data: {
            ...data
        }
    });
    return res || {};
}

export async function isKe3ClaimOpen(data: { cityCode: string | number }) {
    const res: any = await request({
        hostName: 'koala',
        url: 'api/open/comp-page-config/k3-city-is-open.htm',
        data: {
            ...data
        }
    });
    return res || {};
}
