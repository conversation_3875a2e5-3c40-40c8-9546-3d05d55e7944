/*
 * ------------------------------------------------------------------
 * 是否是vip
 * ------------------------------------------------------------------
 */

import { URLCommon, URLParams } from ':common/env';
import { request } from ':common/request';
/** 获取swallow远程配置 */
export const getIsVipInfo = async () => {
    const vipData = await request({
        hostName: 'pony',
        url: 'api/open/user-member-identity/get-user-identity.htm',
        data: {
            carType: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        }
    });
    // 全科要单独展示全科的徽章
    let isVip = false;
    let flagKemuallVip = false;
    let userVipIcon = '';
    vipData.itemList && vipData.itemList.forEach((res) => {

        if (+res.kemu === 0 && res.status === 1) {
            flagKemuallVip = true;
            isVip = true;
            userVipIcon = res.icon;
        }

        if (+res.kemu === URLCommon.kemu && res.status === 1 && !flagKemuallVip) {
            isVip = true;
            userVipIcon = res.icon;
        }
    });

    return { isVip, userVipIcon };
};
// 获取推荐会员接口
export const getMemberRecomand = async (params) => {
    return request({
        hostName: 'panda',
        url: 'api/open/top-lesson/get-vip-recommend.htm',
        data: {
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            ...params
        }
    });
};
