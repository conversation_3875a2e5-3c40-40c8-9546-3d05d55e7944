import { URLCommon } from ':common/env';
import { request } from ':common/request';
import { dateFormat } from ':common/utils';
import { CouponInfo } from './coupon';
import { GroupKey } from './goods';
import { globalGoodsInfo } from './newGoods';

export const newGetGoodsCoupons = async () => {
    const data = await request({
        hostName: 'squirrel',
        url: 'api/web/vip-coupon/get-coupon-list.htm',
        data: {
            bizType: 1
        }
    });
    const itemList = data.itemList;
    const goodsCoupons = [];
    itemList.forEach(item => {

        item = {
            name: item.name,
            uniqKey: item.tplCode,
            desc: item.description,
            priceCent: item.discountValue * 100,
            validStartDate: item.validStartTime,
            validTimeSecond: item.validDuration / 1000,
            send: !item.hasReceived,
            validEndTime: item.ownedValidEndTime
        };

        if (item.uniqKey.indexOf('coupon_act') === -1) {
            goodsCoupons.push(item);
        }
    });

    return {
        goodsCoupons
    };
};

export const newGetUserCoupons = async (data: { groupKey: GroupKey }) => {
    const res = await request({
        hostName: 'squirrel',
        url: 'api/open/coupon/list.htm',
        data: {
            goodsDataType: globalGoodsInfo[data.groupKey].dataType,
            goodsDataCode: globalGoodsInfo[data.groupKey].dataCode,
            tiku: URLCommon.tiku
        }
    });

    const coupons: any[] = res.itemList.map(item => ({
        couponUniqKey: item.couponTplCode,
        couponCode: item.couponCode,
        priceCent: item.discountValue * 100,
        goodsCouponData: {
            name: item.couponName,
            desc: item.description,
            priceCent: item.discountValue * 100,
            uniqKey: item.couponTplCode
        },
        validStartTime: item.activeStartTime,
        validEndTime: dateFormat(item.activeEndTime, 'yyyy-MM-dd'),
        discountType: item.discountType,
        canUse: item.canUse,
        expired: item.status === 3,
        used: item.status === 2
    }));
    const canUse: CouponInfo[] = [];
    const used: CouponInfo[] = [];
    const expired: CouponInfo[] = [];

    coupons.forEach(item => {

        if (item.canUse) {
            canUse.push(item);
        } else if (item.used) {
            used.push(item);
        } else if (item.expired) {
            expired.push(item);
        }
    });
    
    return {
        userCoupons: coupons,
        canUseCoupons: canUse,
        usedCoupons: used,
        expiredCoupons: expired
    };
};

export const newGetCode = (data: { couponUniqKey: any }) => {
    return request({
        hostName: 'squirrel',
        url: 'api/open/vip-coupon/pickup.htm',
        method: 'POST',
        data: {
            couponTplCode: data.couponUniqKey
        }
    });
};

export const newExchangeCoupon = (data: { couponCode: string }): Promise<any> => {
    return request({
        hostName: 'squirrel',
        url: 'api/open/coupon/exchange.htm',
        method: 'POST',
        data: {
            couponCode: data.couponCode
        }
    }).then(data => {
        return {
            exchange: true
        };
    });
};

export const newGetCouponDetail = (data: { couponCode: string }): Promise<any> => {
    return request({
        hostName: 'squirrel',
        url: 'api/open/vip-coupon/get-coupon-detail.htm',
        data: {
            couponTplCodes: data.couponCode
        }
    }).then(data => {
        const item = data.itemList[0];
        return {
            couponName: item.name,
            couponCode: item.tplCode,
            priceCent: item.discountValue * 100,
            validTimeSecond: item.ownedValidEndTime
        };
    });
};