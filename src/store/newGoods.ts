/**
 * 新版商品详情新接口
 * */

import { CarType, PageName, PayType, Platform, URLCommon, URLParams } from ':common/env';
import { request } from ':common/request';
import { dateFormat, formatPrice } from ':common/utils';
import { ComparePriceInfo, CreateOrderParams, GoodsInfo, GroupComparePrice, GroupKey, OrderInfo } from './goods';
import { MCProtocol } from '@simplex/simple-base';
import { webClose } from ':common/core';
import { getAbtest, hostApi } from './chores';
import { getFromPageCode, getFromPathCode, getPushCode, getRecommendGoodsContent, getStatAbtestStr } from ':common/stat';
import { hasCheckbox } from ':common/features/agreement';
import { makeToast } from ':common/features/dom';

const PayRef = 'JIAKAOBAODIAN';

export const globalGoodsInfo: Partial<Record<GroupKey, GoodsInfo>> = {

};

interface NewGoodsInfoRes {
    goods: {
        hasRoute: boolean
        'goodsName': string,
        'goodsDescription': string,
        'dataCode': string,
        'dataType': number,
        'durationDescription': string,
        'tiku': CarType
        times: number
    }
    channelCode: string
    uiConfig: {
        'label': string,
        'highlights': { highlight: string, description: string }[]
    }
    promotion: {
        discountStatus: number
        discountStartTime: string
        discountEndTime: string
        uiConfig: {
            'img': string,
            'videoCover': string,
            'video': string,
            'bgc': string,
            'label': string
        }
    }
    /** 赠品活动信息 */
    giftPromotion?: {
        promotionStatus: number
        promotionStartTime: number
        promotionEndTime: number
        giftChannelCode: GroupKey
    }
    /** 续订模版 */
    renewTpl?: {
        tplCode: string
        payTypes: PayType[],
        renewDescription: string
    }
}

export interface NewGoodsInfo {
    /** 商品名称 */
    name: string;
    /** 商品ID */
    groupKey: GroupKey;
    /** 商品权益列表 */
    // sessionIds: number[];

    /** 价格 */
    payPrice: string;

    /** 是否购买 */
    bought: boolean;

    /** 有效天数 */
    validDays: number;

    /** 是否可以升级 */
    upgrade: boolean;
    /** 升级策略 */
    upgradeStrategyCode?: string;

    /** 是否包含路线 */
    containRoute?: boolean;
    /** 是否包含3D */
    // containThreeD?: boolean;
    /** 促销活动信息 */
    inActivity?: {
        preDiscountPrice: string,
        discountedPrice: string,
        discountStartTime: number,
        discountEndTime: number,
    }
    /** 赠品活动信息 */
    giftPromotion?: {
        promotionStatus: number
        promotionStartTime: number
        promotionEndTime: number
        giftChannelCode: GroupKey
    }
    /** 商品描述 */
    description: string
    /** tip角标和亮点 */
    tips: any
    /** 活动头图相关配置 */
    headConfig: any
}

export enum ActivityType {
    // 分销定制
    promotionCustom = 'promotion_custom',
    // 限时促销
    timeLimited = 'time_limited',
    // 新人专享
    newComer = 'new_comer',
    // 升级优惠
    upgrade = 'upgrade',
    // 优秀学员
    excellentStudent = 'excellent_student',
    // 加购
    additionalPurchase = 'additional_purchase',
    // 福袋
    luckyBag = 'lucky_bag',
    // 秒杀福利
    fs = 'fs'
}

interface PriceRes {
    goods: {
        dataType: number
        dataCode: string
        hasRoute: boolean
        'goodsName': string,
        'goodsDescription': string,
        'durationDescription': string,
        'tiku': CarType
    }
    channelCode: string
    priceConfigCode: string,
    promotionDetailCode: string,
    suggestedPrice: number,
    channelPrice: number,
    salePrice: number,
    appleGoodsId: string,
    activityType: ActivityType,
    activityCode: string,
    userBoughtInfo: {
        effected: boolean,
        'expired': boolean,
        'expireTime': string
    }
}

export async function newGetGroupSessionInfo(data: { groupKeys: GroupKey[], activityType?: ActivityType, needRecommendGoods?: boolean }): Promise<GoodsInfo[]> {
    const goodInfoList: GoodsInfo[] = [];
    const requests = [request({
        url: 'api/open/sales/get-channel-goods-detail.htm',
        hostName: 'squirrel',
        data: {
            channelCodes: data.groupKeys.join(',')
        }
    }), request({
        url: 'api/open/sales/get-channel-goods-price.htm',
        hostName: 'squirrel',
        data: {
            tiku: URLCommon.tiku,
            channelCodes: data.groupKeys.join(','),
            activityType: data.activityType,
            placeId: URLParams.placeId
        }
    }).catch(error => {
        error = error.data;
        if (error.errorCode === 401001) {
            MCProtocol.Core.System.confirm({
                title: '设备超限',
                message: error.message || error.statusText,
                action: '确定',
                cancel: '取消',
                callback: () => {
                    webClose();
                }
            });
        }
        throw error;
    })];
    if (data.needRecommendGoods) {
        requests.push(request({
            url: 'api/open/recommend-goods/get-recommend-goods.htm',
            hostName: 'squirrel',
            data: {
                recommendType: 'goods',
                channelCodes: data.groupKeys.join(','),
                patternCode: URLParams.patternCode,
                sceneCode: URLParams.sceneCode
            }
        }));
    }

    await Promise.all(requests).then(async ([goodRes, priceRes, recommendRes]: [{ itemList: NewGoodsInfoRes[] }, { itemList: PriceRes[] }, any]) => {
        const recommendResMap = {};
        recommendRes?.itemList.forEach(item => {
            if (!recommendResMap[item.channelCode]) {
                recommendResMap[item.channelCode] = [item];
            } else {
                recommendResMap[item.channelCode].push(item);
            }
        });

        goodRes.itemList.forEach((item, index) => {
            const priceItem = priceRes.itemList[index];
            priceItem.salePrice *= 100;
            priceItem.channelPrice *= 100;
            const expiredTime = priceItem.userBoughtInfo.expireTime && dateFormat(priceItem.userBoughtInfo.expireTime, 'yyyy.MM.dd HH:mm');
            const payPrice = formatPrice(priceItem.salePrice);

            let inActivity;
            let headConfig;
            if (item.promotion?.discountStatus === 2) {
                const preDiscountPrice = formatPrice(priceItem.channelPrice);
                const discountedPrice = String(Math.floor(+preDiscountPrice - +payPrice));

                inActivity = {
                    preDiscountPrice,
                    discountedPrice,
                    discountStartTime: item.promotion?.discountStartTime,
                    discountEndTime: item.promotion?.discountEndTime
                };
                headConfig = {
                    ...item.promotion?.uiConfig
                };
            }

            const goodInfo: GoodsInfo = {
                appleId: priceItem.appleGoodsId,
                bought: priceItem.userBoughtInfo.effected,
                expired: priceItem.userBoughtInfo.expired,
                expiredTime,
                containRoute: item.goods.hasRoute,
                sessionIds: [data.groupKeys[index]],
                payPrice,
                originalPrice: formatPrice(priceItem.channelPrice),
                upgrade: priceItem.activityType === ActivityType.upgrade,
                name: item.goods.goodsName,
                goodsName: item.goods.goodsName,
                validDays: item.goods.durationDescription.replace(/[^0-9]/g, ''),
                groupKey: data.groupKeys[index],
                upgradeStrategyCode: priceItem.activityType === ActivityType.upgrade ? priceItem.activityCode : '',
                recommendPurchaseGroupKeyList: recommendResMap[data.groupKeys[index]]?.map(item => item.recommendChannelCode),
                description: item.goods.goodsDescription,
                inActivity,

                tips: item.uiConfig,
                headConfig,
                channelCode: item.channelCode,
                dataCode: item.goods.dataCode,
                dataType: item.goods.dataType,
                promotionDetailCode: priceItem.promotionDetailCode,
                activityType: priceItem.activityType,
                activityCode: priceItem.activityCode,
                priceConfigCode: priceItem.priceConfigCode,
                times: item.goods.times,
                giftPromotion: item.giftPromotion?.promotionStatus === 2 ? item.giftPromotion : null,
                renewTpl: item.renewTpl
            } as unknown as GoodsInfo;

            globalGoodsInfo[data.groupKeys[index]] = goodInfo;

            goodInfoList.push(goodInfo);
        });
    });

    return goodInfoList;
}

export async function newComparePrice(data: { groupKey: GroupKey }): Promise<ComparePriceInfo> {
    const comparePrice = await request({
        url: 'api/open/price-compare/list.htm',
        hostName: 'squirrel',
        data: {
            channelCode: globalGoodsInfo[data.groupKey].channelCode,
            dataType: globalGoodsInfo[data.groupKey].dataType,
            dataCode: globalGoodsInfo[data.groupKey].dataCode,
            activityType: globalGoodsInfo[data.groupKey].activityType,
            activityCode: globalGoodsInfo[data.groupKey].activityCode,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        }
    });

    let allPrice = 0;
    comparePrice?.priceCompareList.forEach(item => {
        item.price = item.price ? item.price * 100 : 0;
        allPrice += item.price;
        item.price = item.price ? formatPrice(item.price) : 0;
    });

    return comparePrice && {
        allPrice: formatPrice(allPrice),
        groupItems: comparePrice.priceCompareList,
        savePrice: formatPrice(+allPrice - (comparePrice.price * 100))
    };
}
export async function newGroupComparePrice(data: { groupKeys: GroupKey[] }): Promise<Partial<Record<GroupKey, ComparePriceInfo>>> {
    const goodsDataList = [];
    data.groupKeys && data.groupKeys.forEach((res) => {
        goodsDataList.push({
            activityType: globalGoodsInfo[res].activityType,
            activityCode: globalGoodsInfo[res].activityCode,
            channelCode: globalGoodsInfo[res].channelCode,
            dataType: globalGoodsInfo[res].dataType,
            dataCode: globalGoodsInfo[res].dataCode

        });
    });
    const compareRes = await request({
        url: 'api/open/price-compare/batch-list.htm',
        hostName: 'squirrel',
        method: 'GET',
        data: {
            goodsDataList: JSON.stringify(goodsDataList),
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        }
    });
    const comparePricePool = {};
    compareRes.itemList.forEach(res => {
        let allPrice = 0;
        res?.priceCompareList.forEach(item => {
            item.price = item.price ? item.price * 100 : 0;
            allPrice += item.price;
            item.price = item.price ? formatPrice(item.price) : 0;
        });

        comparePricePool[res.channelCode] = {
            ...res,
            allPrice: formatPrice(allPrice),
            upGroupItems: res.priceCompareList || [],
            groupItems: res.priceCompareList || [],
            diffPrice: formatPrice(+allPrice - (res.price * 100)),
            savePrice: formatPrice(+allPrice - (res.price * 100))
        };
    });

    return comparePricePool;
}
export async function newCreateMobileOrder(data: CreateOrderParams): Promise<OrderInfo> {

    const appleGoodsId = globalGoodsInfo[data.groupKey].appleId;

    if (Platform.isAndroid) {
        return request({
            hostName: 'squirrel',
            url: 'api/open/order/place-order.htm',
            method: 'POST',
            data: {
                goodsDataName: globalGoodsInfo[data.groupKey].name,
                orderRef: PayRef,
                pageData: JSON.stringify({
                    score12: URLParams.score12,
                    fromPathCode: getFromPathCode(),
                    fromPageCode: await getFromPageCode(),
                    pushCode: getPushCode(),
                    questionId: URLParams.questionId,
                    courseId: URLParams.courseId,
                    sceneCode: URLParams.sceneCode,
                    patternCode: URLParams.patternCode,
                    fromItemCode: URLParams.fromItemCode || '',
                    kemu: URLParams.kemuStyle,
                    carStyle: URLParams.carStyle,
                    routeId: URLParams.routeId,
                    placeId: URLParams.placeId,
                    k2AssetsId: URLParams.k2AssetsId,
                    assetsId: URLParams.assetsId,
                    subject: URLParams.subject,
                    pageName: PageName,
                    fragmentName1: data.fragmentName1,
                    fragmentName2: data.fragmentName2,
                    groupKey: data.groupKey,
                    abTest: await getStatAbtestStr(),
                    recommendGoodsContent: await getRecommendGoodsContent(),
                    hasOpenCheckBox: (await hasCheckbox()) + '',
                    fromH5: 1
                }),
                ...data,
                appleGoodsId: globalGoodsInfo[data.groupKey].appleId,
                goodsDataCode: globalGoodsInfo[data.groupKey].dataCode,
                goodsDataType: globalGoodsInfo[data.groupKey].dataType,
                channelCode: globalGoodsInfo[data.groupKey].channelCode,
                activityType: globalGoodsInfo[data.groupKey].activityType,
                activityCode: globalGoodsInfo[data.groupKey].activityCode,
                priceConfigCode: globalGoodsInfo[data.groupKey].priceConfigCode,
                typeCode: data.payType,
                payType: data.payType,
                platformType: data.payType === PayType.Harmony ? 'huawei_iap' : 'mobile'
            }
        }).then(data => {
            data.orderNumber = data.orderNo;
            data.appleGoodsId = appleGoodsId;
            return data;
        });
    }

    return request({
        hostName: 'squirrel',
        url: 'api/open/order/place-apple-order.htm',
        method: 'POST',
        data: {
            goodsDataName: globalGoodsInfo[data.groupKey].name,
            orderRef: PayRef,
            pageData: JSON.stringify({
                score12: URLParams.score12,
                fromPathCode: getFromPathCode(),
                fromPageCode: await getFromPageCode(),
                pushCode: getPushCode(),
                questionId: URLParams.questionId,
                courseId: URLParams.courseId,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                fromItemCode: URLParams.fromItemCode || '',
                kemu: URLParams.kemuStyle,
                carStyle: URLParams.carStyle,
                routeId: URLParams.routeId,
                placeId: URLParams.placeId,
                k2AssetsId: URLParams.k2AssetsId,
                assetsId: URLParams.assetsId,
                subject: URLParams.subject,
                pageName: PageName,
                fragmentName1: data.fragmentName1,
                fragmentName2: data.fragmentName2,
                groupKey: data.groupKey,
                abTest: await getStatAbtestStr(),
                recommendGoodsContent: await getRecommendGoodsContent(),
                hasOpenCheckBox: (await hasCheckbox()) + '',
                fromH5: 1
            }),
            placeId: URLParams.placeId,
            ...data,
            appleGoodsId: globalGoodsInfo[data.groupKey].appleId,
            goodsDataCode: globalGoodsInfo[data.groupKey].dataCode,
            goodsDataType: globalGoodsInfo[data.groupKey].dataType,
            channelCode: globalGoodsInfo[data.groupKey].channelCode,
            activityType: globalGoodsInfo[data.groupKey].activityType,
            activityCode: globalGoodsInfo[data.groupKey].activityCode,
            priceConfigCode: globalGoodsInfo[data.groupKey].priceConfigCode,
            typeCode: data.payType,
            payType: data.payType
        }
    }).then(data => {
        data.orderNumber = data.orderNo;

        return data;
    });
}
// 已购买推荐商品，按位置推荐
export async function newgetRecommendGoods(data: { examRecords: string, kemu: string | number }) {
    const recommendRes = await request({
        url: 'api/open/recommend-goods/get-recommend-goods.htm',
        hostName: 'squirrel',
        data: {
            recommendType: 'location',
            recommendLocation: 'boughtPage',
            channelCodes: '',
            patternCode: URLParams.patternCode,
            sceneCode: URLParams.sceneCode,
            tiku: URLParams.carStyle,
            ...data
        }
    });
    const groupKeys = [];

    recommendRes.itemList.forEach((ele) => {
        groupKeys.push(ele.recommendChannelCode);
    });
    if (groupKeys && groupKeys.length <= 0) {
        return { itemList: [] };
    }
    const GetGroupSesstionInfo = await newGetGroupSessionInfo({ groupKeys });
    const GetGroupSesstionGroupkey = {};
    GetGroupSesstionInfo && GetGroupSesstionInfo.forEach((res) => {
        GetGroupSesstionGroupkey[res.channelCode] = res;
    });
    const comparePricePool = await GroupComparePrice({ groupKeys: groupKeys.join(',') });
    const recommengGoods = [];
    recommendRes.itemList &&
        recommendRes.itemList.forEach((res) => {
            const newGoodsInfoObject = GetGroupSesstionGroupkey[res.recommendChannelCode];
            let priceDiff = 0;
            const newcomparedGoodsItems = [];
            if (comparePricePool[res.recommendChannelCode]) {
                priceDiff = comparePricePool[res.recommendChannelCode].diffPrice || 0;
                comparePricePool[res.recommendChannelCode].upGroupItems && comparePricePool[res.recommendChannelCode].upGroupItems.forEach((res) => {
                    const object = {
                        ...res,
                        price: res.price * 100
                    };
                    newcomparedGoodsItems.push(object);
                });
            }
            const newObject = {
                ...newGoodsInfoObject,
                comparedGoodsItems: newcomparedGoodsItems,
                recommendChannelCode: res.recommendChannelCode,
                recommendGroup: res.recommendGroup,
                uiConfig: res.uiConfig,
                priceDiff: priceDiff * 100,
                price: newGoodsInfoObject.payPrice * 100,
                canUpgrade: newGoodsInfoObject.upgrade,
                tips: res.description,
                canRebuy: true,
                bought: false
            };
            recommengGoods.push(newObject);
        });
    return { itemList: recommengGoods };
}

export async function newBindOrder(data: { orderNumber: string }): Promise<boolean> {
    return request({
        hostName: 'squirrel',
        url: 'api/open/order/bind.htm',
        method: 'POST',
        data: {
            orderNos: data.orderNumber
        }
    }).then(data => data.value);
}

export async function newIsOrderBind(data: { orderNumber: string }): Promise<any[]> {
    return request({
        hostName: 'squirrel',
        url: 'api/open/order/is-bound.htm',
        data: {
            orderNos: data.orderNumber
        }
    }).then(data => data.itemList);
}

export async function newIsOrderPaid(data: { orderNumber: string }): Promise<boolean> {
    const res = await request({
        hostName: 'squirrel',
        url: 'api/open/order/get-order-status.htm',
        data: {
            orderNos: data.orderNumber
        }
    });

    if (data.orderNumber.split(',').length !== res.itemList.length) {
        makeToast('您当前的订单跟环境不匹配');
    }

    return res.itemList;
}
export async function squirrelPrice(data: { groupKeys: GroupKey[], activityType?: ActivityType }): Promise<any> {
    if (data.groupKeys && data.groupKeys.length <= 0) {
        return [];
    }
    const res: any = await request({
        url: 'api/open/sales/get-channel-goods-price.htm',
        hostName: 'squirrel',
        data: {
            tiku: URLCommon.tiku,
            channelCodes: data.groupKeys.join(','),
            activityType: data.activityType
        }
    });
    res.itemList && res.itemList.forEach((res) => {
        res.salePrice *= 100;
        res.payPrice = formatPrice(res.salePrice);
    });
    return res.itemList || [];

}