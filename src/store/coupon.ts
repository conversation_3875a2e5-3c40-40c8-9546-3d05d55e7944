/*
 * ------------------------------------------------------------------
 * 优惠券相关接口
 * ------------------------------------------------------------------
 */

import { Platform } from ':common/env';
import { request } from ':common/request';
import { dateFormat } from ':common/utils';
import { hostApi } from './chores';
import { GroupKey } from './goods';
import { newExchangeCoupon, newGetCode, newGetCouponDetail, newGetGoodsCoupons, newGetUserCoupons } from './new_coupon';

export interface CouponInfo {
    couponCode: string;
    canUse: boolean;
    used: boolean;
    expired: boolean;
    validEndTime: string;
    priceCent: number;
    goodsCouponData: {
        priceCent: number;
        desc: string;
        uniqKey: string;
        name: string
    }
}

export const getGoodsCoupons = async () => {
    const provide = await hostApi();
    if (!provide) {
        const data = await request({
            hostName: 'sirius',
            url: 'api/web/goods-coupon/get-goods-coupon-list.htm'
        });
        const itemList = data.itemList;
        const goodsCoupons = [];
        itemList.forEach(item => {
            if (item.uniqKey.indexOf('coupon_act') === -1) {
                goodsCoupons.push(item);
            }
        });
        return {
            goodsCoupons
        };
    }

    return newGetGoodsCoupons();
};

// 由于squirrel接受的参数不同，所以新商品中的sessionIds被我赋值成了GroupKey[]
export const getUserCoupons = async (data: { sessionIds: string | GroupKey }) => {
    const provide = await hostApi();
    if (!provide) {
        const res = await request({
            hostName: 'sirius',
            url: 'api/web/goods-coupon/get-goods-coupon-user-list.htm',
            data
        });
        const coupons: CouponInfo[] = res.itemList;
        const canUse: CouponInfo[] = [];
        const used: CouponInfo[] = [];
        const expired: CouponInfo[] = [];
        coupons.forEach(item => {
            item.validEndTime = dateFormat(item.validEndTime, 'yyyy-MM-dd');
            item.priceCent = item.goodsCouponData.priceCent;

            if (item.canUse) {
                canUse.push(item);
            } else if (item.used) {
                used.push(item);
            } else if (item.expired) {
                expired.push(item);
            }
        });
        return {
            userCoupons: coupons,
            canUseCoupons: canUse,
            usedCoupons: used,
            expiredCoupons: expired
        };
    }

    return newGetUserCoupons({
        groupKey: data.sessionIds as GroupKey
    });
};

export const getCode = async (data: { couponUniqKey: any }) => {
    const provide = await hostApi();
    if (!provide) {
        return request({
            hostName: 'sirius',
            url: 'api/open/goods-coupon/send.htm',
            data
        });
    }

    return newGetCode({ couponUniqKey: data.couponUniqKey });
};

export const exchangeCoupon = async (data: { couponCode: string }): Promise<any> => {
    const provide = await hostApi();
    if (!provide) {
        return request({
            hostName: 'sirius',
            url: 'api/open/goods-coupon/exchange2.htm',
            data
        });
    }
    return newExchangeCoupon({
        couponCode: data.couponCode
    });

};

export const getCouponDetail = async (data: { couponCode: string }): Promise<{
    couponName: string,
    couponCode: string,
    priceCent: number,
    validTimeSecond: number
}> => {
    const provide = await hostApi();
    if (!provide) {
        return request({
            hostName: 'sirius',
            url: 'api/web/goods-coupon/get-goods-coupon-detail.htm',
            data
        }).then(data => {
            return {
                couponName: data.name,
                couponCode: data.uniqKey,
                priceCent: data.priceCent,
                validTimeSecond: data.validTimeSecond
            };
        });
    }
    return newGetCouponDetail({
        couponCode: data.couponCode
    });
};

/**
 *  misc优惠券
*/

export const getRouteCoupon = (data: { metaId: string }): Promise<any> => {
    return request({
        hostName: 'misc',
        url: 'api/open/coupon/select-coupon-data.htm',
        data
    }).then(data => {
        data.amount /= 100;

        return data;
    });
};

export async function selectBestCoupon3D(data: {
    groupKey: string;
    /** 推荐选择的优惠券no */
    couponNo?: string;
}): Promise<{
    code: string;
    price: string;
    name: string;
}> {
    // return {
    //     no: '12',
    //     couponCode: '12',
    //     goodsDesc: "科二3D畅享,科二3D全科畅享",
    //     typeDesc: "现金券",
    //     value: "18.00",
    //     status: 1,
    //     statusDesc: "未使用",
    //     expireDate: "2020-12-09",
    //     style: 1,
    //     notReSend: true
    // }
    const EMPTY_COUPON = {
        code: '',
        price: '',
        name: '',
        is3D: true
    };
    return request({
        hostName: 'jiakao3d',
        url: 'api/open/coupon/select.htm',
        data,
        noToast: true
    }).then(res => {
        if (!res || res.status !== 1) {
            return EMPTY_COUPON;
        }
        return {
            code: res.couponCode,
            price: res.value,
            name: res.typeDesc,
            is3D: true
        };
    }, () => EMPTY_COUPON);
}
