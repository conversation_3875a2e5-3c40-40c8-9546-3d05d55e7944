import { CarType, Platform, URLCommon, URLParams } from ':common/env';
import { request } from ':common/request';
import { formatPrice } from ':common/utils';
import { GoodsInfo, GroupKey } from './goods';
import { ActivityType, globalGoodsInfo } from './newGoods';
import { PromotionGoodsInfo } from './promotion';

export const newComerGlobleUiconfig: Partial<Record<GroupKey, any>> = {};

export async function newGetPromotionSessions(data: {
    /** 促销类型,新人专享活动类型为new_comer */
    activityType: ActivityType
}): Promise<PromotionGoodsInfo[]> {
    const res: {
        itemList: Array<{
            channelCode: GroupKey,
            canBuy: boolean
            goods: {
                'goodsName': string,
                'goodsDescription': string,
                'dataCode': string,
                'dataType': string,
                'durationDescription': string,
                'tiku': string,
                'hasRoute': boolean
            }
            userBoughtInfo: {
                'effected': boolean,
                'expired': boolean,
                'expireTime': string
            }
            goodsPrice: {
                'goods': {
                    'dataType': string,
                    'dataCode': string
                },
                'channelCode': string,
                'priceConfigCode': string,
                'promotionDetailCode': string,
                'upgradeCode': string,
                'suggestedPrice': number,
                'channelPrice': number,
                'salePrice': number,
                'appleGoodsId': string,
                'activityType': string,
                'activityCode': string
            }
            promotionDetail: {
                'discountStatus': number,
                'discountStartTime': string,
                'discountEndTime': string,
                'uiConfig': {
                    'img': string,
                    'videoCover': string,
                    'video': string,
                    'bgc': string,
                    'label': string
                },
                'kemu': string,
                'sceneCode': string
            }
        }>
    } = await request({
        hostName: 'squirrel',
        url: 'api/open/promotion/get-promotion-detail.htm',
        data: {
            tiku: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            ...data
        }
    });

    const sessionIdsRes = await request({
        hostName: 'squirrel',
        url: 'api/open/mapping/get-session-ids.htm',
        data: {
            packageCodes: res.itemList.map(item => item.goods.dataCode).join(',')
        }
    });

    return res.itemList.map<PromotionGoodsInfo>((item, index) => {
        const price = item.goodsPrice.salePrice * 100;
        const originalPrice = item.goodsPrice.channelPrice * 100;
        newComerGlobleUiconfig[item.channelCode] = item.promotionDetail;
        // 由于下单是从全局取的数据，所以这里需要给全局赋值
        globalGoodsInfo[item.channelCode] = {
            appleId: item.goodsPrice.appleGoodsId,
            sessionIds: sessionIdsRes[index],
            name: item.goods.goodsName,
            dataCode: item.goods.dataCode,
            dataType: item.goods.dataType,
            channelCode: item.channelCode,
            activityType: item.goodsPrice.activityType,
            activityCode: item.goodsPrice.activityCode,
            priceConfigCode: item.goodsPrice.priceConfigCode
        } as GoodsInfo;

        return {
            appleId: item.goodsPrice.appleGoodsId,
            goodsName: item.goods.goodsName,
            description: item.goods.goodsDescription,
            bought: item.goodsPrice.activityType === ActivityType.upgrade ? true : !item.canBuy,
            sessionIds: [],
            payPrice: formatPrice(price),
            originalPrice: formatPrice(originalPrice),
            activityType: item.goodsPrice.activityType,
            name: item.goods.goodsName,
            validDays: item.goods.durationDescription.replace(/[^0-9]/g, ''),
            groupKey: item.channelCode,
            sceneCode: item.promotionDetail.sceneCode,
            kemu: +item.promotionDetail.kemu,
            label: item.promotionDetail.uiConfig?.label,
            upgrade: item.goodsPrice.activityType === ActivityType.upgrade,
            upgradeStrategyCode: item.goodsPrice.activityType === ActivityType.upgrade ? item.goodsPrice.activityCode : '',
            iOSGoodsInfo: {
                sessionIdList: [],
                groupKey: item.channelCode,
                appleId: item.goodsPrice.appleGoodsId,
                activityType: item.goodsPrice.activityType,
                price: formatPrice(price),
                originalPrice: formatPrice(originalPrice),
                applePrice: formatPrice(price)
            }
        };
    });
}