/*
 * ------------------------------------------------------------------
 * 活动的
 * ------------------------------------------------------------------
 */

import { URLCommon, URLParams } from ':common/env';
import { request } from ':common/request';
import { calcImg } from ':common/utils';
import once from 'lodash/once';
export const queryCompleteTask = (data: { activityId: string }): Promise<any> => {
    return request({
        hostName: 'activity',
        url: 'api/open/gift-benefit/query-complete-task.htm',
        data
    });
};

export const openNextTask = (data: { activityId: string, index: number }): Promise<any> => {
    return request({
        hostName: 'activity',
        url: 'api/open/gift-benefit/open-next-task.htm',
        data: {
            ...data,
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        }
    });
};

export const getExamQuestion = (data: { activityId: string, questionCategory?: 1 | 2 }): Promise<any> => {
    return request({
        hostName: 'activity',
        url: 'api/web/biz/bonus-egg/get-exam-question.htm',
        data
    });
};

export const checkUserOption = (data: { activityId: string, questionId: number, userOption: string, sourceKey: number }): Promise<any> => {
    return request({
        hostName: 'activity',
        url: 'api/web/biz/bonus-egg/check-user-option2.htm',
        data: {
            ...data,
            activityForm: 'treasure'
        }
    });
};

/** 获取模版数据，不区分小红书抖音*/
export const queryUniversalTemplateData = (activityId: string, refetch?: boolean) => {
    return request({
        hostName: 'activity',
        url: 'api/open/share-get/query-universal-material.htm',
        data: {
            activityId,
            refetch
        }
    }).then(res => {
        // 有可能是视频类型数据
        if (res.img) {
            res.img.imgList = res.img.imgList.map(item => ({
                applyScene: item.applyScene,
                imgUrl: calcImg(item.imgUrl)
            }));
        }

        return res;
    });
};

export const useMaterial = (data: any): Promise<any> => {
    return request({
        hostName: 'activity',
        url: 'api/open/share-get/use-material.htm',
        data
    });
};

export const queryTaskSummary = (data: { activityId: string }): Promise<any> => {
    return request({
        hostName: 'activity',
        url: 'api/open/gift-benefit/query-task-summary.htm',
        data: {
            ...data,
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        }
    });
};

export const queryRecommendTask = (): Promise<any> => {
    return request({
        hostName: 'activity',
        url: 'api/open/gift-benefit/query-recommend-task.htm',
        data: {
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        }
    });
};

export const queryEntranceInfo = (data: { entranceCode: string }): Promise<any> => {
    return request({
        hostName: 'activity',
        url: 'api/open/gift-benefit/query-entrance-info.htm',
        data: {
            ...data,
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        }
    });
};

/** 活动基本信息 */
export const getActivityInfo = once((activityId) => {
    return request({
        hostName: 'activity',
        url: 'api/web/share/share-get/activity-info.htm',
        data: {
            activityId: activityId
        }
    });
});