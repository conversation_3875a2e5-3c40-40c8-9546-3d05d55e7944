import { Platform } from ':common/env';
import texts from ':common/features/texts';
import { request } from ':common/request';
import { hostApi } from './chores';
import { GroupKey } from './goods';
import { newGetSalesRightsList } from './new_sales_rights';

export interface SalesRights {
    name: string;
    code: string;
    icon: string;
    lightSpot: string;
    introduce: string;
}

export async function getSalesRightsList(data: { groupKey: GroupKey }): Promise<Array<SalesRights>> {
    const provide = await hostApi();
    if (!provide) {
        return request({
            url: 'api/open/sales/sales-rights-introduce-config/get-rights-code-by-group-key.htm',
            data
        }).then(res => {
            return res.itemList.map(item => {
                if (item.introduce) {
                    item.introduce = item.introduce.replace(/{{appName}}/g, texts.productName);
                }
                return item;
            });
        });
    }

    return newGetSalesRightsList({
        channelCode: data.groupKey
    });
}
