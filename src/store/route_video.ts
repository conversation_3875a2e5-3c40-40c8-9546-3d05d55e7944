/*
 * ------------------------------------------------------------------
 * 路线视频相关接口
 * ------------------------------------------------------------------
 */

import { URLParams } from ':common/env';
import { request } from ':common/request';
import { CreateOrderParams, OrderInfo } from './goods';

export async function orderPaid(data: { orderNumber: number }): Promise<boolean> {
    const res = await request({
        hostName: 'misc',
        url: 'api/open/route-video/order-paid.htm',
        data
    });
    return res.orderPaid;
}

export async function isBinding(data: { orderNumber: number }): Promise<boolean> {
    const res = await request({
        hostName: 'misc',
        url: 'api/open/route-video/isbinding.htm',
        data
    });
    return res.bind;
}

export function createOrder(data: CreateOrderParams): Promise<OrderInfo> {
    return request({
        hostName: 'misc',
        url: 'api/open/route-video/create-order.htm',
        data
    });
}

export function getCoachList(data: { cityCode: number }) {
    return request({
        hostName: 'misc',
        url: 'api/web/coach/coach-list.htm',
        data
    });
}

export function getVideoList(data: { cityCode: number }) {
    return request({
        hostName: 'panda',
        url: 'api/web/route-video/list-data.htm',
        data: {
            placeId: URLParams.placeId || '',
            ...data
        }
    });
}
