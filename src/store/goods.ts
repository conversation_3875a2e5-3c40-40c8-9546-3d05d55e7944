/*
 * ------------------------------------------------------------------
 * 商品相关接口
 * ------------------------------------------------------------------
 */
import { MCProtocol } from '@simplex/simple-base';
import { request } from ':common/request';
import { dateFormat, formatPrice } from ':common/utils';
import { PageName, PayType, Platform, URLParams, URLCommon } from ':common/env';
import { webClose } from ':common/core';
import { ActivityType, globalGoodsInfo, newBindOrder, newComparePrice, newCreateMobileOrder, newGetGroupSessionInfo, newGroupComparePrice, newIsOrderBind, newIsOrderPaid } from './newGoods';
import { hostApi } from './chores';
import { getFromPageCode, getFromPathCode, getPushCode, getRecommendGoodsContent, getStatAbtestStr } from ':common/stat';
import { hasCheckbox } from ':common/features/agreement';

const PayRef = 'JIAKAOBAODIAN';

/** 商品的groupkey */
export enum GroupKey {
    /*
     | 驾驶证相关
     */

    /** 奖励优惠商品科一 */
    ExcellentChannelKe1 = 'channel_excellent_ke1',
    /** 奖励优惠商品科四 */
    ExcellentChannelKe4 = 'channel_excellent_ke4',
    /** 长辈版科一 */
    ElderChannelKe1 = 'channel_ke1_elder',
    /** 长辈版科四 */
    ElderChannelKe4 = 'channel_ke4_elder',
    /** 长辈版科一单次购买模拟考试 */
    ElderChannelKe1Alone = '',
    /** 长辈版科四单次购买模拟考试 */
    ElderChannelKe4Alone = '',

    /** 3d小车 */
    ChannelKe1D3 = 'channel_ke1_3d',
    ChannelKe4D3 = 'channel_ke4_1_3d',
    /** 3d客车 */
    KcChannelKe1D3 = 'channel_ke1_kc_3d',
    KcChannelKe4D3 = 'channel_ke4_kc_3d',
    /** 3d货车 */
    HcChannelKe1D3 = 'channel_ke1_hc_3d',
    HcChannelKe4D3 = 'channel_ke4_hc_3d',

    /** 小车科一 */
    ChannelKe1 = 'channel_ke1',
    /**  小车科一30天 */
    ChannelKe1Month = 'channel_ke1_month_continuous',
    /** 小车科二 */
    ChannelKe2 = 'channel_ke2',
    /**  小车科二30天 */
    ChannelKe2Month = 'channel_ke2_month',
    /** 小车科二升级 */
    ChannelKe2Asset = 'channel_ke2_asset',
    /**  小车科三 */
    ChannelKe3 = 'channel_ke3',
    /**  小车科三group包 */
    ChannelKe3Group = 'channel_ke3_group',
    /**  小车科三路线 */
    ChannelKe3Route = 'channel_ke3_route',
    /**  小车科三视频包 */
    ChannelKe3RouteMeta = 'channel_ke3_route_meta',
    /**  小车科三科四路线 */
    ChannelKe34Route = 'channel_route3dke4vip',
    /**  小车科三路线维语 */
    ChannelKe3RouteWeiyu = 'channel_ke3_route_weiyu',
    /** 小车科三科四 */
    ChannelKe34 = 'channel_ke3ke4vip',
    /**  小车科四 */
    ChannelKe4 = 'channel_ke4_1',
    /**  小车科四30天 */
    ChannelKe4Month = 'channel_ke4_month_continuous',
    /** 小车科一单次购买模拟考试 */
    ChannelKe1Alone = 'channel_ke1_exam',
    /** 小车科四单次购买模拟考试 */
    ChannelKe4Alone = 'channel_ke4_exam',
    /**  小车科四短时提分 */
    ChannelKe4Short = 'channel_ke4_2',
    /**  小车全科 */
    ChannelKemuAll = 'channel_kemuall_new',
    /**  小车全科学生活动 */
    ChannelKemuAllStudent = 'channel_kemuall_stud',
    /**  小车扣满12分三套卷 */
    ChannelKou12V1 = 'channel_kou12_1',
    /**  小车扣满12分 */
    ChannelKou12 = 'channel_kou12_2',
    /**  小车扣满12分短时提分 */
    ChannelKou12Short = 'channel_kou12_3',
    /**  小车扣满12分单次购买模拟考试 */
    ChannelKou12Alone = '',
    /** 小车科一多次购买模拟考试 */
    ChannelKe1ExamNum3 = 'channel_ke1_exam_num3',
    /** 小车科四多次购买模拟考试 */
    ChannelKe4ExamNum3 = 'channel_ke4_exam_num3',
    /** 小车科一考前辅导 */
    ChannelKe1Kqfd = 'channel_ke1_kqfd',
    /** 小车科一模拟考试 */
    ChannelKe1Kcmn = 'channel_ke1_kcmn',
    /** 小车科四模拟考试 */
    ChannelKe4Kcmn = 'channel_ke4_kcmn',

    /*
     | 小车短时促销
     */
    ChannelKe1Sale = 'channel_ke1_sale',
    ChannelKe4Sale = 'channel_ke4_sale',
    ChannelKemuAllSale = 'channel_kemuall_sale',

    /**  客车科一 */
    KcChannelKe1 = 'channel_ke1_kc',
    /**  客车科二 */
    KcChannelKe2 = 'channel_ke2_kc',
    /**  客车科三 */
    KcChannelKe3 = 'channel_ke3_kc',
    /** 客车科三科四 */
    KcChannelKe34 = 'channel_ke3ke4vip_kc',
    /**  客车科四 */
    KcChannelKe4 = 'channel_ke4_kc',
    /** 客车科一单次购买模拟考试 */
    KcChannelKe1Alone = '',
    /** 客车科四单次购买模拟考试 */
    KcChannelKe4Alone = '',
    /**  客车科一科四组合 */
    KcChannelKe1Ke4Group = 'channel_ke1ke4_group_kc',
    /**  客车科二科三组合 */
    KcChannelKe2Ke3Group = 'channel_ke2ke3_group_kc',
    /**  客车科一短时提分 */
    KcChannelKe1Short = 'channel_ke1_1_kc',
    /**  客车科四短时提分 */
    KcChannelKe4Short = 'channel_ke4_1_kc',
    /**  客车全科 */
    KcChannelKemuAll = 'channel_kemuall_kc',
    /**  客车扣满12分三套卷 */
    KcChannelKou12V1 = 'channel_kou12_1_kc',
    /**  客车扣满12分 */
    KcChannelKou12 = 'channel_kou12_2_kc',
    /**  客车扣满12分短时提分 */
    KcChannelKou12Short = 'channel_kou12_3_kc',
    /**  客车扣满12分单次购买模拟考试 */
    KcChannelKou12Alone = '',

    /**  货车科一 */
    HcChannelKe1 = 'channel_ke1_hc',
    /**  货车365天 */
    HcChannelKe1Time365 = 'channel_kemuall_hc_365',
    /**  货车科二 */
    HcChannelKe2 = 'channel_ke2_hc',
    /**  货车科三 */
    HcChannelKe3 = 'channel_ke3_hc',
    /** 货车科三科四 */
    HcChannelKe34 = 'channel_ke3ke4vip_hc',
    /**  货车科四 */
    HcChannelKe4 = 'channel_ke4_hc',
    /** 货车科一单次购买模拟考试 */
    HcChannelKe1Alone = 'channel_ke1_exam_hc',
    /** 货车科四单次购买模拟考试 */
    HcChannelKe4Alone = 'channel_ke4_exam_hc',
    /** 货车科一多次购买模拟考试 */
    HcChannelKe1ExamNum3 = 'channel_ke1_exam_num3_hc',
    /** 货车科四多次购买模拟考试 */
    HcChannelKe4ExamNum3 = 'channel_ke4_exam_num3_hc',
    /** 货车优惠科一 */
    HcChannelExcellentKe1 = 'channel_excellent_ke1_hc',
    /** 货车优惠科四 */
    HcChannelExcellentKe4 = 'channel_excellent_ke4_hc',
    /** 货车科一科四组合包*/
    HcChannelKe1Ke4Group = 'channel_ke1ke4_group_hc',
    /** 货车车科二科三组合 */
    HcChannelKe2Ke3Group = 'channel_ke2ke3_group_hc',
    /**  货车全科 */
    HcChannelKemuAll = 'channel_kemuall_hc',
    /**  货车扣满12分三套卷 */
    HcChannelKou12V1 = 'channel_kou12_1_hc',
    /**  货车扣满12分 */
    HcChannelKou12 = 'channel_kou12_2_hc',
    /**  货车科四短时提分 */
    HcChannelKe4Short = 'channel_ke4_1_hc',
    /**  货车扣满12分短时提分 */
    HcChannelKou12Short = 'channel_kou12_3_hc',
    /**  货车扣满12分单次购买模拟考试 */
    HcChannelKou12Alone = '',
    /**  货车科一模拟考试 */
    ChannelKe1Kcmnhc = 'channel_ke1_kcmn_hc',
    /**  货车科四模拟考试 */
    ChannelKe4Kcmnhc = 'channel_ke4_kcmn_hc',

    /**  摩托科一 */
    MotoChannelKe1 = 'channel_ke1_moto',
    /**  摩托科二 */
    MotoChannelKe2 = 'channel_ke2_moto',
    /**  摩托科三 */
    MotoChannelKe3 = 'channel_ke3_moto',
    /** 摩托车科三科四组合 */
    MotoChannelKe3ke4vip = 'channel_ke3ke4vip_moto',
    /**  摩托科四 */
    MotoChannelKe4 = 'channel_ke4_moto',
    /** 摩托科一单次购买模拟考试 */
    MotoChannelKe1Alone = 'channel_ke1_exam_moto',
    /** 摩托科四单次购买模拟考试 */
    MotoChannelKe4Alone = 'channel_ke4_exam_moto',
    /**   摩托扣满12分 */
    MotoChannelKou12 = 'channel_kou12_2_moto',
    /**  摩托扣满12分三套卷 */
    MotoChannelKou12V1 = 'channel_kou12_1_moto',
    /**  摩托扣满12分短时提分 */
    MotoChannelKou12Short = 'channel_kou12_3_moto',
    /**  摩托科一短时提分 */
    MotoChannelKe1Short = 'channel_ke1_1_moto',
    /**  摩托科四短时提分 */
    MotoChannelKe4Short = 'channel_ke4_1_moto',
    /**  摩托科一科四短时提分 */
    MotoChannelKe1Ke4Short = 'channel_ke1ke4live_moto',
    /**  摩托全科 */
    MotoChannelKemuAll = 'channel_kemuall_moto',
    /**  摩托科一模拟考试 */
    ChannelKe1Kcmnmt = 'channel_ke1_kcmn_mt',
    /**  摩托科一模拟考试 */
    ChannelKe4Kcmnmt = 'channel_ke4_kcmn_mt',

    /**  轻型挂车C6科二 */
    GcChannelKe2 = 'channel_ke2_gc',
    /**  轻型挂车C6科四 */
    GcChannelKe4 = 'channel_ke4_gc',
    /** 轻型挂车C6科四单次购买模拟考试 */
    GcChannelKe4Alone = '',
    /**  轻型挂车C6科四短时提分 */
    GcChannelKe4Short = 'channel_ke4_1_gc',

    /*
     | 资格证相关
     */

    /** 客运 */
    KeYun = 'channel_ke1_ky',
    /** 客运模拟考试单次售卖 */
    KeYunAlone = '',
    /** 货运 */
    HuoYun = 'channel_ke1_hy',
    /** 货运模拟考试单次售卖 */
    HuoYunAlone = '',
    /** 危险品 */
    WeiXian = 'channel_ke1_wxp',
    /** 危险品模拟考试单次售卖 */
    WeiXianAlone = '',
    /** 教练员 */
    JiaoLian = 'channel_ke1_jly',
    /** 教练员模拟考试单次售卖 */
    JiaoLianAlone = '',
    /** 出租车 */
    ChuZu = 'channel_ke1_czc',
    /** 出租车模拟考试单次售卖 */
    ChuZuAlone = '',
    /** 网约车 */
    WangYue = 'channel_ke1_wyc',
    /** 网约车模拟考试单次售卖 */
    WangYueAlone = '',
    /** 危险品押运 */
    WeiXianYaYun = 'channel_ke1_wxpyy',
    /** 危险品押运模拟考试单次售卖 */
    WeiXianYaYunAlone = '',
    /** 叉车 */
    ChaChe = 'channel_ke1_chache',
    /** 叉车模拟考试单次售卖 */
    ChaCheAlone = '',
    /** 危险品装卸 */
    WeiXianZhuangXie = 'channel_ke1_wxpzx',
    /** 危险品装卸模拟考试单次售卖 */
    WeiXianZhuangXieAlone = '',
    /** 爆炸品 */
    BaoZha = 'channel_ke1_bzp',
    /** 爆炸品单次售卖 */
    BaoZhaAlone = '',
    /** 爆炸品押运 */
    BaoZhaYaYun = 'channel_ke1_bzpyy',
    /** 爆炸品押运单次售卖 */
    BaoZhaYaYunAlone = '',
    /** 爆炸品装卸 */
    BaoZhaZhuangXie = 'channel_ke1_bzpzx',
    /** 爆炸品装卸单次售卖 */
    BaoZhaZhuangXieAlone = '',
    /** 爆炸品再教育 */
    JiaoLianZaiJiaoyu = 'channel_ke1_jlyzjy',
    /** 爆炸品再教育单次售卖 */
    JiaoLianZaiJiaoyuAlone = '',

    /** 无人机 */
    WuRenJi = 'channel_ke1_wrj',

    /*
     | 组合包
    */

    /** 长辈版科一科四组合包 */
    ElderChannelKe1Ke4Group = 'channel_ke1ke4_group_elder',
    /** 小车科一科四组合包 */
    ChannelKe1Ke4Group = 'channel_ke1ke4_group',
    /** 小车科二科三组合包 */
    ChannelKe2Ke3Group = 'channel_ke2ke3_group_1',
    /** 小车科二科三包含3D组合包 */
    ChannelKe2Ke3GroupNew = 'channel_ke2ke3vip_group',
    /** 小车科三科四组合包 */
    ChannelKe3Ke4Group = 'channel_ke3ke4_group',

    /** 摩托科一科四组合包 */
    MotoChannelKe1Ke4Group = 'channel_ke1ke4_group_moto',
    /** 摩托科二科三组合包 */
    MotoChannelKe2Ke3Group = 'channel_ke2ke3_group_moto',

    /** 轻型挂车C6科二科四组合包 */
    GcChannelKe2Ke4Group = 'channel_ke2ke4_group_gc',
    /** 拍照搜题1 */
    ChannelSearch1 = 'channel_search_1',
    /** 拍照搜题2 */
    ChannelSearch2 = 'channel_search_2',

    /** 科二3d */
    ChannelKe23D = 'channel_ke2_2_for3Dorders',
    /** 科二科三3d */
    ChannelKe2Ke33D = 'channel_ke2ke3_2_for3Dorders',
    /** 科二3d考场包 */
    ChannelKe2Asset3D = 'channel_ke2_asset_for3Dorders',

    ChannelFilterAdvert = 'channel_filterAdvert',

    ChannelFilterAdvert2 = 'channel_filterAdvert_2',

    ChannelFilterAdvert3 = 'channel_filterAdvert_3',

    ChannelCleanMode = 'channel_cleanMode',

    ChannelCleanMode2 = 'channel_cleanMode_2',

    ChannelCleanMode3 = 'channel_cleanMode_3',

}

// eslint-disable-next-line max-len
export const TimesList: Partial<GroupKey>[] = [GroupKey.ChannelKe1Alone, GroupKey.ChannelKe4Alone, GroupKey.ChannelKe1ExamNum3, GroupKey.ChannelKe4ExamNum3, GroupKey.HcChannelKe1Alone, GroupKey.HcChannelKe4Alone, GroupKey.MotoChannelKe1Alone, GroupKey.MotoChannelKe4Alone, GroupKey.HcChannelKe1ExamNum3, GroupKey.HcChannelKe4ExamNum3, GroupKey.ChannelKe1Kcmn, GroupKey.ChannelKe4Kcmn, GroupKey.ChannelKe1Kcmnhc, GroupKey.ChannelKe4Kcmnhc, GroupKey.ChannelKe1Kcmnmt, GroupKey.ChannelKe4Kcmnmt];

/** 商品信息 */
export interface GoodsInfo {
    /** 商品名称 */
    name: string;
    /** 商品名称 */
    goodsName: string;
    /** 苹果商品id */
    appleId: string;
    /** 商品ID */
    groupKey: GroupKey;
    /** 商品权益列表 */
    sessionIds: number[];
    /** 活动类型 */
    activityType: string;

    /** 价格 */
    payPrice: string;
    /** 原价 */
    originalPrice: string;

    /** 是否购买 */
    bought: boolean;

    /** 有效天数 */
    validDays: string;
    /** 是否过期 */
    expired?: boolean;
    /** 过期时间 */
    expiredTime?: string;

    /** 推荐商品ID */
    recommendGroupKey?: GroupKey;
    /** 加购商品ID */
    recommendPurchaseGroupKeyList?: GroupKey[];

    /** 是否可以升级 */
    upgrade?: boolean;
    /** 升级策略 */
    upgradeStrategyCode?: string;

    /** 是否包含路线 */
    containRoute?: boolean;
    /** 是否包含3D */
    containThreeD?: boolean;
    /** 活动信息 */
    inActivity?: {
        preDiscountPrice: string,
        discountedPrice: string,
        discountStartTime: number,
        discountEndTime: number,
    }
    /** 赠品活动信息 */
    giftPromotion?: {
        promotionStatus: number
        promotionStartTime: number
        promotionEndTime: number
        giftChannelCode: GroupKey
    }
    /** 商品描述 */
    description: string,

    // 新版接口的定义
    /** tip角标和亮点 */
    tips?: any
    /** 活动头图相关配置 */
    headConfig?: any,
    channelCode?: string,
    dataCode?: string,
    dataType?: string,
    promotionDetailCode?: string
    /** 商品使用次数 */
    times?: number,
    activityCode?: string,
    priceConfigCode?: string
    /** 续订模版 */
    renewTpl?: {
        tplCode: string
        payTypes: PayType [],
        renewDescription: string
    }
}

/** 比价信息 */
export interface ComparePriceInfo {
    allPrice: string,
    savePrice: string,
    groupItems: Array<{
        groupKey: GroupKey,
        name: string,
        price: string,
        description: string
    }>
}

/** 创建订单参数 */
export interface CreateOrderParams {
    /** 商品ID */
    groupKey: GroupKey;
    /** 苹果ID */
    appleId?: string
    /** 商品权益列表 */
    sessionIds: number[];
    /** 支付方式 */
    payType?: PayType;
    /** 优惠券code */
    couponCode?: string;
    /** 活动类型 */
    activityType: string;
    /** 额外订单信息 */
    extraInfo?: string;
    /** 商品城市信息（例如考场路线视频就需要城市） */
    goodsCityCode?: string
    /** 打点片段1 */
    fragmentName1: string;
    /** 打点片段2 */
    fragmentName2?: string;
}
/**
 * 创建换购订单参数
 */
export interface OrderInfo {
    orderNumber: number;
    paid: boolean;
    content: string;
    appleGoodsId?: string
}

export interface Kemu2SceneInfo {
    cityCode: string,
    cityName: string,
    hasKemu2TdPermission: boolean,
    hasKemu2VipPermission: boolean,
    sceneId: number,
    sceneName: string
}

interface goodsInfoRes {
    activityType: string,
    appleId: string,
    applePrice: number,
    bought: boolean,
    buySessionIdList: number[],
    containRoute: boolean,
    containThreeD: boolean,
    discountInfo?: {
        /** 2表示做活动 */
        discountStatus: 2,
        preDiscountPrice: number,
        preDiscountApplePrice: number,
        discountStartTime: number,
        discountEndTime: number,
    },
    expireTime?: string,
    expired: boolean,
    groupKey: GroupKey,
    name: string,
    originalActivityType: string,
    originalAppleId: string,
    originalPrice: number,
    price: number,
    recommendGroupKey?: GroupKey,
    recommendPurchaseGroupKeyList?: GroupKey[],
    sessionIdList: number[],
    upgrade: boolean,
    upgradeBuyList?: number[],
    upgradeStrategyCode?: string,
    validDays: string
    description: string
    times: number
}

/** @deprecated */
export async function getSessionInfo(data: { groupKey: GroupKey }): Promise<GoodsInfo> {
    const provide = await hostApi();

    if (!provide) {
        const res: goodsInfoRes = await request({
            url: 'api/open/goods-session-group/detail.htm',
            data: {
                tiku: URLCommon.tiku,
                ...data
            }
        });

        const expiredTime = res.expireTime && dateFormat(res.expireTime, 'yyyy.MM.dd HH:mm');
        const price = Platform.isIOS ? res.applePrice : res.price;
        const payPrice = formatPrice(price);
        const originalPrice = formatPrice(res.originalPrice);
        const discountInfo = res.discountInfo;

        let inActivity;
        if (discountInfo?.discountStatus === 2) {
            const preDiscountPrice = formatPrice(Platform.isAndroid ? discountInfo.preDiscountPrice : discountInfo.preDiscountApplePrice);
            const discountedPrice = String(Math.floor(+preDiscountPrice - +payPrice));
            inActivity = {
                preDiscountPrice,
                discountedPrice,
                discountStartTime: discountInfo.discountStartTime,
                discountEndTime: discountInfo.discountEndTime
            };
        }
        return {
            appleId: res.appleId,
            goodsName: res.name,
            bought: res.bought,
            expired: res.expired,
            expiredTime,
            containRoute: res.containRoute,
            containThreeD: res.containThreeD,
            sessionIds: res.sessionIdList,
            payPrice,
            originalPrice,
            activityType: res.activityType,
            upgrade: res.upgrade,
            recommendGroupKey: res.recommendGroupKey,
            recommendPurchaseGroupKeyList: res.recommendPurchaseGroupKeyList,
            name: res.name,
            validDays: res.validDays,
            groupKey: res.groupKey,
            upgradeStrategyCode: res.upgradeStrategyCode || '',
            description: res.description,
            inActivity,
            times: res.times
        };
    }
    const goodInfoList = await newGetGroupSessionInfo({ groupKeys: [data.groupKey] });

    return goodInfoList[0];
}

export async function getGroupSessionInfo(data: { groupKeys: GroupKey[], activityType?: ActivityType, needRecommendGoods?: boolean }): Promise<GoodsInfo[]> {
    const provide = await hostApi();

    if (!provide) {
        const res: { itemList: goodsInfoRes[] } = await request({
            url: 'api/open/goods-session-group/goods-detail.htm',
            data: {
                tiku: URLCommon.tiku,
                groupKeys: data.groupKeys.join(',')
            }
        }).catch(error => {
            if (error.errorCode === 401001) {
                MCProtocol.Core.System.confirm({
                    title: '设备超限',
                    message: error.message || error.statusText,
                    action: '确定',
                    cancel: '取消',
                    callback: () => {
                        webClose();
                    }
                });
            }
        });
        /**
         * 先排序，再返回
         * */
        const goodInfoPool = {};
        const newGoodsInfoList: GoodsInfo[] = [];
        res.itemList.forEach(item => {
            goodInfoPool[item.groupKey] = item;
        });

        data.groupKeys.forEach((groupKey: GroupKey) => {
            const item = goodInfoPool[groupKey];
            const expiredTime = item.expireTime && dateFormat(item.expireTime, 'yyyy.MM.dd HH:mm');
            const price = Platform.isIOS ? item.applePrice : item.price;
            const payPrice = formatPrice(price);
            const originalPrice = formatPrice(item.originalPrice);
            const discountInfo = item.discountInfo;

            let inActivity;
            if (discountInfo?.discountStatus === 2) {
                const preDiscountPrice = formatPrice(Platform.isAndroid ? discountInfo.preDiscountPrice : discountInfo.preDiscountApplePrice);
                const discountedPrice = String(Math.floor(+preDiscountPrice - +payPrice));
                inActivity = {
                    preDiscountPrice,
                    discountedPrice,
                    discountStartTime: discountInfo.discountStartTime,
                    discountEndTime: discountInfo.discountEndTime
                };
            }
            newGoodsInfoList.push({
                appleId: item.appleId,
                goodsName: item.name,
                bought: item.bought,
                expired: item.expired,
                expiredTime,
                containRoute: item.containRoute,
                containThreeD: item.containThreeD,
                sessionIds: item.sessionIdList,
                payPrice,
                originalPrice,
                activityType: item.activityType,
                upgrade: item.upgrade,
                recommendGroupKey: item.recommendGroupKey,
                recommendPurchaseGroupKeyList: item.recommendPurchaseGroupKeyList,
                name: item.name,
                validDays: item.validDays,
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode || '',
                description: item.description,
                inActivity,
                times: item.times
            });
        });
        return newGoodsInfoList;
    }

    const goodInfoList = await newGetGroupSessionInfo(data);

    return goodInfoList;
}

export interface GoodsExtra {
    label: string;
    highlights: Array<{
        highlight: string;
        description: string;
    }>;
}

export async function getSessionExtra(data: { groupKey: GroupKey }): Promise<GoodsExtra> {
    const provide = await hostApi();
    if (!provide) {
        return request({
            url: 'api/open/goods-session-group/query-extra-info.htm',
            data
        });
    }

    return Promise.resolve(globalGoodsInfo[data.groupKey].tips);

}

export function bindOrder(data: { orderNumber: string }): Promise<boolean> {
    // return request({
    //     url: 'api/open/goods/bind.htm',
    //     data
    // });

    // 由于绑定  sir不能绑定misc的  所以直接全部用squ的区绑定
    return newBindOrder(data);
}

export async function isOrderBind(data: { orderNumber: string }, isSqu = true): Promise<boolean> {
    const provide = await hostApi();
    if (!provide && isSqu) {
        const res = await request({
            url: 'api/web/goods/binding.htm',
            data
        });
        return res.bind;
    }

    const res = await newIsOrderBind(data);
    return res[0].bound;
}

export async function isOrderPaid(data: { orderNumber: string }, isSqu = true): Promise<boolean> {
    const provide = await hostApi();
    if (!provide && isSqu) {
        const res = await request({
            url: 'api/web/goods/get-order-status.htm',
            data
        });
        return res.status >= 1;
    }
    const res = await newIsOrderPaid(data);

    return res[0].orderStatus === 2;
}

export async function createMobileOrder(data: CreateOrderParams): Promise<OrderInfo> {
    const provide = await hostApi();
    if (!provide) {
        const idList = data.sessionIds.join(',');
        data.payType = Platform.isIOS ? 3 : data.payType;
        return request({
            url: 'api/web/goods/create-mobile-order.htm',
            data: {
                tiku: URLParams.carStyle || 'car',
                ref: PayRef,
                page: PageName,
                fromItemCode: URLParams.fromItemCode || '',
                placeId: URLParams.placeId,
                pageData: JSON.stringify({
                    score12: URLParams.score12,
                    fromPathCode: getFromPathCode(),
                    fromPageCode: await getFromPageCode(),
                    pushCode: getPushCode(),
                    questionId: URLParams.questionId,
                    courseId: URLParams.courseId,
                    sceneCode: URLParams.sceneCode,
                    patternCode: URLParams.patternCode,
                    kemu: URLParams.kemuStyle,
                    carStyle: URLParams.carStyle,
                    routeId: URLParams.routeId,
                    placeId: URLParams.placeId,
                    k2AssetsId: URLParams.k2AssetsId,
                    assetsId: URLParams.assetsId,
                    subject: URLParams.subject,
                    pageName: PageName,
                    fragmentName1: data.fragmentName1,
                    fragmentName2: data.fragmentName2,
                    groupKey: data.groupKey,
                    abTest: await getStatAbtestStr(),
                    recommendGoodsContent: await getRecommendGoodsContent(),
                    hasOpenCheckBox: (await hasCheckbox()) + '',
                    fromH5: 1
                }),
                ...data,
                routeId: idList,
                sessionIds: idList,
                typeCode: data.payType
            }
        });
    }
    return newCreateMobileOrder(data);
}
/**
 * 
 *  换购创建订单，
 *  新加四个参数groupId，preBarterOrderNumbers，barterStrategyCode，  dailyActivityType
 *  
 */
export async function createExchangeMobileOrder(data: CreateOrderParams & {
    groupId: number | string,
    preBarterOrderNumbers: any[],
    barterStrategyCode: string,
    dailyActivityType: string,
    barterAppId: number | string
}): Promise<OrderInfo> {
    const idList = data.sessionIds.join(',');
    // ios支付payType需要传3
    if (Platform.isIOS) {
        data.payType = 3;
    }
    return request({
        url: 'api/open/goods/create-barter-order.htm',
        data: {
            tiku: URLParams.carStyle || 'car',
            ref: PayRef,
            page: PageName,
            fromItemCode: URLParams.fromItemCode || '',
            placeId: URLParams.placeId,
            pageData: JSON.stringify({
                score12: URLParams.score12,
                fromPathCode: getFromPathCode(),
                fromPageCode: await getFromPageCode(),
                questionId: URLParams.questionId,
                courseId: URLParams.courseId,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                kemu: URLParams.kemuStyle,
                carStyle: URLParams.carStyle,
                routeId: URLParams.routeId,
                placeId: URLParams.placeId,
                subject: URLParams.subject,
                pageName: PageName,
                fragmentName1: data.fragmentName1,
                fragmentName2: data.fragmentName2,
                groupKey: data.groupKey,
                abTest: await getStatAbtestStr(),
                recommendGoodsContent: await getRecommendGoodsContent(),
                hasOpenCheckBox: (await hasCheckbox()) + ''
            }),
            ...data,
            routeId: idList,
            sessionIds: idList,
            typeCode: data.payType
        }
    });
}

/** @deprecated */
export async function comparePrice(data: { groupKey: GroupKey, upgradeStrategyCode?: string, tiku?: any }): Promise<ComparePriceInfo> {
    const provide = await hostApi();
    if (!provide) {
        const res = await request({
            url: 'api/open/goods-session-group/compare-price.htm',
            data: {
                tiku: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                ...data
            }
        });
        if (!res) {
            return null;
        }
        const itemList = res?.comparedGoodsList || [];
        const priceDiff = res?.priceDiff || 0;
        let allPrice = 0;
        for (let i = 0; i < itemList.length; i++) {
            if (itemList[i].price) {
                allPrice += itemList[i].price;
                itemList[i].price = formatPrice(itemList[i].price);
            } else {
                itemList[i].price = '';
            }
        }
        return {
            allPrice: formatPrice(allPrice),
            groupItems: itemList,
            savePrice: formatPrice(priceDiff)
        };
    }
    return newComparePrice({ groupKey: data.groupKey });
}

export async function GroupComparePrice(data: { groupKeys: string, tiku?: any }): Promise<Partial<Record<GroupKey, ComparePriceInfo>>> {
    const provide = await hostApi();

    if (!provide) {
        const res = await request({
            url: 'api/open/goods-session-group/batch-compare-price.htm',
            data: {
                tiku: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                ...data
            }
        });
        const comparePricePool = {};

        res.itemList.forEach(item => {
            const priceDiff = item.compareResultDTO.priceDiff;
            let allPrice = 0;
            item.compareResultDTO.comparedGoodsList.forEach(ele => {
                if (ele.price) {
                    allPrice += ele.price;
                    ele.price = formatPrice(ele.price);
                } else {
                    ele.price = '';
                }
            });

            comparePricePool[item.groupKey] = {
                upGroupItems: item.compareResultDTO.comparedGoodsList,
                groupItems: item.compareResultDTO.comparedGoodsList,
                diffPrice: formatPrice(priceDiff),
                allPrice: formatPrice(allPrice)
            };
        });

        return comparePricePool || {};
    }

    const groupKeyList: GroupKey[] = data.groupKeys.split(',') as GroupKey[];
    return newGroupComparePrice({ groupKeys: groupKeyList });
}
