import Texts from ':common/features/texts';
import { request } from ':common/request';
import { GroupKey } from './goods';
import { SalesRights } from './sales_rights';

export function newGetSalesRightsList(data: { channelCode: GroupKey }): Promise<Array<SalesRights>> {
    return request({
        hostName: 'squirrel',
        url: 'api/open/sales/get-introduce-rights-ui-config.htm',
        data
    }).then(res => {

        return res.itemList.map(item => {
            if (item.introduce) {
                item.introduce = item.introduce.replace(/{{appName}}/g, Texts.productName);
            }
            return item;
        });
    });
}
