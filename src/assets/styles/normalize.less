////////////////////////////////
// 对原声样式进行规范化、重置
////////////////////////////////

body {
    margin: 0;
    color: #000;
    font-size: 22px;
    font-family: "Helvetica Neue", Helvetica, "Microsoft YaHei", Arial, sans-serif;
    line-height: 1;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

html,
body {
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;
}

body {
    background: url(https://web-resource.mc-cdn.cn/web/jkbd-default/loading.gif) no-repeat center;
    background-size: 300PX auto;
    max-width: 375px;
    margin: 0 auto;
}

@media (min-width: 480px) {
    body {
        background-color: black;
    }
}


::-webkit-scrollbar {
    width: 0;
    height: 0;
}

* {
    box-sizing: border-box;
}

*:after,
*:before {
    box-sizing: content-box;
}

title,
script {
    display: none;
}

a,
abbr,
acronym,
b,
bdo,
bidi,
override,
big,
br,
cite,
code,
dfn,
em,
font,
i,
img,
input,
kbd,
label,
q,
s,
samp,
select,
small,
span,
strike,
strong,
sub,
sup,
textarea,
tt,
u,
var {
    display: inline;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
    padding: 0;
    letter-spacing: 0px;
}

ul,
li,
ol {
    list-style: none;
    padding: 0;
    margin: 0;
}

a {
    cursor: pointer;
    text-decoration: none;
    color: #000;
}

i {
    font-style: normal;
}

img {
    border: 0;
}

input[type="text"],
textarea {
    resize: none;
    outline: none;
    margin: 0;
    border-radius: 0;
    border: solid 1px #000;
    -webkit-appearance: none;
}

button {
    border: solid 1px #000;
    background: none;
    border-radius: 0;
    margin: 0;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
}
