////////////////////////////////
// 通用class
////////////////////////////////

.hide {
    display: none !important;
}

.hotArea {
    position: relative;

    &:after {
        content: "";
        position: absolute;
        left: -30%;
        right: -30%;
        top: -30%;
        bottom: -30%;
    }
}

// 页面最外层的div
.page-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.ipad-box {
    .phone-box {
        max-width: 375px;
        margin: 0 auto;
    }
}

.radio {
    width: 18px;
    height: 18px;
    border: 1px solid currentColor;
    border-radius: 100%;

    &.checked {
        background-color: currentColor;
        position: relative;

        &:before,
        &:after {
            position: absolute;
            content: "";
            background-color: white;
            width: 2px;
            left: 45%;
            bottom: 35%;
            border-radius: 1px;
        }

        &:before {
            height: 4px;
            transform: translateX(-50%) rotate(-50deg);
            transform-origin: right bottom;
        }

        &:after {
            height: 8px;
            transform: translateX(-50%) rotate(40deg);
            transform-origin: left bottom;
        }
    }
}


@keyframes shakeX {

    0%,
    to {
        transform: translateX(0)
    }

    50% {
        transform: translateX(0)
    }

    55%,
    65%,
    75%,
    85%,
    95% {
        transform: translateX(-5px)
    }

    60%,
    70%,
    80%,
    90% {
        transform: translateX(5px)
    }
}


@keyframes couponAnimate {
    0% {
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(1);
    }

    1% {
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(1);
    }

    50% {
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(1.2);
    }

    100% {
        transform: translate(-50%, -50%) scale(0);
    }

}

@keyframes couponAnimateShake {
    0% {
        transform: scale(0.85);
    }

    50% {
        transform: scale(1);
    }

    100% {
        transform: scale(0.85);
    }

}


// 比价动画start
@keyframes compareBoxAnimate {
    0% {
        transform: translate(-50%, -50%) scale(0);
    }

    10% {
        transform: translate(-50%, -50%) scale(1);
    }

    90% {
        transform: translate(-50%, -50%) scale(1);
    }

    100% {
        transform: translate(-50%, -50%) scale(0);
    }
}
@keyframes compareBoxAnimateBottom {
    0% {
        transform: translateX(-50%) scale(0);
    }

    10% {
        transform: translateX(-50%) scale(1);
    }

    90% {
        transform: translateX(-50%) scale(1);
    }

    100% {
        transform: translateX(-50%) scale(0);
    }
}

@keyframes compareTxtAnimate {
    0% {
        transform: scale(1);
        font-size: 18px;
    }

    10% {
        transform: scale(1.1);
        font-size: 18px;
    }

    50% {
        transform: scale(1.1);
        font-size: 18px;
    }

    100% {
        transform: scale(1.1);
        font-size: 14px;
    }
}

@keyframes compareRealBoxAnimate {
    0% {
        width: 0;
        height: 0;
    }

    70% {
        width: 110px;
        height: 50px;

    }

    85% {
        width: 110px;
        height: 50px;
    }

    100% {
        width: 110px;
        height: 50px;
    }
}

@keyframes compareWhiteBoxAnimate {
    0% {
        padding: 4px;
        opacity: 0;
    }

    70% {
        padding: 4px;
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    85% {
        padding: 4px;
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
    }

    100% {
        padding: 4px;
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes compareRightIconAnimate {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }


    100% {
        transform: scale(1);
    }
}

@keyframes compareHorizontaBoxAnimate {
    0% {
        transform: translate(-50%, -50%) scale(0);
    }

    10% {
        transform: translate(-50%, -50%) scale(1);
    }

    90% {
        transform: translate(-50%, -50%) scale(1);
    }

    100% {
        transform: translate(-50%, -50%) scale(0);
    }
}

@keyframes compareHorizontaBoxAnimateBottom {
    0% {
        transform: translateX(-50%) scale(0);
    }

    10% {
        transform: translateX(-50%) scale(1);
    }

    90% {
        transform: translateX(-50%) scale(1);
    }

    100% {
        transform: translateX(-50%) scale(0);
    }
}

@keyframes compareHorizontaRealBoxAnimate {
    0% {
        width: 0;
        height: 0;
    }

    70% {
        width: 143px;
        height: 28px;
    }

    85% {
       width: 143px;
       height: 28px;
    }

    100% {
        width: 143px;
        height: 28px;
    }
}

@keyframes compareHorizontalWhiteBoxAnimate {
    0% {
        padding: 4px;
        opacity: 0;
    }

    70% {
        padding: 4px;
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }

    85% {
        padding: 4px;
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 1;
    }

    100% {
        padding: 4px;
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}



// 比价动画end
