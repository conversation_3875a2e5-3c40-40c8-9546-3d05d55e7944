import { MCBaseStore } from '@simplex/simple-base';
import * as Simple from '@simplex/simple-core';
import * as stat from ':common/stat';
import * as env from ':common/env';
import Texts from ':common/features/texts';

import * as utils from ':common/utils';
import ':common/mcprotocol';
import ':assets/styles/normalize.less?nomodule';
import ':assets/styles/common.less?nomodule';
import { GroupKey } from ':store/goods';
import { registeredPageHide, registeredPageShow } from ':common/features/trigger_page_switch';
import { setStatusBarTheme } from ':common/core';
import { reload } from ':common/features/jump';
import { PayType, Platform, setPayType } from ':common/env';
import { MCProtocol } from '@simplex/simple-base';
import { promisify } from ':common/utils';

/** 设置页面基础宽度 */
const setBaseWidth = () => {
    const resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize';

    const isDgmnq = location.pathname.indexOf('dgmnq.html') !== -1;

    const reCalc = function () {
        // 高度低于700的当做半截弹窗
        const dimensionHeight = document.documentElement.clientHeight;

        const maxWidth = (dimensionHeight > 700 && document.documentElement.clientWidth < 690) ? 1200 : 480;
        const dimensionWidth = Math.min(document.documentElement.clientWidth, maxWidth);
        const rate = dimensionWidth / Package.build.style.baseWidth;

        // 灯光模拟器是以440宽度的弹窗为基准
        // if (isDgmnq) {
        //     rate = dimensionWidth / 440;
        // }
        const baseFontSize = 100 * rate;
        window.baseFontSize = baseFontSize;

        document.documentElement.style.fontSize = baseFontSize + 'px';
    };

    // ios居中弹窗有bug，刚开始webview宽度是0
    if (isDgmnq && env.Platform.isIOS) {
        setTimeout(reCalc, 500);
    } else {
        reCalc();
    }

    window.addEventListener(resizeEvt, reCalc, false);
    document.addEventListener('DOMContentLoaded', reCalc, false);
};

/** 设置页面安全距离 */
const setSafeAreaMeta = () => {
    const metaTags = document.head.getElementsByTagName('meta');
    const viewPortMetaTag = Array.from(metaTags).find(ele => ele.name === 'viewport');
    if (viewPortMetaTag) {
        viewPortMetaTag.content += ',viewport-fit=cover';
    }
};

/** 设置全局变量，便于在html模版中直接取 */
const setTemplateGlobal = () => {
    for (const k in env) {
        window[k] = env[k];
    }

    (window as any).Tools = utils;
    (window as any).GroupKey = GroupKey;
    (window as any).Texts = Texts;
};

const iosBackReload = () => {
    const browserRule = /(iPhone|iPad)/;

    if (browserRule.test(navigator.userAgent)) {
        window.onpageshow = function (e) {
            if (e.persisted || window.performance?.navigation.type === 2) {
                reload();
            }
        };
    }
};

// 根据协议设置默认值;
// const initPayType = () => {
//     promisify(MCProtocol.Pay.channels)().then((data) => {
//         let isWeixinAvailable = true;
//         let isHarmonyAvailable = !!Platform.isHarmony;

//         if (!data.data.wx) {
//             isWeixinAvailable = false;
//         }

//         if (!data.data.huaweiIap) {
//             isHarmonyAvailable = false;
//         }

//         if (Platform.isHarmony) {
//             if (isHarmonyAvailable) {
//                 setPayType(PayType.Harmony);
//             }
//         } else if (isWeixinAvailable) {
//             setPayType(PayType.Weixin);
//         } else {
//             setPayType(PayType.Alipay);
//         }

//     });
// };

export default () => {
    if (Simple.Utils.platform.browser) {
        MCBaseStore.setHosts(Package.hosts);
        stat.init();

        setBaseWidth();
        setSafeAreaMeta();
        setTemplateGlobal();
        // initPayType();
        setStatusBarTheme('light');
        iosBackReload();
        registeredPageShow();
        registeredPageHide();

    }
};
