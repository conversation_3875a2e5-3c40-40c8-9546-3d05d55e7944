/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */

import View from './view/main.html';
import { Component } from '@simplex/simple-core';
interface State {

}
interface Props {
    loadData();
    scroll(e)
}
export default class extends Component<State, Props> {
    timeScroll: any;
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }
    willReceiveProps() {
        return true;
    }
    scroll(e) {
        this.props.scroll && this.props.scroll(e);
        if (this.timeScroll) {
            clearTimeout(this.timeScroll);
        }
       
        this.timeScroll = setTimeout(() => {
            const $scrollBox = (this.getDOMNode().loadingBox) as HTMLElement;
            const $scrollContent = (this.getDOMNode().loadingContent) as HTMLElement;
            const contentHeight = $scrollContent.scrollHeight;
            const boxHeight = $scrollBox.clientHeight;
            const scrollHeight = $scrollBox.scrollTop;
            if (contentHeight - scrollHeight < boxHeight + 50) {
                this.props.loadData && typeof this.props.loadData === 'function' && this.props.loadData(e);
            }
        }, 200);
    }
}