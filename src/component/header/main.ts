/*
 * ------------------------------------------------------------------
 * 通用头部导航栏组件
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import { MCProtocol } from '@simplex/simple-base';
import View from './view/main.html';
import BuyButton from ':component/buyButton/main';
import { getWantAskStatus } from ':store/chores';
import { CHECK_VIP, goHelp, HELP_VIP } from ':common/navigate';
import { getAuthToken, goBack, openVipWebView } from ':common/core';
import { PageName, Platform } from ':common/env';
import { trackEvent } from ':common/stat';
import { login } from ':common/features/login';
import { onPageShow } from ':common/features/page_status_switch';
import { navigateTo } from ':common/features/jump';

interface Props {
    /** 标题 */
    title: string;
    theme: 'black' | 'white'
    endTheme?: 'black' | 'white'
    scrollTop?: number;
    /** 返回按钮点击事件 */
    back?();
}

interface State {
    showIframe: string;
}

export default class Header extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.props = {
            title: '',
            theme: 'white',
            scrollTop: 0
        };

        this.state = {
            showIframe: ''
        };
    }

    willReceiveProps(next) {
        if (
            next.theme !== this.props.theme ||
            next.endTheme !== this.props.endTheme ||
            next.scrollTop !== this.props.scrollTop
        ) {
            this.props = { ...this.props, ...next };

            this.setScrollBg(next.scrollTop, next.finalBgColor);
        }
        return true;
    }

    didMount() {
        this.listenIframe();
        this.setStatusBarHeight();
        this.setScrollBg(0);

        // 为了解决偶现的头部消失的情况（静置一段时间后，头部的高度消失）
        onPageShow(() => {
            this.setStatusBarHeight();
        });
    }

    private maxLimit = 200;
    /** 背景半透明，实现沉浸式效果 */
    public setScrollBg(scrollTop: number, finalBgColor?: string) {
        const headerDom = this.getDOMNode().header as HTMLElement;
        const theme = scrollTop > this.maxLimit / 2 ? this.props.endTheme : (this.props.theme || 'white');
        headerDom.className = 'header ' + theme;

        if (finalBgColor) {
            const r = parseInt(finalBgColor.slice(1, 3), 16);
            const g = parseInt(finalBgColor.slice(3, 5), 16);
            const b = parseInt(finalBgColor.slice(5, 7), 16);
            const rgb = [r, g, b].join(',');
            const alpha = scrollTop / this.maxLimit;

            headerDom.style.backgroundColor = `rgba(${rgb},${alpha}`;
            return;
        }

        const channel = theme === 'white' ? 255 : 0;
        const rgb = [channel, channel, channel].join(',');
        const alpha = scrollTop / this.maxLimit;
        headerDom.style.backgroundColor = `rgba(${rgb},${alpha}`;
    }

    listenIframe() {
        window.addEventListener('message', (e) => {
            const buyButton = this.app.children?.buyButton as BuyButton;
            switch (e.data.type) {
                case 'close':
                    this.setState({
                        showIframe: ''
                    });

                    buyButton.toggleButton(false);
                    break;
                default:
                    break;
            }
        });
    }

    setStatusBarHeight() {
        MCProtocol.Core.System.env((data) => {
            let statusBarHeight = data.data.statusBarHeight;

            if (Platform.isAndroid) {
                statusBarHeight /= window.devicePixelRatio;
            }

            if (statusBarHeight) {
                statusBarHeight += 'px';
            } else {
                statusBarHeight = 'env(safe-area-inset-top)';
            }

            const selfDom = this.getDOMNode().self as HTMLElement;
            selfDom.style.paddingTop = statusBarHeight;
            const headerDom = this.getDOMNode().header as HTMLElement;
            headerDom.style.paddingTop = statusBarHeight;
        });
    }

    back() {
        const { back } = this.props;
        if (back) {
            back();
        } else {
            goBack();
        }
    }

    async help() {
        trackEvent({
            actionName: '在线客服',
            actionType: '点击',
            eventId: 'customer_service'
        });

        const authToken = await getAuthToken();
        if (!authToken) {
            await login();
            return;
        }

        if (location.href.includes('buyed.html')) {
            openVipWebView({
                url: CHECK_VIP + location.search
            });
        } else {
            openVipWebView({
                url: HELP_VIP + location.search
            });
        }
    }
}
