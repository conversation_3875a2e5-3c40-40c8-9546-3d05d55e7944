.top-header {
    padding-top: 30px;
    height: 55px;
    box-sizing: content-box;
    position: relative;
    z-index: 100;
    .header {
        display: flex;
        align-items: center;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        padding: 30px 15px 0;
        max-width: 375px;
        margin: 0 auto;
        height: 85px;
        box-sizing: border-box;

        .back {
            width: 25px;
            height: 25px;
            background-size: 25px 25px;
            background-repeat: no-repeat;
            background-position: center;
            box-sizing: content-box;
        }

        .title {
            flex: 1;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .help {
            width: 22px;
            height: 22px;
            background-size: 22px 22px;
            background-repeat: no-repeat;
            background-position: center;
            box-sizing: content-box;
        }

        &.white {
            background-color: white;

            .title {
                color: #333;
            }

            .back {
                background-image: url(../images/black_back.png);
            }

            .help {
                background-image: url(../images/black_kf.png);
            }
        }

        &.black {
            background-color: black;

            .title {
                color: white;
            }

            .back {
                background-image: url(../images/back.png);
            }

            .help {
                background-image: url(../images/kf.png);
            }
        }
    }

    #frame {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 100000;
        height: 70%;
        width: 100%;
        border-radius: 10px 10px 0 0;
    }
}
