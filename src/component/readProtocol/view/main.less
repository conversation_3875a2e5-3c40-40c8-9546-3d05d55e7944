.protocol-box {
    font-size: 12px;
    display: flex;
    align-items: center;

    // 图片预加载
    &:after {
        visibility: hidden;
        content: "";
        position: absolute;
        background: url(../images/check_3.png), url(../images/check_4.png);
    }

    .check-box {
        display: inline-block;
        width: 12px;
        height: 12px;
        background: url(../images/check_3.png);
        background-size: 100% 100%;
        margin-right: 4px;

        &.active {
            background: url(../images/check_4.png);
            background-size: 100% 100%;
        }
    }

    .txt {
        color: #333;

        .noEvent {
            pointer-events: none;
        }

        .go-protocol {
            color: #984321;
        }
    }

    &.elder {
        font-size: 14px;
    }
}

.theme7 {
    .check-box {
        background: url(../images/check_1.png);
        background-size: 100% 100%;

        &.active {
            background: url(../images/check_2.png);
            background-size: 100% 100%;
        }
    }

    .txt {
        color: #ae8a79;

        .go-protocol {
            color: #f3d3c4;
        }
    }
}

.themekqfd {
    .check-box {
        background: url(../images/kqfd.png);
        background-size: 100% 100%;

        &.active {
            background: url(../images/kqfd-active.png);
            background-size: 100% 100%;
        }
    }

    .txt {
        color: #fff;
        font-size: 12px;

        .go-protocol {
            color: #fff;
            font-size: 12px;
        }
    }
}

.themekqfd-dialog {
    .check-box {
      
        background: url(../images/check_blue.png);
        background-size: 100% 100%;

        &.active {
            background: url(../images/checked_blue.png);
            background-size: 100% 100%;
        }
    }

    .txt {
        font-size: 13px;
        color: #333;

        .go-protocol {
            color: #1DACF9
        }

    }
}

.themestud {
    .check-box {
        background: url(../images/check_5.png);
        background-size: 100% 100%;

        &.active {
            background: url(../images/check_6.png);
            background-size: 100% 100%;
        }
    }

    .txt {
        color: #333333;
        font-size: 12px;
        line-height: 13px;

        .go-protocol {
            color: #FE328A;
            font-size: 12px;
        }
    }
}