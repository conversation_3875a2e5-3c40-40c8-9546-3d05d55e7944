<import name="style" content="./main" module="S" />

<div class=":protocol-box {{URLCommon.isElder?S.elder:''}} {{S['theme'+props.theme]}}">
    <span
        class="hotArea :check-box {{state.hasRead ? S.active : ''}} {{state.hasCheckbox ? '' : 'hide'}}"
        sp-on:click="changeReaded"
    ></span>
    <span class=":txt txt">
        <span class="{{state.hasCheckbox?'':S.noEvent}}" sp-on:click="changeReaded"
            >{{props.protocolText1 || '开通前请阅读'}}</span
        >
        <span class=":go-protocol go-protocol" sp-on:click="goProtocol">
            {{props.protocolText2 || ('《会员协议》')}}
            <sp:if value="{{self.isRenew}}">
                <span style="color: #666;">(含自动续费条款)</span>
            </sp:if>
        </span>
    </span>
</div>
