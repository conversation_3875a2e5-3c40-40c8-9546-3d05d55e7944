/*
 * ------------------------------------------------------------------
 * 协议勾选组件, 勾选状态全局统一
 * ------------------------------------------------------------------
 */

import { openWeb } from ':common/core';
import { Platform } from ':common/env';
import createEventBus from ':common/eventbus';
import { hasCheckbox, hasReaded, setProtocolStatus, setReaded } from ':common/features/agreement';
import { PROTOCOL1_URL } from ':common/navigate';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GroupKey } from ':store/goods';
import { globalGoodsInfo } from ':store/newGoods';

interface State {
    hasCheckbox: boolean
    hasRead: boolean
}

interface Props {
    groupKey?: GroupKey
    protocolUrl?: string,
    theme?: string
}

const eventbus = createEventBus<{
    /** 协议勾选 */
    protocolCheck: null;
}>();

export const syncReadedStatus = (status) => {
    setReaded(status);
    eventbus.emit('protocolCheck', null);
};

export default class ReadProtocol extends Component<State, Props> {
    // 是否是续订,续订才显示文案
    get isRenew() {
        const { groupKey } = this.props;
        return !!globalGoodsInfo[groupKey]?.renewTpl;
    }
    $constructor() {
        this.$super({ name: module.id, view: View });
        this.state = {
            hasCheckbox: false,
            hasRead: false
        };
    }

    willReceiveProps() {
        return true;
    }

    async didMount() {
        this.setState({
            hasRead: await hasReaded(),
            hasCheckbox: await hasCheckbox()
        });
        eventbus.on('protocolCheck', this.setReaded);
    }

    willUnmount() {
        eventbus.off('protocolCheck', this.setReaded);
    }

    setReaded = async () => {
        const hasRead = await hasReaded();
        this.setState({ hasRead });
    }

    changeReaded() {
        const { hasRead } = this.state;
        setReaded(!hasRead);

        if (Platform.isIOS) {
            setProtocolStatus(!hasRead);
        }

        eventbus.emit('protocolCheck', null);
    }

    goProtocol() {
        const { protocolUrl } = this.props;

        openWeb({
            url: protocolUrl || PROTOCOL1_URL
        });
    }
    getReaded() {
        return {
            hasRead: this.state.hasRead,
            hasCheckbox: this.state.hasCheckbox
        };
    }
}
