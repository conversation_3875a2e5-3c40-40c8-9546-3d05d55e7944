/*
 * ------------------------------------------------------------------
 * 选择支付方式弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import DialogInner, { Dialog } from ':component/dialog/main';
import { GoodsInfo, GroupKey } from ':store/goods';
import { globalGoodsInfo } from ':store/newGoods';

interface ShowInfo {

}
interface State {
    closeTime: number,
    visible: boolean
}

interface Props {
    goodsInfo: GoodsInfo
}
const sendKe2VipGroupKeyOpenTimesMap = {

};

export default class sendKe2Dialog extends Dialog<ShowInfo, void, State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            showInfo: {},
            closeTime: 0,
            visible: false
        };
    }
    timer: ReturnType<typeof setInterval>
    // 如果在活动期间，并且当前展示的商品的groupKey在赠送的商品内就弹窗，否则调用show无效
    async show({ type }: { type?: 'autoClose' }) {
        const { goodsInfo } = this.props;
        
        if (!goodsInfo.giftPromotion?.promotionStatus) {
            return Promise.reject('活动已经过期');
        }

        if (type === 'autoClose') {
            if (sendKe2VipGroupKeyOpenTimesMap[goodsInfo.groupKey]) {
                return Promise.reject('自动打开只能有一次');
            }
            // 月卡和半年卡算一个商品
            if (goodsInfo.groupKey === GroupKey.ChannelKe1Month || goodsInfo.groupKey === GroupKey.ChannelKe1) {
                sendKe2VipGroupKeyOpenTimesMap[GroupKey.ChannelKe1Month] = 1;
                sendKe2VipGroupKeyOpenTimesMap[GroupKey.ChannelKe1] = 1;
            } else {
                sendKe2VipGroupKeyOpenTimesMap[goodsInfo.groupKey] = 1;
            }

            this.setState({
                closeTime: 5
            });
            this.timer = setInterval(() => {
                let { closeTime } = this.state;
                closeTime--;
                if (closeTime === 0) {
                    clearInterval(this.timer);
                    this.close();
                }
                this.setState({
                    closeTime
                });
            }, 1000);
        }
        this.setState({ visible: true });
        return this.children.dialog.show();
    }
    close() {
        this.setState({ visible: false });
        this.hide();
    }
    willReceiveProps() {
        return true;
    }
}
