<import name="style" content="./main" module="S" />
<import name="Count" content=":component/count/main" />
<import name="dialog" content=":component/dialog/main" />

<div class=":send-ke2vip-time">
    <com:dialog>
        <div class=":dialog-content">
            <div class=":close" sp-on:click="close"></div>
            <div class=":btn" sp-on:click="close">
                <sp:if value="{{state.visible && props.goodsInfo.giftPromotion.promotionStatus}}">
                    <div class=":sign">加赠特惠<span class=":time-color">
                            <com:Count startTime="{{props.goodsInfo.giftPromotion.promotionStartTime}}"
                                endTime="{{props.goodsInfo.giftPromotion.promotionEndTime}}" />
                        </span>
                        失效</div>
                </sp:if>
                <sp:if value="{{state.closeTime}}">
                    <div class=":txt">{{state.closeTime}}秒后自动关闭弹窗</div>
                </sp:if>
            </div>
        </div>
    </com:dialog>
</div>
