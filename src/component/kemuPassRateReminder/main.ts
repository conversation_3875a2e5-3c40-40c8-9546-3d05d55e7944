/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */

import View from './view/main.html';
import { Component } from '@simplex/simple-core';
import { getKemuPassRateReminder } from ':store/chores';
import { getSystemInfo, openWeb } from ':common/core';

interface State {
    info: any
}

export default class extends Component<State> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            info: null
        };
    }
    async didMount() {
        const { _userCity, _cityCode } = await getSystemInfo();
        const cityCode = _userCity || _cityCode;

        getKemuPassRateReminder({
            userCity: cityCode
        }).then(data => {
            this.setState({
                info: data
            });
        });
    }
    goSource() {
        const { info } = this.state;

        if (info.sourceActionUrl) {
            openWeb({
                url: info.sourceActionUrl
            });
        }
    }
    willReceiveProps() {
        return true;
    }
}