import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
}

interface Props {
    img: string
}

export default class News extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
        };
    }

    willReceiveProps() {
        return true;
    }
}
