/*
 * ------------------------------------------------------------------
 * 最近购买用户轮播
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper-bundle.css';
import { getRecentNews } from ':store/chores';
import { GroupKey } from ':store/goods';

Swiper.use([Autoplay]);

interface State {
    newsList: any[];
}

interface Props {
    top: string;
    name: string;
    groupKey: GroupKey;
}

export default class News extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            newsList: []
        };
    }

    willReceiveProps() {
        return true;
    }

    didMount() {
        this.fetchNews();
    }

    async fetchNews() {
        const newsList = await getRecentNews({ groupKey: this.props.groupKey });

        if (!newsList.length) {
            return;
        }
        
        this.setState({ newsList }, () => {
            this.setSwiper();
        });
    }

    setSwiper() {
        const swiper = new Swiper(this.getDOMNode().news as HTMLElement, {
            loop: true,
            direction: 'vertical',
            slidesPerView: 1,
            speed: 500,
            autoplay: {
                delay: 2000,
                stopOnLastSlide: false,
                disableOnInteraction: false
            },
            on: {
                observerUpdate: (swiper) => {
                    if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {
                        console.log('resume swiper', this.props.name);
                        swiper.autoplay.running = true;
                        swiper.autoplay.stop();
                        swiper.animating = false;
                        swiper.autoplay.start();
                    }
                }
            }
        });
    }
}
