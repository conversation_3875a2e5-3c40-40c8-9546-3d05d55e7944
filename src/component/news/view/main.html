<import name="style" content="./main" module="S" />

<div
    class=":container swiper-container {{props.bg1?S.bg1:''}} {{!state.newsList.length && 'hide'}}"
    style="top:{{props.top}};"
    ref="news"
    skip="{{state.newsList.length ? 'true' : 'false'}}"
>
    <div class="swiper-wrapper">
        <sp:each for="{{state.newsList}}">
            <div class="swiper-slide">
                <div class=":news">
                    <img class=":avatar" src="{{$value.avatar}}" />
                    <div class=":nickname">{{$value.nickname}}</div>
                    <div>&nbsp;已开通{{props.name}}</div>
                </div>
            </div>
        </sp:each>
    </div>
</div>
