/*
 * ------------------------------------------------------------------
 * 商品价格tips
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { Component } from '@simplex/simple-core';
import { GoodsExtra, GoodsInfo, GroupKey } from ':store/goods';

interface Props {
    goodsInfo: GoodsInfo;
    comparePriceMap: Partial<Record<GroupKey, { diffPrice: string; allPrice: string }>>;
    labelMap: Partial<Record<GroupKey, GoodsExtra>>;
}

export default class extends Component<unknown, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }
   
    willReceiveProps() {
        return true;
    }

    get inActivity() {
        return this.props.goodsInfo.inActivity;
    }

    get diffPrice() {
        return this.props.comparePriceMap[this.props.goodsInfo.groupKey]?.diffPrice;
    }

    get label() {
        return this.props.labelMap[this.props.goodsInfo.groupKey]?.label;
    }
}
