<import name="style" content="./main"  />

<import name="Count" content=":component/count/main" />

<div class="price-tag {{props.class}} ">
    <sp:if value="self.inActivity">
        <div class="time-tip">
            <span class="sp">立减{{self.inActivity.discountedPrice}}元</span>
            <com:Count
                startTime="{{self.inActivity.discountStartTime}}"
                endTime="{{self.inActivity.discountEndTime}}"
            />
        </div>

        <sp:elseif value="self.diffPrice > 0" />
        <span class="tip">比分开买立省{{self.diffPrice}}元<i></i></span>

        <sp:elseif value="self.label" />
        <span class="tip">{{self.label}}<i></i></span>
    </sp:if>
</div>
