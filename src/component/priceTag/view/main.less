.price-tag {
    position: absolute;
    top: -10px;
    right: 0;
    .time-tip {
        height: 22px;
        font-size: 12px;
        display: flex;
        align-items: center;
        padding: 0 6px;
        background: linear-gradient(
            90deg,
            #ff7810 0%,
            #fe3c29 55%,
            #fe6164 100%
        );
        border-radius: 33px 33px 33px 2px;
        color: white;
        overflow: hidden;
        transform: scale(0.7);
        transform-origin: right;

        .sp {
            white-space: nowrap;
        }

        .count-content {
            white-space: nowrap;
            margin-left: 5px;
        }
    }

    .tip {
        height: 22px;
        font-size: 12px;
        display: flex;
        align-items: center;
        padding: 0 6px;
        background: linear-gradient(
            90deg,
            #ff7810 0%,
            #fe3c29 55%,
            #fe6164 100%
        );
        border-radius: 33px 33px 33px 2px;
        color: white;
        overflow: hidden;
        transform: scale(0.7);
        transform-origin: right;
        white-space: nowrap;
    }
 
    &.noScale {
        .time-tip {
            transform: scale(1);
        }

        .tip {
            transform: scale(1);
        }
    }

    &.animation {
        animation: shakeX 1.5s infinite;
    }
}

@keyframes shakeX {
    0%,
    to {
        transform: translateX(0);
    }

    50% {
        transform: translateX(0);
    }

    55%,
    65%,
    75%,
    85%,
    95% {
        transform: translateX(-5px);
    }

    60%,
    70%,
    80%,
    90% {
        transform: translateX(5px);
    }
}
