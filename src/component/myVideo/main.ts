/*
 * ------------------------------------------------------------------
 * 视频播放器
 * ------------------------------------------------------------------
 */

import { pauseAllVideos } from ':common/features/dom';
import { MCProtocol } from '@simplex/simple-base';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { Platform } from ':common/env';

export default class MyVideo extends Component<any, any> {
    $video: HTMLVideoElement;
    timer: ReturnType<typeof setTimeout>
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            // 视频地址
            src: '',
            // 视频总时长
            duration: 1,
            // 视频播放时间
            playTime: 0,
            // 视频是否在播放
            isPlaying: false,
            // 视频是否有声音
            isMuted: true,
            // 是否展示控制器
            showControls: false,
            // 恢复video大小
            showVideo: false
        };

    }
    didMount() {
        const $ref = this.getDOMNode();
        const $video = $ref.video as HTMLVideoElement;
        const $progressIn = $ref.progressIn as HTMLVideoElement;
        this.$video = $video;

        $video.addEventListener('click', () => {
            this.setState({
                showControls: true
            });
            this.delayControls();
        });

        $video.addEventListener('pause', () => {
            this.setState({
                isPlaying: false
            });
            this.delayControls();
        });
        $video.addEventListener('play', () => {
            this.setState({
                isPlaying: true,
                showVideo: true
            });
            this.delayControls();

            this.props.play && this.props.play();
        });
        $video.addEventListener('durationchange', () => {
            this.setState({
                duration: $video.duration
            });
        });

        $video.addEventListener('timeupdate', () => {
            if ($progressIn) {
                $progressIn.style.width = `${$video.currentTime / this.state.duration * 100}%`;
            }
        });
        $video.addEventListener('ended', () => {
            // const videoSrc = $video.currentSrc;

            this.setState({
                isPlaying: false,
                showVideo: false
            });
            this.exitFullScreen($video);
            this.props.endPlay && this.props.endPlay();

            // $video.src = '';
            // $video.src = videoSrc;
        });

        this.listenFullscreen((isFullscreen) => {
            if (isFullscreen) {
                console.log('全屏');
                MCProtocol.Core.Web.setting({
                    orientation: 'landscape'
                });
            } else {
                MCProtocol.Core.Web.setting({
                    orientation: 'portrait'
                });
                console.log('退出全屏');
            }
        });

        if (this.props.autoplay) {
            this.play();
        }

        this.setState({
            showVideo: false
        });
    }
    listenFullscreen(callback) {
        if (Platform.isIOS) {
            this.$video.addEventListener('webkitbeginfullscreen', () => {
                callback && callback(true);
            });
            this.$video.addEventListener('webkitendfullscreen', () => {
                callback && callback(false);
            });
        } else {
            this.$video.addEventListener('fullscreenchange', () => {
                if (document.fullscreenElement === this.$video) {
                    callback && callback(true);
                } else {
                    callback && callback(false);
                }
            });
        }
    }
    exitFullScreen(dom?) {
        const fullDom = dom || document;
        
        if (fullDom.exitFullscreen) {
            fullDom.exitFullscreen()
                .catch(err => {
                    console.error(err);
                });
        } else if (fullDom.mozCancelFullScreen) {
            /* Firefox */
            fullDom.mozCancelFullScreen();
        } else if (fullDom.webkitExitFullscreen) {
            /* Chrome, Safari and Opera */
            fullDom.webkitExitFullscreen();
        } else if (fullDom.msExitFullscreen) {
            /* IE/Edge */
            fullDom.msExitFullscreen();
        }
    }
    delayControls() {

        this.timer && clearTimeout(this.timer);

        this.timer = setTimeout(() => {
            this.setState({
                showControls: false
            });
        }, 2000);
    }
    play() {
        if (!this.state.src) {
            this.setState({
                src: this.props.src
            });
        }

        setTimeout(() => {
            pauseAllVideos();
            this.$video.play();
            this.props.onPlay && this.props.onPlay();
        }, 200);

    }
    pause() {
        this.$video.pause();
    }
    closeMuted() {
        this.$video.muted = true;
        this.setState({
            isMuted: false
        });
        this.delayControls();
    }
    openMuted() {
        this.$video.muted = false;
        this.setState({
            isMuted: true
        });
        this.delayControls();
    }
    willReceiveProps(nextProps) {
        // nextProps.controls判断这个属性的原因是，自己画的video是点击的时候才加载视频的（节约流量）
        if (nextProps.src !== this.state.src && nextProps.controls) {
            this.setState({
                src: nextProps.src
            });
        }
        return true;
    }
}
