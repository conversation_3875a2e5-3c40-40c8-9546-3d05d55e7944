.my-video {
  width: 100%;
  height: 100%;
  position: relative;
 
  video {
    width: 1px;
    height: 1px;
    // object-fit: fill;
    &.show-video{
      width: 100%;
      height: 100%;
    }
  }

  .play-icon,
  .pause-icon {
    width: 52px;
    height: 52px;
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 10;
    transform: translate(-50%, -50%);

    &.play-icon {
      background: url(../images/<EMAIL>) no-repeat center center/cover;
    }

    &.pause-icon {
      background: url(../images/<EMAIL>) no-repeat center center/cover;
    }

  }

  .muted-icon,
  .nomuted-icon {
    position: absolute;
    bottom: 17px;
    right: 20px;
    z-index: 100;
    width: 38px;
    height: 26px;

    &.muted-icon {
      background: url(../images/<EMAIL>) no-repeat center center/cover;
    }

    &.nomuted-icon {
      background: url(../images/<EMAIL>) no-repeat center center/cover;
    }
  }

  .contorl-box {
    position: absolute;
    bottom: 0;
    z-index: 10;
    width: 100%;

    .progress {
      width: 100%;
      height: 2px;
      background-color: #fff;

      .progress-in {
        width: 0;
        height: 100%;
        background-color: #eb4431;
      }
    }
  }
}

.hide {
  display: none;
}