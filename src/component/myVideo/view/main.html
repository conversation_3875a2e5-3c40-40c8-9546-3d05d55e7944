<import name="style" content="./main" />
<div class="my-video">
    <sp:if value="{{!props.controls}}">
        <div
            sp-on:click="play"
            class="play-icon {{state.isPlaying?'hide':''}}"
        ></div>
        <div
            sp-on:click="pause"
            class="pause-icon {{state.isPlaying && state.showControls?'':'hide'}}"
        ></div>
        <div
            sp-on:click="closeMuted"
            class="muted-icon {{state.isPlaying && state.showControls && state.isMuted?'':'hide'}}"
        ></div>
        <div
            sp-on:click="openMuted"
            class="nomuted-icon {{state.isPlaying && state.showControls && !state.isMuted?'':'hide'}}"
        ></div>

    </sp:if>

    <video
        ref="video"
        class="{{(props.showVideo || state.showVideo)?'show-video':'12'}}"
        playsinline="true"
        controls="{{props.controls}}"
        src="{{state.src}}"
        poster="{{props.poster}}"
    ></video>
    <sp:if value="{{!props.controls}}">
        <div class="contorl-box {{state.isPlaying?'':'hide'}}">
            <div class="progress">
                <div
                    ref="progressIn"
                    class="progress-in"
                    skip="true"
                ></div>
            </div>
        </div>
    </sp:if>
</div>
