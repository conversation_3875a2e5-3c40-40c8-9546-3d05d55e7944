/*
 * ------------------------------------------------------------------
 * 售卖倒计时
 * ------------------------------------------------------------------
 */

import { reload } from ':common/features/jump';
import { getServerTime } from ':store/chores';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    startTime: number;
    endTime: number;
}

interface State {
    time: string;
}

export default class Count extends Component<State, Props> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            time: ''
        };
    }

    async didMount() {
        if (!(this.props.startTime && this.props.endTime)) {
            return;
        }
        const now = await getServerTime();
        const leftTime = this.props.endTime - now;
        this.startCount(leftTime);
    }

    private timer: number;

    startCount(leftTime: number) {
        clearInterval(this.timer);

        if (leftTime > 0) {
            this.timer = window.setInterval(() => {
                if (leftTime > 0) {
                   
                    this.setState({
                        time: this.getHMT(leftTime)
                    });
                    leftTime -= 1000;
                } else {
                    reload();
                }
            }, 1000);

            this.setState({
                time: this.getHMT(leftTime)
            });
        }
    }

    getHMT(leftTime: number) {
        let day: any = Math.floor((leftTime) / (60 * 60 * 1000 * 24));

        let hour: any = Math.floor((leftTime - (day * 60 * 60 * 1000 * 24)) / (60 * 60 * 1000) % 24);
        let min: any = Math.floor((leftTime - (day * 60 * 60 * 1000 * 24) - (hour * 60 * 60 * 1000)) / (60 * 1000));
        let sec: any = Math.floor((leftTime - (day * 60 * 60 * 1000 * 24) - (hour * 60 * 60 * 1000) - (min * 60 * 1000)) / (1000));

        if (hour < 10) {
            hour = '0' + hour;
        }
        if (min < 10) {
            min = '0' + min;
        }
        if (sec < 10) {
            sec = '0' + sec;
        }

        if (day <= 0) {
            return hour + ':' + min + ':' + sec;
        } else if (day >= 3) {
            day += '天';
            return day + hour + ':' + min + ':' + sec;
        }
        return ((day * 24) + parseInt(hour)) + ':' + min + ':' + sec;
    }
    willReceiveProps() {
        return true;
    }
}
