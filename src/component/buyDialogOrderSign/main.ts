/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * ------------------------------------------------------------------
 * 科一科四vip购买弹窗
 * ------------------------------------------------------------------
 */

import { URLCommon } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayTypeComponent from ':component/payType/main';
import { GoodsInfo, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { typeCode } from ':common/features/bottom';
import { iosBuySuccess } from ':common/features/ios_pay';
import { newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackEvent } from ':common/stat';
import { Coupon, goodsInfoWithCoupon } from ':common/features/coupon';
import { couponAnimate, pauseAllVideos } from ':common/features/dom';
import { noPayOrderSign } from ':store/chores';
import { showClock } from ':common/utils';

interface State {
    tabIndex: number
    show: boolean
    time: number
    goodsList: GoodsInfo[],
    showTime: string[]
}

interface Props {
    title1: string,
    goodsInfoPool: GoodsInfo[],
    standbyPool: any[]
    couponPool: object,
    labelPool: object,
    comparePricePool: object
    close(closeType?: any, info?: any)
}

const sTitleMap = {
    [GroupKey.ChannelKe1]: '- 限时赠送科一VIP高效学 -',
    [GroupKey.ChannelKe4]: '- 限时赠送科四VIP高效学 -',
    [GroupKey.ChannelKe1Ke4Group]: '- 科一科四VIP高效学一起买更优惠 -',
    [GroupKey.ChannelKe4Short]: '- 讲师带学，高效学会科四 -',
    [GroupKey.HcChannelKe1]: '- 限时赠送科一VIP高效学 -',
    [GroupKey.HcChannelKe4]: '- 限时赠送科四VIP高效学 -',
    [GroupKey.HcChannelKe4Short]: '- 讲师带学，高效学会科四 -',
    [GroupKey.HcChannelKe1Ke4Group]: '- 科一科四VIP高效学一起买更优惠 -',
    [GroupKey.KcChannelKe1]: '- 限时赠送科一VIP高效学 -',
    [GroupKey.KcChannelKe4]: '- 限时赠送科四VIP高效学 -',
    [GroupKey.KcChannelKe1Short]: '- 讲师带学，高效学会科一 -',
    [GroupKey.KcChannelKe4Short]: '- 讲师带学，高效学会科四 -',
    [GroupKey.KcChannelKe1Ke4Group]: '- 科一科四VIP高效学一起买更优惠 -',
    [GroupKey.MotoChannelKe1]: '- 限时赠送科一VIP高效学 -',
    [GroupKey.MotoChannelKe4]: '- 限时赠送科四VIP高效学 -',
    [GroupKey.MotoChannelKe1Short]: '- 讲师带学，高效学会科一 -',
    [GroupKey.MotoChannelKe1Ke4Group]: '- 科一科四VIP高效学一起买更优惠 -',
    [GroupKey.ChannelKou12Short]: '- 讲师带学，助力快速拿回驾照 -',
    [GroupKey.HcChannelKou12Short]: '-讲师带学，助力快速拿回驾照 -',
    [GroupKey.KcChannelKou12Short]: '-讲师带学，助力快速拿回驾照 -'
};

export enum buyDialogCloseType {
    BOUGHT = 'bought',
    BOUGHTAUTO = 'boughtauto'
}

const fragmentName1 = '未支付订单提醒弹窗';

export default class extends Component<State, Props> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeComponent;
    };
    sTitleMap = sTitleMap;
    linghtTitleMap = {
        [GroupKey.ChannelKe1Ke4Group]: '仅需3步，高效学科一科四',
        [GroupKey.HcChannelKe1Ke4Group]: '仅需3步，高效学科一科四',
        [GroupKey.KcChannelKe1Ke4Group]: '仅需3步，高效学科一科四',
        [GroupKey.MotoChannelKe1Ke4Group]: '仅需3步，高效学科一科四',
        [GroupKey.ChannelKemuAll]: '一站式学习，考不过最高补偿140元',
        [GroupKey.HcChannelKemuAll]: '一站式学习，考不过最高补偿100元',
        [GroupKey.KcChannelKemuAll]: '一站式学习，考不过最高补偿100元',
        [GroupKey.MotoChannelKemuAll]: '一站式学习，考不过最高补偿100元'
    }
    get nowGoodInfo() {
        const { tabIndex, goodsList } = this.state;
        // const { goodsInfoPool } = this.props;
        return goodsList[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.props;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsList } = this.state;
        const { goodsInfoPool, couponPool } = this.props;
        const nowPayPrice = goodsList[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    getGroupKeyInfo(groupKey) {
        const { goodsList } = this.state;
        // const { goodsInfoPool } = this.props;
        const goodInfo = goodsList.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            show: false,
            time: 0,
            showTime: [],
            goodsList: []
        };

    }
    async didMount() {
        const { goodsInfoPool, comparePricePool, standbyPool } = this.props;
        const groupKeys = goodsInfoPool.map(item => item.groupKey);
        const goodsList = [...goodsInfoPool];

        if (standbyPool && standbyPool.length) {
            standbyPool.forEach(item => {
                if (!groupKeys.includes(item.groupKey)) {
                    groupKeys.push(item.groupKey);
                }
            });
        }

        noPayOrderSign({
            groupKeys: groupKeys,
            tiku: URLCommon.tiku
        }).then((data) => {
            // data = {
            //     remind: true,
            //     time: 600,
            //     channelCode: GroupKey.ChannelKe1
            // }
            if (data.remind) {
                let belongStandbyGoods: GoodsInfo | null = null;
                let tabIndex = groupKeys.indexOf(data.channelCode);
                if (standbyPool && standbyPool.length) {
                    belongStandbyGoods = standbyPool.find(item => item.groupKey === data.channelCode);
                }

                if (belongStandbyGoods) {
                    goodsList[0] = belongStandbyGoods;
                    tabIndex = 0;
                }

                this.setState({
                    show: true,
                    time: data.time,
                    showTime: showClock(data.time, 'hh:mm:ss').split(':'),
                    goodsList,
                    tabIndex
                }, () => {

                    // 页面进入点
                    trackEvent({
                        groupKey: this.nowGoodInfo.groupKey,
                        actionType: '出现',
                        payStatus: '2',
                        payPathType: 1,
                        fragmentName1
                    });

                    couponAnimate({
                        zIndex: 1003,
                        couponTargetDomSelect: '.page-buy-dialog-order-sign-box .coupon-position-bottom',
                        compareTargetDomSelect: `.page-buy-dialog-order-sign-box .tab-cards .${goodsList[goodsList.length - 1]?.groupKey}`,
                        couponData: this.nowCouponInfo,
                        compareData: comparePricePool[goodsList[goodsList.length - 1].groupKey],
                        goodsData: this.nowGoodInfo,
                        compareGoodsData: goodsList[goodsList.length - 1]
                    });
                    this.setPageInfo();
                });

                setInterval(() => {
                    let { time } = this.state;

                    if (time === 0) {
                        this.close();
                        return;
                    }
                    time--;
                    console.log(showClock(time, 'hh:mm:ss').split(':'));
                    this.setState({
                        time,
                        showTime: showClock(time, 'hh:mm:ss').split(':')
                    });

                }, 1000);
            }

        });

    }

    tabChangeCall = (tabIndex) => {

        if (tabIndex === this.state.tabIndex) {
            return;
        }

        this.setState({
            tabIndex
        }, () => {
            pauseAllVideos();
            this.setPageInfo();
        });

    }
    setPageInfo() {
        this.setBuyBottom();
    }

    setBuyBottom() {
        const { tabIndex, goodsList } = this.state;
        // const { goodsInfoPool } = this.props;
        const nowGoodInfo: GoodsInfo = goodsList[tabIndex];
        let bottomType: typeCode = typeCode.type1;

        // 有活动的时候按钮不同
        if (nowGoodInfo.inActivity) {
            bottomType = typeCode.type5;
        }

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });

        switch (bottomType) {
            case typeCode.type1:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    price: this.showPrice,
                    validDays: nowGoodInfo.validDays,
                    fragmentName1,
                    payPathType: 0
                });
                break;
            case typeCode.type5:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${nowGoodInfo.inActivity.discountedPrice}元`,
                    price: this.showPrice,
                    originalPrice: '日常价￥' + nowGoodInfo.inActivity.preDiscountPrice,
                    fragmentName1,
                    payPathType: 0
                });
                break;
            default:
                break;
        }
    }

    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsList } = this.state;
        // const { goodsInfoPool } = this.props;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsList[tabIndex].groupKey,
            sessionIds: goodsList[tabIndex].sessionIds,
            activityType: goodsList[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsList[tabIndex].groupKey });
        });

    }
    close() {
        const { close } = this.props;

        this.setState({
            show: false
        });

        close && close();

    }
    getStatus() {
        return this.state.show;
    }
    onClose() {
        this.close();
    }
    willReceiveProps() {
        return true;
    }
}
