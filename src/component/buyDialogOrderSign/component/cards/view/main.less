.tab-cards {
    padding: 0px 15px 0 15px;

    .cards {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .time-tip {
            position: absolute;
            top: -10px;
            right: 0px;
            height: 22px;
            font-size: 12px;
            display: flex;
            align-items: center;
            padding: 0 6px;
            background: linear-gradient(90deg, #FFD878 0%, #FFC400 100%);
            border-radius: 33px 33px 33px 2px;
            color: #6F2117;
            z-index: 1;
            transform: scale(0.7);
            transform-origin: right;

            &.red {
                background: linear-gradient(90deg, #ff7810 0%, #fe3c29 55%, #fe6164 100%);
                color: white;

                &.animate {
                    animation: tipHide 1.3s;
                }
            }

            .sp {
                white-space: nowrap;
            }

            .count-content {
                margin-left: 5px;
                white-space: nowrap;
            }
        }

        .card {
            background: white;
            border: 1px solid #F4C2A2;
            width: 164px;
            height: 118px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            position: relative;
            padding-top: 8px;
            box-sizing: border-box;


            .div1 {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                flex: 1;

                .p1 {
                    font-size: 15px;
                    font-weight: bold;
                    color: #171717;
                    line-height: 25px;
                }

                .p2 {
                    .i {
                        font-size: 12px;
                        font-weight: bold;
                        color: #171717;
                        line-height: 18px;
                    }

                    .sp {
                        font-size: 20px;
                        font-weight: bold;
                        color: #171717;
                        line-height: 30px;
                    }

                    .b {
                        font-size: 12px;
                        color: #171717;
                        line-height: 14px;
                        transform: scale3d(0.9, 0.9, 0.9);
                    }

                    .lab {
                        font-size: 12px;
                        color: #968C85;
                        transform: scale3d(0.8);
                        line-height: 14px;
                        text-decoration: line-through;
                    }
                }
            }

            .div2 {
                background-color: #FFE7D7;
                border-radius: 0 0 8px 8px;

                .sp {
                    display: block;
                    font-size: 12px;
                    font-weight: bold;
                    color: #AA4120;
                    line-height: 16px;
                    padding: 4px 0;
                    margin: 0 auto;
                    text-align: center;
                }
            }


            &.active {
                width: 170px;
                height: 137px;
                background: #FFF5E2;
                border: 2px solid #ED9964;
                color: #692204;

                .div1 {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    flex: 1;

                    .p1 {
                        font-size: 18px;
                        font-weight: bold;
                        color: #692204;
                        line-height: 25px;
                    }

                    .p2 {
                        .i {
                            font-size: 13px;
                            font-weight: bold;
                            color: #AA4120;
                            line-height: 18px;
                        }

                        .sp {
                            font-size: 22px;
                            font-weight: bold;
                            color: #AA4120;
                            line-height: 30px;
                        }

                        .b {
                            font-size: 12px;
                            color: #AA4120;
                            line-height: 14px;
                            transform: scale3d(0.9, 0.9, 0.9);
                        }
                    }
                }

                .div2 {
                    background-color: #F9BE95;
                    border-radius: 0 0 8px 8px;

                    .sp {
                        display: block;
                        font-size: 14px;
                        font-weight: bold;
                        color: #692204;
                        line-height: 20px;
                        padding: 4px 0;
                        margin: 0 auto;
                        text-align: center;
                    }
                }
            }
        }

        .three-card {
            background: white;
            border: 1px solid #F4C2A2;
            width: 104px;
            height: 118px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            position: relative;
            padding-top: 8px;
            box-sizing: border-box;

            .view-fix {
                position: fixed;
                z-index: 1001;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                width: 0;
                height: 0;
                background: rgba(0, 0, 0, 0.3);

                &.animate {
                    animation: maskHide 1.2s;
                }

                .img {
                    position: absolute;
                    width: 200px;
                    height: 200px;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%) scale(0);
                    background: url(http://exam-room.mc-cdn.cn/exam-room/2023/09/05/20/f4f7a8d757de4abeb1c960dc753e3962.png) no-repeat center center/cover;

                    &.animate {
                        animation: sign 1s;
                        animation-delay: 0.1s;
                    }
                }
            }

            .info {
                display: flex;
                flex-direction: column;
                align-items: center;
                flex: 1;

                .name {
                    height: 36px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 14px;
                    font-weight: bold;
                    color: #171717;
                    line-height: 20px;
                    text-align: center;
                }

                .price-box {
                    margin-top: 3px;

                    .i {
                        font-size: 13px;
                        font-weight: bold;
                        color: #ED391A;
                        line-height: 18px;
                    }

                    .sp {
                        font-size: 20px;
                        font-weight: bold;
                        color: #ED391A;
                        line-height: 30px;
                    }

                    .b {
                        font-size: 12px;
                        color: #AA4120;
                        line-height: 14px;
                        transform: scale3d(0.9, 0.9, 0.9);
                    }

                    .lab {
                        font-size: 12px;
                        color: #968C85;
                        transform: scale3d(0.9, 0.9, 0.9);
                        line-height: 14px;
                        text-decoration: line-through;
                    }
                }


                .other-price {
                    display: inline-block;
                    font-size: 10px;
                    color: #968C85;
                    position: relative;

                    &::after {
                        content: '';
                        width: 100%;
                        height: 1px;
                        position: absolute;
                        left: 0;
                        top: 50%;
                        background-color: #968C85;
                    }
                }
            }

            .other-info {
                background-color: #FFE7D7;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 0 0 8px 8px;

                .sp {
                    transform: scale(0.8);
                    transform-origin: center;
                    white-space: nowrap;
                    display: block;
                    font-size: 12px;
                    font-weight: bold;
                    color: #AA4120;
                    line-height: 16px;
                    padding: 4px 0;
                    margin: 0 auto;
                    text-align: center;
                }
            }

            &.active {
                width: 119px;
                height: 137px;
                background: #FFF5E2;
                border: 2px solid #ED9964;
                color: #692204;

                .info {
                    .name {
                        color: #692204;
                        font-size: 16px;
                    }

                    .price-box {
                        margin-top: 6px;

                        .i {
                            font-size: 14px;
                            color: #ED391A;
                        }

                        .sp {
                            font-size: 26px;
                            color: #ED391A;
                        }

                        .b {
                            color: #AA4120;
                        }

                        .lab {
                            color: #968C85;
                        }
                    }
                }

                .other-info {
                    background: linear-gradient(112deg, #ED9964 0%, #FAC9A8 100%);
                    height: 30px;
                    border-radius: 0 0 6px 6px;

                    .sp {

                        color: #692204;
                    }
                }
            }
        }
    }
}

@keyframes sign {

    0% {
        left: 50%;
        right: 50%;
        transform: translate(-50%, -50%) scale(0);
    }

    1% {
        left: 50%;
        right: 50%;
        transform: translate(-50%, -50%) scale(1);
    }

    50% {
        left: 50%;
        right: 50%;
        transform: translate(-50%, -50%) scale(1.2);
    }

    100% {
        top: 440px;
        left: 290px;
        transform: translate(-50%, -50%) scale(0);
    }
}

@keyframes maskHide {

    0% {
        width: 100%;
        height: 100%;
    }

    1% {
        width: 100%;
        height: 100%;
    }

    99% {
        width: 100%;
        height: 100%;
    }

    100% {
        width: 0px;
        height: 0px;
    }
}

@keyframes tipHide {

    0% {
        width: 0;
        height: 0px;
        overflow: hidden;
    }

    1% {
        width: 0;
        height: 0px;
        overflow: hidden;
    }

    99% {
        width: 0;
        height: 0px;
        overflow: hidden;
    }

    100% {
        width: auto;
        height: 22px;
    }
}
