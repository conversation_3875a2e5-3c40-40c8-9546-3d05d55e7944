.page-buy-dialog-order-sign-box {
    position: absolute;
    z-index: 1002;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);

    .page-buy-dialog-order-sign {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        background: linear-gradient(180deg, #ffeddc 1%, #fffaf0 39%, #ffffff 74%, #ffffff);
        border-radius: 6px 6px 0 0;
    }

    .phone-box {
        position: relative;
        height: 100%;
        display: flex;
        flex-direction: column;

        .content {
            flex: 1;
            .sign-header{
                padding-top: 22px;
                text-align: center;
                .title{
                    font-size: 18px;
                    line-height: 25px;
                    font-weight: bold;
                }
                .dec{
                    margin-top: 7px;
                    color: #666666;
                    font-size: 15px;
                    line-height: 21px;
                }
            }
        }
    }

    .close {
        position: absolute;
        z-index: 10;
        width: 40px;
        height: 40px;
        top: 0px;
        right: 0px;
        background: url(../images/close.png) no-repeat center center/20px 20px;
    }

    .buy-title {
        padding: 15px 15px 10px 15px;
        height: 30px;
        line-height: 30px;
        font-size: 18px;
        color: #681309;
        box-sizing: content-box;
        font-weight: bold;
    }

    .alone-box {
        height: 75px;
        background: url(../images/20.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        padding: 4px 15px 0 15px;
        box-sizing: border-box;
        margin: 4px 15px 0 15px;

        .desc {
            .desc1 {
                font-size: 18px;
                font-weight: bold;
                color: #681309;
                line-height: 18px;
            }

            .desc2 {
                font-size: 14px;
                font-weight: 400;
                color: #9f5217;
                line-height: 20px;
                padding-top: 10px;
            }
        }

        .price {
            flex: 1;
            text-align: right;
            padding-right: 10px;

            i {
                font-size: 18px;
                font-weight: 400;
                color: #681309;
                line-height: 25px;
                padding-right: 4px;
            }

            b {
                font-size: 30px;
                font-weight: bold;
                color: #681309;
                line-height: 36px;
            }
        }
    }

    .sec2 {
        padding: 15px 15px 10px 15px;

        .s-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            line-height: 21px;
            color: #692204;
        }


        .tab-content {
            position: relative;
            margin-top: 10px;
            border-radius: 6px;
            box-sizing: border-box;
        }

        .tab-content1 {
            .add-buy {
                display: flex;
                justify-content: space-around;

                .step {
                    width: 79px;
                    height: 88px;
                    background: linear-gradient(100deg, #fef7e7 4%, #fff7e7 91%);
                    border-radius: 8px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: relative;

                    .c {
                        width: 60px;
                        font-size: 13px;
                        color: #151514;
                        text-align: center;
                        font-weight: bold;
                        line-height: 18px;
                    }

                    .b {
                        font-size: 12px;
                        position: absolute;
                        bottom: 0;
                        height: 22px;
                        width: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #a03c1c;
                    }

                    .tip {
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 36px;
                        height: 18px;
                        font-size: 12px;
                        border-radius: 8px 0 8px 0;
                        background-color: #F8E5D0;
                        color: #67442E;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .scale {
                            font-weight: bold;
                            display: inline-block;
                            transform: scale(0.8);
                        }
                    }
                }
            }
        }

        .tab-content2 {
            padding: 7px;
            padding-left: 64px;
            position: relative;
            background-color: #FFDDB5;

            .diff-box {
                position: absolute;
                left: 0;
                width: 64px;
                height: 86px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 7px;
                box-sizing: border-box;

                .diff {
                    line-height: 17px;
                    color: #aa4120;
                    font-size: 12px;
                    text-align: center;

                    .unit {
                        color: #f73b31;
                    }

                    .price {
                        font-size: 16px;
                        color: #f73b31;
                    }
                }
            }

            .compare-box {
                display: flex;
                justify-content: space-between;

                .item {
                    width: 65px;
                    height: 86px;
                    background: #fffbf6;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    border-radius: 5px;
                    border: 1px solid #fddec8;
                    color: #a03c1c;
                    position: relative;
                    flex: 1;

                    .name {
                        font-weight: bold;
                        font-size: 13px;
                        text-align: center;
                    }

                    .price-box {
                        margin-top: 8px;
                        font-weight: bold;

                        .unit {
                            font-size: 12px;
                        }

                        .price {
                            font-size: 16px;
                        }
                    }

                    &:not(:last-child)::after {
                        content: "";
                        width: 17px;
                        height: 17px;
                        background: url(../images/<EMAIL>) no-repeat center center/cover;
                        position: absolute;
                        z-index: 1;
                        top: 50%;
                        right: 0;
                        transform: translate(10px, -50%);
                    }
                }
            }

            .light-box {
                margin-left: -54px;

                .light-title {
                    text-align: center;
                    font-size: 15px;
                    color: #692204;
                    font-weight: bold;
                    line-height: 21px;
                    margin-top: 8px;
                }

                .light-list {
                    margin-top: 10px;
                    width: 325px;
                    height: 56px;
                    background: linear-gradient(163deg, #fffbf6 12%, #fff7ee 95%);
                    border: 0.5px solid #ffcc8a;
                    border-radius: 6px;
                    display: flex;
                    align-items: center;

                    .light-item {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        position: relative;

                        .num-icon {
                            width: 23px;
                            height: 20px;

                            &.num-icon1 {
                                background: url(../images/<EMAIL>) no-repeat center center/cover;
                            }

                            &.num-icon2 {
                                background: url(../images/<EMAIL>) no-repeat center center/cover;
                            }

                            &.num-icon3 {
                                background: url(../images/<EMAIL>) no-repeat center center/cover;
                            }
                        }

                        .txt {
                            font-size: 13px;
                            font-weight: bold;
                            color: #692204;
                            line-height: 18px;
                            margin-top: 1px;
                        }

                        &:nth-of-type(n+2):before {
                            content: '';
                            width: 20px;
                            height: 20px;
                            background: url(../images/<EMAIL>) no-repeat center center/cover;
                            position: absolute;
                            top: 50%;
                            left: 0;
                            transform: translate(-50%, -50%);
                        }
                    }
                }
            }
        }
    }

    .footer {
        margin-top: auto;
        flex-shrink: 0;
    }

    .paytype-box {
        padding: 0 15px;
    }

    .buy-footer {
        background-color: transparent;

        .coupon-pick {
            background-color: transparent;
        }
    }
}
