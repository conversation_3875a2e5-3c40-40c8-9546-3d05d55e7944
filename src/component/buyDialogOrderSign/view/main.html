<import name="style" content="./main" />

<import name="cards" content="../component/cards/main" />
<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="goComment" content=":component/goComment/main" />

<div class="page-buy-dialog-order-sign-box" sp:if="state.show">
    <div class="page-buy-dialog-order-sign ipad-box">
        <div class="phone-box">
            <div class="close" sp-on:click="onClose"></div>
            <div class="content">
                <div class="sign-header">
                    <div class="title">您上次的订单还未支付哦！</div>
                    <div class="dec">剩<span
                            style="color:#FF4A40">{{state.showTime[0] !== '00'?state.showTime[0]+'小时':''}}{{state.showTime[1]}}分{{state.showTime[2]}}秒</span>自动取消订单
                    </div>
                </div>
                <com:goComment type="1" theme="theme1" groupKey="{{self.nowGoodInfo.groupKey}}" />

                <sp:if value="state.goodsList.length > 1">
                    <com:cards comparePricePool="{{props.comparePricePool}}" labelPool="{{props.labelPool}}"
                        goodsList="{{state.goodsList}}" tabChange="{{self.tabChangeCall}}"
                        tabIndex="{{state.tabIndex}}" />
                    <sp:else />
                    <div class="alone-box" key="aloneBox">
                        <div class="desc">
                            <p class="desc1">{{self.nowGoodInfo.upgrade?'升级':''}}{{self.nowGoodInfo.name}}</p>
                            <p class="desc2">{{props.labelPool[self.nowGoodInfo.groupKey].label}}</p>
                        </div>

                        <div class="price"><i>¥</i><b>{{self.showPrice || '--'}}</b></div>
                    </div>
                </sp:if>
                <div class="sec2" key="sec2">
                    <div class="s-title">
                        <!-- 正常版的逻辑是如果sTitleMap中能找到就使用，不能找到就看是否有比价（只有2个全科在里面找不到，不能先判断比价，因为不是所有比价的都有直播课） -->
                        <sp:if value="self.sTitleMap[self.nowGoodInfo.groupKey]">
                            {{self.sTitleMap[self.nowGoodInfo.groupKey]}}
                            <sp:elseif value="props.comparePricePool[self.nowGoodInfo.groupKey]" />
                            比分开买<b>立省{{props.comparePricePool[self.nowGoodInfo.groupKey].diffPrice}}</b>元，额外独享千元直播课
                        </sp:if>
                    </div>
                    <sp:if value="props.comparePricePool[self.nowGoodInfo.groupKey]">
                        <div class="tab-content tab-content2">
                            <div class="diff-box">
                                <div class="diff">
                                    <span>比分开买立省</span>
                                    <span class="unit">￥</span>
                                    <span
                                        class="price">{{props.comparePricePool[self.nowGoodInfo.groupKey].diffPrice}}</span>
                                </div>
                            </div>
                            <div class="compare-box">
                                <sp:each for="props.comparePricePool[self.nowGoodInfo.groupKey].groupItems">
                                    <div class="item">
                                        <div class="name">{{$value.name}}</div>
                                        <sp:if value="$value.price">
                                            <div class="price-box">
                                                <span class="unit">￥</span>
                                                <span class="price">{{$value.price}}</span>
                                            </div>
                                            <sp:else />
                                            <div class="price-box">
                                                <div class="unit">
                                                    {{$value.description}}
                                                </div>
                                            </div>
                                        </sp:if>
                                    </div>
                                </sp:each>
                            </div>
                            <sp:if value="props.labelPool[self.nowGoodInfo.groupKey].highlights.length">
                                <div class="light-box">
                                    <div class="light-title">
                                        {{self.linghtTitleMap[self.nowGoodInfo.groupKey] || '请配置文案'}}
                                    </div>
                                    <div class="light-list">
                                        <sp:each for="props.labelPool[self.nowGoodInfo.groupKey].highlights">
                                            <div sp:if="{{$index < 3}}"
                                                data-rightsIntroduceCode="{{$value.rightsIntroduceCode}}"
                                                sp-on:click="gotoRightDetail" class="light-item">
                                                <div class="num-icon num-icon{{$index + 1}}"></div>
                                                <div class="txt">{{$value.highlight}}</div>
                                            </div>
                                        </sp:each>
                                    </div>
                                </div>
                            </sp:if>
                        </div>
                        <sp:else />
                        <div class="tab-content tab-content1">
                            <div class="add-buy">
                                <sp:each for="props.labelPool[self.nowGoodInfo.groupKey].highlights">
                                    <div class="step" data-rightsIntroduceCode="{{$value.rightsIntroduceCode}}"
                                        sp-on:click="gotoRightDetail">
                                        <div class="c">{{$value.highlight}}</div>
                                        <div class="b">{{$value.description}}</div>
                                        <div class="tip"><span class="scale">权益{{$index + 1}}</span></div>
                                    </div>
                                </sp:each>
                            </div>
                        </div>
                    </sp:if>
                </div>
            </div>
            <div class="footer">
                <com:buyButton>
                    <div sp:slot="couponEntry" class="go_coupon coupon-position-bottom">
                        {{self.nowCouponInfo.couponCode?'已优惠' +
                            self.nowCouponInfo.priceCent + '元':''}}
                    </div>
                </com:buyButton>
            </div>
        </div>
    </div>
</div>
