/*
 * ------------------------------------------------------------------
 * VIP过期弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { Dialog } from ':component/dialog/main';

interface ShowInfo {
    time: string;
}

export default class ExpiredDialog extends Dialog<ShowInfo> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }
    public show(showInfo: ShowInfo) {
        return super.show({
            time: showInfo.time
        });
    }
    didMount() {
        this.event.on('closeDialog', 'click', () => {
            this.hide();
        });
    }
}
