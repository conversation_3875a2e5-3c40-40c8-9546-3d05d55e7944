<import name="style" content="./main" module="S" />
<import name="Count" content=":component/count/main" />
<import name="sendKe2Dialog" content=":component/sendKe2Dialog/main" />

<div class=":send-ke2vip-right {{self.showTxt && state.visible?S.show:'hide'}}" ref="moveBox" skip-attribute="style"
    sp-on:touchstart="onTouchStart" sp-on:touchmove="onTouchMove" sp-on:touchend="onTouchEnd" sp-on:click="onOpen"
    style="right: 0; bottom:
    {{Platform.isIOS? (props.bottom || 100):(props.bottom || 100)+120}}px;">
    <div class=":close" sp-on:click="onClose"></div>

    <com:sendKe2Dialog goodsInfo="{{props.goodsInfo}}" />
</div>
