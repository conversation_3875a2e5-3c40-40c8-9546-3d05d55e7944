/*
 * ------------------------------------------------------------------
 * 选择支付方式弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { GoodsInfo, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import { globalGoodsInfo } from ':store/newGoods';

let allowMove = false;
let $box: HTMLElement;
let boxPoint;
let startPoint = {
    x: 0,
    y: 0
};

export default class extends Component<any, { goodsInfo: GoodsInfo }> {
    get showTxt() {
        const { goodsInfo } = this.props;
        return goodsInfo.giftPromotion?.promotionStatus;
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            visible: false
        };
    }
    async didMount() {
        $box = this.getDOMNode().moveBox as HTMLElement;

        setTimeout(() => {
            const initPonit = $box.getBoundingClientRect();

            boxPoint = {
                x: initPonit.x,
                y: initPonit.y
            };

        }, 500);

        setTimeout(() => {
            this.setState({
                visible: true
            });
        }, 2000);

    }
    onOpen() {
        const { goodsInfo } = this.props;
        this.children.sendKe2Dialog.show({ goodsInfo: goodsInfo });
    }
    onTouchStart(e) {
        const point = e.touches[0];
        allowMove = true;
        startPoint = {
            x: point.pageX,
            y: point.pageY
        };
    }
    onTouchMove(e): any {
        e.preventDefault();
        if (allowMove === false) {
            return;
        }
        const point = e.touches[0];
        const movePoint = {
            x: point.pageX,
            y: point.pageY
        };

        const dis = {
            x: movePoint.x - startPoint.x,
            y: movePoint.y - startPoint.y
        };

        // 超出屏幕
        if (boxPoint.x + dis.x < 0 || boxPoint.x + dis.x + $box.offsetWidth > document.body.clientWidth || boxPoint.y + dis.y < 0 || boxPoint.y + dis.y + $box.offsetHeight > document.body.clientHeight) {
            return;
        }

        $box.style.top = boxPoint.y + dis.y + 'px';
        $box.style.left = boxPoint.x + dis.x + 'px';

    }
    onTouchEnd() {
        allowMove = false;

        const initPonit = $box.getBoundingClientRect();

        boxPoint = {
            x: initPonit.x,
            y: initPonit.y
        };
    }
    onClose(e) {
        e.stopPropagation();
        this.setState({
            visible: false
        });
    }
    willReceiveProps() {
        return true;
    }
}
