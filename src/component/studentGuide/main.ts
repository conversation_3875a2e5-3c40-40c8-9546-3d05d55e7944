/*
 * ------------------------------------------------------------------
 * 底部购买按钮右上角的装饰
 * ------------------------------------------------------------------
 */

import { getAuthToken, openVipWebView } from ':common/core';
import { getSwallowConfig } from ':store/chores';
import { GroupKey } from ':store/goods';
import { ActivityType, squirrelPrice } from ':store/newGoods';
import { Authenticate, IdentifiedStatus, queryAuthRes } from ':store/student';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
    show: boolean,
    // 是否认证
    identified: boolean,
    // 跳转的页面地址
    goToPage?: string,
    imgCover?: string
}

// 该组件其实应该是单例模式，但又不完全是，数据应该共享一份防止重复请求，但dom可以有多处
// 因为该项目没有全局共享store，故模拟一下
let dataFetched: Promise<State> | null = null;

export default class StudentGuide extends Component<State> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.props = {
        };

        this.state = {
            show: false,
            // 是否认证
            identified: false,
            goToPage: '',
            imgCover: ''
        };
    }

    didMount() {
        if (!dataFetched) {
            dataFetched = this.fetchData();
        } else {
            dataFetched.then(sharedState => {
                this.setState({ ...sharedState });
            });
        }
    }
    async fetchData() {
        const authToken = await getAuthToken();
        const config = await getSwallowConfig({ key: 'jk_xuesheng_vip' });
        const kemuAllPriceItem = (await squirrelPrice({
            groupKeys: [GroupKey.ChannelKemuAll]
        }))[0];

        // 配置展示，只有全科没有买并且不能升级的情况才能展示
        if (config?.cover && !kemuAllPriceItem.userBoughtInfo.effected && !(kemuAllPriceItem.activityType === ActivityType.upgrade)) {
            this.setState({
                goToPage: config.url,
                imgCover: config.cover,
                show: true
            });
            // 如果登录了 就要判断是否已经认证过
            if (authToken) {
                const authRes: Authenticate = await queryAuthRes();
                const identified = (authRes.authStatus === IdentifiedStatus.identified);
                this.setState({
                    identified: identified
                });
            }

        }
        return { ...this.state };
    }
    goStudent() {
        const { goToPage } = this.state;
        openVipWebView({
            url: goToPage
        });
    }
    willReceiveProps() {
        return true;
    }
}
