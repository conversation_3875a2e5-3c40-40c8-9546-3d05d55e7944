<import name="style" content="./main" module="S" />
<import name="dialog" content=":component/dialog/main" />
<import name="payType" content=":component/payType/main" />
<import name="readProtocol" content=":component/readProtocol/main" />

<div>
    <com:dialog position="bottom">
        <div class=":payContent">
            <div class=":payHd">
                <p
                    sp-on:click="close"
                    class=":closeIcon"
                ></p>
                <div class=":payTitle">选择支付方式</div>
            </div>
            <div class=":payBody">
                <com:payType groupKey="{{state.showInfo.groupKey}}" />
            </div>
            <div class=":payFt">
                <com:readProtocol />
                <div class=":payConfirm" sp-on:click="payConfirm">
                    确认支付&nbsp;&nbsp;¥&nbsp;<span
                        >{{state.showInfo.payPrice || '--'}}</span
                    >
                    <sp:if value="state.showInfo.coupon && state.showInfo.coupon.priceCent">
                        <div class=":tips">已优惠{{state.showInfo.coupon.priceCent}}元</div> 
                    </sp:if>
                </div>
            </div>
        </div>
    </com:dialog>
</div>
