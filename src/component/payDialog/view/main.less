.pay-content {
    padding: 25px 15px 15px 15px;
    background: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}

.pay-hd {
    text-align: center;
    position: relative;
    padding-bottom: 15px;

    .close-icon {
        width: 30px;
        height: 30px;
        position: absolute;
        left: 0;
        z-index: 1;
        background: url(../images/close.png) no-repeat left top;
        background-size: 16px 16px;
    }

    .pay-title {
        font-size: 18px;
        color: #333333;
        letter-spacing: 0;
    }
}

.pay-ft {
    padding: 80px 0 0 0;

    .pay-confirm {
        height: 44px;
        line-height: 44px;
        text-align: center;
        background: #26231f;
        background-image: url(../images/button_bg.png);
        //background: linear-gradient(90deg,#F3BA96 0,#DD966B 100%);
        background-size: 100% 100%;
        border-radius: 4px;
        font-size: 18px;
        margin-top: 25px;
        color: #cab96e;
        letter-spacing: 0;
        position: relative;
        .tips{
            padding: 0 10px;
            height: 22px;
            background: linear-gradient(90deg, #ff7810, #fe3c29 55%, #fe6164);
            border-radius: 33px 33px 33px 2px;
            position: absolute;
            right: 10px;
            top: 0;
            transform: translateY(-80%);
            font-size: 12px;
            color: white;
            display: flex;
            align-items: center;
        }
    }
}