/*
 * ------------------------------------------------------------------
 * 选择支付方式弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import DialogInner, { Dialog } from ':component/dialog/main';
import PayTypeCom from ':component/payType/main';
import { PayType } from ':common/env';
import { checkReaded } from ':common/features/agreement';
import { trackConfirmPay } from ':common/stat';
import { GroupKey } from ':store/goods';
import { openVipWebView } from ':common/core';

interface ShowInfo {
    groupKey: GroupKey;
    payPrice: string;
    coupon?: object
    fragmentName1: string;
    onPay()
}

export default class PayDialog extends Dialog<ShowInfo, PayType> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }
    payConfirm() {
        checkReaded(() => {

            trackConfirmPay({
                groupKey: this.state.showInfo.groupKey,
                fragmentName1: this.state.showInfo.fragmentName1,
                fragmentName2: '选择支付方式弹窗'
            });

            const payType = (this.children.dialog.children.payType as PayTypeCom).getPayType();

            if (this.state.showInfo.onPay) {
                this.state.showInfo.onPay();
            } else {
                this.fakeHide(payType);
            }
        });
    }
    close() {
        this.hide();
    }
}
