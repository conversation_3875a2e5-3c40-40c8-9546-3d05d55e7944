<import name="style" content="./main" />

<import name="Count" content=":component/count/main" />
<import name="PriceTag" content=":component/priceTag/main" />

<div class="ipad-box">
    <div class="phone-box">
        <div class="bottom-tabs">
            <sp:if value="props.goodsList.length == 2">
                <div class="hd-tabs">
                    <sp:each for="props.goodsList">
                        <div class="hd-tab {{props.tabIndex===$index?'active':''}} {{$value.groupKey}} {{props.standbyPool.length?'select-goods':''}}"
                            data-idx="{{$index}}" sp-on:click="onTabClick">
                            <div>
                                {{$value.name.replace('undefined', '') + ' ' +($value.payPrice ||
                                '--')}}元
                            </div>
                            <sp:if value="$index !== 0">
                                <com:PriceTag class="noScale {{state.animation?'animation':''}}" goodsInfo="{{$value}}"
                                    comparePriceMap="{{props.comparePricePool}}" labelMap="{{props.labelPool}}" />
                            </sp:if>

                            <div skip="true" class="animate-box"></div>
                        </div>
                    </sp:each>
                </div>
            </sp:if>

            <sp:if value="props.goodsList.length == 3">
                <div class="tab-box">
                    <sp:each for="props.goodsList">
                        <div sp-on:click="onTabClick"
                            class="tab {{$index == props.tabIndex?'active':''}} {{$value.groupKey}} {{props.standbyPool.length?'select-goods':''}}"
                            data-idx="{{$index}}">
                            <div class="name">{{$value.name.replace('undefined', '')}}</div>
                            <div class="price">
                                <span class="unit">￥</span>{{$value.payPrice}}
                            </div>
                            <sp:if value="$index !== 0">
                                <com:PriceTag goodsInfo="{{$value}}" comparePriceMap="{{props.comparePricePool}}"
                                    labelMap="{{props.labelPool}}" />
                            </sp:if>

                            <div skip="true" class="animate-box"></div>
                        </div>
                    </sp:each>
                </div>
            </sp:if>
        </div>
    </div>
</div>
