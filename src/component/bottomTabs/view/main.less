.bottom-tabs {
    padding: 6px 10px 0;
    border-radius: 10px 10px 0px 0px;
    background-color: #fff;

    .hd-tabs {
        display: flex;
        justify-content: space-between;
        -webkit-justify-content: space-between;
        width: 100%;

        .hd-tab {
            position: relative;
            font-weight: bold;
            border: 1px solid #c0c2c5;
            color: #741b01;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            width: 172px;
            height: 45px;
            padding: 5px;
            border-radius: 5px;
            font-size: 14px;
            text-align: center;

            &.active {
                color: #ffffff;
                background-size: 30px 14px;
                background-color: #211c1c;
                border: none;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    left: 0px;
                    top: 0;
                    width: 30px;
                    height: 15px;
                    background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/2-icon.png) no-repeat center center/cover;
                }

                &.select-goods {
                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 50%;
                        transform: translate(-50%, -100%);
                        border: 6px solid #211C1C;
                        border-color: transparent;
                        border-bottom-color: #211C1C;
                    }
                }
            }

        }

    }

    .tab-box {
        width: 100%;
        background-color: #fff;
        height: 80px;
        display: flex;
        justify-content: space-between;

        .tab {
            position: relative;
            height: 100%;
            width: 114px;
            border-radius: 5px;
            border: 1px solid #C0C2C5;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #741B01;

            .name {
                width: 100px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                line-height: 18px;
            }

            .price {
                font-size: 22px;

                .unit {
                    font-size: 13px;
                }
            }

            &.active {
                color: white;
                background: linear-gradient(122deg, #211C1C 0%, #1E1515 100%);
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    right: -1px;
                    bottom: 0;
                    width: 30px;
                    height: 15px;
                    background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/2-icon.png) no-repeat center center/cover;
                }

                &.select-goods {
                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 50%;
                        transform: translate(-50%, -100%);
                        border: 6px solid #211C1C;
                        border-color: transparent;
                        border-bottom-color: #211C1C;
                    }
                }
            }
        }
    }
}
