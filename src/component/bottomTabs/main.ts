/*
 * ------------------------------------------------------------------
 * 底部的购买tabs组件
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { Component } from '@simplex/simple-core';
import { GoodsInfo } from ':store/goods';

interface Props {
    goodsList: GoodsInfo[];
    tabChange(number);
    tabIndex: number
}

interface State {
}

export default class extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }
    didMount() {
        // 由于有的手机动画basesize不变，所以延迟加动画
        setTimeout(() => {
            this.setState({
                animation: true
            });
        }, 100);

        console.log(this.props, '125');

    }
    onTabClick(e) {
        const idx = +e.refTarget.getAttribute('data-idx');
        this.props.tabChange && this.props.tabChange(idx);
    }
    willReceiveProps() {
        return true;
    }
}
