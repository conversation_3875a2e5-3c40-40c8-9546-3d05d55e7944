/*
 * ------------------------------------------------------------------
 * 通用弹窗组件，异步Promise式用法
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    /* 弹窗位置 */
    position?: 'center' | 'top' | 'bottom';
}

interface State {
    visible: boolean;
}

export default class DialogInner extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            visible: false
        };

        this.props = {
            position: 'center'
        };
    }

    private closeFn: any;
    private closePromise?: Promise<any>;
    private onCloseClick() {
        this.hide();
    }

    willR<PERSON>eiveP<PERSON>() {
        return true;
    }

    onMaskClick() {
        //
    }

    public show(cb?: AnyFunc) {
        if (this.closePromise) {
            return this.closePromise;
        }

        this.setState({ visible: true }, cb);
        this.closePromise = new Promise((resolve) => {
            this.closeFn = resolve;
        });
        return this.closePromise;
    }

    public hide(hideInfo?: any) {
        this.setState({ visible: false });
        if (this.closeFn) {
            this.closeFn(hideInfo);
            this.closeFn = null;
            this.closePromise = null;
        }
    }

    public fakeHide(hideInfo?: any) {
        if (this.closeFn) {
            this.closeFn(hideInfo);
            this.closeFn = null;
            this.closePromise = null;
        }
    }

}

/** 
 * 其他具体的弹窗实现可继承此类
 * @template ShowInfo 弹窗展示信息，由调用者传入
 * @template HideInfo 弹窗关闭结果，返回给调用者
 * @template State {@link Component.state}
 */
class DialogWrapper<ShowInfo = void, HideInfo = void, State = unknown, Props = unknown> extends Component<State & { showInfo: ShowInfo }, Props> {
    declare children: {
        dialog: DialogInner;
    };

    public show(...showInfo: [ShowInfo, AnyFunc?]): Promise<HideInfo | void> {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        this.setState({ showInfo: showInfo[0] });
        return this.children.dialog.show(showInfo[1]);
    }

    public hide(hideInfo?: HideInfo) {
        return this.children.dialog.hide(hideInfo);
    }

    public fakeHide(hideInfo?: HideInfo) {
        return this.children.dialog.fakeHide(hideInfo);
    }
}

export { DialogWrapper as Dialog };
