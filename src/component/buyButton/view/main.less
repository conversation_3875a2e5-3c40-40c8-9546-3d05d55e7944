.buy-footer {
    padding: 10px 10px 0;
    position: relative;
    background-color: #fff;

    .other-info {
        display: flex;
        height: 40px;
        align-items: center;
        justify-content: space-between;

        .coupon-pick {
            display: flex;
            height: 30px;
            font-size: 12px;
            line-height: 30px;
            background: #ffffff;
            box-sizing: border-box;
            margin-top: 2px;
            color: #741B01;
            text-align: right;
        }
    }

    .other-info2 {
        position: absolute;
        top: 10px;
        right: 15px;
    }

    // type = 1
    .footer-type1 {
        .pay-button {
            display: flex;
            width: 100%;
            height: 44px;
            border-radius: 22px;
            background: linear-gradient(103deg, #FFFFFF 0%, #F9DBC0 45%, #EFAF8B 100%);

            .total-price {
                width: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                background: url(../images/k2-pay-4.png) no-repeat center center/100% 100%;

                .unit {
                    font-size: 13px;
                    font-weight: bold;
                    color: #EDEAD2;
                }

                .buy-goods {
                    font-size: 12px;
                    color: white;

                    .price {
                        font-size: 20px;
                        font-weight: bold;
                        color: #EDEAD2;
                        padding: 0 4px;
                    }
                }
            }

            .pay-btn {
                width: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: #692204;
            }
        }

    }

    // type = 2
    .footer-type2 {
        .pay-button {
            height: 48px;
            color: white;
            border-radius: 24px;
            background: linear-gradient(113deg, #353B4E 0%, #1D222B 100%);
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .pay-price {
                font-weight: 500;
                font-size: 16px;
            }

            .valid-days {
                margin-top: 2px;
                font-size: 12px;
                transform: scale(0.8);
                color: rgba(255, 255, 255, 0.7);
            }
        }
    }

    // type = 3
    .footer-type3 {
        .pay-button {
            height: 44px;
            color: #5E2E06;
            border-radius: 22px;
            background: linear-gradient(90deg, #F2AB79 0%, #F9BE95 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            .pay-price {
                font-size: 18px;
            }

            .valid-days {
                margin-top: 5px;
                font-size: 12px;
                margin-left: 5px;
                color: rgba(94, 46, 6, 0.8);
                font-size: 12px;
                transform: scale3d(0.85, 0.85, 1);
            }
        }

    }

    // type = 4
    .footer-type4 {
        .pay-button {
            height: 48px;
            color: white;
            border-radius: 24px;
            background: url(../images/bt-type4.png) no-repeat center center/cover;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .pay-price {
                font-weight: 500;
                font-size: 16px;
            }

            .valid-days {
                margin-top: 2px;
                font-size: 12px;
                transform: scale(0.8);
                color: rgba(255, 255, 255, 0.7);
            }
        }

    }

    // type = 5
    .footer-type5 {
        .activity-pay {
            height: 48px;
            color: #5E2E06;
            font-size: 16px;
            border-radius: 24px;
            background: linear-gradient(90deg, #F2AB79 0%, #F9BE95 100%);
            text-align: center;
            line-height: 44px;
            position: relative;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding-top: 2px;
            padding: 2px 20px;

            .div1 {
                display: flex;
                flex: 1;
                align-items: center;
                justify-content: center;
                box-sizing: border-box;

                .p1 {
                    display: flex;
                    align-items: baseline;

                    .b {
                        font-size: 16px;
                        font-weight: bold;
                        color: #5E2E06;
                    }

                    .sp1 {
                        font-size: 26px;
                        font-weight: bold;
                        color: #5E2E06;
                        padding-left: 6px;
                    }
                }

                .p2 {
                    display: flex;
                    flex-direction: column;
                    padding-left: 0.08rem;
                    align-items: center;
                    justify-content: center;

                    .l1 {
                        background: #8B3108;
                        font-size: 12px;
                        font-weight: bold;
                        color: #FFFFFF;
                        line-height: 16px;
                        transform: scale3d(0.95, 0.95, 0.95);
                        padding: 0 3px;
                        border-radius: 2px;
                    }

                    .l2 {
                        height: 14px;
                        font-size: 12px;
                        color: rgba(146, 90, 63, 0.7);
                        line-height: 14px;
                        transform: scale3d(0.9, 0.9, 0.9);
                        padding-top: 4px;
                        white-space: nowrap;
                    }
                }
            }

            .div0 {
                width: 1px;
                height: 30px;
                background: rgba(146, 90, 63, 0.5);
                margin-left: 5px;
            }

            .div2 {
                display: flex;
                flex: 1;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                padding-left: 20px;

                .sp2 {
                    font-size: 16px;
                    font-weight: bold;
                    color: #5E2E06;
                    line-height: 22px;
                }

                .i {
                    font-size: 12px;
                    color: rgba(146, 90, 63, 0.8);
                    line-height: 14px;
                    padding: 0 0 0 3px;
                    transform: scaleX(0.8) scaleY(0.8);
                }
            }
        }

    }

    // type = 6
    .footer-type6 {
        .activity-btns {
            display: flex;
            border-radius: 22px;
            height: 44px;
            width: 100%;
            overflow: hidden;
            background: linear-gradient(103deg, #FFFFFF 0%, #F9DBC0 45%, #EFAF8B 100%);

            .btn1 {
                width: 60%;
                background: url(../images/type6-bg.png) no-repeat;
                background-size: 100% 100%;
                display: flex;
                align-items: center;
                justify-content: center;

                .p1 {
                    display: flex;
                    align-items: center;

                    .i1 {
                        font-size: 16px;
                        font-weight: bold;
                        color: #EDEAD2;
                    }

                    .b1 {
                        font-size: 26px;
                        font-weight: bold;
                        color: #EDEAD2;
                    }
                }

                .p2 {
                    display: flex;
                    flex-direction: column;
                    padding-left: 8px;

                    .sp1 {
                        background: linear-gradient(90deg, #FF7810 0%, #FE3C29 55%, #FE6164 100%);
                        border-radius: 2px;
                        font-size: 12px;
                        font-weight: bold;
                        color: #FFFFFF;
                        line-height: 16px;
                        transform: scaleX(0.85) scaleY(0.85);
                        transform-origin: bottom;
                        padding: 1px 5px;
                    }

                    .sp2 {
                        font-size: 12px;
                        color: #FFFFFF;
                        line-height: 12px;
                        transform: scale3d(0.85, 0.85, 0.85);
                        padding-top: 2px;
                        text-align: center;
                    }
                }
            }

            .btn2 {
                flex: 1;
                font-size: 16px;
                color: #692204;
                line-height: 22px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 2px 5px 0 0;

                .sp3 {
                    font-size: 16px;
                    font-weight: bold;
                    color: #5E2E06;
                    line-height: 22px;
                }

                .sp4 {
                    font-size: 10px;
                    color: #925A3F;
                    line-height: 14px;
                    transform: scale3d(0.9, 0.9, 0.9);
                }
            }
        }
    }

    // type = 7
    .footer-type7 {
        .pay-confirm {
            background: linear-gradient(90deg, #D7A286 0%, #EBCDBB 100%);
            border-radius: 24px;
            width: 100%;
            height: 48px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;

            .p1 {
                color: #5D2C20;
                font-size: 16px;
                font-weight: bold;
                line-height: 21px;
                text-align: center;
            }

            .p2 {
                font-size: 10px;
                color: #6E301E;
                line-height: 14px;
                text-align: center;
                padding-top: 2px;
            }
        }
    }

    &.elder {
        .other-info {
            .coupon-pick {
                font-size: 15px;
            }
        }
    }
}
