<import name="style" content="./main" />

<import name="payType" content=":component/payType/main" />
<import name="tag" content=":component/tag/main" />
<import name="numScroll" content=":component/numScroll/main" />
<import name="readProtocol" content=":component/readProtocol/main" />

<div>
    <sp:if value="!Platform.isIOS && state.config.type && !state.hidden">
        <div class="buy-footer {{URLCommon.isElder?'elder':''}}" style="border-radius: {{state.config.cornerRadius}}px {{state.config.cornerRadius}}px 0 0;">

            <sp:if value="Platform.isAndroid && !props.noPayType">
                <div style="margin-bottom: 8px;">
                    <com:payType theme="horizontal" groupKey="{{state.config.groupKey}}" />
                </div>
            </sp:if>
            
            <sp:if value="state.config.type == 1">
                <div class="footer-type1" ref="pay-btn">
                    <div class="pay-button">
                        <div class="total-price">
                            <div class="unit">¥</div>
                            <div class="buy-goods">
                                <span class="price">{{state.config.price}}</span
                                >/{{state.config.validDays}}天
                            </div>
                        </div>
                        <div class="pay-btn">
                            {{state.config.title || '立即购买'}}
                        </div>
                    </div>
                </div>
            </sp:if>

            <sp:if value="state.config.type == 2">
                <div class="footer-type2" ref="pay-btn">
                    <div class="pay-button">
                        <div class="pay-price">{{'¥ ' + state.config.price + state.config.title}}</div>
                        <div class="valid-days">{{state.config.subtitle}}</div>
                    </div>
                </div>
            </sp:if>

            <sp:if value="state.config.type == 3">
                <div class="footer-type3" ref="pay-btn">
                    <div class="pay-button">
                        <div class="pay-price">{{state.config.title}}</div>
                        <div class="valid-days">{{state.config.subtitle}}</div>
                    </div>
                </div>
            </sp:if>

            <sp:if value="state.config.type == 4">
                <div class="footer-type4" ref="pay-btn">
                    <div class="pay-button">
                        <div class="pay-price">{{state.config.title}}</div>
                        <div class="valid-days">{{state.config.subtitle}}</div>
                    </div>
                </div>
            </sp:if>

            <sp:if value="state.config.type == 5">
                <div class="footer-type5" ref="pay-btn">
                    <div class="activity-pay">
                        <div class="div1">
                            <p class="p1">
                                <b class="b">¥</b>
                                <span class="sp1">
                                    {{state.config.price}}
                                </span>
                            </p>
                            <p class="p2">
                                <label class="l1"
                                    >{{state.config.discount}}</label
                                >
                                <label class="l2"
                                    >{{state.config.originalPrice}}</label
                                >
                            </p>
                        </div>
                        <div class="div0"></div>
                        <div class="div2">
                            <span class="sp2">{{state.config.title}}</span>
                            <i class="i">{{state.config.subtitle}}</i>
                        </div>
                    </div>
                </div>
            </sp:if>

            <sp:if value="state.config.type == 6">
                <div class="footer-type6" ref="pay-btn">
                    <div class="activity-btns">
                        <div class="btn1">
                            <div class="p1">
                                <span class="i1">¥&nbsp;&nbsp;</span>
                                <span class="b1">
                                    <sp:if value="state.config.animate">
                                        <com:numScroll num="{{state.config.animate.price}}" toNum="{{state.config.animate.toPrice}}"></com:numScroll>
                                        <sp:else />
                                        <span>{{state.config.price}}</span>
                                    </sp:if>
                                </span>
                            </div>
                            <div class="p2">
                                <span class="sp1"
                                    >{{state.config.discount}}</span
                                >
                                <span class="sp2"
                                    >{{state.config.originalPrice}}</span
                                >
                            </div>
                        </div>
                        <div class="btn2">
                            <span class="sp3">{{state.config.title}}</span>
                            <span
                                class="sp4 {{state.config.validDays > 0 ? '': 'hide'}}"
                                >{{state.config.subtitle}}</span
                            >
                        </div>
                    </div>
                </div>
            </sp:if>

            <sp:if value="state.config.type == 7">
                <div class="footer-type7" ref="pay-btn">
                    <div class="pay-confirm">
                        <p class="p1"><b>{{state.config.title}}</b></p>
                        <p class="p2">{{state.config.subtitle}}</p>
                    </div>
                </div>
            </sp:if>

            <div class="other-info">
                <com:readProtocol protocolText2="{{props.protocolText2}}" protocolUrl="{{props.protocolUrl}}"
                    theme="{{state.config.type}}" groupKey="{{state.config.groupKey}}" />
                <div class="coupon-pick">
                    <sp:slot name="couponEntry"></sp:slot>
                </div>
            </div>

            <div class="other-info2">
                <sp:if value="state.config.tag">
                    <com:tag
                        theme="{{self.tagTheme}}"
                        text="{{state.config.tag.text}}"
                    />
                </sp:if>
            </div>
        </div>
    </sp:if>
</div>
