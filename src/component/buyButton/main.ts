/*
 * ------------------------------------------------------------------
 * 底部购买按钮组件
 * 
 * iOS端为原生按钮(https://mckj.feishu.cn/docs/doccnQ4QaaKdQ4LijdKum3WMeJb#78ZVFB)
 * ------------------------------------------------------------------
 */

import { MCProtocol } from '@simplex/simple-base';
import { Platform } from ':common/env';
import { hiddenIOSPayButton, iosPay, PartialIOSGoods, setIOSPayButton, setIOSPayButtonTagText, startIOSBottomAnimation } from ':common/features/ios_pay';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GroupKey } from ':store/goods';
import ReadProtocol, { syncReadedStatus } from ':component/readProtocol/main';
import Tag from ':component/tag/main';
import NumScroll from ':component/numScroll/main';
import { checkReaded, hasCheckbox, hasReaded, iosCheckReaded } from ':common/features/agreement';
import { trackConfirmPay, trackGoPay } from ':common/stat';
import { ButtonConfig, defaultButtonConfig } from ':common/features/bottom';

export interface PayStatProps {
    /** 打点片段1 */
    fragmentName1: string;
    /** 打点片段2 */
    fragmentName2?: string;
    payPathType?: 0 | 1,
    actionName?: string;
    videoId?: string;
}

type PayConfig = ButtonConfig & PayStatProps & { extraInfo?: any } & { goodsCityCode?: string };

interface State {
    hidden: boolean;
    config: PayConfig;
}

export default class BuyButton extends Component<State> {

    declare children: {
        readProtocol: ReadProtocol;
        tag: Tag;
        numScroll: NumScroll;
    };

    get tagTheme() {
        const mapper = {
            6: 'type6'
        };
        return mapper[this.state.config.type];
    }

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = { config: {} as any, hidden: false };

    }

    didMount() {
        this.event.on('pay-btn', 'click', () => {
            this._processPay?.();
        });
    }
    clearUndefinded(obj = {}) {
        for (const key in obj) {
            if (typeof obj[key] === 'string') {
                obj[key] = obj[key].replace(/undefined/g, '');
            }
        }
    }
    /** 设置支付按钮 */
    public async setButtonConfig(config: PayConfig) {
        const { hidden } = this.state;
        
        // console.log(config);
        if (this.soloText && !config.tag?.text) {
            config.tag = {
                text: this.soloText,
                ...config.tag
            };
        }
        config = { ...defaultButtonConfig, process: this.process, ...config };

        this.clearUndefinded(config);

        this.state.config = config;

        // 如果是被隐藏的时候就不设置（ios的情况下，会设置按钮，由于层级最高，会挡住客服的弹窗）
        if (hidden) {
            return Promise.resolve();
        }

        if (!Platform.isIOS) {
            return new Promise(resolve => {
                this.setState({ config }, resolve);
            });
        }

        const { fragmentName1, fragmentName2, ...obj } = config;
        return setIOSPayButton({
            protocolRequired: await hasCheckbox(),
            ...obj
        }, {
            fragmentName1,
            fragmentName2,
            payPathType: this.state.config.payPathType,
            actionName: this.state.config.actionName
        });
    }
    public hideButton() {
        return this.setButtonConfig({ type: 0 } as any);
    }

    public toggleButton(hidden: boolean) {
        this.setState({ hidden });
        if (hidden) {
            return hiddenIOSPayButton();
        } else {
            return this.setButtonConfig(this.state.config);
        }
    }

    private _processPay: any;
    private process: 0 | 1;

    /** 设置支付相关的回调 */
    public setPay({
        androidPay,
        iosPaySuccess,
        getIOSGoods,
        intercepter,
        isInDialog
    }: {
        /** 安卓的支付方法，主动创建订单 */
        androidPay: (stat: PayStatProps) => void;
        /** iOS支付成功后的回调 */
        iosPaySuccess: (groupKey: GroupKey) => void;
        getIOSGoods?: () => PartialIOSGoods,
        /** 拦截支付点击事件，返回true或者Promise<true>表示要拦截，不进行支付 */
        intercepter?: () => any;
        /** 此按钮是否在半屏弹窗中 */
        isInDialog?: boolean;
    }) {
        // 监听同步选择框（IOS底部按钮）
        MCProtocol.Listener.clickMembershipProtocolCheckBox(async (isSelected) => {
            if ((await hasReaded()) !== isSelected) {
                syncReadedStatus(isSelected);
            }
        });    

        const processPay = async () => {
            const { fragmentName1, fragmentName2 } = this.state.config;

            const oortStat = {
                groupKey: this.state.config.groupKey,
                payPathType: this.state.config.payPathType || 1,
                fragmentName1,
                fragmentName2,
                videoId: this.state.config.videoId
            };
            if (isInDialog) {
                trackConfirmPay(oortStat);
            } else {
                trackGoPay(oortStat);
            }

            // 处理拦截
            if (intercepter) {
                const intercept = await intercepter();
                if (intercept) {
                    return;
                }

                if (Platform.isIOS) {
                    iosCheckReaded(() => {
                        iosPay(getIOSGoods ? getIOSGoods() : this.state.config.groupKey, {
                            fragmentName1,
                            fragmentName2,
                            payPathType: this.state.config.payPathType || 1,
                            actionName: this.state.config.actionName || (isInDialog ? '确认支付' : '去支付'),
                            goodsCityCode: this.state.config.goodsCityCode || '',
                            extraInfo: this.state.config.extraInfo
                        });
                    });
                }
            }
            if (!Platform.isIOS) {
                checkReaded(() => {
                    androidPay({
                        fragmentName1,
                        fragmentName2
                    });
                });
            }
        };

        if (!Platform.isIOS) {
            this._processPay = processPay;
        } else {
            this.process = intercepter ? 1 : 0;
            if (intercepter) {
                MCProtocol.Listener.clickBuy(processPay);
            }
            // 由于MCProtocol.Vip.makeApplePayment这个协议不会触发buyResult，但是又需要调用iosPaySuccess，所以就放到了全局
            window.iosBuySuccess = (groupKey) => {
                iosPaySuccess(groupKey);
            };
            MCProtocol.Listener.buyResult(function (data) {
                if (data.type === 1) {
                    window.iosBuySuccess(data.groupKey);
                }
            });
        }
    }

    private soloText = '';
    /** 单独设置tag文案，频繁调用setButtonConfig会有性能问题 */
    public setTagText(text: string) {
        this.soloText = text;
        if (!Platform.isIOS) {
            if (!this.state.config.tag) {
                this.setState({ config: { ...this.state.config, tag: { text } } });
            } else {
                this.children.tag?._set({ props: { text } });
            }
        } else {
            this.state.config.tag = { ...this.state.config.tag, text };
            setIOSPayButtonTagText(text);
        }
    }

    /** 开始执行价格立减的动画 */
    public startAnimation() {
        if (Platform.isIOS) {
            startIOSBottomAnimation();
        } else {
            this.children.numScroll.startAnimation();
        }
    }
}
