<import name="style" content="./main" module="S" />


<div class=":discuzz">
    <div class=":title">社区热议</div>
    <div class=":discuzzBody">
        <sp:each for="state.comments" value="item">
            <div class=":discuzzItem" data-topicID="{{item.topicId}}" sp-on:click="onItemClick">
                <img class=":discuzzAvatar" src="{{item.author.avatar}}" />
                <div class=":discuzzMid">
                    <div class=":discuzzName">{{item.author.name}}</div>
                    <div class=":discuzzTime">{{Tools.dateFormat(item.createTime, 'MM月dd')}}日 {{item.location ?
                        '来自 &nbsp;' + item.location : ''}}</div>
                    <div class=":discuzzTitle">{{item.title}}</div>
                    <div class=":discuzzContent">{{item.summary}}</div>
                    <sp:if value="item.imageList && item.imageList.length">
                        <div class=":discuzzImgs">
                            <sp:each for="item.imageList.slice(0, 2)" value="item">
                                <img class=":discuzzImg"
                                    src="{{Tools.calcImg(item.detail.url)}}">
                            </sp:each>
                        </div>
                    </sp:if>

                    <sp:if value="item.tagList && item.tagList.length">
                        <div class=":discuzzTags">
                            <sp:each for="item.tagList.slice(0, 2)" value="item">
                                <div class=":discuzzTag">{{item.labelName}}</div>
                            </sp:each>
                        </div>
                    </sp:if>

                    <div class=":discuzzOps">
                        <div class=":discuzzOp">
                            <div class=":discuzzIcon {{S.discuzzIconThumb}}" />
                            <span>{{item.zanCount}}</span>
                        </div>
                        <div class=":discuzzOp">
                            <div class=":discuzzIcon {{S.discuzzIconComment}}" />
                            <span>{{item.commentCount}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </sp:each>
    </div>
    <div class=":discuzzMore" sp-on:click="onMoreClick">查看更多</div>
</div>
