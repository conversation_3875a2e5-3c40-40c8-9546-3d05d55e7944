/*
 * main
 *
 * name: xiao<PERSON>a
 * date: 16/3/24
 */

import { Component } from '@simplex/simple-core';
import { Platform } from ':common/env';

import View from './view/main.html';
import { getDiscuzzList } from ':store/chores';
import { trackEvent } from ':common/stat';
import { openWeb } from ':common/core';
import jump from ':common/features/jump';

export default class extends Component<{
    timer: any,
    comments: Record<string, any>
}> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            comments: [],
            timer: null
        };
    }

    didMount() {
        setTimeout(() => {
            this.getDiscuzzList();
        }, 500);
    }

    async getDiscuzzList() {
        const retData = await getDiscuzzList();
        this.setState({
            comments: retData.slice(0, 3)
        });
    }

    onMoreClick() {
        trackEvent({
            fragmentName1: '社区热议',
            actionType: '点击',
            actionName: '看更多内容'
        });
        openWeb({
            url: 'http://saturn.nav.mucang.cn/tag/detail?tagId=61406&selectTab=3'
        });
    }

    onItemClick(e) {
        trackEvent({
            fragmentName1: '社区热议',
            actionType: '点击',
            actionName: '看帖子详情'
        });
        const topicID = e.refTarget.getAttribute('data-topicID');
        if (Platform.isWeiXin) {
            jump.navigateTo('https://m.jiakaobaodian.com/bbs/topic/' + topicID + '.html?from=jiakaozhushou');
         
        } else {
            jump.navigateTo('http://saturn.nav.mucang.cn/topic/detail?topicId=' + topicID + '');
        }
    }
}