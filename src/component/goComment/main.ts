/*
 * ------------------------------------------------------------------
 * 去看看
 * ------------------------------------------------------------------
 */

import { openVipWebView } from ':common/core';
import { DIANPING_URL } from ':common/navigate';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    groupKey: string;
}

export default class Tag extends Component<unknown, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.props = {
            groupKey: ''
        };

        this.state = {
        };
    }
    goComment() {

        openVipWebView({
            url: `${DIANPING_URL}${location.search}&backType=close&listenBuyed=false&groupKey=${this.props.groupKey}`,
            style: 0
        });

    }
    willReceiveProps() {
        return true;
    }
}
