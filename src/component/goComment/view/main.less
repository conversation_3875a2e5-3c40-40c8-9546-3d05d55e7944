.dialog-header{
    padding-right: 40px;
    .hedaer-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
    
        .content {
            padding: 9px 6px 7px 6px;
            flex: 1;
            overflow: hidden;
        }
    
        .title {
            padding-left: 9px;
            border-radius: 30px;
            background-color: #fffaef;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 30px;
    
            .msg {
                width: 16px;
                height: 16px;
                background: url(../images/msg.png) no-repeat left center;
                background-size: 16px 16px;
            }
    
            .txt {
                color: #333333;
                font-size: 12px;
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                padding-left: 8px;
            }
    
            .btn {
                background-color: rgba(243, 232, 188, .5);
                color: #634A12;
                font-size: 13px;
                line-height: 20px;
                padding: 5px 10px 4px 12px;
                border-radius: 30px;
                height: 30px;
                box-sizing: border-box;
            }
        }
      
    }
    
    .theme1 {
        .content {
            padding: 15px 15px 10px 15px;
    
            .title {
                padding-right: 2px;
    
                .btn {
                    background: none;
                    border-radius: 15px;
                    border: 1px solid #EAC99E;
                    color: #9F5217;
                    height: 26px;
                    line-height: 16px;
                }
            }
        }
    }
}
