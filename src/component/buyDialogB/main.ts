/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * ------------------------------------------------------------------
 * 科一科四vip购买弹窗
 * ------------------------------------------------------------------
 */

import { ABTestKey, ABTestType, CarType, KemuType, Platform, URLCommon, URLParams } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayTypeComponent from ':component/payType/main';
import { getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupComparePrice, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { typeCode } from ':common/features/bottom';
import { iosDialogBuySuccess } from ':common/features/ios_pay';
import { openVipWebView, webClose } from ':common/core';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackGoPay } from ':common/stat';
import { Coupon, getBestCoupon, goodsInfoWithCoupon } from ':common/features/coupon';
import { showSetting } from ':common/features/embeded';
import Texts from ':common/features/texts';
import { getTabIndex } from ':common/features/cache';
import { onPageShow } from ':common/features/page_status_switch';
import { BUYED_URL, openAuth } from ':common/navigate';
import { couponAnimate, pauseAllVideos } from ':common/features/dom';
import isNumber from 'lodash/isNumber';
import { getAbtest, getSwallowConfig, StrategyType } from ':store/chores';
import { onWebBack } from ':common/features/persuade';
import { hesitateUserPersuade } from ':common/features/hesitate';

enum typeMap {
    page = 'page',
    component = 'component'
}
interface State {
    kemu: KemuType,
    tiku: CarType,
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object
    standbyPool: GoodsInfo[],
    addGoods: boolean,
    showStudentConfig: boolean,
    strategy: StrategyType

}

interface Props {
    title1: string,
    fragmentName1: string
    // 学时小包用
    isStudy: boolean
    type: typeMap,
    close(closeType?: any, info?: any)
}

const sTitleMap = {
    [GroupKey.ChannelKe1]: '- 限时赠送科一VIP高效学 -',
    [GroupKey.ChannelKe4]: '- 限时赠送科四VIP高效学 -',
    [GroupKey.ChannelKe1Month]: '- 限时赠送30天科一VIP高效学 -',
    [GroupKey.ChannelKe4Month]: '- 限时赠送30天科四VIP高效学 -',
    [GroupKey.ChannelKe1Ke4Group]: '- 科一科四VIP高效学一起买更优惠 -',
    [GroupKey.ChannelKemuAll]: '- 全科VIP一起买更优惠 -',
    [GroupKey.ChannelKe4Short]: '- 讲师带学，高效学会科四 -'
};

export enum buyDialogCloseType {
    BOUGHT = 'bought',
    BOUGHTAUTO = 'boughtauto'
}

export default class extends Component<State, Props> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeComponent;
    };
    sTitleMap = sTitleMap;
    linghtTitleMap = {
        [GroupKey.ChannelKe1Ke4Group]: '仅需3步，高效学科一科四',
        [GroupKey.ChannelKemuAll]: '一站式学习，考不过最高补偿140元'
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        let { tabIndex } = this.state;
        const { couponPool, goodsInfoPool, addGoods } = this.state;
        if (addGoods && tabIndex === 0) {
            tabIndex = 1;
        }
        return couponPool[goodsInfoPool[tabIndex].groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        let { tabIndex } = this.state;
        const { goodsInfoPool, couponPool, addGoods } = this.state;
        if (addGoods && tabIndex === 0) {
            tabIndex = 1;
        }
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (this.nowCouponInfo?.couponCode) {
            const showPrice = goodsInfoWithCoupon(goodsInfoPool[tabIndex], { code: couponPool[goodsInfoPool[tabIndex].groupKey].couponCode, price: couponPool[goodsInfoPool[tabIndex].groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    get addGoodsPrice() {
        const { goodsInfoPool } = this.state;

        return (+goodsInfoPool[1]?.payPrice - +goodsInfoPool[0]?.payPrice).toFixed(2);
    }
    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {
        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;
        const goodsInfoPool: GoodsInfo[] = [];

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            kemu,
            tiku,
            tabIndex: 0,
            goodsInfoPool,
            standbyPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            addGoods: false,
            showStudentConfig: false,
            strategy: {}
        };

    }
    async didMount() {

        const { tiku, kemu, standbyPool } = this.state;
        const { isStudy, type } = this.props;
        const goodsInfoPool = [];
        // 先设置为0， 再设置高度（原生会有默认高度）
        showSetting({ androidH: 1, iosH: 1 });

        if (type !== typeMap.component) {
            // app代理方法
            this.appEventProxy();
        }
        const { strategy } = await getAbtest(URLCommon.tiku);
        switch (tiku) {
            case CarType.CAR:
                if (strategy[ABTestKey.key29] === ABTestType.B) {
                    if (kemu === KemuType.Ke1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1Month
                        } as GoodsInfo);
                    } else {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe4Month
                        } as GoodsInfo);
                    }

                } else {
                    goodsInfoPool.push({
                        groupKey: kemu === KemuType.Ke1 ? GroupKey.ChannelKe1 : GroupKey.ChannelKe4
                    } as GoodsInfo);
                }

                if (kemu === KemuType.Ke1) {
                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKe1Ke4Group
                    } as GoodsInfo);

                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKemuAll
                    } as GoodsInfo);
                    if (strategy[ABTestKey.key29] === ABTestType.B) {
                        standbyPool.push({
                            groupKey: GroupKey.ChannelKe1
                        } as GoodsInfo);
                    }
                } else {
                    if (strategy[ABTestKey.key29] === ABTestType.B) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe4
                        } as GoodsInfo);
                    }
                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKe4Short
                    } as GoodsInfo);
                }

                break;
            default: break;
        }
        this.state.strategy = strategy;
        this.state.goodsInfoPool = goodsInfoPool;
        this.state.standbyPool = standbyPool;
        // 注册底部支付方法
        this.children.buyButton.setPay({
            isInDialog: true,
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                let { tabIndex } = this.state;
                const { goodsInfoPool, addGoods } = this.state;
                if (addGoods && tabIndex === 0) {
                    tabIndex = 1;
                }
                if (type === typeMap.component) {
                    this.close(buyDialogCloseType.BOUGHT, { groupKey: goodsInfoPool[tabIndex].groupKey });
                } else if (isStudy) {
                    iosDialogBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey, goUse: true });
                } else {
                    iosDialogBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
                }
            }
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey
        });

        this.setPageInfo();
        this.getStudentConfig();
        await this.getGoodInfo();

        // 页面进入点
        trackGoPay({
            groupKey: this.nowGoodInfo.groupKey,
            fragmentName1: this.props.fragmentName1,
            questionId: URLParams.questionId,
            payPathType: 0
        });

    }

    appEventProxy() {
        onWebBack(async () => {
            this.close();
            return Promise.resolve();
        });
    }

    setPageHeight() {
        const { goodsInfoPool, strategy } = this.state;
        const { type } = this.props;

        if (type !== typeMap.component) {
            let height;

            if (strategy[ABTestKey.key29] === ABTestType.B) {
                if (goodsInfoPool.length === 1) {
                    height = 85 + 75 + 240;
                } else {
                    height = 85 + 137 + 351;
                }
            } else {
                height = 85 + 137 + 351;
            }

            if (URLCommon.kemu === KemuType.Ke4) {
                height -= 118;
            }

            showSetting({ androidH: height, iosH: (height - 120) });
        }
    }

    tabChangeCall = (tabIndex) => {

        if (tabIndex === this.state.tabIndex) {
            return;
        }

        this.setState({
            tabIndex
        }, () => {
            this.children.sendKe2Dialog.show({ type: 'autoClose' });
            pauseAllVideos();
            this.setPageInfo();
        });

    }
    setPageInfo() {
        this.setBuyBottom();
    }

    setBuyBottom() {
        const fragmentName2 = '支付弹窗';
        const { fragmentName1 } = this.props;
        const { goodsInfoPool, addGoods } = this.state;
        let { tabIndex } = this.state;
        if (addGoods && tabIndex === 0) {
            tabIndex = 1;
        }
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        let bottomType: typeCode = typeCode.type1;

        // 有活动的时候按钮不同
        if (nowGoodInfo.inActivity) {
            bottomType = typeCode.type5;
        }

        switch (bottomType) {
            case typeCode.type1:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    price: this.showPrice,
                    validDays: nowGoodInfo.validDays,
                    fragmentName1,
                    fragmentName2,
                    payPathType: 0,
                    tag: {
                        text: this.state.labelPool[nowGoodInfo.groupKey]?.label
                    }
                });
                break;
            case typeCode.type5:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${nowGoodInfo.inActivity.discountedPrice}元`,
                    price: this.showPrice,
                    originalPrice: '日常价￥' + nowGoodInfo.inActivity.preDiscountPrice,
                    fragmentName1,
                    fragmentName2,
                    payPathType: 0
                });
                break;
            default:
                break;
        }
    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const newGoodsPool = [];
        const groupKeys = this.state.standbyPool.concat(this.state.goodsInfoPool).map(item => item.groupKey);
        const standbyGoodsCount = this.state.standbyPool.length;

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            const [standbyGoodsListInfo, normalGoodsListInfo] = [goodsListInfo.slice(0, standbyGoodsCount), goodsListInfo.slice(standbyGoodsCount)];

            normalGoodsListInfo.forEach((goodInfo, index) => {
                // 如果是第一个商品，就替换对应的名称
                if (index === 0) {
                    goodInfo.name = Texts.currentKemuTxt + this.props.title1;
                }
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    openVipWebView({
                        url: BUYED_URL + '?iosH5Head=show'
                    });
                    (new Promise<void>(resolve => {
                        onPageShow(resolve);
                    })).then(() => {
                        this.close(buyDialogCloseType.BOUGHTAUTO);
                    });
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            if (Platform.isZhiHui) {
                tabIndex = 1;
            }

            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }

            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool,
                standbyPool: standbyGoodsCount ? [
                    {
                        ...newGoodsPool[0],
                        tempName: '连续包月'
                    },
                    ...standbyGoodsListInfo.map(goodInfo => ({
                        ...goodInfo,
                        tempName: '半年卡'
                    }))
                ] : []
            }, () => {
                this.children.sendKe2Dialog.show({ type: 'autoClose' });
            });

            await this.getLabel(goodsListInfo);
            this.setPageInfo();
            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon(goodsListInfo);
                await this.getComparePrice(goodsListInfo);

                couponAnimate({
                    couponTargetDomSelect: '.coupon-position-bottom',
                    compareTargetDomSelect: `.tab-cards .${newGoodsPool[2]?.groupKey}`,
                    couponData: this.nowCouponInfo,
                    compareData: this.state.comparePricePool[newGoodsPool[2]?.groupKey],
                    compareGoodsData: newGoodsPool[2],
                    goodsData: this.nowGoodInfo
                });

                this.setPageHeight();
                this.setPageInfo();
            }, 60);
        });
    }
    getCoupon = async (goodsInfoPool: GoodsInfo[]) => {
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel(goodsInfoPool: GoodsInfo[]) {
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                // item.highlights = couponList[0].highlights;

                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice(goodsInfoPool: GoodsInfo[]) {
        await GroupComparePrice({ groupKeys: goodsInfoPool.map(item => item.groupKey).join(',') }).then(comparePricePool => {
            for (const k in comparePricePool) {
                const item = comparePricePool[k];
                comparePricePool[k] = {
                    diffPrice: item.savePrice,
                    allPrice: item.allPrice,
                    groupItems: item.groupItems
                };
            }
            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {
        let { tabIndex } = this.state;
        const { goodsInfoPool, addGoods } = this.state;
        const { isStudy, type } = this.props;

        if (addGoods && tabIndex === 0) {
            tabIndex = 1;
        }

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            if (type === typeMap.component) {
                this.close(buyDialogCloseType.BOUGHT, { groupKey: goodsInfoPool[tabIndex].groupKey });

            } else if (isStudy) {
                newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey, goUse: true }, 2);
            } else {
                newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey }, 2);
            }
        });

    }
    async getStudentConfig() {
        const config = await getSwallowConfig({ key: 'jk_xuesheng_vip' });

        this.setState({
            showStudentConfig: config?.cover
        });
    }
    async close(closeType?: any, info?: any) {
        const { type, close } = this.props;
        this.children.buyButton.hideButton();
        if (type === typeMap.component) {
            close && close(closeType, info);
        } else {
            // eslint-disable-next-line no-lonely-if
            if (!closeType && await hesitateUserPersuade()) {
                return;
            } else {
                webClose();
            }
        }
    }
    onClose() {
        this.close();
    }
    async gotoRightDetail(e) {
        const rightsIntroduceCode = e.refTarget.getAttribute('data-rightsIntroduceCode');

        const groupKeys = [];
        this.state.goodsInfoPool && this.state.goodsInfoPool.forEach((res) => {
            groupKeys.push([res.groupKey]);
        });
        openAuth({
            authId: rightsIntroduceCode,
            groupKeys: groupKeys.join(','),
            groupKey: this.nowGoodInfo.groupKey
        });
        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);

    }
    selectAddGoods() {
        this.setState({
            addGoods: !this.state.addGoods
        });
        this.setPageInfo();
    }
    // 科四切换商品
    changeGoods = (goodsInfo) => {
        const { goodsInfoPool } = this.state;
        const tabIndex = 0;
        goodsInfoPool[tabIndex] = goodsInfo;

        this.setState({
            goodsInfoPool
        });
        this.setPageInfo();
        this.children.sendKe2Dialog.show({ type: 'autoClose' });
    }
    willReceiveProps() {
        return true;
    }
}
