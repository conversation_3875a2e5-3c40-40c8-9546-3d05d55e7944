<import name="style" content="./main" />

<import name="cards" content="../component/cards/main" />
<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="goComment" content=":component/goComment/main" />
<import name="studentGuide" content=":component/studentGuide/main" />
<import name="SendVipEnter" content=":component/sendVipEnter/main" />
<import name="standbyGoods" content=":application/car/component/standbyGoods/main" />
<import name="sendKe2Dialog" content=":component/sendKe2Dialog/main" />
<import name="sendKe2Right" content=":component/sendKe2Right/main" />

<div class="page-buy-dialogB ipad-box">
    <div class="phone-box">
        <div class="close" sp-on:click="onClose"></div>
        <div class="content">
            <sp:if value="props.isStudy || Platform.isXueTang">
                <div class="buy-title" key="buy-title">立即开通</div>
                <sp:else />
                <com:goComment type="1" theme="theme1" groupKey="{{self.nowGoodInfo.groupKey}}" />
            </sp:if>

            <sp:if value="state.goodsInfoPool.length > 1">
                <com:cards comparePricePool="{{state.comparePricePool}}" labelPool="{{state.labelPool}}"
                    goodsList="{{state.goodsInfoPool}}" tabChange="{{self.tabChangeCall}}"
                    tabIndex="{{state.tabIndex}}" />
                <sp:else />
                <div class="alone-box" key="aloneBox">
                    <sp:if value='{{props.isKqmj}}'>
                        <div class="desc">
                            <p class="desc1">{{self.nowGoodInfo.name}}</p>
                            <p class="desc2">{{state.labelPool[self.nowGoodInfo.groupKey].label}}</p>
                        </div>
                        <sp:else />
                        <div class="desc">
                            <p class="desc1">{{props.title1}}</p>
                            <p class="desc2">{{props.subTitle1}}</p>
                        </div>
                    </sp:if>

                    <div class="price"><i>¥</i><b>{{self.showPrice || '--'}}</b></div>
                </div>
            </sp:if>
            <div class="{{URLCommon.kemu === KemuType.Ke1 && state.tabIndex === 0 && state.standbyPool.length?'':'hide'}}">
                <com:standbyGoods changeGoods="{{self.changeGoods}}" standbyPool="{{state.standbyPool}}" />
            </div>
            <div class="sec2" key="sec2">
                <div class="s-title">
                    <!-- 正常版的逻辑是如果sTitleMap中能找到就使用，不能找到就看是否有比价（只有2个全科在里面找不到，不能先判断比价，因为不是所有比价的都有直播课） -->
                    <sp:if value="self.sTitleMap[self.nowGoodInfo.groupKey]">
                        {{self.sTitleMap[self.nowGoodInfo.groupKey]}}
                        <sp:elseif value="state.comparePricePool[self.nowGoodInfo.groupKey]" />
                        比分开买<b>立省{{state.comparePricePool[self.nowGoodInfo.groupKey].diffPrice}}</b>元，额外独享千元直播课
                    </sp:if>
                </div>
                <sp:if value="state.comparePricePool[self.nowGoodInfo.groupKey]">
                    <div class="tab-content tab-content2">
                        <div class="diff-box">
                            <div class="diff">
                                <span>比分开买立省</span>
                                <span class="unit">￥</span>
                                <span
                                    class="price">{{state.comparePricePool[self.nowGoodInfo.groupKey].diffPrice}}</span>
                            </div>
                        </div>
                        <div class="compare-box">
                            <sp:each for="state.comparePricePool[self.nowGoodInfo.groupKey].groupItems">
                                <div class="item">
                                    <div class="name">{{$value.name}}</div>
                                    <sp:if value="$value.price">
                                        <div class="price-box">
                                            <span class="unit">￥</span>
                                            <span class="price">{{$value.price}}</span>
                                        </div>
                                        <sp:else />
                                        <div class="price-box">
                                            <div class="unit">
                                                {{$value.description}}
                                            </div>
                                        </div>
                                    </sp:if>
                                </div>
                            </sp:each>
                        </div>
                        <sp:if value="state.labelPool[self.nowGoodInfo.groupKey].highlights.length">
                            <div class="light-box">
                                <div class="light-title">{{self.linghtTitleMap[self.nowGoodInfo.groupKey] || '请配置文案'}}
                                </div>
                                <div class="light-list">
                                    <sp:each for="state.labelPool[self.nowGoodInfo.groupKey].highlights">
                                        <div sp:if="{{$index < 3}}"
                                            data-rightsIntroduceCode="{{$value.rightsIntroduceCode}}"
                                            sp-on:click="gotoRightDetail" class="light-item">
                                            <div class="num-icon num-icon{{$index + 1}}"></div>
                                            <div class="txt">{{$value.highlight}}</div>
                                        </div>
                                    </sp:each>
                                </div>
                            </div>
                        </sp:if>
                    </div>
                    <sp:else />
                    <div class="tab-content tab-content1">
                        <div class="add-buy">
                            <sp:each for="state.labelPool[self.nowGoodInfo.groupKey].highlights">
                                <div class="step" data-rightsIntroduceCode="{{$value.rightsIntroduceCode}}"
                                    sp-on:click="gotoRightDetail">
                                    <div class="c">{{$value.highlight}}</div>
                                    <div class="b">{{$value.description}}</div>
                                    <div class="tip"><span class="scale">权益{{$index + 1}}</span></div>
                                </div>
                            </sp:each>
                        </div>
                        <sp:if value="{{state.strategy[ABTestKey.key29] !== ABTestType.B}}">
                            <!-- 当卖2个商品的时候一定没有全科，所以展示推荐升级（不能直接看有没有配置，因为如果2个商品又配置了，也是不能展示的，因为不卖全科），当卖3个商品的时候，就要判断是否配置展示学生优惠 -->
                            <sp:if value="state.goodsInfoPool.length === 2 || (state.goodsInfoPool.length === 3 &&
                                !state.showStudentConfig)">
                                <div class="add-goods">
                                    <div class="left-tip"></div>
                                    <div class="goods-name" data-after-content="有效期180天">特惠加购科四VIP</div>
                                    <div class="price-box" data-after-content="单独买50元">
                                        <span class="unit">￥</span><span class="price">{{self.addGoodsPrice}}</span>
                                    </div>
                                    <div sp-on:click="selectAddGoods" class="check-box {{state.addGoods?'active':''}}">
                                    </div>
                                </div>
                                <sp:else />
                                <div style="margin-top: 30px;">
                                    <com:studentGuide />
                                </div>
                            </sp:if>
                        </sp:if>
                    </div>
                </sp:if>
            </div>
        </div>
        <div class="footer">
            <com:buyButton>
                <div sp:slot="couponEntry" class="go_coupon coupon-position-bottom">
                    {{self.nowCouponInfo.couponCode?'已优惠' +
                        self.nowCouponInfo.priceCent + '元':''}}
                </div>
            </com:buyButton>
        </div>
    </div>

    <com:SendVipEnter name="right" entranceCode="{{URLCommon.kemu === 1?'ke1_dtjq_right':'ke4_dtjq_right'}}"
        position="right" />

    <com:sendKe2Dialog goodsInfo="{{self.nowGoodInfo}}" />
    <com:sendKe2Right goodsInfo="{{self.nowGoodInfo}}" bottom="{{1}}" />
</div>
