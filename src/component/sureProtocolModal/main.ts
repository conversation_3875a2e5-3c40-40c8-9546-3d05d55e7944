/*
 * ------------------------------------------------------------------
 * 选择支付方式弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import ReadProtocol from ':component/readProtocol/main';
import { Application } from '@simplex/simple-core';
import { Dialog } from ':component/dialog/main';
import { Platform } from ':common/env';
import { MCProtocol } from '@simplex/simple-base';
import { setProtocolStatus } from ':common/features/agreement';

export default class SureProtocolModal extends Application {
    declare children: {
        readProtocol: ReadProtocol
        dialog: Dialog<any, boolean>
    }
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
    }
    show() {
        return this.children.dialog.show({});
    }
    agreeProtocol() {
        this.children.dialog.children.readProtocol.changeReaded();
        
        setProtocolStatus(true);

        return this.children.dialog.hide(true);
    }
    disAgreeProtocol() {
        return this.children.dialog.hide(false);
    }
}
