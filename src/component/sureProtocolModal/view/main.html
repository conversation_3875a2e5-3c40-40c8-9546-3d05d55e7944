<import name="style" content="./main" module="S" />
<import name="dialog" content=":component/dialog/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />

<div class=":sure-protocol-modal">
    <com:dialog zIndex="1100">
        <div class=":modal-content">
            <!-- <div class=":title">敬请确认</div> -->
            <div class=":body">
                <com:middleProtocol />
                <div style="display: none;">
                    <com:readProtocol />
                </div>
            </div>
            <div class=":footer">
                <div sp-on:click="agreeProtocol" class=":btn :agree">同意并继续</div>
                <div sp-on:click="disAgreeProtocol" class=":btn :noagree">不同意</div>
            </div>
        </div>
    </com:dialog>
</div>
