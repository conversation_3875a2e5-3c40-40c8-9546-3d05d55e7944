.move-goods-modal {
    width: 98px;
    height: 169px;
    background: #333333;
    border: 2px solid #ffffff;
    border-radius: 4px;
    position: fixed;
    top: 140px;
    right: 15px;
    z-index: 1001;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    display: none;

    &.hide {
        display: none !important;
    }

    &.show {
        display: flex;
    }

    .mask {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0);
    }

    .close {
        position: absolute;
        z-index: 2;
        top: 5px;
        right: 5px;
        width: 18px;
        height: 18px;
        background: url(../images/<EMAIL>) center center/cover;
    }



    .video {
        flex: 1;
        width: 100%;
        max-height: 140px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;

        video {
            width: 100%;
            object-fit: cover;
            max-height: 140px;
        }

        .play-icon {
            width: 38px;
            height: 38px;
            position: absolute;
            z-index: 2;
            background: url(../images/<EMAIL>) center center/cover;
        }
    }

    .dec {
        flex-shrink: 0;
        width: 100%;
        height: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        font-size: 16px;
        background-color: #FF4A40;
        border-radius: 0 0 4px 4px;
    }
}
