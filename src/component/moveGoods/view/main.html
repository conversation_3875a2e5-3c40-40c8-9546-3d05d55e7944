<import name="style" content="./main" module="S" />

<div class=":move-goods-modal {{props.info.videoUrl && !state.hide?S.show:S.hide}}"
    ref="videoBox" skip-attribute="style" sp-on:touchstart="onTouchStart" sp-on:touchmove="onTouchMove"
    sp-on:touchend="onTouchEnd" sp-on:click="goDetail">
    <div class=":mask"></div>
    <div class=":close" sp-on:click="closeModal"></div>
    <div class=":video">
        <video ref="video" src="{{props.info.videoUrl}}" poster="{{props.info.videoPoster}}" webkit-playsinline
            playsinline
            loop></video>
        <sp:if value="{{!state.playing}}">
            <div class=":play-icon" sp-on:click="playVideo"></div>
        </sp:if>
    </div>
    <div class=":dec">商品讲解</div>
</div>
