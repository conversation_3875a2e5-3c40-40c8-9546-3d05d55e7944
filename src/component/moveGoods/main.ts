/*
 * ------------------------------------------------------------------
 * 商品讲解弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { Component } from '@simplex/simple-core';
import { pauseAllVideos } from ':common/features/dom';
import { trackEvent } from ':common/stat';
import jump from ':common/features/jump';
import { openVipWebView } from ':common/core';
import { GroupKey } from ':store/goods';
import { Platform } from ':common/env';

let allowMove = false;
let $videoBox: HTMLElement;
let boxPoint;
let startPoint = {
    x: 0,
    y: 0
};
const fragmentName1 = '商品讲解小窗';
interface State {
    hide: boolean,
    playing: boolean
}
interface Props {
    groupKey: GroupKey
    info: {
        videoUrl: string
        entrance: string
        stat: {
            fromPathCode: string
            fromPageCode: string
        }
    }
}

export default class MoveGoods extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            hide: false,
            playing: false
        };
    }
    $video: HTMLVideoElement
    didMount() {
        $videoBox = this.getDOMNode().videoBox as HTMLElement;
        this.$video = this.getDOMNode().video as HTMLVideoElement;
        const initPonit = $videoBox.getBoundingClientRect();

        boxPoint = {
            x: initPonit.x,
            y: initPonit.y
        };

        setTimeout(() => {
            trackEvent({
                payStatus: 0,
                fragmentName1,
                actionType: '出现'
            });
        }, 1500);

        this.$video.addEventListener('pause', () => {
            this.setState({
                playing: false
            });
        });
        this.$video.addEventListener('play', () => {
            this.setState({
                playing: true
            });
        });
    }
    onTouchStart(e) {
        const point = e.touches[0];
        allowMove = true;
        startPoint = {
            x: point.pageX,
            y: point.pageY
        };
    }
    onTouchMove(e): any {
        e.preventDefault();
        if (allowMove === false) {
            return;
        }
        const point = e.touches[0];
        const movePoint = {
            x: point.pageX,
            y: point.pageY
        };

        const dis = {
            x: movePoint.x - startPoint.x,
            y: movePoint.y - startPoint.y
        };

        // 超出屏幕
        if (boxPoint.x + dis.x < 0 || boxPoint.x + dis.x + $videoBox.offsetWidth > document.body.clientWidth || boxPoint.y + dis.y < 0 || boxPoint.y + dis.y + $videoBox.offsetHeight > document.body.clientHeight) {
            return;
        }

        $videoBox.style.top = boxPoint.y + dis.y + 'px';
        $videoBox.style.left = boxPoint.x + dis.x + 'px';

    }
    onTouchEnd() {
        allowMove = false;

        const initPonit = $videoBox.getBoundingClientRect();

        boxPoint = {
            x: initPonit.x,
            y: initPonit.y
        };
    }
    closeModal(e) {
        e.stopPropagation();
        this.setState({
            hide: true
        });
        this.$video.pause();
    }
    playVideo(e) {
        e.stopPropagation();
        pauseAllVideos();
        this.$video.muted = false;
        this.$video.play();
    }
    goDetail(e) {
        const { info, groupKey } = this.props;
        const stat = info.stat;
        e.stopPropagation();
        trackEvent({
            fragmentName1,
            actionType: '点击',
            actionName: '跳转详情页'
        });

        openVipWebView({
            url: `https://laofuzi.kakamobi.com/jkbd-vip/index/videoGoods.html?entrance=${info.entrance}&groupKey=${groupKey}&fromPathCode=${stat.fromPathCode}&fromPageCode=${stat.fromPageCode}&from=${stat.fromPageCode}`,
            protocolParams: {
                fromPathCode: stat.fromPathCode
            }
        });
    }
    willReceiveProps(next) {
        if (this.props.info?.videoUrl !== next.info?.videoUrl) {
            setTimeout(() => {
                this.$video?.play();
            }, 100);
        }
        return true;
    }
}
