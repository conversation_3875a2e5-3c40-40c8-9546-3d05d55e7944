/*
 * ------------------------------------------------------------------
 * 问大家
 * ------------------------------------------------------------------
 */

import jump from ':common/features/jump';
import { DIANPING_URL } from ':common/navigate';
import { GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    groupKey: GroupKey;
}

export default class Count extends Component<unknown, Props> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.props = {
            groupKey: null
        };

    }
    goDianPing() {
        const { groupKey } = this.props;

        jump.navigateTo(DIANPING_URL, {
            groupKey
        });
    }
    willR<PERSON><PERSON><PERSON>P<PERSON>() {
        return true;
    }
}
