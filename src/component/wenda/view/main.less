.wenda-content {

    padding-top: 12px;

    .kemuPassRateReminder-box {
        margin-top: -12px;
        margin-bottom: -12px;
    }

    .sec-wenda {
        background: #ffffff;
        border-radius: 10px;
        padding: 15px;
        position: relative;
        overflow: hidden;


    }

    .wenda-t {
        height: 30px;

        .sp1 {
            color: #7F3511;
            font-size: 19px;
            padding-right: 22px;
            background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/wen.png) no-repeat right center;
            background-size: 17px 17px;
        }

        .sp2 {
            color: #6E6E6E;
            font-size: 13px;
        }
    }

    .wenda-li {
        padding-top: 15px;
        padding-bottom: 12px;
        border-top: 1px solid #F2F2F2;

        .div1 {
            p {
                color: #494455;
                font-weight: bold;
                background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/q.png) no-repeat left top;
                background-size: 22px 22px;
                font-size: 15px;
                line-height: 22px;
                padding-left: 30px;

            }
        }

        .div2 {
            padding-top: 15px;

            img {
                width: 22px;
                height: 22px;
                border-radius: 100%;
                margin: 0;
            }

            b {
                color: #6E6E6E;
                font-size: 14px;
                padding-left: 8px;
                flex: 1;
            }

            span {
                color: #7F3511;
                font-size: 13px;
                line-height: 14px;
                padding: 2px 10px;
                background-color: #FFF0E5;
                border-bottom-left-radius: 4px;
                border-top-right-radius: 4px;
            }
        }

        .div3 {
            color: #494455;
            font-size: 14px;
            padding-left: 30px;
            padding-top: 9px;

            p {
                line-height: 1.5;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
        }
    }

    .wenda-f {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 65px;
        background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.4) 100%);
        display: flex;
        border-radius: 10px;

        span {
            display: block;
            color: #5E2E06;
            margin: 36px auto 0 auto;
            background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/arrow.png) no-repeat right center;
            height: 21px;
            line-height: 21px;
            font-size: 15px;
            padding-right: 12px;
            background-size: 7px 11px;
        }
    }
}

.webkit-flex-center {
    display: flex;
    align-items: center;
    justify-content: space-between;
    -webkit-display: flex;
    -webkit-align-items: center;
    -webkit-justify-content: space-between;
}
