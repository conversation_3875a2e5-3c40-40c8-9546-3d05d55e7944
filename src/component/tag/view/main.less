.tag_i {
    white-space: nowrap;
}

.tag {
    position: absolute;
    right: 0;
    top: -15px;
    height: 20px;
    background: linear-gradient(90deg, #ff7d76 0%, #ff4a40 100%);
    font-size: 12px;
    color: #ffffff;
    border-radius: 6px 0px 6px 0px;
    line-height: 18px;
    padding: 2px 6px 0 6px;
    box-sizing: border-box;
}

.kqmj {
    position: absolute;
    right: 0;
    top: -15px;
    height: 20px;
    background: linear-gradient(90deg, #d06f34 0%, #c53119 100%);
    font-size: 12px;
    color: #ffffff;
    border-radius: 6px 0px 6px 0px;
    line-height: 18px;
    padding: 2px 6px 0 6px;
    box-sizing: border-box;
}

.ke2tag {
    position: absolute;
    right: 12px;
    top: -15px;
    background: linear-gradient(360deg, #f9c39f 0, #fedec7 100%);
    border-radius: 0 10px 0 8px;
    font-size: 12px;
    font-weight: 500;
    color: #622604;
    line-height: 16px;
    padding: 2px 9px;
    -webkit-transform: scale3d(0.9, 0.9, 1);
    transform: scale3d(0.9, 0.9, 1);
}

.tag1 {
    position: absolute;
    right: 0;
    top: -15px;
    height: 20px;
    background: linear-gradient(90deg, #ff7d76 0%, #ff4a40 100%);
    font-size: 12px;
    color: #ffffff;
    border-radius: 10px 10px 10px 0px;
    line-height: 18px;
    padding: 3px 8px 0 8px;
    box-sizing: border-box;
}

.double11 {
    position: absolute;
    right: 0;
    top: -15px;
    height: 20px;
    background: linear-gradient(90deg, #ff7711 0%, #ff4526 50%, #fe5e60 100%);
    font-size: 12px;
    color: #ffffff;
    border-radius: 10px 10px 10px 0px;
    line-height: 18px;
    padding: 3px 8px 0 8px;
    box-sizing: border-box;
}

.ke2 {
    position: absolute;
    right: 5px;
    top: -15px;
    height: 20px;
    // background: linear-gradient(90deg, #ff7711 0%,#ff4526 50%, #fe5e60 100%);
    // border-radius: 10px 10px 10px 0px;

    background: linear-gradient(90deg, #ff7810 0%, #fe3c29 55%, #fe6164 100%);
    border-radius: 33px 33px 33px 2px;

    font-size: 12px;
    color: #ffffff;
    line-height: 18px;
    padding: 2px 8px 0 8px;
    box-sizing: border-box;
}

.mnks {
    position: absolute;
    right: 5px;
    top: -7px;
    height: 20px;
    background: linear-gradient(90deg, #ffd878 0%, #ffc400 100%);
    font-size: 12px;
    color: #6f2117;
    border-radius: 10px 10px 10px 10px;
    line-height: 18px;
    padding: 2px 8px 0 8px;
    box-sizing: border-box;

    .arrow {
        position: absolute;
        width: 10px;
        height: 7px;
        background: url(../images/1.png) no-repeat;
        background-size: 100% 100%;
        bottom: -7px;
        right: 30px;
    }
}

.type6 {
    position: absolute;
    right: 6px;
    top: -15px;
    font-size: 11px;
    font-family: PingFangSC-Medium, PingFang SC, sans-serif;
    font-weight: 500;
    color: #6f2117;
    line-height: 16px;
    padding: 2px 8px;
    background: linear-gradient(90deg, #ffd97b 0%, #ffc400 100%);
    border-radius: 33px 33px 33px 2px;
}
