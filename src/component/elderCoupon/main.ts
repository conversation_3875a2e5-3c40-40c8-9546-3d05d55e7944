/*
 * ------------------------------------------------------------------
 * 选择支付方式弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { Dialog } from ':component/dialog/main';
import { GroupKey } from ':store/goods';
import { getAuthToken } from ':common/core';
import { login } from ':common/features/login';
import { getCode, getCouponDetail } from ':store/coupon';
import { trackDialogShow, trackEvent } from ':common/stat';
import { URLCommon } from ':common/env';

interface State {
    coupon: any
}

const sendCouponKey = 'conpon-elder-vip';

export default class ElderCouponDialog extends Dialog<void, boolean, State> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    } s
    didMount() {
        if (URLCommon.isElder) {
            this.getSendCouponInfo();
        }
    }
    public show() {
        trackDialogShow({
            fragmentName1: '优惠券弹窗'
        });
        return super.show();
    }
    getSendCouponInfo() {
        getCouponDetail({
            couponCode: sendCouponKey
        }).then(data => {
            this.setState({
                coupon: {
                    name: data.couponName,
                    price: data.priceCent / 100,
                    validDays: data.validTimeSecond / (24 * 3600)
                }
            });
        });
    }
    async getSendCoupon() {
        trackEvent({
            fragmentName1: '优惠券弹窗',
            actionType: '点击收下'
        });
        const authToken = await getAuthToken();
        if (authToken) {
            await getCode({ couponUniqKey: sendCouponKey });
            this.hide(true);
        } else {
            await login(false);
            this.getSendCoupon();
        }
    }
    close() {
        this.hide();
    }
}
