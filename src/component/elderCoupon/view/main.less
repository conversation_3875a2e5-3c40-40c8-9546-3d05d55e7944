.elder-coupon-content {
    width: 320px;
    height: 306px;
    background: url(../images/tc_bj.png);
    background-size: 100% 100%;
    padding: 30px 24px 0;
    position: relative;

    .close {
        position: absolute;
        top: 14px;
        right: 14px;
        width: 16px;
        height: 16px;
        background: url(../images/close.png) no-repeat center center/cover;
    }

    .title {
        font-size: 27px;
        text-align: center;
        font-weight: 600;
        color: #3b1715;
    }

    .coupon {
        margin-top: 15px;
        width: 273px;
        height: 91px;
        background: url(../images/<EMAIL>) no-repeat center center/cover;
        position: relative;

        .price-box {
            position: absolute;
            top: 28px;
            left: 14px;
            color: #fd1e34;

            .unit {
                font-size: 15px;
            }

            .price {
                font-size: 36px;
            }
        }

        .name {
            position: absolute;
            top: 23px;
            left: 82px;
            font-size: 20px;
            color: #cf7a78;
        }

        .validDays {
            position: absolute;
            left: 82px;
            bottom: 23px;
            font-size: 12px;
            color: #cf7a78;
        }
    }

    .get {
        margin: 54px auto;
        width: 220px;
        height: 46px;
        background: linear-gradient(90deg, #f95b38 2%, #e83e30);
        border-radius: 25px;
        box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.2) inset,
            0px -1px 0px 0px rgba(168, 28, 15, 0.35) inset;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        font-size: 21px;
        font-weight: 600;
        position: relative;

        .tips {
            position: absolute;
            top: 0;
            right: 10px;
            transform: translateY(-80%);
            width: 76px;
            height: 24px;
            background: linear-gradient(298deg, #fadd88 8%, #ffe5c1 84%);
            border-radius: 63px 63px 63px 0px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 600;
            font-size: 13px;
            color: #ed0026;
        }
    }
}
