<import name="style" content="./main" module="S" />
<import name="dialog" content=":component/dialog/main" />
<import name="payType" content=":component/payType/main" />
<import name="readProtocol" content=":component/readProtocol/main" />

<div>
    <com:dialog >
        <div class=":elder-coupon-content">
            <div class=":close" sp-on:click="close"></div>
            <div class=":title">恭喜获得</div>
            <div class=":coupon">
                <div class=":price-box">
                    <span class=":unit">￥</span>
                    <span class=":price">{{state.coupon.price}}</span>
                </div>
                <div class=":name">{{state.coupon.name}}</div>
                <div class=":validDays">有效期:{{state.coupon.validDays}}天</div>
            </div>
            <div class=":get" sp-on:click="getSendCoupon">高兴收下<span class=":tips">立减{{state.coupon.price}}元</span></div>
        </div>
    </com:dialog>
</div>
