/*
 * ------------------------------------------------------------------
 * 科目二3D购买弹窗
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { URLCommon } from ':common/env';
import { GoodsInfo, GroupKey } from ':store/goods';
import { newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackEvent } from ':common/stat';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { iosBuySuccess } from ':common/features/ios_pay';
import { goodsInfoWithCoupon, Coupon } from ':common/features/coupon';
import PayTypeCom from ':component/payType/main';
import { noPayOrderSign } from ':store/chores';
import { pauseAllVideos } from ':common/features/dom';
import { typeCode } from ':common/features/bottom';
import { showClock } from ':common/utils';

interface State {
    tabIndex: number
    goodsList: GoodsInfo[]
    show: boolean
    time: number
    showTime: string[]
}

interface Props {
    title1: string,
    goodsInfoPool: GoodsInfo[],
    standbyPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object
    close(closeType?: any, info?: any)
}

const sTitleMap = {
    [GroupKey.ChannelKe2]: '- 科二VIP 只需三步 -',
    [GroupKey.ChannelKe2Asset]: '- 解锁本地科二考场 -',
    // [GroupKey.ChannelKe2Ke3Group]: '- 科二科三VIP高效学一起买更优惠 -',
    [GroupKey.ChannelKe2Ke3GroupNew]: '- 科二科三VIP高效学一起买更优惠 -'
};

const fragmentName1 = '未支付订单提醒弹窗';

export default class Ke23D extends Component<State, Props> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeCom;
    };
    sTitleMap = sTitleMap
    // linghtTitleMap = {
    //     [GroupKey.ChannelKe2Ke3Group]: '仅需3步，高效学科二科三'
    // }
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {
            tabIndex: 0,
            show: false,
            time: 0,
            showTime: [],
            goodsList: []
        };
    }

    get nowGoodInfo() {
        const { tabIndex, goodsList } = this.state;
        // const { goodsInfoPool } = this.props;

        return goodsList[tabIndex];
    }

    get nowCouponInfo() {
        const { couponPool } = this.props;

        return couponPool[this.nowGoodInfo.groupKey];
    }

    /**
   * 如果有优惠券的价格为0的就显示0
   * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
  */
    get showPrice() {
        const { tabIndex, goodsList } = this.state;
        const { goodsInfoPool, couponPool } = this.props;
        const nowPayPrice = goodsList[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }

    getLabelTip(index: number) {
        const { goodsList } = this.state;
        const { goodsInfoPool, comparePricePool } = this.props;

        if (comparePricePool[goodsList[index]?.groupKey]) {
            return '比分开买立省' + comparePricePool[goodsList[index]?.groupKey].diffPrice + '元';
        } else if (goodsList[index]?.groupKey === GroupKey.ChannelKe2) {
            return '考不过补偿40元';
        } else {
            return 'vip功能更齐全';
        }
    }

    async didMount() {
        const { goodsInfoPool, comparePricePool, standbyPool } = this.props;
        const groupKeys = goodsInfoPool.map(item => item.groupKey);
        const goodsList = [...goodsInfoPool];

        if (standbyPool && standbyPool.length) {
            standbyPool.forEach(item => {
                if (!groupKeys.includes(item.groupKey)) {
                    groupKeys.push(item.groupKey);
                }
            });
        }

        noPayOrderSign({
            groupKeys: groupKeys,
            tiku: URLCommon.tiku
        }).then((data) => {
            // data = {
            //     remind: true,
            //     time: 600,
            //     channelCode: GroupKey.ChannelKe2
            // };
         
            if (data.remind) {
                let belongStandbyGoods: GoodsInfo | null = null;
                let tabIndex = groupKeys.indexOf(data.channelCode);
                if (standbyPool && standbyPool.length) {
                    belongStandbyGoods = standbyPool.find(item => item.groupKey === data.channelCode);
                }

                if (belongStandbyGoods) {
                    goodsList[0] = belongStandbyGoods;
                    tabIndex = 0;
                }

                this.setState({
                    show: true,
                    time: data.time,
                    showTime: showClock(data.time, 'hh:mm:ss').split(':'),
                    goodsList,
                    tabIndex: tabIndex
                }, () => {

                    // 页面进入点
                    trackEvent({
                        groupKey: this.nowGoodInfo.groupKey,
                        actionType: '出现',
                        payStatus: '2',
                        payPathType: 1,
                        fragmentName1
                    });

                    this.setPageInfo();
                });

                setInterval(() => {
                    let { time } = this.state;

                    if (time === 0) {
                        this.close();
                        return;
                    }
                    time--;

                    this.setState({
                        time,
                        showTime: showClock(time, 'hh:mm:ss').split(':')
                    });

                }, 1000);
            }

        });

    }

    tabChangeCall = (tabIndex) => {

        if (tabIndex === this.state.tabIndex) {
            return;
        }

        this.setState({
            tabIndex
        }, () => {
            pauseAllVideos();
            this.setPageInfo();
        });

    }
    onTabChange(e) {
        const index = +e.refTarget.getAttribute('data-index');

        this.tabChangeCall(index);
    }
    setPageInfo() {
        this.setBuyBottom();
    }

    setBuyBottom() {
        const { tabIndex, goodsList } = this.state;
        const nowGoodInfo: GoodsInfo = goodsList[tabIndex];
        let bottomType: typeCode = typeCode.type1;

        // 有活动的时候按钮不同
        if (nowGoodInfo.inActivity) {
            bottomType = typeCode.type5;
        }

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });

        switch (bottomType) {
            case typeCode.type1:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    price: this.showPrice,
                    validDays: nowGoodInfo.validDays,
                    fragmentName1,
                    payPathType: 0
                });
                break;
            case typeCode.type5:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${nowGoodInfo.inActivity.discountedPrice}元`,
                    price: this.showPrice,
                    originalPrice: '日常价￥' + nowGoodInfo.inActivity.preDiscountPrice,
                    fragmentName1,
                    payPathType: 0
                });
                break;
            default:
                break;
        }
    }

    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsList } = this.state;
        // const { goodsInfoPool } = this.props;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsList[tabIndex].groupKey,
            sessionIds: goodsList[tabIndex].sessionIds,
            activityType: goodsList[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsList[tabIndex].groupKey });
        });

    }
    close() {
        const { close } = this.props;

        this.setState({
            show: false
        });

        close && close();

    }
    getStatus() {
        return this.state.show;
    }
    onClose() {
        this.close();
    }
    willReceiveProps() {
        return true;
    }
}
