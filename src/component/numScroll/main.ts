/*
 * ------------------------------------------------------------------
 * 模拟数字滚动效果
 * ------------------------------------------------------------------
 */

import { ticker } from ':common/utils';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    num: number;
    toNum: number;
}

export default class extends Component<unknown, Props> {

    private cancelTicker: () => void;
    private animationCount: any;

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }

    /** 不同进位需要的动画数字串，起始数字不一样～～ */
    getNumbers(value: number) {
        const isDecrease = this.isDecrease;
        return new Array(11).fill(0).map((v, i) => {
            return isDecrease ? (value - i + 10) % 10 : (+value + i) % 10;
        });
    }

    /** 数字展示的初始值 */
    get segments() {
        const a = String(this.props.num);
        const b = String(this.props.toNum);

        return String(this.props.num).padStart(Math.max(a.length, b.length), '0').split('').reverse();
    }

    /** 数字一共改变了多少 */
    get diff() {
        return Math.abs(this.props.num - this.props.toNum);
    }

    /** 数字是减少还是增加 */
    get isDecrease() {
        return Number(this.props.num) > Number(this.props.toNum);
    }

    /** 计算除个位数以外，其他位置进位改变的条件 */
    getAnimationCount() {
        const segments = this.segments.slice().map(Number);
        const original = segments.slice();
        segments.pop();

        const counts = [];
        let accum = 0;

        segments.forEach((segment, i) => {
            const remain = this.isDecrease ? segment : 9 - segment;
            accum = (remain * Math.pow(10, i)) + accum;
            const incr = Math.pow(10, i + 1);
            counts.push({
                value: accum,
                ref: 'num' + (i + 1),
                incr,
                counter: 0,
                segment: original[i + 1]
            });
        });

        return counts;
    }

    didMount() {
        this.animationCount = this.getAnimationCount();
        this.checkLeadingZero();
    }

    willUnmount() {
        this.cancelTicker?.();
    }

    startAnimation() {
        const num0 = this.getDOMNode().num0 as HTMLElement;
        num0.style.animationIterationCount = `${this.diff / 10}`;
        num0.style.animationDuration = `${12 / this.diff}s`;
        num0.classList.add('num-animate');
    }

    /** 检查是否有前导0，有的话需要隐藏 */
    checkLeadingZero() {
        const refs = this.getDOMNode();
        this.animationCount.reduceRight((prevZero, count) => {
            const ref = refs[count.ref] as HTMLElement;
            if (Math.abs(count.segment - count.counter) % 10 === 0) {
                if (prevZero) {
                    ref.parentElement.parentElement.style.display = 'none';
                } else {
                    ref.parentElement.parentElement.style.display = 'initial';
                }
                return prevZero;
            } else {
                ref.parentElement.parentElement.style.display = 'initial';
                return false;
            }
        }, true);
    }

    onAnimationStart(e) {
        // 每次个位数滚动一下需要的时间
        const duration = 1200 / this.diff;
        const lastTime = performance.now();

        const refs = this.getDOMNode();

        // 监听不到动画的准确更新时间，这里用tick来做监听
        this.cancelTicker = ticker((time = performance.now()) => {
            // 个位数一共滚了多少次
            const elapsedTick = (time - lastTime) / duration;

            for (const count of this.animationCount) {
                // 触发其他进位改变，则手动触发其数字过渡动画效果
                if (elapsedTick > count.value) {
                    count.value += count.incr;
                    count.counter++;

                    const ref = refs[count.ref] as HTMLElement;
                    ref.style.transform = `translateY(-${(count.counter % 11) / 11 * 100}%)`;
                }
            }

            this.checkLeadingZero();
        }, duration);
    }

    onAnimationEnd(e) {
        console.log('ended');
        this.cancelTicker();
    }
}
