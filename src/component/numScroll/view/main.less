@keyframes numScroll {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(-90.9%);
    }
}

.container {
    display: flex;
    direction: rtl;
    text-align: center;
}

.placeholder {
    position: relative;
    overflow: hidden;
}

.value {
    visibility: hidden;
}

.wrapper {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
}

.num {
    display: flex;
    flex-direction: column;
}

:global(.num-animate) {
    animation-name: numScroll;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
}

.num-other {
    transition: transform 0.2s linear;
}
