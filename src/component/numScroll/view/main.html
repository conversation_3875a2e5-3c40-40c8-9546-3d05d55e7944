<import name="style" content="./main" module="S" />

<div class=":container">
    <sp:each for="{{self.segments}}">
        <div class=":placeholder">
            <div class=":value">{{$value}}</div>
            <div class=":wrapper">
                <sp:if value="{{$index === 0}}">
                    <!-- 个位数需要持续的动画 -->

                    <div class=":num" sp-on:animationstart="onAnimationStart" sp-on:animationend="onAnimationEnd" ref="num0">
                        <sp:each for="{{self.getNumbers($value)}}">
                            <span>{{$value}}</span>
                        </sp:each>
                    </div>

                    <sp:else />

                    <!-- 其他进位需要突变的动画 -->

                    <div class=":num :num-other" ref="num{{$index}}">
                        <sp:each for="{{self.getNumbers($value)}}">
                            <span>{{$value}}</span>
                        </sp:each>
                    </div>
                </sp:if>
            </div>
        </div>
    </sp:each>
</div>