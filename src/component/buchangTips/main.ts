/*
 * ------------------------------------------------------------------
 * 最近购买用户轮播
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';

export default class News extends Component {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }

    willReceiveProps() {
        return true;
    }

    goBuchang() {
        (this.app as any).goAuth('bgbc');
    }
}
