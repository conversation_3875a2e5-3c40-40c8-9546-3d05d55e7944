/*
 * ------------------------------------------------------------------
 * 客货车科二科三vip
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { PayType, setPageName, URLParams } from ':common/env';
import { trackGoPay } from ':common/stat';
import { getSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { showSetting } from ':common/features/embeded';
import { iosBuySuccess } from ':common/features/ios_pay';
import { webClose } from ':common/core';
import PayTypeCom from ':component/payType/main';
setPageName(URLParams.fromPage || '项目视频详情页');
const fragmentName1 = URLParams.fragmentName1 || '未知片段';
interface Props {
    groupKey: GroupKey;
    getCurrentGoodsInfo?(goodsInfo: GoodsInfo)
}
interface State {
    goodsInfo: GoodsInfo;
}

export default class extends Component<State, Props> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeCom;
    }
    $constructor() {

        this.$super({
            name: module.id,
            target: document.body,
            view: View
        });
        this.state = {
            goodsInfo: null
        };
    }
    get currentGoods() {
        return this.state.goodsInfo;
    }
    async didMount() {
        const { groupKey } = this.props;
        showSetting({
            iosH: 270,
            androidH: 415
        });
        trackGoPay({
            groupKey,
            fragmentName1,
            payPathType: 0
        });
        ensureSiriusBound({ groupKey, type: PayBoundType.GoLogin });
        const goodsInfo = await getSessionInfo({ groupKey });
        this.props.getCurrentGoodsInfo(goodsInfo);
        this.setState({ goodsInfo });
        this.setPayment();
    }

    private setPayment() {
        this.children.buyButton.setPay({
            androidPay: this.pay.bind(this),
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => iosBuySuccess({ groupKey: this.currentGoods.groupKey }),
            isInDialog: true
        });
        this.children.buyButton.setButtonConfig({
            groupKey: this.currentGoods.groupKey,
            type: 1,
            title: '确认协议并支付',
            price: this.currentGoods.payPrice,
            validDays: this.currentGoods.validDays,
            fragmentName1,
            fragmentName2: '支付弹窗'
        });
    }

    /** 发起支付 */
    async pay(stat: PayStatProps) {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.currentGoods.groupKey,
            sessionIds: this.currentGoods.sessionIds,
            activityType: this.currentGoods.activityType,
            ...stat
        }, false).then(() => newBuySuccess({ groupKey: this.currentGoods.groupKey }, 2));
    }

    onCloseClick() {
        webClose();
    }
}
