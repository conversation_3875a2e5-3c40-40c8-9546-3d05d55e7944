html,
body {
    height: 100vh;
    overflow: hidden;
}

.container-khkemu23 {
    position: relative;
    background: linear-gradient(180deg,
            #ffeddc 0%,
            #fffaf0 38%,
            #ffffff 73%,
            #ffffff 100%);
    box-shadow: 0px 1px 0px 0px #ffffff;
    border-radius: 6px 6px 0px 0px;
    padding: 15px 15px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.title {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC, sans-serif;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
}

.close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    background: url("../images/close.png");
    background-size: cover;
}

.pay-type {
    margin-bottom: -10px;
    margin-left: 10px;
}

.buy-btn {
    margin: 0 -10px;
}

.detail {
    flex: 1;
    overflow-y: auto;
}