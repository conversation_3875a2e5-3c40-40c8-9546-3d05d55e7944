<import name="style" content="./main" module="S" />

<div class="{{S['wrapper-' + props.theme]}} {{Platform.isIOS && 'hide'}}">
    <sp:each for="{{state.list}}">
        <div class=":payItem" ref="pay-type" data-type="{{$value.payType}}">
            <div class=":payIcon {{$value.payType === PayType.Harmony?S['hwpayIcon']:''}}
                {{$value.payType === PayType.Weixin?S['wxpayIcon']:''}}
                {{$value.payType === PayType.Alipay?S['alipayIcon']:''}}
                {{$value.payType === PayType.OtherPay?S['friendPayIcon']:''}}">
            </div>
            <div class=":payDesc">{{$value.text}}</div>
            <div class=":xzIcon {{state.payType === $value.payType ? S.active : ''}}"></div>
        </div>
    </sp:each>
</div>
