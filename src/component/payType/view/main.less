.wrapper-horizontal {
    display: flex;
    align-items: center;

    .pay-item {
        border-bottom: none;
        align-items: center;
        padding: 4px 0;
        height: auto;
    }

    .pay-item+.pay-item {
        margin-left: 15px;
    }

    .pay-icon {
        display: none;
    }

    .pay-desc {
        order: 1;
        padding-left: 10px;
        white-space: nowrap;
    }

    .xz-icon {
        width: 18px;
        height: 18px;
        background: url(../images/8.png) no-repeat;
        background-size: 100% 100%;
    }

    .active {
        background: url(../images/5.png) no-repeat;
        background-size: 100% 100%;
    }
}

.wrapper-mnks {
    display: flex;
    align-items: center;

    .pay-item {
        border-bottom: none;
        align-items: center;
    }

    .pay-item+.pay-item {
        margin-left: 15px;
    }

    .pay-icon {
        display: none;
    }

    .pay-desc {
        order: 1;
        padding-left: 10px;
        color: #ffffff;
        font-size: 14px;
        white-space: nowrap;
    }

    .xz-icon {
        width: 18px;
        height: 18px;
        background: url(../images/uncheck.png) no-repeat;
        background-size: 100% 100%;
    }

    .active {
        background: url(../images/check.png) no-repeat;
        background-size: 100% 100%;
    }
}

.wrapper-mnks-driver {
    display: flex;
    align-items: center;

    .pay-item {
        border-bottom: none;
        align-items: center;
    }

    .pay-item+.pay-item {
        margin-left: 15px;
    }

    .pay-icon {
        display: none;
    }

    .pay-desc {
        order: 1;
        padding-left: 10px;
        color: #333333;
        white-space: nowrap;
        font-size: 14px;
    }

    .xz-icon {
        width: 18px;
        height: 18px;
        background: url(../images/mnks-driver.png) no-repeat;
        background-size: 100% 100%;
    }

    .active {
        background: url(../images/mnks_driver_click.png) no-repeat;
        background-size: 100% 100%;
    }
}

.wrapper-persuade {
    .active {
        background: url(../images/1.png) no-repeat;
        background-size: 100% 100%;
    }
}

.wrapper-kqfd-dialog {
    .xz-icon {}

    .active {
        background: url(../images/kqfd-active.png) no-repeat;
        background-size: 100% 100%;
    }
}

.wrapper-stud {
    display: flex;
    align-items: center;
    justify-content: center;

    .pay-item {
        border-bottom: none;
        align-items: center;
    }

    .pay-item+.pay-item {
        margin-left: 15px;
    }

    .pay-icon {
        display: none;
    }

    .pay-desc {
        order: 1;
        padding-left: 10px;
        color: #161313;
        white-space: nowrap;
        font-size: 14px;
    }

    .xz-icon {
        width: 18px;
        height: 18px;
        background: url(../images/stud1.png) no-repeat;
        background-size: 100% 100%;
    }

    .active {
        background: url(../images/stud2.png) no-repeat;
        background-size: 100% 100%;
    }

    .pay-item {
        padding: 10px 0;
    }
}

.pay-item {
    display: flex;
    height: 22px;
    padding: 15px 0;
    line-height: 22px;
    border-bottom: 1px solid #eee;
    box-sizing: content-box;
}

.pay-icon {
    width: 22px;
    height: 22px;
}

.alipay-icon {
    background: url(../images/alipay.png) no-repeat;
    background-size: 100% 100%;
}

.wxpay-icon {
    background: url(../images/wxpay.png) no-repeat;
    background-size: 100% 100%;
}

.hwpay-icon {
    background: url(../images/hwpay.png) no-repeat;
    background-size: 100% 100%;
}

.friend-pay-icon {
    background: url(../images/friend-pay.png) no-repeat;
    background-size: 100% 100%;
}

.pay-desc {
    font-size: 15px;
    color: #333333;
    flex: 1;
    white-space: nowrap;
    padding-left: 13px;
}

.xz-icon {
    width: 20px;
    height: 20px;
    background: url(../images/xz1_un_check.png) no-repeat;
    background-size: 100% 100%;
}

.active {
    background: url(../images/xz1_check.png) no-repeat;
    background-size: 100% 100%;
}
