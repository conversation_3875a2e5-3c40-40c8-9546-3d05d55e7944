/*
 * ------------------------------------------------------------------
 * 选择支付方式组件
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import { MCProtocol } from '@simplex/simple-base';
import View from './view/main.html';
import { setPayType, getPayType, PayType, Platform, URLParams } from ':common/env';
import { promisify } from ':common/utils';
import createEventBus from ':common/eventbus';
import { GroupKey } from ':store/goods';
import { globalGoodsInfo } from ':store/newGoods';

const EventChange = 'change';

interface State {
    payType: PayType;
    list: {
        payType: PayType,
        text: string
    }[]
}

interface Props {
    theme?: 'vertical' | 'horizontal' | 'persuade';
    groupKey: GroupKey | ''
}

const eventbus = createEventBus<{
    /** 协议勾选 */
    payType: PayType
}>();

// 能使用的支付方式
let canUseList: State['list'] = [];
export default class PayTypeCom extends Component<State, Props> {
    // 是否是续订,续订不能显示朋友帮我付
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            payType: getPayType(),
            list: []
        };

        this.props = {
            theme: 'vertical',
            groupKey: ''
        };
    }
    willReceiveProps(nextProps: Props) {
        const goodsPayList = globalGoodsInfo[nextProps.groupKey]?.renewTpl?.payTypes;
        const list = this.calcUseList(goodsPayList);
        this.setState({
            list
        }, () => {
            this.setDefaultPayType();
        });
        return true;
    }
    calcUseList(goodsPayList) {
        // 如果不限制支付方式，就使用所有能用的支付方式
        if (!goodsPayList) {
            return canUseList;
        }
        return canUseList.filter((item) => {
            const tempList = goodsPayList.filter(ele => ele === item.payType);
            return tempList.length;
        });
    }
    didMount() {
        // 多个组件只会执行一次$constructor，所以需要重新赋值
        eventbus.on('payType', (payType) => {
            this.setComPayType(payType);
        });

        new Promise((resolve) => {
            const list = [];
            // 方便调试，需要在浏览器上也显示数组
            if (Platform.isMuCang) {
                promisify(MCProtocol.Pay.channels)().then((data) => {

                    const isWeixinAvailable = data.data.wx;
                    const isHarmonyAvailable = !!Platform.isHarmony && data.data.huaweiIap;

                    if (isHarmonyAvailable) {
                        list.push({
                            payType: PayType.Harmony,
                            text: '华为支付'
                        });
                    }

                    if (isWeixinAvailable) {
                        list.push({
                            payType: PayType.Weixin,
                            text: '微信支付'
                        });
                    }

                    list.push({
                        payType: PayType.Alipay,
                        text: '支付宝支付'
                    });

                    // 鸿蒙的不能有帮朋友付，部分页面也不能代付,noReplacePay通过props过于困难，直接在不同页面写死
                    if ((Platform.isAndroid && !Platform.isHarmony) && !URLParams.noReplacePay) {
                        list.push({
                            payType: PayType.OtherPay,
                            text: '找人代付'
                        });
                    }

                    resolve(list);
                });
            } else {
                list.push({
                    payType: PayType.Weixin,
                    text: '微信支付'
                });
                list.push({
                    payType: PayType.Alipay,
                    text: '支付宝支付'
                });
 
                if ((Platform.isAndroid && !Platform.isHarmony) && !URLParams.noReplacePay) {
                    list.push({
                        payType: PayType.OtherPay,
                        text: '找人代付'
                    });
                }
               
                resolve(list);
            }
        }).then((list: State['list']) => {
            const { groupKey } = this.props;
            const goodsPayList = globalGoodsInfo[groupKey]?.renewTpl?.payTypes;

            canUseList = list;

            this.setState({
                list: this.calcUseList(goodsPayList)
            }, () => {
                this.setDefaultPayType();
            });

        });

        this.event.on('pay-type', 'click', (e) => {
            const payType = +e.refTarget.getAttribute('data-type');

            // 代他人付不设置全局的支付类型
            eventbus.emit('payType', payType);
        });
    }
    setDefaultPayType() {
        const { list } = this.state;
        const index = list.findIndex(item => item.payType === getPayType());
        let payType;
        if (index !== -1) {
            payType = list[index].payType;
        } else {
            payType = list[0].payType;
        }

        eventbus.emit('payType', payType);
    }
    setComPayType(payType) {
        setPayType(payType);

        this.setState({
            payType: getPayType()
        });
    }

    public getPayType() {
        return getPayType();
    }
}
