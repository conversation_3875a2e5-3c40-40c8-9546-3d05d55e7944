/*
 * ------------------------------------------------------------------
 * 挽留弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import main, { Dialog } from ':component/dialog/main';
import { GoodsInfo, GroupKey } from ':store/goods';
import { trackDialogShow, trackEvent, trackGoPay } from ':common/stat';
import { checkReaded } from ':common/features/agreement';
import PayTypeCom from ':component/payType/main';
import Share from ':component/Share/main';
import { KemuType, PayType, URLCommon } from ':common/env';
import { openWeb } from ':common/core';

const fragmentName1 = '挽留弹窗';

interface ShowInfo {
    goodsInfo: GoodsInfo,
    groupKey: GroupKey,
    payPrice: string,
    title: string,
    txt1?: string,
    txt2?: string,
    txt3?: string,
    txt4?: string,
    tag?: {
        text: string
    },
    kemu?: KemuType,
    type?: string
}

export default class PersuadeDialogCom extends Dialog<ShowInfo, PayType | false> {
    declare children: Dialog['children'] & { Share: Share; };
    get payTypeCom() {
        return this.children.dialog.children.payType as PayTypeCom;
    }
    get title() {
        return this.state.showInfo.title.replace('undefined', '');
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }

    didMount() {
        this.bindEvents();
    }

    bindEvents() {

        this.event.on('close', 'click', () => {
            this.hide();
        });

        this.event.on('refuse', 'click', () => {
            this.hide(false);
        });

        this.event.on('intro', 'click', () => {
            if (this.state.showInfo.kemu === KemuType.Ke2) {
                openWeb({
                    url: 'http://saturn.nav.mucang.cn/tag/detail?tagId=28777&selectTab=3'
                });
            } else if (this.state.showInfo.kemu === KemuType.Ke3) {
                openWeb({
                    url: 'http://saturn.nav.mucang.cn/tag/detail?tagId=28779&selectTab=3'
                });
            } else {
                openWeb({
                    url: 'http://saturn.nav.mucang.cn/tag/detail?tagId=37456'
                });
            }
        });

        this.event.on('pay-confirm', 'click', () => {
            const payType = this.payTypeCom.getPayType();
            checkReaded(() => {
                trackGoPay({
                    groupKey: this.state.showInfo.groupKey,
                    fragmentName1
                });
                this.hide(payType);
            });
        });
    }

    public show(showInfo: ShowInfo, cb?: AnyFunc) {

        // 打开挽留弹窗时关闭其他弹窗(由于ElderCouponDialog关闭之后还会弹出payDialog，所以payDialog延迟关闭)
        this.app.children?.ElderCouponDialog?.hide();

        setTimeout(() => {
            this.app.children?.payDialog?.hide();
        }, 200);

        return super.show({
            txt1: '省不少时间',
            txt2: '背500题真的过了',
            txt3: '仿真考场模拟有用',
            txt4: '一开始不信，后来考过了',
            kemu: URLCommon.kemu,
            ...showInfo
        }, () => {
            trackDialogShow({
                groupKey: this.state.showInfo.groupKey,
                fragmentName1
            });
            cb?.();
        });
    }
    // 只有在老年版的科一或科四中才有入口触发事件
    onShare() {
        const { showInfo } = this.state;
        const goodsInfo = showInfo.goodsInfo;
        let imgUrl = 'http://exam-room.mc-cdn.cn/exam-room/2022/06/21/14/12867fef7bb440da8fdcb423eb212533.png';

        if (URLCommon.kemu === KemuType.Ke1) {
            imgUrl = 'http://exam-room.mc-cdn.cn/exam-room/2022/06/21/14/12867fef7bb440da8fdcb423eb212533.png';
        }

        if (URLCommon.kemu === KemuType.Ke4) {
            imgUrl = 'http://exam-room.mc-cdn.cn/exam-room/2022/06/21/14/25066b25372f4c88bfa329a9afc140ac.png';
        }

        trackEvent({
            actionType: '点击问问朋友',
            fragmentName1: '挽留弹窗',
            fragmentName2: '分享入口'
        });

        switch (goodsInfo.groupKey) {
            case GroupKey.ChannelKemuAll:
                imgUrl = 'http://exam-room.mc-cdn.cn/exam-room/2022/06/21/14/8f1380df9db24847835c89eeb9ffe73b.png';
                break;
            case GroupKey.ElderChannelKe1Ke4Group:
                imgUrl = 'http://exam-room.mc-cdn.cn/exam-room/2022/06/21/14/282a355206cd463f87efa73b1ca71047.png';
                break;
            default:
                break;
        }

        this.children.Share.show({
            imgUrl
        });

        this.hide();
    }
}
