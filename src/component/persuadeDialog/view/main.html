<import name="style" content="./main" module="S" />
<import name="dialog" content=":component/dialog/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="payType" content=":component/payType/main" />
<import name="Count" content=":component/count/main" />
<import name="Share" content=":component/share/main" />

<div class=":persuadeDialogBox">
    <com:dialog>
        <div class=":persuadeWrap">
            <div class=":persuadeDialog">
                <sp:if value='{{state.showInfo.type=="kemu23"}}'>
                    <div class=":kemu23-payHd">
                        <div class=":div1">嫌贵？立送科三15项3D模拟练车</div>
                        <div class=":div2">
                            <div class=":desc">
                                <p class=":p1">3D模拟练车</p>
                                <p class=":p2"><span>科三必练</span></p>
                                <p class=":p3">手机模拟真实考试项目</p>
                            </div>
                            <div class=":mnlc"></div>
                        </div>
                        <div class=":hint">

                            <p><i></i>开通就送{{state.showInfo.payPrice || '--'}}元3D练车</p>
                        </div>
                    </div>
                <sp:else />
                    <div class=":payHd">
                        <i class=":closeIcon" ref="close"></i>
                        <div class=":payTitle">{{self.title}}</div>
                        <div class=":paySubt {{Platform.isXueTang?'hide':''}}" ref="intro">更多社区学员评论 ></div>
                    </div>
                    <sp:if value="URLCommon.isElder && (URLCommon.kemu === KemuType.Ke1 || URLCommon.kemu === KemuType.Ke4)">
                        <div class=":elder-img"></div>
                    <sp:else/>
                        <sp:slot name="text-body">
                            <div class=":payTag">
                                <p>{{state.showInfo.txt1}}</p>
                                <p>{{state.showInfo.txt2}}</p>
                            </div>
                            <div class=":payTag">
                                <p>{{state.showInfo.txt3}}</p>
                                <p>{{state.showInfo.txt4}}</p>
                            </div>
                        </sp:slot>
                    </sp:if>
                </sp:if>

                <div class=":payBody">
                    <com:payType theme="persuade" groupKey="{{state.showInfo.groupKey}}" />
                </div>

                <div class=":payFt">
                    <div class=":ftBtns">
                        <sp:if value="URLCommon.isElder && (URLCommon.kemu === KemuType.Ke1 || URLCommon.kemu === KemuType.Ke4)">
                            <p class=":ftBtn1" sp-on:click="onShare">问问朋友</p>
                            <sp:else/>
                            <p class=":ftBtn1" ref="refuse">残忍放弃</p>
                        </sp:if>
                        <p class=":ftBtn2" ref="pay-confirm">
                            <sp:if value="state.showInfo.payPrice <= 0">
                                立即开通
                                <sp:else />
                                <span class=":finalPayPrice">{{state.showInfo.payPrice || '--'}}</span>元立即开通
                            </sp:if>

                            <sp:if value="state.showInfo.goodsInfo.inActivity">
                                <div class=":timeTip">
                                    <span class=":sp">立减{{state.showInfo.goodsInfo.inActivity.discountedPrice}}元</span>
                                    <com:Count name="Count"
                                        startTime="{{state.showInfo.goodsInfo.inActivity.discountStartTime}}"
                                        endTime="{{state.showInfo.goodsInfo.inActivity.discountEndTime}}" />
                                </div>
                                <sp:elseif value="state.showInfo.tag.text" />
                                <span class=":tip">{{state.showInfo.tag.text}}<i></i></span>
                            </sp:if>
                        </p>
                    </div>
                    <com:readProtocol theme="persuade" protocolText2="{{props.protocolText2}}" protocolUrl="{{props.protocolUrl}}" />
                    <sp:if value='{{state.showInfo.type=="kemu23"}}'>
                        <div class=":close" ref="close"></div>
                    </sp:if>
                </div>
            </div>
        </div>
    </com:dialog>
</div>

<com:Share/>