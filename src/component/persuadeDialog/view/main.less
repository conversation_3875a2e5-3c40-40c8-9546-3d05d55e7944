.persuade-dialog-box {
    .persuade-wrap {
        width: 320px;
        // min-height: 300px;
        background: #ffffff;
        border-radius: 20px;
        // position: relative;

        .persuade-dialog {
            width: 100%;
            box-sizing: border-box;
            padding: 25px 15px 20px 15px;
            background: #fff;
            border-radius: 10px;

            .pay-hd {
                text-align: center;
                position: relative;
                padding-bottom: 15px;

                .close-icon {
                    width: 16px;
                    height: 16px;
                    background: url(../images/close.png) no-repeat;
                    background-size: 100% 100%;
                    position: absolute;
                    right: 0;
                    top: -8px;
                }

                .pay-title {
                    font-size: 18px;
                    color: #333333;
                    letter-spacing: 0;
                    font-weight: bold;
                }

                .pay-subt {
                    color: #666666;
                    font-size: 13px;
                    padding-top: 7px;

                    span {
                        color: #fe5959;
                    }
                }
            }

            .elder-img {
                width: 100%;
                height: 148px;
                border-radius: 8px;
                overflow: hidden;
                background: url(http://exam-room.mc-cdn.cn/exam-room/2022/06/15/16/cc5eafd30b44428d90318c72e29b7e00.png) no-repeat center center/cover;
            }

            .kemu23-payHd {
                position: relative;
                margin-top: -25px;
                margin-left: -15px;
                margin-right: -15px;

                .div1 {
                    height: 38px;
                    width: 265px;
                    background: url(../images/hd.png) no-repeat;
                    background-size: 265px 38px;
                    font-weight: bold;
                    font-size: 15px;
                    color: #ffffff;
                    text-align: center;
                    line-height: 36px;
                    position: absolute;
                    top: -4px;
                    left: 50%;
                    margin-left: -132px;
                }

                .div2 {
                    height: 156px;
                    background: url(../images/bg.png) no-repeat;
                    background-size: 100% 100%;
                    padding: 47px 20px 0 20px;
                    display: flex;
                    justify-content: space-between;
                    box-sizing: border-box;

                    .desc {}

                    .p1 {
                        font-size: 18px;
                        font-weight: bold;
                        color: #333333;
                        line-height: 25px;
                    }

                    .p2 {
                        display: flex;
                        padding: 5px 0;

                        span {
                            color: #782900;
                            font-size: 12px;
                            background: #FFDA9D;
                            line-height: 17px;
                            padding: 1px 3px 0 5px;
                        }

                    }

                    .p3 {
                        color: #6E6E6E;
                        font-size: 12px;
                        line-height: 17px;
                    }

                    .mnlc {
                        width: 120px;
                        height: 84px;
                        background: url(../images/mnlc.png) no-repeat;
                        background-size: 100% 100%;
                    }
                }

                .hint {
                    position: absolute;
                    right: 20px;
                    bottom: -10px;

                    p {
                        font-size: 12px;
                        color: #712700;
                        padding: 5px 10px;
                        border-radius: 30px;
                        background: linear-gradient(90deg, #FFE0A7 0%, #FFC376 100%);
                        position: relative;

                        i {
                            position: absolute;
                            width: 10px;
                            height: 4px;
                            right: 24px;
                            top: -4px;
                            background: url(../images/arro.png) no-repeat;
                            background-size: 100% 100%;
                        }
                    }
                }
            }

            .pay-tag {
                display: flex;
                justify-content: space-between;

                p {
                    white-space: pre-wrap;
                    color: #333333;
                    font-size: 13px;
                    line-height: 18px;
                    padding: 6px 12px;
                    background: #fdedee;
                    border-radius: 4px;
                    margin-bottom: 10px;
                    text-align: center;
                    align-items: center;
                    display: flex;
                    justify-content: center;
                    width: 140px;
                    box-sizing: border-box;
                    height: 50px;
                }
            }

            .pay-body {
                padding-top: 8px;
            }

            .pay-ft {
                padding: 30px 0 0 0;

                .ft-btns {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    position: relative;
                    padding-bottom: 8px;
                }

                .ft-btn1 {
                    width: 140px;
                    height: 44px;
                    line-height: 44px;
                    text-align: center;
                    font-size: 16px;
                    color: #666666;
                    letter-spacing: 0;
                    border-radius: 44px;
                    border: 1px solid #999999;
                }

                .ft-btn2 {
                    position: relative;
                    width: 140px;
                    height: 44px;
                    line-height: 44px;
                    text-align: center;
                    background: linear-gradient(90deg, #00e0e5 0%, #0086fa 100%);
                    font-size: 16px;
                    color: #ffffff;
                    letter-spacing: 0;
                    border-radius: 44px;
                }
            }


            .time-tip {
                position: absolute;
                top: -10px;
                right: 0;
                transform: translateY(-60%);
                height: 22px;
                font-size: 12px;
                display: flex;
                align-items: center;
                padding: 0 6px;
                background: linear-gradient(90deg, #FF7810 0%, #FE3C29 55%, #FE6164 100%);
                border-radius: 33px 33px 33px 2px;
                color: white;
                overflow: hidden;
                transform: scale(0.7);
                transform-origin: right;

                .sp {
                    white-space: nowrap;
                    margin-right: 5px;
                }
            }

            .tip {
                position: absolute;
                z-index: 100;
                top: -18px;
                right: 0;
                font-size: 12px;
                line-height: 14px;
                color: #6F2117;
                background: linear-gradient(90deg, #FFD878 0%, #FFC400 100%);
                padding: 5px 10px;
                border-radius: 30px;
                transform: scale(0.8);
                transform-origin: right bottom;
            }
        }
    }
}

.close {
    width: 28px;
    height: 28px;
    padding: 10px;
    background: url(../images/yuan-close.png) no-repeat center;
    background-size: 28px 28px;
    position: absolute;
    bottom: -68px;
    left: 50%;
    margin-left: -24px;
    box-sizing: content-box;
}
