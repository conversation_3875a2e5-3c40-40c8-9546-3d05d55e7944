/*
 * ------------------------------------------------------------------
 * 科一科四vip购买弹窗
 * ------------------------------------------------------------------
 */

import { CarType, KemuType, PayType, URLCommon, URLParams } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayTypeComponent from ':component/payType/main';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
import { typeCode } from ':common/features/bottom';
import { iosBuySuccess, iosDialogBuySuccess } from ':common/features/ios_pay';
import { openVipWebView, webClose } from ':common/core';
import { ensureSiriusBound, getDefaultPayType, goPayStatus, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackGoPay, trackDialogShow, trackEvent } from ':common/stat';
import { Coupon, getBestCoupon, goodsInfoWithCoupon } from ':common/features/coupon';
import { showSetting } from ':common/features/embeded';
import { zigezhengGroupKeyObj, zigezhengTextMap, zigezhengTextObj } from ':common/features/zigezheng';
import Texts from ':common/features/texts';
import { BUY_STATUS } from ':common/features/trigger_page_switch';
import { getTabIndex, toggleStatus } from ':common/features/cache';
import { onPageShow } from ':common/features/page_status_switch';
import { BUYED_URL, DETAIL_URL, openAuth } from ':common/navigate';
import { reload } from ':common/features/jump';
import { couponAnimate, pauseAllVideos } from ':common/features/dom';
import { onWebBack } from ':common/features/persuade';
import { hesitateUserPersuade } from ':common/features/hesitate';

enum typeMap {
    page = 'page',
    component = 'component'
}
interface State {
    kemu: KemuType,
    tiku: CarType,
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object
}

interface Props {
    title1: string,
    fragmentName1: string
    // 学时小包用
    isStudy: boolean
    type: typeMap,
    close(closeType?: any, info?: any)
}

const sTitleMap = {
    [GroupKey.ChannelKe1]: '- 限时赠送科一VIP高效学 -',
    [GroupKey.ChannelKe4]: '- 限时赠送科四VIP高效学 -',
    [GroupKey.ChannelKe1Ke4Group]: '- 科一科四VIP高效学一起买更优惠 -',
    [GroupKey.ChannelKe4Short]: '- 讲师带学，高效学会科四 -',
    [GroupKey.HcChannelKe1]: '- 限时赠送科一VIP高效学 -',
    [GroupKey.HcChannelKe4]: '- 限时赠送科四VIP高效学 -',
    [GroupKey.HcChannelKe4Short]: '- 讲师带学，高效学会科四 -',
    [GroupKey.HcChannelKe1Ke4Group]: '- 科一科四VIP高效学一起买更优惠 -',
    [GroupKey.KcChannelKe1]: '- 限时赠送科一VIP高效学 -',
    [GroupKey.KcChannelKe4]: '- 限时赠送科四VIP高效学 -',
    [GroupKey.KcChannelKe1Short]: '- 讲师带学，高效学会科一 -',
    [GroupKey.KcChannelKe4Short]: '- 讲师带学，高效学会科四 -',
    [GroupKey.KcChannelKe1Ke4Group]: '- 科一科四VIP高效学一起买更优惠 -',
    [GroupKey.MotoChannelKe1]: '- 限时赠送科一VIP高效学 -',
    [GroupKey.MotoChannelKe4]: '- 限时赠送科四VIP高效学 -',
    [GroupKey.MotoChannelKe1Short]: '- 讲师带学，高效学会科一 -',
    [GroupKey.MotoChannelKe1Ke4Group]: '- 科一科四VIP高效学一起买更优惠 -',
    [GroupKey.ChannelKou12Short]: '- 讲师带学，助力快速拿回驾照 -',
    [GroupKey.HcChannelKou12Short]: '-讲师带学，助力快速拿回驾照 -',
    [GroupKey.KcChannelKou12Short]: '-讲师带学，助力快速拿回驾照 -'
};

export enum buyDialogCloseType {
    BOUGHT = 'bought',
    BOUGHTAUTO = 'boughtauto'
}

export default class extends Component<State, Props> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeComponent;
    };
    get sTitleMap() {
        if (URLCommon.isZigezheng) {
            return zigezhengTextObj;
        }
        return sTitleMap;
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {
        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;
        const goodsInfoPool: GoodsInfo[] = [];

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            kemu,
            tiku,
            tabIndex: 0,
            goodsInfoPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {}
        };

    }
    async didMount() {
        const { tiku, kemu } = this.state;
        const { isStudy, type } = this.props;
        const goodsInfoPool = [];
        if (type !== typeMap.component) {
            // app代理方法
            this.appEventProxy();
        }

        if (URLCommon.is3DSingle) {
            switch (tiku) {
                case CarType.CAR:
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1D3
                        } as GoodsInfo);
                    } else {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe4D3
                        } as GoodsInfo);
                    }
                    break;
                case CarType.TRUCK:
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe1D3
                        } as GoodsInfo);
                    } else {
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe4D3
                        } as GoodsInfo);
                    }

                    break;
                case CarType.BUS:
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.KcChannelKe1D3
                        } as GoodsInfo);
                    } else {
                        goodsInfoPool.push({
                            groupKey: GroupKey.KcChannelKe4D3
                        } as GoodsInfo);
                    }
                    break;
                default: break;
            }

        } else if (URLCommon.isElder) {
            if (kemu === 1) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ElderChannelKe1
                } as GoodsInfo);

                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKemuAll
                } as GoodsInfo);
            } else {
                goodsInfoPool.push({
                    groupKey: GroupKey.ElderChannelKe4
                } as GoodsInfo);
            }
        } else if (URLCommon.isZigezheng) {
            goodsInfoPool.push({
                groupKey: zigezhengGroupKeyObj[tiku]
            } as GoodsInfo);
        } else if (URLCommon.isScore12) {
            switch (tiku) {
                case CarType.CAR:
                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKou12
                    } as GoodsInfo);

                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKou12Short
                    } as GoodsInfo);
                    break;
                case CarType.TRUCK:
                    goodsInfoPool.push({
                        groupKey: GroupKey.HcChannelKou12
                    } as GoodsInfo);

                    goodsInfoPool.push({
                        groupKey: GroupKey.HcChannelKou12Short
                    } as GoodsInfo);
                    break;
                case CarType.BUS:
                    goodsInfoPool.push({
                        groupKey: GroupKey.KcChannelKou12
                    } as GoodsInfo);
                    goodsInfoPool.push({
                        groupKey: GroupKey.KcChannelKou12Short
                    } as GoodsInfo);
                    break;
                case CarType.MOTO:
                    goodsInfoPool.push({
                        groupKey: GroupKey.MotoChannelKou12
                    } as GoodsInfo);
                    goodsInfoPool.push({
                        groupKey: GroupKey.MotoChannelKou12Short
                    } as GoodsInfo);
                    break;
                default: break;
            }
        } else {
            switch (tiku) {
                case CarType.CAR:
                    goodsInfoPool.push({
                        groupKey: kemu === KemuType.Ke1 ? GroupKey.ChannelKe1 : GroupKey.ChannelKe4
                    } as GoodsInfo);
                    if (kemu === KemuType.Ke1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1Ke4Group
                        } as GoodsInfo);

                    } else {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe4Short
                        } as GoodsInfo);
                    }

                    break;
                case CarType.TRUCK:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.HcChannelKe1 : GroupKey.HcChannelKe4
                    } as GoodsInfo);

                    if (kemu === KemuType.Ke1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe1Ke4Group
                        } as GoodsInfo);
                        // goodsInfoPool.push({
                        //     groupKey: GroupKey.HcChannelKemuAll
                        // } as GoodsInfo);
                    } else {
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe4Short
                        } as GoodsInfo);
                    }

                    break;
                case CarType.BUS:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.KcChannelKe1 : GroupKey.KcChannelKe4
                    } as GoodsInfo);

                    if (kemu === KemuType.Ke1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.KcChannelKe1Ke4Group
                        } as GoodsInfo);
                        // goodsInfoPool.push({
                        //     groupKey: GroupKey.KcChannelKemuAll
                        // } as GoodsInfo);
                    } else {
                        goodsInfoPool.push({
                            groupKey: GroupKey.KcChannelKe4Short
                        } as GoodsInfo);
                    }
                    break;
                case CarType.MOTO:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.MotoChannelKe1 : GroupKey.MotoChannelKe4
                    } as GoodsInfo);

                    // if (kemu === KemuType.Ke1) {
                    goodsInfoPool.push({
                        groupKey: GroupKey.MotoChannelKe1Ke4Group
                    } as GoodsInfo);
                    // }

                    this.setState({
                        tabIndex: kemu === 1 ? 1 : 0
                    }, () => {
                        this.setPageInfo();
                    });

                    break;
                case CarType.GUACHE:
                    // 挂车只有科四
                    goodsInfoPool.push({
                        groupKey: GroupKey.GcChannelKe4
                    } as GoodsInfo);

                    goodsInfoPool.push({
                        groupKey: GroupKey.GcChannelKe4Short
                    } as GoodsInfo);

                    break;
                default: break;
            }
        }

        this.state.goodsInfoPool = goodsInfoPool;

        trackEvent({
            eventId: 'debug',
            actionType: '触发',
            actionName: '加载',
            groupKey: this.nowGoodInfo.groupKey,
            fragmentName1: this.props.fragmentName1,
            questionId: URLParams.questionId,
            payPathType: 0
        });

        // 注册底部支付方法
        this.children.buyButton.setPay({
            isInDialog: true,
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                if (type === typeMap.component) {
                    this.close(buyDialogCloseType.BOUGHT, { groupKey: this.nowGoodInfo.groupKey });
                } else if (isStudy) {
                    iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey, goUse: true });
                } else {
                    iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
                }
            }
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey
        });

        this.setPageInfo();

        await this.getGoodInfo();

        // 页面进入点
        trackGoPay({
            groupKey: this.nowGoodInfo.groupKey,
            fragmentName1: this.props.fragmentName1,
            questionId: URLParams.questionId,
            payPathType: 0
        });

    }
    appEventProxy() {
        // 设置老年版的高度
        if (URLCommon.isElder) {
            showSetting({ androidH: 496, iosH: 350 });
        } else if (URLCommon.isZigezheng && +URLParams.type !== 1) {
            showSetting({ androidH: 329, iosH: 183 });
        } else {
            showSetting({ androidH: 429, iosH: 283 });
        }

        onWebBack(async () => {
            this.close();
            return Promise.resolve();
        });
    }

    tabChangeCall = (tabIndex) => {

        if (tabIndex === this.state.tabIndex) {
            return;
        }

        // sessionStorage.setItem('tabIndex', tabIndex);

        this.setState({
            tabIndex
        }, () => {
            pauseAllVideos();
            this.setPageInfo();
        });

    }
    setPageInfo() {
        this.setBuyBottom();
    }

    setBuyBottom() {
        const fragmentName2 = '支付弹窗';
        const { fragmentName1 } = this.props;
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        let bottomType: typeCode = typeCode.type1;

        // 有活动的时候按钮不同
        if (nowGoodInfo.inActivity) {
            bottomType = typeCode.type5;
        }

        if (URLCommon.isZigezheng) {
            bottomType = typeCode.type2;
        }

        switch (bottomType) {
            case typeCode.type1:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    price: this.showPrice,
                    validDays: nowGoodInfo.validDays,
                    fragmentName1,
                    fragmentName2,
                    payPathType: 0
                });
                break;
            case typeCode.type2:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    price: this.showPrice,
                    fragmentName1,
                    fragmentName2,
                    payPathType: 0
                });
                break;
            case typeCode.type5:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${nowGoodInfo.inActivity.discountedPrice}元`,
                    price: this.showPrice,
                    originalPrice: '日常价￥' + nowGoodInfo.inActivity.preDiscountPrice,
                    fragmentName1,
                    fragmentName2,
                    payPathType: 0
                });
                break;
            default:
                break;
        }
    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                // 如果是第一个商品，就替换对应的名称
                if (index === 0) {
                    goodInfo.name = Texts.currentKemuTxt + this.props.title1;
                    if (URLCommon.isScore12) {
                        goodInfo.name = '扣满12分' + this.props.title1;

                    }
                }
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    openVipWebView({
                        url: BUYED_URL + '?iosH5Head=show'
                    });
                    (new Promise<void>(resolve => {
                        onPageShow(resolve);
                    })).then(() => {
                        this.close(buyDialogCloseType.BOUGHTAUTO);
                    });
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }

            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool
            });

            await this.getLabel();
            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getComparePrice();

                const { type } = this.props;
                if (type !== typeMap.component) {
                    couponAnimate({
                        couponTargetDomSelect: '.coupon-position-bottom',
                        compareTargetDomSelect: `.bottom-tabs .${newGoodsPool[1]?.groupKey}`,
                        couponData: this.nowCouponInfo,
                        compareData: this.state.comparePricePool[newGoodsPool[1]?.groupKey],
                        compareGoodsData: newGoodsPool[1],
                        goodsData: this.nowGoodInfo,
                        compareAnimateType: 2
                    });
                }

                this.setPageInfo();
            }, 60);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};
        // eslint-disable-next-line max-len
        const shortList = [GroupKey.ChannelKe4Short, GroupKey.HcChannelKe4Short, GroupKey.KcChannelKe1Short, GroupKey.KcChannelKe4Short, GroupKey.MotoChannelKe1Short, GroupKey.ChannelKou12Short, GroupKey.HcChannelKou12Short, GroupKey.KcChannelKou12Short];
        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                // 如果是短时提分的就展示固定文案
                if (shortList.includes(goodsInfoPool[index].groupKey)) {
                    item.label = '赠送千元直播课';
                }
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool, tiku } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice,
                        groupItems: item.groupItems
                    };
                }
            });
            console.log(comparePricePool);
            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const { isStudy, type } = this.props;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            if (type === typeMap.component) {
                this.close(buyDialogCloseType.BOUGHT, { groupKey: goodsInfoPool[tabIndex].groupKey });

            } else if (isStudy) {
                newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey, goUse: true }, 2);
            } else {
                newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey }, 2);
            }
        });

    }
    async close(closeType?: any, info?: any) {
        const { type, close } = this.props;
        this.children.buyButton.hideButton();
        if (type === typeMap.component) {
            close && close(closeType, info);
        } else {
            // eslint-disable-next-line no-lonely-if
            if (!closeType && await hesitateUserPersuade()) {
                return;
            } else {
                webClose();
            }
        }
    }
    onClose() {
        this.close();
    }
    willReceiveProps() {
        return true;
    }

    onPlayEvent = () => {
        const { fragmentName1 } = this.props;
        trackEvent({
            fragmentName1,
            actionType: '点击',
            actionName: '播放视频'
        });
    }
}
