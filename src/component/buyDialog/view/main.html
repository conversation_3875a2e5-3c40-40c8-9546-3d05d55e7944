<import name="style" content="./main" />

<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="goComment" content=":component/goComment/main" />
<import name="myVideo" content=":component/myVideo/main" />
<import name="SendVipEnter" content=":component/sendVipEnter/main" />

<div class="page-buy-dialog ipad-box">
    <div class="phone-box">
        <div class="close" sp-on:click="onClose"></div>

        <!-- 资格证有2种情况，一种是做错题弹出，一种是答题技巧，答题技巧的样式和做错题弹出不同 -->
        <sp:if value="URLCommon.isZigezheng && +URLParams.type !== 1">
            <div class="sec3">
                <div class="s-title">
                    <p>错题太多，学的慢?</p>
                    <span>只需3步，省时省力，高效学会</span>
                </div>
                <div class="steps">
                    <div class="step step1">
                        <i class="icon1"></i>
                        <span class="sp1">精简题库</span>
                    </div>
                    <div class="step step2">
                        <i class="icon2"></i>
                        <span class="sp2">真实<br>考场模拟</span>
                    </div>
                    <div class="step step3">
                        <i class="icon3"></i>
                        <span class="sp3">考前秘卷</span>
                    </div>
                </div>
            </div>
            <sp:else />
            <sp:if value="props.isStudy || Platform.isXueTang || +URLParams.type === 1">
                <div class="buy-title" key="buy-title">立即开通</div>
                <sp:else />
                <com:goComment type="1" theme="theme1" groupKey="{{self.nowGoodInfo.groupKey}}" />
            </sp:if>
           
            <sp:if value="state.goodsInfoPool.length > 1">
                <com:bottomTabs tabIndex="{{state.tabIndex}}" labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}" goodsList="{{state.goodsInfoPool}}"
                    tabChange="{{self.tabChangeCall}}" />
                <sp:else />
                <div class="alone-box" key="aloneBox">
                    <sp:if value='{{props.isKqmj}}'>
                        <div class="desc">
                            <p class="desc1">{{self.nowGoodInfo.name}}</p>
                            <p class="desc2">{{state.labelPool[self.nowGoodInfo.groupKey].label}}</p>
                        </div>
                        <sp:else />
                        <div class="desc">
                            <p class="desc1">{{props.title1}}</p>
                            <p class="desc2">{{props.subTitle1}}</p>
                        </div>
                    </sp:if>

                    <div class="price"><i>¥</i><b>{{self.showPrice || '--'}}</b></div>
                </div>
            </sp:if>
            <div class="sec2" key="sec2">
                <sp:if value="URLCommon.isElder">
                    <div
                        class="video-box hide {{self.nowGoodInfo.groupKey == GroupKey.ElderChannelKe1 || self.nowGoodInfo.groupKey == GroupKey.ElderChannelKe4?'show':''}}">
                        <com:myVideo name="myVideo1" src="{{Texts.TVHOSTMAP.maiche + (URLCommon.kemu === KemuType.Ke1 ? '/2022/07/26/b57dc8fa2a2a469495c884bd85f15628.middle.mp4'
                            :
                            '/2022/07/26/ded61eb5c65d4dffa0cc76b43a646c3d.middle.mp4')}}"
                            poster="https://web-resource.mc-cdn.cn/web/vip/22.png" onPlay="{{self.onPlayEvent}}" />
                    </div>

                    <div class="video-box2 hide {{self.nowGoodInfo.groupKey == GroupKey.ChannelKemuAll?'show':''}}">
                        <div style="z-index: 1;position:relative;width:100%;height:100%;">
                            <com:myVideo name="myVideo2"
                                src="{{Texts.TVHOSTMAP.maiche + '/2022/07/26/49da886e3d584afda4518a56dbc806ed.middle.mp4'}}"
                                poster="http://exam-room.mc-cdn.cn/exam-room/2022/07/26/14/6309302414a643be987b2d5697441c67.png"
                                onPlay="{{self.onPlayEvent}}" name="kemuAllVideo" />
                        </div>
                    </div>
                    <sp:else />

                    <div class="s-title">
                        <!-- 正常版的逻辑是如果sTitleMap中能找到就使用，不能找到就看是否有比价（只有2个全科在里面找不到，不能先判断比价，因为不是所有比价的都有直播课） -->
                        <sp:if value="self.sTitleMap[self.nowGoodInfo.groupKey]">
                            {{self.sTitleMap[self.nowGoodInfo.groupKey]}}
                            <sp:elseif value="URLCommon.isScore12" />
                            - 限时赠送扣满12分VIP课程 -
                            <sp:elseif value="URLCommon.isZigezheng" />
                            - 开通即可获得{{self.sTitleMap[URLCommon.tiku]}}VIP -
                            <sp:elseif value="state.comparePricePool[self.nowGoodInfo.groupKey]" />
                            比分开买<b>立省{{state.comparePricePool[self.nowGoodInfo.groupKey].diffPrice}}</b>元，额外独享千元直播课
                        </sp:if>
                    </div>
                    <sp:if value="state.comparePricePool[self.nowGoodInfo.groupKey]">
                        <div class="tab-content tab-content2">
                            <div class="diff-box">
                                <div class="diff">
                                    <span>比分开买立省</span>
                                    <span class="unit">￥</span>
                                    <span
                                        class="price">{{state.comparePricePool[self.nowGoodInfo.groupKey].diffPrice}}</span>
                                </div>
                            </div>
                            <div class="compare-box">
                                <sp:each for="state.comparePricePool[self.nowGoodInfo.groupKey].groupItems">
                                    <div class="item">
                                        <div class="name">{{$value.name}}</div>
                                        <sp:if value="$value.price">
                                            <div class="price-box">
                                                <span class="unit">￥</span>
                                                <span class="price">{{$value.price}}</span>
                                            </div>
                                            <sp:else />
                                            <div class="price-box">
                                                <div class="unit">
                                                    {{$value.description}}
                                                </div>
                                            </div>
                                        </sp:if>
                                    </div>
                                </sp:each>
                            </div>
                        </div>
                        <sp:else />
                        <div class="tab-content tab-content1">
                            <div class="add-buy">
                                <sp:each for="state.labelPool[self.nowGoodInfo.groupKey].highlights">
                                    <div class="step {{$value.description?'hasDescription':''}}"
                                        data-rightsIntroduceCode="{{$value.rightsIntroduceCode}}">
                                        <div class="c">{{$value.highlight}}</div>
                                        <div class="b">{{$value.description}}</div>

                                        <sp:if
                                            value="$index == 0 && URLCommon.isScore12 && (URLCommon.tiku === CarType.TRUCK || URLCommon.tiku === CarType.BUS) && $value.highlight.indexOf('精简') > -1">
                                            <div class="icon-multi"></div>
                                        </sp:if>
                                    </div>
                                </sp:each>
                            </div>
                        </div>
                    </sp:if>
                </sp:if>
            </div>
        </sp:if>
        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon coupon-position-bottom">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元':''}}
            </div>
        </com:buyButton>
    </div>
    <com:SendVipEnter name="right" entranceCode="{{URLCommon.kemu === 1?'ke1_dtjq_right':'ke4_dtjq_right'}}"
        position="right" />
</div>
