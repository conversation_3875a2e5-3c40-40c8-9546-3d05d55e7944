.page-buy-dialog {
    background: linear-gradient(180deg, #ffeddc 0%, #ffffff 100%);
    height: 100%;
    position: relative;
    overflow-y: auto;

    .phone-box {
        position: relative;
    }

    .show{
        display: block!important;
    }

    .add-90day-sign {
        position: absolute;
        top: 45px;
        height: 20px;
        background: none;
        .icon {
            display: none;
        }
       
    }

    .close {
        position: absolute;
        z-index: 10;
        width: 40px;
        height: 40px;
        top: 0px;
        right: 0px;
        background: url(../images/close.png) no-repeat center center/20px 20px;
    }

    .buy-title {
        padding: 15px 15px 10px 15px;
        height: 30px;
        line-height: 30px;
        font-size: 18px;
        color: #681309;
        box-sizing: content-box;
        font-weight: bold;
    }

    .alone-box {
        height: 75px;
        background: url(../images/20.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        padding: 4px 15px 0 15px;
        box-sizing: border-box;
        margin: 4px 15px 0 15px;

        .desc {
            .desc1 {
                font-size: 18px;
                font-weight: bold;
                color: #681309;
                line-height: 18px;
            }

            .desc2 {
                font-size: 14px;
                font-weight: 400;
                color: #9f5217;
                line-height: 20px;
                padding-top: 10px;
            }
        }

        .price {
            flex: 1;
            text-align: right;
            padding-right: 10px;

            i {
                font-size: 18px;
                font-weight: 400;
                color: #681309;
                line-height: 25px;
                padding-right: 4px;
            }

            b {
                font-size: 30px;
                font-weight: bold;
                color: #681309;
                line-height: 36px;
            }
        }
    }

    .sec2 {
        padding: 15px 15px 10px 15px;

        .video-box {
            width: 100%;
            height: 194px;
            background: url(https://web-resource.mc-cdn.cn/web/vip/22.png) no-repeat center center/cover;

            &.show {
                display: block !important;
            }
        }

        .video-box2 {
            width: 100%;
            height: 194px;
            position: relative;
            background: url(http://exam-room.mc-cdn.cn/exam-room/2022/07/26/14/6309302414a643be987b2d5697441c67.png) no-repeat center center/cover;

            &.show {
                display: block !important;
            }

            .tab-content3 {
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                padding: 21px 7px 0;

                .title {
                    width: 189px;
                    height: 19px;
                    background: url(../images/eld-title.png) no-repeat center;
                    background-size: cover;
                    margin: 0 auto;
                }

                .compare-box {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 19px;

                    .zhibo-item {
                        width: 64px;
                        background: url(../images/zhibo.png) no-repeat !important;
                        background-size: 100% 100% !important;
                    }

                    .item {
                        width: 62px;
                        height: 86px;
                        background: #fffbf6;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        border-radius: 5px;
                        border: 1px solid #fddec8;
                        color: #a03c1c;
                        position: relative;
                        flex: 1;

                        .name {
                            font-weight: bold;
                            font-size: 13px;
                            text-align: center;
                        }

                        .price-box {
                            margin-top: 8px;
                            font-weight: bold;

                            .unit {
                                font-size: 12px;
                            }

                            .price {
                                font-size: 16px;
                            }
                        }

                        &:not(:last-child)::after {
                            content: "";
                            width: 17px;
                            height: 17px;
                            background: url(../images/<EMAIL>) no-repeat center center/cover;
                            position: absolute;
                            z-index: 1;
                            top: 50%;
                            right: 0;
                            transform: translate(10px, -50%);
                        }
                    }
                }

                .diff-box {
                    text-align: center;
                    font-size: 13px;
                    font-weight: 500;
                    color: #963314;
                    margin-top: 15px;

                    .price {
                        color: #ff4a40;
                        margin: 0 3px;
                    }
                }
            }
        }

        .s-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            line-height: 21px;
            color: #692204;
        }

        // 老年版
        .eld-steps {
            display: -webkit-box;
            padding: 15px 3px 0 3px;
            overflow-x: scroll;
            overflow-y: hidden;
            -webkit-overflow-scrolling: touch;

            .step-title {
                background: url(../images/23.png) no-repeat;
                background-size: 100% 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                border: none;
                width: 96px;
                height: 86px;
                box-sizing: border-box;
            }

            .step {
                width: 71px;
                height: 86px;
                border-radius: 5px;
                position: relative;
                display: flex;
                flex-direction: column;
                box-sizing: border-box;
                background: url(../images/24.png) no-repeat;
                background-size: 100% 100%;
                margin-left: 6px;

                i {
                    width: 33px;
                    height: 15px;
                    box-sizing: border-box;
                    margin-top: 1px;
                    margin-left: 1px;
                }

                .icon1 {
                    background: url(../images/15.png) no-repeat;
                    background-size: 100% 100%;
                }

                .icon2 {
                    background: url(../images/16.png) no-repeat;
                    background-size: 100% 100%;
                }

                .icon3 {
                    background: url(../images/17.png) no-repeat;
                    background-size: 100% 100%;
                }

                .icon4 {
                    background: url(../images/21.png) no-repeat;
                    background-size: 100% 100%;
                }

                span {
                    font-size: 15px;
                    line-height: 22px;
                    color: #8c3418;
                    font-weight: bold;
                    padding: 0 2px 8px 2px;
                    text-align: center;
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }

        .tab-content {
            position: relative;
            margin-top: 10px;
            height: 100px;
            border-radius: 6px;
            box-sizing: border-box;
        }

        .tab-content1 {
            .add-buy {
                display: flex;
                justify-content: space-around;

                .step {
                    width: 78px;
                    height: 88px;
                    display: flex;
                    background: url(../images/14.png) no-repeat center center/cover;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: relative;

                    &.hasDescription {
                        background: url(../images/13.png) no-repeat center center/cover;
                    }

                    .icon-multi {
                        width: 51px;
                        height: 19px;
                        background: url(http://exam-room.mc-cdn.cn/exam-room/2022/07/12/11/baa34cadd2524f2088f3bad93e562116.png) no-repeat center center/cover;
                        position: absolute;
                        right: -20px;
                        top: -10px;
                    }

                    .c {
                        width: 70px;
                        font-size: 14px;
                        color: #aa4120;
                        text-align: center;
                        font-weight: bold;
                        margin-bottom: 0;
                    }



                    .b {
                        font-size: 12px;
                        position: absolute;
                        bottom: 0;
                        height: 22px;
                        width: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #a03c1c;
                    }

                    &::before {
                        content: "";
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 32px;
                        height: 14px;
                    }

                    &.hasDescription {

                        &:nth-of-type(1)::before {
                            background: url(../images/<EMAIL>) no-repeat center center/cover;
                        }

                        &:nth-of-type(2)::before {
                            background: url(../images/<EMAIL>) no-repeat center center/cover;
                        }

                        &:nth-of-type(3)::before {
                            background: url(../images/<EMAIL>) no-repeat center center/cover;
                        }

                        .c {
                            width: 60px;
                            font-size: 13px;
                            color: #8c3418;
                            text-align: center;
                            font-weight: bold;
                            margin-bottom: 12px;
                            line-height: 18px;
                        }

                    }

                }
            }
        }

        .tab-content2 {
            padding: 7px;
            padding-left: 64px;
            position: relative;
            background: url(../images/19.png) no-repeat center center/cover;

            .diff-box {
                position: absolute;
                left: 0;
                height: 100%;
                width: 64px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 7px;
                box-sizing: border-box;

                .diff {
                    line-height: 17px;
                    color: #aa4120;
                    font-size: 12px;
                    text-align: center;

                    .unit {
                        color: #f73b31;
                    }

                    .price {
                        font-size: 16px;
                        color: #f73b31;
                    }
                }
            }

            .compare-box {
                display: flex;
                justify-content: space-between;

                .item {
                    width: 65px;
                    height: 86px;
                    background: #fffbf6;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    border-radius: 5px;
                    border: 1px solid #fddec8;
                    color: #a03c1c;
                    position: relative;
                    flex: 1;

                    .name {
                        font-weight: bold;
                        font-size: 13px;
                        text-align: center;
                    }

                    .price-box {
                        margin-top: 8px;
                        font-weight: bold;

                        .unit {
                            font-size: 12px;
                        }

                        .price {
                            font-size: 16px;
                        }
                    }

                    &:not(:last-child)::after {
                        content: "";
                        width: 17px;
                        height: 17px;
                        background: url(../images/<EMAIL>) no-repeat center center/cover;
                        position: absolute;
                        z-index: 1;
                        top: 50%;
                        right: 0;
                        transform: translate(10px, -50%);
                    }
                }
            }
        }
    }

    .sec3 {
        padding: 15px 15px 10px 15px;

        .s-title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            color: #333;
            line-height: 1.4;

            span {
                font-size: 13px;
            }
        }

        .steps {
            padding: 8px 5px 0;
            display: flex;
            justify-content: space-between;

            .step {
                width: 102px;
                height: 86px;
                border-radius: 5px;
                position: relative;
                display: flex;
                flex-direction: column;
                box-sizing: border-box;
                background: url(../images/25.png) no-repeat;
                background-size: 100% 100%;

                i {
                    width: 33px;
                    height: 15px;
                    box-sizing: border-box;
                    margin-top: 1px;
                    margin-left: 1px;
                }

                .icon1 {
                    background: url(../images/15.png) no-repeat;
                    background-size: 100% 100%;
                }

                .icon2 {
                    background: url(../images/16.png) no-repeat;
                    background-size: 100% 100%;
                }

                .icon3 {
                    background: url(../images/17.png) no-repeat;
                    background-size: 100% 100%;
                }

                .icon4 {
                    background: url(../images/21.png) no-repeat;
                    background-size: 100% 100%;
                }

                span {
                    font-size: 15px;
                    line-height: 22px;
                    color: #8c3418;
                    font-weight: bold;
                    padding: 0 2px 8px 2px;
                    text-align: center;
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }
    }

    .paytype-box {
        padding: 0 15px;
    }

    .bottom-tabs {
        background-color: transparent;

        .hd-tabs {
            .hd-tab {
                height: 60px;
                border-radius: 8px;
                border: 1px solid #f4c2a2;
                background-color: #fff;

                &.active {
                    background: linear-gradient(113deg,
                            #353b4e 0%,
                            #1d222b 100%);
                }
            }
        }
    }

    .buy-footer {
        background-color: transparent;

        .coupon-pick {
            background-color: transparent;
        }
    }
}
