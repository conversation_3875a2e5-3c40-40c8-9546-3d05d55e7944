/*
 * ------------------------------------------------------------------
 * 选择支付方式弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { GoodsInfo, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';

export default class extends Component<any, { goodsInfo: GoodsInfo }> {
    get showTxt() {
        const { goodsInfo } = this.props;

        return goodsInfo.giftPromotion?.promotionStatus;
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
        };
    }
    willReceiveProps() {
        return true;
    }
}
