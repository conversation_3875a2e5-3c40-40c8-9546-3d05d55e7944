<import name="style" content="./main" module="S" />
<import name="Count" content=":component/count/main" />

<div class=":send-ke2vip-txt {{self.showTxt?S.show:'hide'}}">
    <span class=":time-limit"></span>
    距离加赠科二VIP活动结束仅剩 &nbsp;
    <sp:if value="{{self.showTxt}}">
        <span class=":time-color">
            <com:Count startTime="{{props.goodsInfo.giftPromotion.promotionStartTime}}"
                endTime="{{props.goodsInfo.giftPromotion.promotionEndTime}}" />
        </span>
    </sp:if>
</div>
