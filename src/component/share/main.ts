/*
 * ------------------------------------------------------------------
 * 分享(因为只在安卓上面展示，所以不用考虑ios底部按钮层级问题)
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
import { Dialog } from ':component/dialog/main';

interface ShowInfo {
    imgUrl: string;
}
interface State {
    menus: Array<any>
    showInfo: ShowInfo
}

export default class ShareDialog extends Dialog<ShowInfo> {
    declare state: State
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            menus: [{
                icon: 'weixin_friend',
                title: '微信好友'
            }, {
                icon: 'weixin_moment',
                title: '朋友圈'
            }, {
                icon: 'qq',
                title: 'QQ好友'
            }, {
                icon: 'q_zone',
                title: 'QQ空间'
            }, {
                icon: 'sina',
                title: '新浪微博'
            }],
            showInfo: {
                imgUrl: ''
            }
        };
    }
    public show(showInfo: ShowInfo) {
        this.shareWeixinImageSettings(showInfo.imgUrl);
        return super.show({
            imgUrl: showInfo.imgUrl
        });
    }
    didMount() {
        this.event.on('closeDialog', 'click', () => {
            this.hide();
        });
    }
    onItem(e) {
        const channel = e.refTarget.dataset.channel;
        const { showInfo } = this.state;

        this.shareWeixinImage(showInfo.imgUrl, channel);
        this.hide();
    }
    shareWeixinImage(imageUrl: string, channel: string) {
        return new Promise<void>(resolve => {
            MCProtocol.Core.Share.open({
                channel,
                type: 'image',
                shareData: { imageUrl },
                callback() {
                    resolve();
                }
            });
        });
    }
    shareWeixinImageSettings(imageUrl: string) {
        MCProtocol.Core.Share.setting({
            needServer: true,
            shareEvent: 'jiakaobaodian',
            type: 'image',
            shareData: {
                imageUrl
            }
        });
    }
}
