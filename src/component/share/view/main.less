.share-dialog {
    .dialog-wrap {
        max-height: 100vh;
        padding-top: 30px;
        padding-bottom: 250px;
        overflow-y: auto;

        .img-box {
            margin-top: 40px;
            width: 295px;
            margin: 0 auto;

            img {
                width: 100%;
            }
        }

        .menu {
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #fff;
            height: 222px;
            border-radius: 14px 14px 0px 0px;

            .title {
                height: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 14px;
                border-radius: 14px 14px 0px 0px;
            }

            .list {
                display: flex;
                justify-content: space-around;
                align-items: center;

                .item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .weixin_friend {
                        width: 48px;
                        height: 48px;
                        background: url(../images/weixin_friend.png) no-repeat;
                        background-size: 100% 100%;
                    }

                    .qq {
                        width: 48px;
                        height: 48px;
                        background: url(../images/qq.png) no-repeat;
                        background-size: 100% 100%;
                    }

                    .weixin_moment {
                        width: 48px;
                        height: 48px;
                        background: url(../images/weixin_moment.png) no-repeat;
                        background-size: 100% 100%;
                    }

                    .q_zone {
                        width: 48px;
                        height: 48px;
                        background: url(../images/q_zone.png) no-repeat;
                        background-size: 100% 100%;
                    }

                    .sina {
                        width: 48px;
                        height: 48px;
                        background: url(../images/sina.png) no-repeat;
                        background-size: 100% 100%;
                    }
                    .dec {
                        margin-top: 8px;
                        font-size: 13px;
                        line-height: 18px;
                    }
                }
            }

            .cancel-btn {
                width: 345px;
                height: 42px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 20px auto 0;
                background: #F2F2F2;
                border-radius: 23px;
                font-size: 17px;
            }
        }
    }
}
