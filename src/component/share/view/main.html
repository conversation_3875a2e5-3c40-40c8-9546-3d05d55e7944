<import name="style" content="./main" module="S" />
<import name="dialog" content=":component/dialog/main" />

<div class=":share-dialog">
    <com:dialog>
        <div class=":dialogWrap">
            <div class=":img-box">
                <img src="{{state.showInfo.imgUrl}}" alt="">
            </div>
            <div class=":menu">
                <div class=":title">- 分享到 -</div>
                <div class=":list">
                    <sp:each for="state.menus" value="item">
                        <div class=":item" sp-on:click="onItem" data-channel="{{item.icon}}">
                            <div class="{{S[item.icon]}}"></div>
                            <div class=":dec">
                                {{item.title}}
                            </div>
                        </div>
                    </sp:each>
                </div>
                <div ref="closeDialog" class=":cancel-btn">取消</div>
            </div>
        </div>
    </com:dialog>
</div>
