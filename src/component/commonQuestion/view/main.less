.common-question {

    // 短时提分的样式
    .k4-6 {
        background: #FFF5DF;
        border-radius: 10px;
        margin: 15px 15px 0;

        .img1 {
            width: 269px;
            height: 44px;
            background: url(../images/9.png) no-repeat right center;
            background-size: 100% 100%;
            margin: 0 auto;
        }

    }

    .sec-w {
        padding: 30px 15px;

        .sec-dp {
            background: #fff;
            border-radius: 10px;
            padding: 0 23px 10px 21px;
        }

        .dp-hd {
            display: block;
            width: 148px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            margin: 0 auto;
            color: #ffffff;
            font-size: 16px;
            background: linear-gradient(0deg, #d87447 0%, #f2b082 100%);
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
        }

        .dp-li {
            display: flex;
            display: -webkit-flex;
            padding: 24px 0;
            border-bottom: 1px dashed #d4d4d4;
            box-sizing: border-box;

            &:nth-last-child(1) {
                border: none;
            }

            .avatar {
                width: 26px;
                height: 26px;
                border-radius: 100%;
            }

            .content {
                -webkit-flex: 1;
                flex: 1;
                padding-left: 11px;
            }

            .p1 {
                font-size: 14px;
                color: #6b6870;
                padding: 2px 0 8px 0;
            }

            .p2 {
                font-size: 14px;
                color: #494455;
                line-height: 20px;
            }
        }

        .sec-qa {
            background: #fff;
            border-radius: 10px;
            padding: 0 21px 20px 18px;
        }

        .qa-hd {
            display: block;
            width: 128px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            margin: 0 auto;
            color: #ffffff;
            font-size: 16px;
            background: linear-gradient(0deg, #d87447 0%, #f2b082 100%);
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
        }

        // 科目2专属头
        .qa-hd-ke2 {
            display: block;
            width: 202px;
            height: 34px;
            background: url(../images/<EMAIL>) no-repeat center center/cover;
            margin: 0 auto 10px;
        }

        .qa-li {
            display: flex;
            display: -webkit-flex;
            padding: 12px 0;
            box-sizing: border-box;

            .icon {
                width: 18px;
                height: 18px;
                border-radius: 100%;
                background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/q.png) center center/cover;
            }

            .content {
                -webkit-flex: 1;
                flex: 1;
                padding-left: 5px;
            }

            .p1 {
                font-size: 14px;
                color: #494455;
                padding: 0 0 8px 0;
                font-weight: bold;
            }

            .p2 {
                font-size: 14px;
                color: #75717d;
                line-height: 20px;
            }
        }
    }

    &.elder {
        .sec-w {
            .qa-hd {
                font-size: 20px;
            }

            .qa-li {
                .p1 {
                    font-size: 18px;
                    line-height: 1.3;
                    letter-spacing: 2px;
                }

                .p2 {
                    font-size: 18px;
                    line-height: 1.3;
                    letter-spacing: 2px;
                }
            }
        }
    }

    .sec8 {
        padding-top: 15px;

        .sec8-w {
            background-color: #ffffff;
            border-radius: 10px;

            padding: 26px 20px 20px 20px;
        }

        .qa {
            color: #494455;
            font-size: 15px;
            line-height: 21px;
            padding-bottom: 12px;

            .p-q {
                padding-left: 28px;
                background: url(../images/q.png) no-repeat left;
                background-size: 23px 21px;
                font-weight: 600;
            }

            .p-a {
                padding-left: 26px;
                padding-top: 8px;
                font-size: 14px;

                .sp {
                    display: block;
                }
            }
        }
    }

    .buchang-info {
        margin: 27px 15px 0px 15px;
        background: #ffffff;
        padding: 15px 10px 0px 10px;

        .buchang-info-title {
            width: 100%;
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 22px;
            text-align: center;
            margin-bottom: 16px;
        }

        .buchang-info-content {
            border-bottom: 1px solid #F4F7F7;
            padding-bottom: 15px;
            margin-bottom: 15px;

            &:last-child {
                border-bottom: none;
                margin-bottom: 0px;
            }

            .content-title {
                display: flex;
                align-items: center;
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                margin-bottom: 9px;

                .question {
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #04A5FF;
                    border-radius: 4px;
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #FFFFFF;
                    margin-right: 9px;
                }
            }

            .content-desc {
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
                line-height: 18px;
            }
        }
    }

    .sec12 {
        margin: 12px 15px 20px 15px;
        background: #ffffff;
        border: 1px solid #111b30;
        border-radius: 10px;
        padding: 30px 20px 5px 20px;

        .sec-qa {
            padding: 0px;
            border-radius: 0;

            .qa-li {
                padding: 0px 0px 20px 0px;
                align-items: flex-start;

                .content {
                    padding-left: 0px;

                    .p1 {
                        color: #333333;
                    }

                    .p2 {
                        font-size: 13px;
                        color: #333;
                        opacity: 0.8;
                    }

                }
            }
        }

        .sec12-icon {
            width: 18px;
            height: 18px;
            background: #333333;
            border-radius: 50%;
            font-size: 12px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }


    }

    .sec13 {
        margin: 12px 15px 20px 15px;
        background: #ffffff;
        border-radius: 10px;
        padding: 30px 20px 5px 20px;

        .sec-qa {
            padding: 0px;
            border-radius: 0;

            .qa-li {
                padding: 0px 0px 20px 0px;
                align-items: flex-start;

                .content {
                    padding-left: 0px;

                    .p1 {
                        color: #333333;
                    }

                    .p2 {
                        font-size: 13px;
                        color: #333;
                        opacity: 0.8;
                    }

                }
            }
        }

        .sec13-icon {
            width: 18px;
            height: 18px;
            background: #5ABDFF;
            border-radius: 50%;
            font-size: 12px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
    }

    .sec18 {
        .sec-qa-box {
            margin: 0 auto 0;
            padding-bottom: 35px;
            width: 352px;
            background: url(http://exam-room.mc-cdn.cn/exam-room/2023/12/11/17/f2596df2230d48eb9e3d541a5eecc3c2.png) no-repeat top center/ 352px 167px;
            position: relative;
            padding: 75px 10px 30px;

            .bt-bg {
                position: absolute;
                width: 100%;
                bottom: 0;
                left: 0;
                right: 0;
                height: ~'calc(100% - 167px)';
                transform: translateY(-3px);
                background: url(http://exam-room.mc-cdn.cn/exam-room/2023/12/11/17/67c16f03688e4f4ea93963705e70e0b0.png) no-repeat bottom center/ 352px 100%;
            }

            .sec-qa {
                position: relative;
                z-index: 2;
            }
        }

    }
}
