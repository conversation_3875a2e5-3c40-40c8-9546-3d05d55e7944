/*
 * ------------------------------------------------------------------
 * vip常见问题
 * ------------------------------------------------------------------
 */

import { URLParams } from ':common/env';
import { zigezhengTextObj } from ':common/features/zigezheng';
import Texts from ':common/features/texts';
import { URLCommon } from ':common/env';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    type: '1' | '2' | '3' | '4' | '5' | '6'
    kemuTxt: ''
}

export default class CommonQuestion extends Component<unknown, Props> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

    }
    getCarText() {
        const carStyle = URLParams.carStyle;
        let carText = '';

        if (URLCommon.isZigezheng) {
            carText = zigezhengTextObj[carStyle];
        } else {
            carText = Texts.currentcarStyleLicenseTxt;
        }

        return carText;
    }
    willReceiveProps() {
        return true;
    }
}
