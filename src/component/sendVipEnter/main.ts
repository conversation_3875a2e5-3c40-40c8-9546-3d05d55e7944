/*
 * ------------------------------------------------------------------
 * 兑换vip入口
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { queryEntranceInfo } from ':store/activity';
import { openWeb } from ':common/core';

interface Props {
    entranceCode: string
}
interface State {
    enterInfo: any
}

export default class extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            enterInfo: {}
        };
    }
    didMount() {
        this.getEnterInfo();
    }
    getEnterInfo() {
        // queryEntranceInfo({
        //     entranceCode: this.props.entranceCode
        // }).then(data => {
        //     this.setState({
        //         enterInfo: data.extraInfo
        //     });
        // });

        this.setState({
            enterInfo: {
                entranceCode: null,
                entranceDescription: null,
                entranceName: null,
                extraInfo: null,
                userGroupCode: null
            }
        });
    }
    onGoActive() {
        const { enterInfo } = this.state;

        openWeb({
            url: enterInfo.actionUrl
        });
    }
    willReceiveProps() {
        return true;
    }
}
