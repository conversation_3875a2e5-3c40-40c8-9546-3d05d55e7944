<import name="style" content="./main" module="S" />

<div class="{{S.modal}} {{props.show?'':S.hide}}" data-type="mask" sp-on:click="cancleFn">
    <div class="{{S.modalContent}}">
        <div sp:if="props.showClose" class="{{S.colse}}" sp-on:click="cancleFn"></div>
        <sp:if value="props.customTitle">
            <sp:slot name="title"></sp:slot>
        <sp:else/>
            <div class="{{S.title}}">{{props.titleText}}</div>
        </sp:if>
      
        <div class="{{S.contentBody}}">
            <sp:slot name="body"></sp:slot>
        </div>

        <sp:if value="props.customTitle">
            <slot name="footer"></slot>
        <sp:else/>
           <div class="{{S.footer}}">
                <div sp-on:click="cancleFn" class="{{S.footerActive}} {{S.cancle}}">{{props.cancleText}}</div>
                <div sp-on:click="okFn" class="{{S.footerActive}} {{S.ok}}">{{props.okText}}</div>
            </div>
        </sp:if>
    </div>
</div>