.modal {
  position: fixed;
  z-index: 100;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);

  &.hide {
    display: none;
  }

  .modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 30/2px;
    border-radius: 10px;

    .colse {
      position: absolute;
      top: 20/2px;
      right: 20/2px;
      width: 36/2px;
      height: 36/2px;
      background: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/<EMAIL>) no-repeat center center/cover;
    }

    &.center {
      width: 640/2px;
      border-radius: 20/2px;
      padding: 50/2px 30/2px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    &.left {
      top: 0;
      left: 0;
      min-width: 100/2px;
      height: 100%;
      border-radius: 0 20/2px 20/2px 0;
    }

    &.top {
      top: 0;
      left: 0;
      width: 100%;
      min-height: 100/2px;
      border-radius: 0 0 20/2px 20/2px;
    }

    &.right {
      top: 0;
      right: 0;
      min-width: 100/2px;
      height: 100%;
      border-radius: 20/2px 0 0 20/2px;
    }

    &.bottom {
      bottom: 0;
      left: 0;
      width: 100%;
      min-height: 100/2px;
      border-radius: 20/2px 20/2px 0 0;
    }

    .title {
      text-align: center;
      color: #333;
      font-size: 36/2px;
      font-weight: bold;
    }

    .footer {
      display: flex;
      justify-content: space-between;

      .footer-active {
        width: 270/2px;
        height: 88/2px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32/2px;
        border-radius: 60/2px;
        margin-top: 30/2px;

        &.cancle {
          border: 2/2px solid #333;
        }

        &.ok {
          background: linear-gradient(90deg, rgba(0, 224, 229, 1) 0%, rgba(0, 134, 250, 1) 100%);
          color: white;
        }
      }
    }
  }
}