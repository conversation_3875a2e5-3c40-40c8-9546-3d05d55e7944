/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';

export default class extends Component {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
        };
        this.props = {
            // 是否自定义头
            customTitle: false,
            // 头部文案
            titleText: '请传入标题',
            // 是否自定义底部
            customFooter: false,
            // 取消文案
            cancleText: '取消',
            // 确定文案
            okText: '确定',
            show: false,
            // 弹框的位置
            position: 'center',
            // 是否展示关闭按钮
            showClose: true,
            // 点击遮罩是否可以关闭
            maskClose: false
        };

    }
    willReceiveProps() {
        return true;
    }
    cancleFn(e) {
        let { maskClose } = this.props;
        let target = e.refTarget;

        if (target.getAttribute('data-type') === 'mask' && !maskClose) {
            return;
        }

        this.props.cancleFn && this.props.cancleFn(e);

    }
    okFn(e) {
        this.props.okFn && this.props.okFn(e);
    }
}
