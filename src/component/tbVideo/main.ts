/*
 * ------------------------------------------------------------------
 * 视频播放器
 * ------------------------------------------------------------------
 */

import { getSystemInfo } from ':common/core';
import { pauseAllVideos } from ':common/features/dom';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    autoplay: boolean
    videoList: string[]
    duration: number
    onPlay?()
    onPlayEnd?()
    onVideoClick?()
}

export default class MyVideo extends Component<any, Props> {
    $video: HTMLVideoElement;
    timer: ReturnType<typeof setTimeout>
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            // 0:初始状态 1: 非wifi情况 2: 点击了播放，但是视频在loading
            type: 0,
            // 网络状态
            netWork: 'wifi',
            // 第几个视频
            videoIndex: 0,
            // 视频地址
            src: '',
            // 视频总时长
            duration: 1,
            // 视频播放时间
            playTime: 0,
            // 视频是否在播放
            isPlaying: false,
            // 视频是否有声音
            isMuted: true,
            // 是否展示控制器
            showControls: false,
            // 恢复video大小
            showVideo: false
        };

    }
    async didMount() {
        // 产品要求无网也要直接播放
        // const systemInfo = await getSystemInfo();

        // this.setState({
        //     netWork: systemInfo._network || 'wifi'
        // });

        const $ref = this.getDOMNode();
        const $video = $ref.video as HTMLVideoElement;
        const $progressIn = $ref.progressIn as HTMLVideoElement;
        this.$video = $video;

        $video.addEventListener('click', () => {
            this.setState({
                showControls: true
            });
            this.delayControls();
        });

        $video.addEventListener('pause', () => {
            // 更换视频会有暂停，所以延迟一点去恢复状态
            setTimeout(() => {
                if ($video.paused) {
                    this.setState({
                        isPlaying: false
                    });
                    this.delayControls();
                }
            }, 500);
        });
        $video.addEventListener('play', () => {
            this.setState({
                type: 0,
                isPlaying: true,
                showVideo: true
            });
            this.delayControls();
        });
        $video.addEventListener('durationchange', () => {
            this.setState({
                duration: this.props.duration || $video.duration
            });
        });

        $video.addEventListener('timeupdate', () => {
            $progressIn.style.width = `${$video.currentTime / this.state.duration * 100}%`;
        });
        $video.addEventListener('ended', () => {
            const { videoList } = this.props;
            const { videoIndex } = this.state;
            const src = videoList[videoIndex + 1];

            if (src) {
                this.setState({
                    src,
                    videoIndex: videoIndex + 1
                });
                this.play();
            } else {
                this.setState({
                    isPlaying: false,
                    showVideo: false
                });
                this.props.onPlayEnd && this.props.onPlayEnd();
            }
        });

        if (this.props.autoplay) {
            this.play();
        }

        this.setState({
            showVideo: false
        });
    }
    delayControls() {

        this.timer && clearTimeout(this.timer);

        this.timer = setTimeout(() => {
            this.setState({
                showControls: false
            });
        }, 2000);
    }
    play() {
        const { videoList } = this.props;
        const { src, videoIndex, netWork } = this.state;

        if (!src) {
            this.setState({
                src: videoList[videoIndex]
            });
        }

        if (window.allowNoWifiPlay || netWork === 'wifi') {
            this.setState({
                type: 2
            });
            setTimeout(() => {
                pauseAllVideos();
                this.$video.play();
                this.props.onPlay && this.props.onPlay();
            }, 200);
        } else {
            this.setState({
                type: 1
            });
        }
    }
    pause() {
        this.setState({
            type: 0
        });
        this.$video.pause();
    }
    goOnPlay() {
        window.allowNoWifiPlay = true;
        this.play();
    }
    closeMuted() {
        this.$video.muted = true;
        this.setState({
            isMuted: false
        });
        this.delayControls();
    }
    openMuted() {
        this.$video.muted = false;
        this.setState({
            isMuted: true
        });
        this.delayControls();
    }
    onVideoClick() {
        this.props.onVideoClick && this.props.onVideoClick();
    }
    willReceiveProps() {
        return true;
    }
}
