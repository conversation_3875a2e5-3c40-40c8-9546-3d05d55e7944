<import name="style" content="./main" />
<div class="my-video">
    <sp:if value="{{!props.controls}}">
        <div sp-on:click="play" class="play-icon {{state.isPlaying?'hide':''}}"></div>
        <div sp-on:click="pause" class="pause-icon {{state.isPlaying && state.showControls?'':'hide'}}"></div>
        <div sp-on:click="closeMuted"
            class="muted-icon {{state.isPlaying && state.showControls && state.isMuted?'':'hide'}}"></div>
        <div sp-on:click="openMuted"
            class="nomuted-icon {{state.isPlaying && state.showControls && !state.isMuted?'':'hide'}}"></div>

        <div class="mask {{state.type?'':'hide'}}">
            <div class="pause-reason {{state.type==2?'':'hide'}}">
                <div class="dec">加载中...</div>
            </div>
            <div class="pause-reason no-wifi {{state.type==1?'':'hide'}}">
                <div class="dec">当前为非WIFI环境，是否使用流量播放视频</div>
                <div class="active-box">
                    <div sp-on:click="pause" class="active-btn reload-play">
                        暂停播放
                    </div>
                    <div sp-on:click="goOnPlay" class="active-btn look-all">
                        继续播放
                    </div>
                </div>
            </div>
        </div>
    </sp:if>

    <video ref="video" class="{{(props.showVideo || state.showVideo)?'show-video':''}}" controls="{{props.controls}}"
        playsinline="true" src="{{state.src}}" poster="{{props.poster}}" sp-on:click="onVideoClick"></video>
    <div class="contorl-box {{state.isPlaying?'':'hide'}}">
        <div class="progress">
            <div ref="progressIn" class="progress-in" skip="true"></div>
        </div>
    </div>
</div>
