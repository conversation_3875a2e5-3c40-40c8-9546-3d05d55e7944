import { getBarrageList } from ':store/chores';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
    barrageList: any[];
}

export default class Count extends Component<State> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            barrageList: []
        };
    }

    async didMount() {
        this.getBarrage();
    }

    getBarrage() {

        getBarrageList().then(data => {
            const barrageList = data.barrageList || [];
           
            for (let i = 0; i < barrageList.length; i++) {
                barrageList[i].niceTime = this.niceTime(barrageList[i].createTime);
            }
            this.setState({
                barrageList
            }, () => {
                this.scrollRecentVip(document.getElementsByClassName('barrage-con')[0]);
            });
        });

    }

    scrollRecentVip(area) {
        let index = 0;
        const len = this.state.barrageList.length;

        setInterval(() => {
            index++;
            if (index >= len) {
                area.style.transition = 'none';
                index = 0;
                area.style.transform = 'translateY(0%)';
            } else {
                area.style.transition = '.4s all';
                area.style.transform = 'translateY(-' + (index * 100 / len) + '%)';
            }
        }, 3000);

    }

    niceTime(timeStamp) {
        if (!timeStamp) {
            return '';
        }

        const date = new Date(timeStamp);
        const now = new Date();
        const deltaTime = (now.getTime() - date.getTime()) / 1000;
        // 不足1小时的显示：xx分钟前
        // 大于1小时小于24小时的显示：xx小时前
        // 大于24小时的显示：xx天前
        if (deltaTime < 60) {
            return deltaTime + '秒前';
        } else if (deltaTime < 3600) {
            return Math.floor(deltaTime / 60) + '分钟前';
        } else if (deltaTime < 3600 * 24) {
            return Math.floor(deltaTime / 3600) + '小时前';
        }

        return Math.floor(deltaTime / (3600 * 24)) + '天前';

    }

    willReceiveProps() {
        return true;
    }
}
