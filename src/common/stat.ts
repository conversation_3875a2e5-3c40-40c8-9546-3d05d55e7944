/* eslint-disable no-alert */
/*
 * ------------------------------------------------------------------
 * 打点相关
 * ------------------------------------------------------------------
 */

import { getAbtest, getVipRecommendGoods, hostApi } from ':store/chores';
import { GroupKey } from ':store/goods';
import { OORT, MCProtocol } from '@simplex/simple-base';
import once from 'lodash/once';
import { Features, PageName, URLParams, URLCommon, Platform, KemuType, CarType } from './env';
import { hasCheckbox } from './features/agreement';
import { onPageHide, onPageShow } from './features/page_status_switch';
import { getUUID, promisify } from './utils';

/** 打点初始化 */
export function init() {
    OORT.init({
        // 必填
        appName: 'jiakaobaodian',
        // 选填
        productCategory: 'jiakaobaodian',
        // 选填
        product: '驾考宝典',
        // 打点事件名分隔符
        joinStr: '_'
    });
}
export const getFromPathCode = function () {
    const fromPathCode = window.mucangStat?.getFromPathCode() || window.mucang?.fromPathCode?.replace(/\(?null\)?/g, '') || URLParams.fromPathCode || '';

    return fromPathCode;
};

export const getPushCode = function () {
    return window.mucang?.pushCode?.replace(/\(?null\)?/g, '') || URLParams.pushCode || '';
};

export const getFromPageCode = async function () {
    // 目前只有这一个页面在Ke2的时候在3d中使用
    if (location.href.indexOf('mnks.html') > -1 && URLCommon.kemu === KemuType.Ke2) {
        return URLParams.fromPageCode;
    }
    return URLParams.from;
};

export const getStatAbtestStr = async function () {
    // 目前只有这一个页面在Ke2的时候在3d中使用
    if (location.href.indexOf('mnks.html') > -1 && URLCommon.kemu === KemuType.Ke2) {
        return URLParams.abTest;
    }
    return (await getAbtest()).abTest;
};

export const getRecommendGoodsContent = async function () {
    // 在statRecKey存在的情况下 不需要发请求，因为打点只需要recKey
    if (URLParams.statRecKey) {
        return {
            recKey: URLParams.statRecKey
        };
    }
    if (URLParams.recKey) {
        const res = await getVipRecommendGoods({ recKey: `${URLCommon.tiku || CarType.CAR}_${URLCommon.kemu || KemuType.Ke1}_${URLParams.sceneCode}_${URLParams.patternCode}_${URLParams.recKey}` });

        return {
            ...res,
            goodsUiList: res.goodsUiList.map(item => item.channelCode)
        };
    }
    return {
        recKey: ''
    };
};

/** 获取app会话id */
const getSessionId = once(async () => {
    // simple-base把features全都小写了
    if (Features.indexOf('core.luban.mucang.cn/native/logsession') > -1 || Features.indexOf('core.luban.mucang.cn/native/logSession') > -1) {
        const res = await promisify(MCProtocol.Core.Native.logSession)();
        const data = res.data;
        const sessionId = (data && data.sessionId) || '';

        return sessionId;
    }
    return '';
});

/** 打点通用参数 */
interface StatProps {
    /** 打点group */
    eventId?: string;
    /** 片段1名称 */
    fragmentName1?: string;
    /** 片段2名称 */
    fragmentName2?: string;
    /** 动作类型 */
    actionType: string;
    /** 动作名称 */
    actionName?: string;
    /** 其他参数 */
    [k: string]: unknown;
}

/** 通用打点方法 */
export async function trackEvent(props: StatProps) {
    const { fragmentName1 = '', fragmentName2 = '', actionType = '', actionName = '', ...otherProps } = props;
    let { eventId = 'jiakaobaodian' } = props;
    // 需要删除
    if (URLCommon.is3D) {
        eventId = 'jiakao3D';
    }
    const fromPathCode = getFromPathCode();

    const data = {
        // 处理通用参数
        strs: {
            carStyle: URLCommon.tiku,
            kemu: 'kemu' + URLCommon.kemu,
            fragmentName1,
            fragmentName2,
            actionType,
            actionName,

            /**
             * 访问模式code
             * 
             * 访问模式：101 普通模式，102 长辈模式
             */
            patternCode: URLParams.patternCode,
            /**
             * 访问场景code
             * 
             * 访问场景：101 普通场景，102 扣满12分
             */
            sceneCode: URLParams.sceneCode,
            /** 老版参数 */
            score12: URLParams.score12,
            /** 推送code */
            pushCode: getPushCode(),

            /** 
             * 渠道入口id
             * 
             * 取参优先级：最初入口的传参>本页面的自带参数>默认值
             */
            fromPageCode: await getFromPageCode(),
            /** 
             * 渠道子入口id
             * 
             * 配合渠道入口id区分更细粒度，按需求可上传入口处的 课程id、视频id、题目id等
             */
            fromItemCode: URLParams.fromItemCode,
            /** 
             * 路径来源code
             * 
             * 新定义的页面来源参数，不影响原 fromPageCode.
             * 单一编号为int 格式，通过逗号拼接不同层级的编号，传到properties参数中的strs中。 
             * 例如： ""fromPathCode "": ""1,2,3""" 
             */

            fromPathCode: fromPathCode,

            /** 科三考场ID */
            placeId: URLParams.placeId,
            /** 科三路线ID */
            routeId: URLParams.routeId,
            /** 项目视频ID */
            subject: URLParams.subject,
            /** 科二考场资源ID */
            k2AssetsId: URLParams.k2AssetsId,
            /** 新版，科二科三统一考场资源ID */
            assetsId: URLParams.assetsId,
            /** 教练带学视频课程ID */
            courseId: URLParams.courseId,
            /** 学时 */
            targetContent: URLParams.targetContent,
            newOrderSystem: await hostApi(),
            vsId: await getSessionId(),
            hasOpenCheckBox: (await hasCheckbox()) + '',
            abTest: await getStatAbtestStr(),
            recommendGoodsContent: await getRecommendGoodsContent(),
            ...otherProps
        },
        numbers: {
        }
    };

    const eventName = [fragmentName1, fragmentName2, actionType + actionName].filter(Boolean).join('_');

    OORT.logEvt(otherProps.PageName || PageName, eventName, eventId, data);

    // 测试环境如果fromPathCode不对就弹出来
    if (location.href.includes('.ttt.')) {
        if (!fromPathCode) {
            alert(`${otherProps.PageName || PageName}_${eventName}:无FPcode字段`);
            return;
        }
        if (!(/^([0-9]{6},)*[0-9]{6}$/g).test(fromPathCode)) {
            alert(`${otherProps.PageName || PageName}_${eventName}:FPcode不是6位数，${fromPathCode}`);
            return;
        }
        const fromPathCodeObj = {};
        let repeat = false;
        fromPathCode.split(',').forEach(item => {
            if (fromPathCodeObj[item]) {
                fromPathCodeObj[item]++;
                repeat = true;
            } else {
                fromPathCodeObj[item] = 1;
            }
        });

        if (repeat) {
            alert(`${otherProps.PageName || PageName}_${eventName}:FPcode重复，${fromPathCode}`);
            return;
        }

    }

}

let viewId: string;
let isExit: boolean;

/** 记录页面停留时长用 */
async function trackEnter() {
    const eventName = '页面进入';
    isExit = false;
    MCProtocol.Core.System.stat({
        eventId: 'pageView',
        eventName: eventName,
        eventLabel: eventName,
        properties: {
            strs: {
                vsId: await getSessionId(),
                viewId,
                viewName: PageName
            }
        }
    });
}

/** 记录页面停留时长用 */
export async function trackExit() {
    const eventName = '页面消失';
    if (isExit) {
        return;
    }
    isExit = true;
    MCProtocol.Core.System.stat({
        eventId: 'pageView',
        eventName: eventName,
        eventLabel: eventName,
        properties: {
            strs: {
                viewName: PageName,
                viewId,
                vsId: await getSessionId()
            }
        }
    });
}

/** 
 * 页面展示打点
 * @hint 如果主动调这个方法，例如切换tab时，请先调用trackExit完成前面的记录
 */
export async function trackPageShow(props?: { fragmentName1?: string, groupKey?: GroupKey, abTest?: string, payStatus?: number | string }) {
    // 刷新viewId
    viewId = getUUID();

    // 这两个点是同时配合着打的
    trackEnter();
    trackEvent({
        actionType: '展示',
        payStatus: props?.payStatus || '2',
        vsId: await getSessionId(),
        viewId,
        ...props
    });
}

/** 通用页面进入打点 */
export async function trackPageLoad(props?: { groupKey?: GroupKey, fragmentName1?: string, abTest?: string, payStatus?: number | string }) {
    // 记录页面停留时长的页面监听show和hide的事件
    onPageShow(() => {
        trackPageShow(props);
    });

    onPageHide(() => {
        trackExit();
    });

    // 可能监听不到页面关闭，所以用js的api去监听
    window.onbeforeunload = function () {
        trackExit();
    };

    document.addEventListener('visibilitychange', function () {
        if (document.visibilityState !== 'visible') {
            trackExit();
        }
    });
    window.onpagehide = () => {
        trackExit();
    };

    trackPageShow(props);
}

/** 弹窗展示打点 */
export function trackDialogShow(props: { fragmentName1: string, groupKey?: GroupKey, payPathType?: 0 | 1 }) {
    return trackEvent({
        actionType: '出现',
        payStatus: '2',
        ...props
    });
}

/** 
 * 去支付打点
 * @param {Object} props
 * @param {0|1} props.payPathType 
 */
export function trackGoPay(props: {
    groupKey: GroupKey,
    fragmentName1: string,
    fragmentName2?: string,
    /** 
     * 支付路径, 默认为1
     * * 0 页面按钮 -> 支付弹窗 -> 拉起支付
     * * 1 页面按钮 -> 拉起支付 -> 支付弹窗
     * 
     * 一般来说，落地页为1，半屏弹窗为0
     */
    payPathType?: 0 | 1,
    /**
     * 用来做abTest的字段
     */
    abTest?: string,
    /**
    * 做题页当前的题目
    */
    questionId?: string,
    /**
     * 视频id
     */
    videoId?: string
}) {

    trackEvent({
        actionName: '去支付',
        actionType: '点击',
        payPathType: 1,
        ...props
    });
}

/** 确认支付打点 **/
export function trackConfirmPay(props: {
    groupKey: GroupKey,
    fragmentName1: string,
    fragmentName2?: string,
    payPathType?: number,
    abTest?: string,
    /**
     * 视频id
     */
    videoId?: string
}) {
    trackEvent({
        actionName: '确认支付',
        actionType: '点击',
        ...props
    });
}
