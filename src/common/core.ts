/*
 * ------------------------------------------------------------------
 * 核心功能
 * ------------------------------------------------------------------
 */

import { Platform, URLCommon, URLParams } from './env';
import { MCProtocol } from '@simplex/simple-base';
import { promisify } from './utils';
import { trackExit } from './stat';
import jump from './features/jump';

/** 获取用户的登录token */
export const getAuthToken = (): Promise<string> => {
    return new Promise((resolve) => {
        let authToken = URLParams.authToken || localStorage.getItem('authToken');

        if (authToken && authToken !== 'undefined') {
            resolve(authToken);
        } else if (Platform.isMuCang) {
            MCProtocol.Core.User.get(function (data) {
                if (data.success.toString() === 'true') {
                    authToken = data.data.authToken;
                } else {
                    authToken = '';
                }
                resolve(authToken);
            });
        } else {
            resolve('');
        }
    });
};

export function getUserInfo(): Promise<any> {
    return new Promise((resolve) => {
        if (!Platform.isMuCang) {
            resolve({});
        } else {
            MCProtocol.Core.User.get(function (data) {
                resolve(data.success.toString() === 'true' ? data.data : {});
            });
        }
    });
}

/**
 * 确保用户已登录，没有的话就先登录
 */
export async function ensureLogin() {
    let authToken = await getAuthToken();

    if (!authToken) {
        const data = await promisify(MCProtocol.Core.User.login)({
            from: 'jiakaobaodian',
            skipAuthRealName: true,
            pageType: 'quicklogin'
        });
        authToken = data.authToken;
    }

    return authToken;
}

export function goBack() {
    trackExit();
    if (Platform.isMuCang) {
        MCProtocol.Core.Web.back();
    } else {
        history.back();
    }
}

export function webClose() {
    trackExit();
    MCProtocol.Core.Web.close();
}

/** 新开一个普通的webview */
export function openWeb(config: { url: string, title?: string, params?: object }) {
    config.url = config.url.trim();

    const urlObj = jump.dealUrl(config.url);
    const baseUrl = urlObj.baseUrl;
    const hashParamsStr = urlObj.hashParamsStr;
    let urlParams = urlObj.urlParams;

    // 可能是协议，所以优先设置协议传过来的参数（因为是配置的），其次设置调用传过来的参数，最后是公共参数
    urlParams = {
        kemuStyle: URLCommon.kemu,
        carStyle: URLCommon.tiku,
        sceneCode: URLParams.sceneCode,
        patternCode: URLParams.patternCode,
        ...config.params,
        ...urlParams
    };

    const url = `${baseUrl}?${jump.delRepeatUrlParams({ ...urlParams }, false)}${hashParamsStr}`;

    if (Platform.isMuCang) {
        MCProtocol.Core.Web.open({
            titleBar: true,
            toolbar: true,
            menu: false,
            button: false,
            title: config.title || '',
            url
        });
    } else {
        window.open(url);
    }
}

function getDefaultKey() {
    // eslint-disable-next-line max-len
    const defaultKeys = ['carStyle', 'kemuStyle', 'score12', 'sceneCode', 'patternCode', 'fromPage', 'fragmentName1', 'fromPageCode', 'fromItemCode', 'questionId', 'subject', 'courseId', 'bizVersion', 'fromPathCode'];

    return defaultKeys.join(',');
}

/** 新开一个vip的webview */
export function openVipWebView(config: {
    url: string;
    /** ios用 */
    style?: number;
    protocolParams?: object
}) {
    if (!Platform.isMuCang) {
        window.open(config.url);
        return;
    }

    let paramsStr = '';
    if (config.protocolParams) {
        for (const key in config.protocolParams) {
            paramsStr += `&${key}=${config.protocolParams[key]}`;
        }
    }

    // ios new-vip
    if (Platform.isIOS) {
        openWeb({
            url: `http://jiakao.nav.mucang.cn/buyWebView?style=${config.style || ''}${paramsStr}&keys=${getDefaultKey()}&url=${encodeURIComponent(config.url)}`
        });

    } else {
        openWeb({
            url: `http://jiakao.nav.mucang.cn/vip/new-vip?afull=full${paramsStr}&page=${encodeURIComponent(config.url)}`
        });
    }
}

export function saveCache(obj: { key: string, value: string }) {
    if (Platform.isMuCang) {
        MCProtocol.Core.System.setcache({
            key: obj.key,
            value: obj.value
        });
    } else {
        window.localStorage.setItem(obj.key, obj.value);
    }
}

export function getCache(key: string): Promise<string> {
    return new Promise((resolve) => {
        if (Platform.isMuCang) {
            MCProtocol.Core.System.getcache({
                key: key,
                callback: function (data) {
                    resolve(data || null);
                }
            });
        } else {
            resolve(window.localStorage.getItem(key) || null);
        }
    });
}

/** 获取系统信息 */
export function getSystemInfo(): Promise<Record<string, string>> {
    return new Promise((resovle) => {
        if (Platform.isMuCang) {
            MCProtocol.Core.System.info((baseParams) => {
                if (baseParams.data) {
                    if (typeof baseParams.data === 'string') {
                        baseParams = JSON.parse(baseParams.data);
                    } else {
                        baseParams = baseParams.data;
                    }
                } else if (typeof baseParams === 'string') {
                    baseParams = JSON.parse(baseParams);
                }
                resovle(baseParams);
            });
        } else {
            resovle(URLParams);
        }
    });
}

/** 设置顶部的状态栏颜色 */
export const setStatusBarTheme = (theme: 'light' | 'dark') => {
    MCProtocol.Core.Web.setStatusBarTheme({
        theme
    });
};
