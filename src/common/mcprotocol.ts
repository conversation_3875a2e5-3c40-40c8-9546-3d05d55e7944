/*
 * ------------------------------------------------------------------
 * 木仓协议相关 (https://mckj.feishu.cn/docs/doccnEz2iQQ3AlVwr9cqcHQ7zrf#)
 * ------------------------------------------------------------------
 */

import { MCProtocol } from '@simplex/simple-base';
import { ABTestKey, CarType, KemuType, URLCommon, URLParams } from './env';
import { deleteEmpty } from './utils';

MCProtocol.register('Vip.boughtByCoupon', function () {
    return {
    };
});

MCProtocol.register('Vip.QADetail', function (config) {
    return {
        url: config.url,
        groupKey: config.groupKey
    };
});

MCProtocol.register('Vip.BuyGoods', function (config) {
    return {
        groupKey: config.groupKey,
        orderParams: deleteEmpty(config.orderParams)
    };
});

MCProtocol.register('Vip.orderNumberToPay', function (config) {
    return {
        orderNumber: config.orderNumber,
        groupKey: config.groupKey
    };
});

MCProtocol.register('Vip.loginsms', function () {
    return {};
});

MCProtocol.register('Vip.requestInterceptClose');

MCProtocol.register('Pay.channels', function (config) {
    return {
        callback: function (data) {
            if (typeof data === 'object') {
                config.callback((data));
            } else if (typeof data === 'string') {
                config.callback(JSON.parse(data));
            }
        }
    };
});

MCProtocol.register('Core.Native.logSession', function (config) {
    return config;
});

MCProtocol.register('Vip.show', function (config) {
    return {
        h5whRate: config.h5whRate,
        h5ContentMaxWidth: config.h5ContentMaxWidth
    };
});

MCProtocol.register('Vip.setBottom', function (config) {
    return config;
});

MCProtocol.register('Vip.updateBottomTag', function (config) {
    return config;
});

MCProtocol.register('Vip.startBottomAnimation', function (config) {
    return config;
});

MCProtocol.register('Vip.orderParams', function (config) {
    return deleteEmpty(config);
});

MCProtocol.register('Listener.buyResult', function (callback) {
    return {
        callbackName: 'buyResult',
        callback: function (data) {
            callback(data.data);
        }
    };
});

MCProtocol.register('Listener.clickBuy', function (callback) {
    return {
        callbackName: 'clickBuy',
        callback: function (data) {
            callback(data.data);
        }
    };
});

MCProtocol.register('Listener.clickMembershipProtocolCheckBox', function (callback) {
    return {
        callbackName: 'clickMembershipProtocolCheckBox',
        callback: function (data) {

            callback(data.data.isSelected);
        }
    };
});

// 考前秘卷页面是原生页面，在点击残忍拒绝的时候需要通知客户端关闭webview
MCProtocol.register('Vip.onRefuseClick');

MCProtocol.register('jiakao.close');

MCProtocol.register('Vip.setting.switchType', function (config) {
    return config;
});

MCProtocol.register('Pay.restoreApplePay', function (callback) {
    return {
        callback
    };
});

MCProtocol.register('jiakao-global.resAbTest', function (params: { key: ABTestKey, success: boolean }) {
    return {
        key: params.key,
        success: params.success
    };
});

MCProtocol.register('jiakao-global.getAbTestConfig', function (config: { keys: string[], callback: (keysMap: object) => void }) {
    return config;
});

MCProtocol.register('Vip.getProtocolStatus', function (callback: ({ check }: { check: boolean }) => void) {
    return {
        callback
    };
});

/**
 * check false:未勾选；true：已勾选
 * 
*/
MCProtocol.register('Vip.setProtocolStatus', function (config: { check: boolean }) {
    return config;
});

MCProtocol.register('Vip.getPracticeProgress', function (config) {
    return {
        car: config.car,
        kemu: config.kemu,
        kemuStyle: config.kemuStyle,
        carStyle: config.carStyle,
        sceneCode: config.sceneCode,
        patternCode: config.patternCode,
        callback: (res) => {
            let data;

            try {
                if (typeof res.data === 'string') {
                    data = JSON.parse(res.data);
                } else {
                    data = res.data;
                }
            } catch (error) {
                data = [];
            }

            config.callback && config.callback(data);
        }
    };
});
// 已购买页学习步骤的协议注册
MCProtocol.register('Vip.getPassRate', function (config) {
    return {
        car: config.car,
        kemu: config.kemu,
        kemuStyle: config.kemuStyle,
        carStyle: config.carStyle,
        sceneCode: config.sceneCode,
        patternCode: config.patternCode,
        callback: config.callback
    };
});

MCProtocol.register('Vip.getPracticeRecord', function (config) {
    return {
        // 是否是专业知识
        certificatePro: config.certificatePro || '',
        car: config.car,
        kemu: config.kemu,
        kemuStyle: config.kemuStyle,
        carStyle: config.carStyle,
        sceneCode: config.sceneCode,
        patternCode: config.patternCode,
        type: config.type,
        callback: config.callback
    };
});
MCProtocol.register('Vip.getExamRecord', function (config) {
    return {
        // 是否是专业知识
        certificatePro: config.certificatePro || '',
        car: config.car,
        kemu: config.kemu,
        kemuStyle: config.kemuStyle,
        carStyle: config.carStyle,
        sceneCode: config.sceneCode,
        patternCode: config.patternCode,
        type: config.type,
        minScore: config.minScore,
        callback: config.callback
    };
});
MCProtocol.register('Vip.getExamRule', function (config) {
    return {
        car: config.car,
        kemu: config.kemu,
        kemuStyle: config.kemuStyle,
        carStyle: config.carStyle,
        sceneCode: config.sceneCode,
        patternCode: config.patternCode,
        callback: config.callback
    };
});
MCProtocol.register('Vip.getJiakao3dPassRate', function (config) {
    return {
        car: config.car,
        kemu: config.kemu,
        kemuStyle: config.kemuStyle,
        carStyle: config.carStyle,
        sceneCode: config.sceneCode,
        patternCode: config.patternCode,
        callback: config.callback
    };
});
MCProtocol.register('jiakao-global.fixVipPermissions', function (config) {
    return config;
});
MCProtocol.register('Vip.makeApplePayment', function (config) {
    return config;
});

MCProtocol.register('Vip.cachedOrders', function (config) {
    return config;
});
MCProtocol.register('Vip.saveOrder', function (config) {
    return config;
});
MCProtocol.register('Vip.removeOrder', function (config) {
    return config;
});

MCProtocol.register('Core.System.showLoading', function (config) {
    return {
        title: config?.title || '加载中'
    };
});

MCProtocol.register('Core.System.hideLoading', function () {
    return {

    };
});
MCProtocol.register('data.exam.recordRange', function (config) {
    return config;
});
MCProtocol.register('jiakao-global.web.showExamDateAlert', function (config) {
    return config;
});
MCProtocol.register('jiakao-global.web.getExamDate', function (config) {
    return config;
});
MCProtocol.register(
    'jiakao-global.web.previewSharedContent',
    function (config) {
        return config;
    }
);
MCProtocol.register('data.practice.normalReport', function (config) {
    return config;
});
MCProtocol.register('data.practice.vipReport', function (config) {
    return config;
});
MCProtocol.register('data.exam.report', function (config) {
    return config;
});
MCProtocol.register('data.practice.knowledgePointReport', function (config) {
    return config;
});
MCProtocol.register('data.practice.errorReport', function (config) {
    return config;
});

MCProtocol.register('jiakao-global.web.showExamTipAlert', function (config) {
    return config;
});

MCProtocol.register('jiakao-global.web.previewVideoTemplate', function (config: {
    // 模板类型 1-驾考成绩分享，2-成绩单分享，3-徽章分享
    type: 1 | 2 | 3,
    // 多个分享图片 url，英文逗号分割
    images: string,
    // 是否启用本地默认模板
    default: boolean,
    // 主分享文案，例如："这是一个分享测试"
    shareContent: string,
    // 小红书分享内容，例如："#驾考宝典#驾照考试"
    xhsShareTags: string,
    // 抖音分享标签，例如："#驾考宝典#驾照考试"
    douYinShareTags: string
    // 社区话题类型， json 字符串
    shequParams: object
  
}) {
    return config;
});

// 是否支持视频模版
MCProtocol.register('jiakao-global.web.supportVideoTemplate', function (config) {
    return config;
});

MCProtocol.register(
    'jiakao-global.web.sharedContentWithChannel',
    function (config) {
        return config;
    }
);

MCProtocol.register(
    'jiakao-global.web.shareCheckInstall',
    function (config) {
        return config;
    }
);
