/*
 * ------------------------------------------------------------------
 * pag动画
 * ------------------------------------------------------------------
 */

export function base64ToBlob(data: string) {
    const [header, content] = data.split(',');
    const mime = header.match(/:(.*?);/)[1];
    const bytes = window.atob(content);

    let n = bytes.length;
    const ia = new Uint8Array(n);
    while (n--) {
        ia[n] = bytes.charCodeAt(n);
    }

    return new Blob([ia], { type: mime });
}

export async function loadAnimation(base64: string) {
    console.time('animation');
    const PAG = await window.pagInit;
    console.time('pagLoad');
    // 加载 PAG 素材为 PAGFile 对象
    const pagFile = await PAG.PAGFile.load(base64ToBlob(base64));
    console.timeEnd('pagLoad');
    console.log('pag size', pagFile.width(), pagFile.height());
    console.log('pag duration', pagFile.duration());
    return {
        file: pagFile,
        async start(target: HTMLCanvasElement, cb?: () => void) {
            console.time('pagInit');
            // 实例化 PAGView 对象
            const pagView = await PAG.PAGView.init(pagFile, target);
            console.timeEnd('pagInit');
            console.timeEnd('animation');
            // 播放 PAGView
            pagView.addListener('onAnimationEnd', function onAnimationEnd() {
                pagView.removeListener('onAnimationEnd', onAnimationEnd);
                pagView.destroy();
                cb?.();
            });
            await pagView.play();
        }
    };
}