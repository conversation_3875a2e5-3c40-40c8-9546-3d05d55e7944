/*
 * ------------------------------------------------------------------
 * 页面切换显示隐藏相关
 * ------------------------------------------------------------------
 */

import { Platform } from ':common/env';
import { MCProtocol } from '@simplex/simple-base';
import isFunction from 'lodash/isFunction';
import once from 'lodash/once';

export type Listener = () => Promise<void> | void;

const onPageShowlistener: Listener[] | undefined = [];
const onPageHidelistener: Listener[] | undefined = [];

export async function pageSwitchHandler(listenerList?: Listener[]) {
    listenerList.forEach(item => {
        if (item && isFunction(item)) {
            item();
        }
    });
}

const pageSwitchInit = once(() => {
    if (!Platform.isMuCang) {
        document.addEventListener('visibilitychange', function () {
            if (document.visibilityState === 'visible') {
                pageSwitchHandler(onPageShowlistener);
            } else {
                pageSwitchHandler(onPageHidelistener);
            }
        });
        return;
    }

    let timer = null;
    MCProtocol.Listener.show(function () {
        clearTimeout(timer);
        timer = setTimeout(() => {
            pageSwitchHandler(onPageShowlistener);
        }, 50);
    });
    
    MCProtocol.Listener.hide(function () {
        pageSwitchHandler(onPageHidelistener);
    });
});

/** 页面show的时候触发  */
export function onPageShow(listener?: Listener): void {
    pageSwitchInit();
    onPageShowlistener.push(listener);
}

export function onPageHide(listener?: Listener): void {
    pageSwitchInit();
    onPageHidelistener.push(listener);
}
