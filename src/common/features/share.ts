import { URLParams } from ':common/env';
import { promisify } from ':common/utils';
import { MCProtocol } from '@simplex/simple-base';
import { makeToast } from './dom';

export async function shareRedbook(shareData) {
    if (+URLParams.bizVersion > 18) {
        const { success: hasXiaohongshu } = await promisify(MCProtocol['jiakao-global'].web.shareCheckInstall)({
            shareChannel: 'xiaohongshu'
        });

        if (!hasXiaohongshu) {
            makeToast('没有发现小红书APP');
            return;
        }
    }
   
    const { success } = await promisify(MCProtocol['jiakao-global'].web.sharedContentWithChannel)({
        ...shareData,
        shareChannel: 'xiaohongshu',
        shareChannelName: '小红书'
    });

    // eslint-disable-next-line consistent-return
    return success;
}

export async function shareDouyin(shareData) {
    console.log('抖音分享');
    const { success: hasDouyin } = await promisify(MCProtocol['jiakao-global'].web.shareCheckInstall)({
        shareChannel: 'douyin'
    });

    if (!hasDouyin) {
        makeToast('没有发现抖音APP');
        return;
    }

    const { success } = await promisify(MCProtocol['jiakao-global'].web.sharedContentWithChannel)({
        ...shareData,
        shareChannel: 'douyin',
        shareChannelName: '抖音'
    });

    // eslint-disable-next-line consistent-return
    return success;
}