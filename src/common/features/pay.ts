/*
 * ------------------------------------------------------------------
 * 支付相关
 * ------------------------------------------------------------------
 */

import { MCProtocol } from '@simplex/simple-base';
import { getAuthToken, getCache, openVipWebView, openWeb, saveCache, webClose } from ':common/core';
import { getPayType, PayType, Platform, URLParams } from ':common/env';
import { STATUS_URL } from ':common/navigate';
import { promisify } from ':common/utils';
import { bindOrder, createExchangeMobileOrder, createMobileOrder, CreateOrderParams, GroupKey, isOrderBind, isOrderPaid, OrderInfo } from ':store/goods';
import { partial } from ':common/functional';
import noop from 'lodash/noop';
import { makeToast } from './dom';
import { toggleStatus } from './cache';
import { BUY_STATUS } from './trigger_page_switch';
import jump, { reload } from './jump';
import { login } from './login';
import { saveType } from './ios_pay';
import { newIsOrderBind, newIsOrderPaid } from ':store/newGoods';
import once from 'lodash/once';
import { checkReaded, hasReaded } from './agreement';
import SureProtocolModal from ':component/sureProtocolModal/main';

const payChannels: Record<PayType, string> = {
    [PayType.Alipay]: 'alipay_mobile',
    [PayType.Weixin]: 'weixin_mobile',
    [PayType.ApplePay]: 'apple_mobile',
    [PayType.Harmony]: 'huawei_mobile'
};

export enum PayBoundType {
    GoStatusPage = 1,
    GoLogin = 2
}

interface PayBoundConfig {
    type: PayBoundType;
    groupKey: GroupKey;
}

/** 发起支付参数 */
interface PayOrderConfig extends CreateOrderParams, PayBoundConfig { }

export function goPayStatus(groupKey: GroupKey) {

    jump.replace(STATUS_URL, {
        boughtGroupKey: groupKey
    });
}

const saveList: saveType[] = ['vip', 'route'];

/** 确保之前的订单已经绑定过，否则先去绑定 */
async function ensureOrderBound(
    isPaidApi: ({ orderNumber: number }) => Promise<boolean>,
    isBoundApi: ({ orderNumber: number }) => Promise<boolean>,
    config?: PayBoundConfig,
    /** 登录完后直接关闭当前页面 */
    closeSelfAfterLogin?: boolean
) {
    const authToken = await getAuthToken();
    const onceLogin = once(() => {
        if (closeSelfAfterLogin) {
            promisify(MCProtocol.Core.User.login)({
                from: 'jiakaobaodian',
                skipAuthRealName: true,
                pageType: 'quicklogin'
            }).then(data => {
                console.log('closeSelfAfterLogin', data);
                if (data && data.success && data.data.authToken) {
                    webClose();
                }
            });
        } else {
            login(false);
        }
    });

    let getCacheOrder: (serviceName: saveType) => Promise<any>;
    let clearCacheOrder: (serviceName: saveType, orderNumber: number) => void;

    if (Platform.isIOS) {
        const cachedOrders = promisify(MCProtocol.Vip.cachedOrders);
        getCacheOrder = async (serviceName: saveType) => {
            const data = await cachedOrders({ serviceName });

            const orderNumber = data.success && data.data?.result?.[0];
            return orderNumber;
        };
        clearCacheOrder = (serviceName: saveType, orderNumber: number) => {
            MCProtocol.Vip.removeOrder({
                serviceName,
                orderNumber
            });
        };
    } else {
        getCacheOrder = async (serviceName: saveType) => {
            const data = await getCache(serviceName);
            const orderNumber = data && JSON.parse(data).orderNumber;
            return orderNumber;
        };
        clearCacheOrder = (serviceName: saveType) => {
            saveCache({
                key: serviceName,
                value: ''
            });
        };
    }

    const bindCacheOrders = async (serviceName: saveType) => {
        const orderNumber = await getCacheOrder(serviceName);
        if (!orderNumber) {
            return;
        }
        const isPaidApi = isOrderPaid;
        const isBoundApi = isOrderBind;
        const orderPaid = await isPaidApi({ orderNumber }, serviceName === 'vip');

        if (orderPaid) {
            // 订单已支付，绑定订单
            console.log(orderNumber + '订单已支付，绑定订单');
            const orderBound = await isBoundApi({ orderNumber }, serviceName === 'vip');
            if (orderBound) {
                // 如果已经绑定，清空订单数据
                clearCacheOrder(serviceName, orderNumber);
                // 继续后续流程
                console.log(orderNumber + '已经绑定，清空订单数据');
                return;
            }

            if (authToken) {
                await bindOrder({ orderNumber: orderNumber });
                clearCacheOrder(serviceName, orderNumber);
                setTimeout(() => {
                    reload();
                }, 2000);
            } else {
                // 如果未绑定，跳转登录
                await makeToast('检测到您有未绑定订单，请登录后使用！');
                await new Promise(onceLogin);
            }

        } else {

            // 未支付的订单直接清除
            clearCacheOrder(serviceName, orderNumber);
        }
    };

    await Promise.all(saveList.map(bindCacheOrders));
}
let allowPay = true;
/** 发起支付 */
async function startPay(
    ensureOrderBoundFn: (config: PayBoundConfig) => Promise<void>,
    createOrderApi: (config: CreateOrderParams) => Promise<OrderInfo>,
    config: PayOrderConfig,
    goStatusPage = true
) {
    if (!allowPay) {
        await new Promise((reject) => {
            console.info('不能resolve走到后面的支付', '防止重复点击');
        });
    }
    allowPay = false;

    setTimeout(() => {
        allowPay = true;

    }, 1000);

    if (!Platform.isIOS) {
        await ensureOrderBoundFn(config);
    } else {
        throw new Error('iOS请使用iospay方法创建订单!');
    }

    if (Platform.isVIVO) {
        const authToken = await getAuthToken();
        if (!authToken) {
            login(false);
            // 刷新页面，不要resolve
            // await new Promise((reject) => {
            //     console.info('不能resolve走到后面的支付', '成功');
            // });
            throw new Error('登录成功就会刷新，未登录回来就会重新打开支付选择窗');
        }
    }
    delete config.type;

    // 如果检测不通过就不会resolve,就不会继续执行创建订单
    await checkReaded();

    if (getPayType() === PayType.OtherPay) {
        openVipWebView({
            url: `https://share-m.kakamobi.com/activity.kakamobi.com/jkbd-vip/pages/replaceInvite.html?channelCode=${config.groupKey}#/replaceInvite`
        });
        // 卡住代码，不再执行；避免弹出选择支付方式弹窗
        await new Promise(() => ({}));
    }

    const retData = await createOrderApi({
        ...config,
        payType: getPayType()
    });

    // 告诉app订单信息
    MCProtocol.Vip.orderNumberToPay({
        orderNumber: retData.orderNumber,
        groupKey: config.groupKey
    });

    // 缓存订单号
    saveCache({
        key: config.groupKey === GroupKey.ChannelKe3RouteMeta ? 'route' : 'vip',
        value: JSON.stringify({ orderNumber: retData.orderNumber })
    });
    // 如果优惠券金额大于商品价格，则实际不用支付，直接成功
    if (retData.paid) {
        // 告诉app去刷新服务器接口，更新用户权限
        MCProtocol.Vip.boughtByCoupon();

    } else {
        const extraData = {
            vipType: config.groupKey === GroupKey.ChannelKe3RouteMeta ? 'vip_route' : 'vip_jk'
        };

        window.location.href = 'http://pay.nav.mucang.cn/pay?payType=vip&content=' + encodeURIComponent(retData.content) + '&orderNumber=' + retData.orderNumber + '&extraData=' + JSON.stringify(extraData) + '&productId=' + retData.appleGoodsId + '&payChannel=' + payChannels[getPayType()] + '&callback=';
        await new Promise<void>((resolve, reject) => {
            window.buyCancel = () => {
                window.buyCancel = noop;
                reject(new Error('支付取消'));
            };
            window.buyFailed = () => {
                window.buyFailed = noop;
                reject(new Error('支付失败'));
            };
            window.buySuccess = () => {
                window.buySuccess = noop;
                resolve();
            };
        });
    }

    // 默认跳到支付结果页
    if (goStatusPage) {
        await toggleStatus(BUY_STATUS);
        goPayStatus(config.groupKey);
    }
}
/** 换购发起支付，换购创建订单新接口，需先创建订单在拉取支付 */
async function startExchangePay(
    ensureOrderBoundFn: (config: PayBoundConfig) => Promise<void>,
    createOrderApi: (config: CreateOrderParams & {
        groupId: string | number,
        preBarterOrderNumbers: any[],
        barterStrategyCode: string,
        dailyActivityType: string,
        barterAppId: number | string

    }) => Promise<OrderInfo>,
    vipType: string,
    config: PayOrderConfig & {
        groupId: string | number,
        preBarterOrderNumbers: any[],
        barterStrategyCode: string,
        dailyActivityType: string,
        barterAppId: number | string
    }
) {
    await ensureOrderBoundFn(config);
    delete config.type;
    const retData = await createOrderApi(config);
    // 如果优惠券金额大于商品价格，则实际不用支付，直接成功
    if (retData.paid) {
        // 告诉app去刷新服务器接口，更新用户权限
        MCProtocol.Vip.boughtByCoupon();
        reload();
        return;
    }
    if (Platform.isIOS) {
        const data = await promisify(MCProtocol.Vip.makeApplePayment)({
            appleId: config.barterAppId,
            content: retData.content,
            orderNumber: retData.orderNumber,
            serviceName: 'vip'
        });
        await new Promise<void>((resolve, reject) => {
            if (data.success && data.data.status === 'success') {
                resolve();
            } else {
                reject(new Error('支付失败'));
            }
        });

    } else {
        const extraData = {
            vipType
        };
        window.location.href = 'http://pay.nav.mucang.cn/pay?payType=vip&content=' + encodeURIComponent(retData.content) + '&orderNumber=' + retData.orderNumber + '&extraData=' + JSON.stringify(extraData) + '&payChannel=' + payChannels[getPayType()] + '&callback=';
        await new Promise<void>((resolve, reject) => {
            window.buyCancel = () => {
                window.buyCancel = noop;
                reject(new Error('支付取消'));
            };
            window.buyFailed = () => {
                window.buyFailed = noop;
                reject(new Error('支付失败'));
            };
            window.buySuccess = () => {
                window.buySuccess = noop;
                resolve();
            };
        });
    }
}
/** 换购发起支付，换购有单独的创建订单的接口 */
/** 确保Sirius订单已绑定 */
export const ensureSiriusBound = partial(ensureOrderBound, isOrderPaid, isOrderBind);

/** 发起Sirius支付 */
export const startSiriusPay = partial(startPay, ensureSiriusBound, createMobileOrder);

/** 发起Sirius支付 换购支付 */
export const startExchangeSiriusPay = partial(startExchangePay, ensureSiriusBound, createExchangeMobileOrder, 'vip_jk');

/** 默认支付方式 */
export async function getDefaultPayType() {
    if (!Platform.isMuCang) {
        return PayType.Weixin;
    }
    const data = await promisify(MCProtocol.Pay.channels)();
    return !data.data.wx ? PayType.Alipay : PayType.Weixin;
}

/**
 * 
 * 安卓用：
 * 
 *  type 1:落地页 2:半窗页
 *  
 */
// type: 1:落地页 2:半窗页  目前ios这边除了(科二3D购买弹窗、科二考场视频购买弹窗)的支付成功逻辑没走这里，其他的页面（包括落地页和弹窗页）都是走的这里

export async function newBuySuccess(config: {
    groupKey: GroupKey,
    goUse?: boolean
    goUseTxt?: string
    useUrl?: string
    noClose?: boolean
    apiHost?: 'squirrel'
}, type = 1) {
    const { groupKey } = config;
    const from = URLParams.from;

    // eslint-disable-next-line max-len
    const url = STATUS_URL + location.search + '&boughtGroupKey=' + groupKey + `${config.goUse ? '&goUse=true' : ''}${config.goUseTxt ? '&goUseTxt=' + encodeURIComponent(config.goUseTxt) : ''}${config.useUrl ? '&useUrl=' + encodeURIComponent(config.useUrl) : ''}${config.apiHost ? '&apiHost=squirrel' : ''}`;

    if (!config.noClose) {
        await toggleStatus(BUY_STATUS);
    }

    if (Platform.isMuCang) {
        // 半截窗页打开新页面
        if (type === 2) {
            // 3d单包的协议前缀不一样
            if (URLParams._appName === 'jiakao3d') {
                openWeb({
                    url: `https://jiakao3d.nav.mucang.cn/new-vip?from=${from}&url=${encodeURIComponent(url.replace('landscape', 'portrait'))}`
                });
            } else {
                openWeb({
                    url: `http://jiakao.nav.mucang.cn/vip/new-vip?from=${from}&page=${encodeURIComponent(url.replace('landscape', 'portrait'))}`
                });
            }
        } else {
            // 落地页替换当前页
            openVipWebView({
                url
            });
        }
    } else {
        jump.navigateTo(url);
    }
}
