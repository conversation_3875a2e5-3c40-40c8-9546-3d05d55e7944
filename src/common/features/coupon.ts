/*
 * ------------------------------------------------------------------
 * 优惠券相关
 * ------------------------------------------------------------------
 */

import { getAuthToken, openVipWebView } from ':common/core';
import { Platform, URLParams } from ':common/env';
import { COUPON_DETAIL_URL } from ':common/navigate';
import { getPushCode } from ':common/stat';
import { formatPrice } from ':common/utils';
import { CouponInfo, getUserCoupons } from ':store/coupon';
import { GoodsInfo, GroupKey } from ':store/goods';
import { onPageShow } from './page_status_switch';

export interface Coupon {
    code: string;
    price: string;
    name: string
    /** 展示用 */
    hint?: string;
}

export type Coupons = Partial<Record<GroupKey, Coupon>>;

export function couponInfo2Coupon(couponInfo: CouponInfo): Coupon {
    return { code: couponInfo.couponCode || '', price: formatPrice(couponInfo.priceCent), name: couponInfo.goodsCouponData.name };
}

/** 获取用户最佳优惠券 */
export async function getBestCoupon(goodsInfo: GoodsInfo): Promise<Coupon> {

    // 安卓才有优惠券
    if (Platform.isIOS) {
        return {
            code: '',
            price: '',
            name: ''
        };
    }

    const { canUseCoupons } = await getUserCoupons({ sessionIds: goodsInfo.sessionIds.join(',') });

    if (!canUseCoupons.length) {
        return {
            code: '',
            price: '',
            name: ''
        };
    }

    if (URLParams.couponCode) {
        const coupon = canUseCoupons.find(coupon => coupon.couponCode === URLParams.couponCode);
        return coupon ? couponInfo2Coupon(coupon) : {
            code: '',
            price: '',
            name: ''
        };
    }

    let maxPrice = 0;
    let maxIndex = 0;
    for (let i = 0; i < canUseCoupons.length; i++) {
        const iData = canUseCoupons[i];
        if (iData.priceCent > maxPrice) {
            maxPrice = iData.priceCent;
            maxIndex = i;
        }
    }
    const bestCoupon = canUseCoupons[maxIndex];

    return couponInfo2Coupon(bestCoupon);
}

/** 批量获取用户最佳优惠券 */
export async function batchGetBestCoupon(goodsList: GoodsInfo[]) {
    const couponList = await Promise.all(goodsList.map((goods) => getBestCoupon(goods)));

    return couponList.reduce<Coupons>((coupons, coupon, i) => {
        const goods = goodsList[i];
        coupons[goods.groupKey] = coupon;
        return coupons;
    }, {});
}

/** 减掉优惠券后的商品 */
export function goodsInfoWithCoupon(goodsInfo: GoodsInfo, coupon: Coupon): GoodsInfo {
    return {
        ...goodsInfo,
        payPrice: String(Math.max((+(goodsInfo?.payPrice || 0) * 100) - (+(coupon.price || 0) * 100), 0) / 100)
    };
}

/** 计算优惠券的展示 */
export function couponWithHint(coupon: Coupon = { code: '', price: '', name: '' }, canSelect = false) {
    if (!coupon.code) {
        return canSelect ? { ...coupon, hint: '领取优惠券' } : { ...coupon };
    }
    return {
        ...coupon,
        hint: '已优惠' + coupon.price + '元' + (canSelect ? ' >' : '')
    };
}

/** 选择优惠券 */
export async function selectUserCoupon(goodsInfo: GoodsInfo, currentCouponCode?: string): Promise<CouponInfo | null> {
    openVipWebView({
        // eslint-disable-next-line max-len
        url: `${COUPON_DETAIL_URL}?groupKey=${goodsInfo.groupKey}&sessionIds=${JSON.stringify(goodsInfo.sessionIds)}${currentCouponCode ? '&selCouponCode=' + currentCouponCode : ''}&_appName=${URLParams._appName}&sceneCode=${URLParams.sceneCode}&patternCode=${URLParams.patternCode}&pushCode=${getPushCode()}`
    });

    await new Promise<void>(resolve => {
        onPageShow(resolve);
    });

    const key = goodsInfo.groupKey + '_selectCoupon';
    const selectedCoupenStr = localStorage.getItem(key) || sessionStorage.getItem(key);
    const selectedCoupon = selectedCoupenStr && JSON.parse(selectedCoupenStr);

    if (selectedCoupon) {
        localStorage.removeItem(key);
        sessionStorage.setItem(key, selectedCoupenStr);
        return selectedCoupon;
    }
    return null;
}
