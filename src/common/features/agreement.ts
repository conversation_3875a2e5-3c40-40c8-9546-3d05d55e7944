/*
 * ------------------------------------------------------------------
 * 用户协议勾选相关
 * ------------------------------------------------------------------
 */
import { MCProtocol } from '@simplex/simple-base';
import { getConfig } from ':store/chores';
import { makeToast } from './dom';
import SureProtocolModal from ':component/sureProtocolModal/main';

export async function hasCheckbox() {
    const res = await getConfig();
    return res.jk_buy_agreement === 'true';
}

const storageKey = 'checkboxKey';
let hasRead: boolean | undefined;

export async function hasReaded() {
    if (typeof hasRead === 'undefined') {
        // 1、默认配置为需要手动勾选；未取到远程配置展示勾选框，需要勾选后，才能点击购买；
        //    如未勾选协议，点击购买，toast提示：请您先同意《驾考宝典会员协议》，同时该句文案有个震动提醒的效果，同登录页面的提醒；
        // 2、配置布尔值：false-无勾选框，默认为已勾选状态，点击购买，直接进行购买；true-有勾选框，同默认配置说明；
        const storageReaded = window.localStorage.getItem(storageKey);
        if (storageReaded === 'true') {
            return true;
        }
        return !await hasCheckbox();
    }
    return hasRead;
}

export function setReaded(value: boolean) {
    hasRead = value;
    window.localStorage.setItem(storageKey, String(value));
}

let sureProtocol;
export function checkReaded(fn?: AnyFunc) {
    return (new Promise<void>((resolve, reject) => {
        hasReaded().then(hasRead => {
            if (!hasRead) {
                if (!sureProtocol) {
                    sureProtocol = new SureProtocolModal({});
                    sureProtocol.render();
                }
                sureProtocol.show().then(result => {
                    if (result) {
                        resolve();
                    }
                });

            } else {
                resolve();
            }
        });
    })).then(() => {
        fn && fn();
    });

}

export function setProtocolStatus(status) {
    MCProtocol.Vip.setProtocolStatus({
        check: status
    });
}

// 不能跟上面的checkReaded合并，因为挽留弹窗中的支付也会调用这个方法,但是ios底部勾选后并不会通知h5去把中间的勾选框勾上
export function iosCheckReaded(fn?: AnyFunc) {
    return (new Promise<void>((resolve, reject) => {
        hasReaded().then(isChecked => {
            if (isChecked) {
                resolve();
            } else {
                if (!sureProtocol) {
                    sureProtocol = new SureProtocolModal({});
                    sureProtocol.render();
                }
                sureProtocol.show().then(result => {
                    if (result) {
                        resolve();
                    }
                });
            }
        });
    })).then(() => {
        fn && fn();
    });
}
