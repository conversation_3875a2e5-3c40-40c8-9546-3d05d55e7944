/*
 * ------------------------------------------------------------------
 * 挽留弹窗相关
 * ------------------------------------------------------------------
 */

import { goBack } from ':common/core';
import { Platform } from ':common/env';
import { MCProtocol } from '@simplex/simple-base';
import once from 'lodash/once';

export type Listener = () => void | boolean | Promise<void | boolean>;

let listener: Listener | undefined;

export async function webBackHandler(listener?: Listener) {
    if (listener) {
        const didBack = await listener();
        if (!didBack) {
            return;
        }
    }
    goBack();
}

const init = once(() => {
    // VIP写法
    if (Platform.isAndroid) {
        MCProtocol.Vip.requestInterceptClose();
        window.onWebviewClose = () => webBackHandler(listener);
    } else {
        // 普通写法
        MCProtocol.Core.Web.requestInterceptClose();
        MCProtocol.Listener.onWebviewClose(() => {
            webBackHandler(listener);
        });
    }
});

/** 拦截返回，返回true(同步异步皆可)表示仍旧返回 */
export function onWebBack(intercepter?: Listener) {
    init();
    listener = intercepter;
}
