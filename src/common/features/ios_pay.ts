/*
 * ------------------------------------------------------------------
 * ios原生支付相关
 * ------------------------------------------------------------------
 */

import { MCProtocol, MCBaseUtils } from '@simplex/simple-base';
import { Features, PageName, Platform, URLCommon, URLParams, Version } from ':common/env';
import { STATUS_URL } from ':common/navigate';
import { openVipWebView } from ':common/core';
import { createMobileOrder, getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { ButtonConfig } from './bottom';
import { BUY_STATUS } from './trigger_page_switch';
import { toggleStatus } from './cache';
import jump from './jump';
import { getAbtest } from ':store/chores';
import { promisify } from ':common/utils';
import { ensureSiriusBound, PayBoundType } from './pay';
import { getFromPageCode, getFromPathCode, getPushCode, getStatAbtestStr, trackEvent } from ':common/stat';
import { hasReaded, iosCheckReaded, setProtocolStatus } from './agreement';

export interface IOSGoodsInfo {
    sessionIdList: number[],
    groupKey: GroupKey,
    appleId: string,
    activityType: string,
    price: number,
    originalPrice: number,
    applePrice: number
}

async function setBottom(config) {

    const promise = promisify(MCProtocol.Vip.setBottom)(config);

    setTimeout(async () => {
        setProtocolStatus(await hasReaded());
    }, 100);

    return promise;
}

/** 设置iOS支付按钮 */
export async function setIOSPayButton(
    { type, process = 0, goodsInfo, animate, ...style }: ButtonConfig,
    orderParams: { fragmentName1: string; fragmentName2?: string; abTest?: string, payPathType?: 0 | 1, actionName?: string }
) {
    if (type as any === 0) {
        return setBottom({ type: 0 });
    }

    const bizVersion = +URLParams.bizVersion;
    // 老版本没有5和6两种按钮样式，fallback到3和1的按钮样式
    if (bizVersion >= 1 || (Features.indexOf('vip.luban.mucang.cn/getBizVersion') > -1)) {
        // do nothing 在此版本加了两个按钮样式
    } else if (type === 5) {
        type = 3;
    } else if (type === 6) {
        type = 1;
    }
    const config = {
        process,
        type,
        style,
        goodsInfo,
        animate: animate || ''
    };
    const ret = setBottom(config);
    /** detail接口的额外参数(除了groupKey外) */
    MCProtocol.Vip.orderParams({
        pageName: PageName,
        from: URLParams.from,
        fragmentName1: orderParams.fragmentName1,
        fragmentName2: orderParams.fragmentName2,
        actionName: orderParams.actionName,
        abTest: await getStatAbtestStr()
    });
    return ret;
}

export function hiddenIOSPayButton() {
    return new Promise<void>(resolve => {
        if (Platform.isIOS) {
            setBottom({ type: 0 });
        }
        resolve();
    });
}

/** 单独刷新iOS支付按钮右上角的tag文本 */
export function setIOSPayButtonTagText(text: string) {
    MCProtocol.Vip.updateBottomTag({
        tag: { text }
    });
}

export function startIOSBottomAnimation() {
    MCProtocol.Vip.startBottomAnimation();
}

/**
 * @deprecated 已废弃, 请用iosDialogBuySuccess
 */
export async function iosBuySuccess(config: { groupKey: GroupKey, goUse?: boolean }) {
    await toggleStatus(BUY_STATUS);
    jump.replace(STATUS_URL, {
        boughtGroupKey: config.groupKey,
        goUse: config.goUse
    });
}

/** 横屏半截弹窗购买成功 */
export async function iosDialogBuySuccess(config: { groupKey: GroupKey, goUse?: boolean, apiHost?: 'squirrel', noClose?: boolean }) {
    if (!config.noClose) {
        await toggleStatus(BUY_STATUS);
    }
    openVipWebView({
        url: `${STATUS_URL}?boughtGroupKey=${config.groupKey}${config.goUse ? '&goUse=true' : ''}${config.apiHost ? '&apiHost=squirrel' : ''}`
    });
}

/**
 *  iOS支付协议 
 * 
*/

export type saveType = 'vip' | 'route' | 'lesson'

export type PartialIOSGoods = Pick<GoodsInfo, 'groupKey' | 'appleId' | 'sessionIds' | 'activityType'>

let allowPay = true;

export async function iosPay(groupKey: GroupKey | PartialIOSGoods, params: {
    fragmentName1: string, fragmentName2?: string, abTest?: string, payPathType?: 0 | 1, actionName?: string, extraInfo?: any, goodsCityCode?: string
}) {

    await iosCheckReaded();

    if (!allowPay) {
        return;
    }
    allowPay = false;

    setTimeout(() => {
        allowPay = true;

    }, 1000);

    let goodsInfo: PartialIOSGoods;

    if (typeof groupKey === 'string') {
        const goodsInfoList = await getGroupSessionInfo({ groupKeys: [groupKey] });
        goodsInfo = goodsInfoList[0];
    } else {
        goodsInfo = groupKey;
        groupKey = goodsInfo.groupKey;
    }

    // makeApplePayment协议历史的原因 导致不能所有版本都由我们来下单
    if (Version.h5IsSquirrel > 0) {
        await ensureSiriusBound({
            type: PayBoundType.GoLogin,
            groupKey
        });
        const orderInfo = await createMobileOrder({
            groupKey: groupKey,
            appleId: goodsInfo.appleId,
            sessionIds: goodsInfo.sessionIds,
            payType: 3,
            activityType: goodsInfo.activityType,
            fragmentName1: params.fragmentName1,
            fragmentName2: params.fragmentName2,
            extraInfo: params.extraInfo,
            goodsCityCode: params.goodsCityCode
        });

        // 目前只有这一个groupKey是misc的，所以saveType是route
        const saveType = groupKey === GroupKey.ChannelKe3RouteMeta ? 'route' : 'vip';

        MCProtocol.Vip.saveOrder({
            orderNumber: orderInfo.orderNumber,
            serviceName: saveType
        });
        MCProtocol.Vip.makeApplePayment({
            appleId: goodsInfo.appleId,
            content: orderInfo.content,
            orderNumber: orderInfo.orderNumber,
            callback: (data) => {
                if (data.success && data.data.status === 'success') {
                    window.iosBuySuccess(groupKey);
                }
            },
            serviceName: saveType
        });
    } else {

        MCProtocol.Vip.BuyGoods({
            groupKey: groupKey,
            orderParams: {
                pageName: PageName,
                from: URLParams.from,
                score12: URLParams.score12,
                fromPathCode: getFromPathCode(),
                fromPageCode: await getFromPageCode(),
                questionId: URLParams.questionId,
                courseId: URLParams.courseId,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                kemu: 'kemu' + URLCommon.kemu,
                carStyle: URLCommon.tiku,
                routeId: URLParams.routeId,
                placeId: URLParams.placeId,
                subject: URLParams.subject,
                pushCode: getPushCode(),
                abTest: await getStatAbtestStr(),
                ...params
            }
        });
    }
}