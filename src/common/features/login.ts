/*
 * ------------------------------------------------------------------
 * 登录相关
 * ------------------------------------------------------------------
 */
import { promisify } from ':common/utils';
import { MCProtocol } from '@simplex/simple-base';
import jump from './jump';
import { getAuthToken } from ':common/core';

export const login = async (reload = true) => {
    const info = await promisify(MCProtocol.Core.User.login)({
        from: 'jiakaobaodian',
        skipAuthRealName: true,
        pageType: 'quicklogin'
    });

    console.log('登录结果：', info);
    if (info.success) {
        if (reload) {
            setTimeout(() => {
                jump.reload();
                return '';
            }, 2000);
        }
        return await getAuthToken();
    }

    throw new Error('登录失败');

};