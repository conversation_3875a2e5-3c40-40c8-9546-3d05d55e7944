/*
 * ------------------------------------------------------------------
 * 以半屏形式打开
 * ------------------------------------------------------------------
 */

import { Platform } from ':common/env';
import { MCProtocol } from '@simplex/simple-base';

/** 如果为横屏，则h5ContentMaxWidth实际上是最大高度，宽高是反的～～ */
export function setEmbeddedHeight(h5whRate: number) {
    MCProtocol.Vip.show({
        h5whRate,
        h5ContentMaxWidth: 480
    });
}

/** 设置半屏高度 */
export function showSetting(config: { iosH: number, androidH: number }) {
    if (Platform.isIOS) {
        setEmbeddedHeight(375 / config.iosH);
    }

    if (Platform.isAndroid) {
        setEmbeddedHeight(375 / config.androidH);
    }
}
