/**
 * 权益相关接口
 * 
 */

import { CarType, URLCommon } from ':common/env';
import { getSessionList } from ':store/chores';

const sessionMap = {
    [CarType.CAR]: 'session-elite',
    [CarType.TRUCK]: 'session-ke1ke4-half-year-package-huoche',
    [CarType.BUS]: 'session-ke1ke4-half-year-package-keche',
    [CarType.GUACHE]: 'session-ke4-gc'
};

export enum promiseIconList {
    jj500t = 'http://exam-room.mc-cdn.cn/exam-room/2022/03/15/17/36ff6bb4b4dd470c907807c23a1b684c.png',
    zskcmn = 'https://web-resource.mc-cdn.cn/web/vip/14.png',
    bgbc = 'https://web-resource.mc-cdn.cn/web/vip/12.png',
    kqmj = 'https://web-resource.mc-cdn.cn/web/vip/18.png',
    jpzbk = 'http://exam-room.mc-cdn.cn/exam-room/2022/03/15/17/344aaa37ead84d8c8d7b11be3f113080.png',
    motoKe1Ke4ShortJpzbk = 'http://exam-room.mc-cdn.cn/exam-room/2024/03/18/17/33a0dcdb9ab147c4b83a9598eab4822b.png',
    bxbd = 'http://exam-room.mc-cdn.cn/exam-room/2022/03/15/17/cbb48463673046348bafcf6c0e941475.png',
    k1vip = 'https://web-resource.mc-cdn.cn/web/vip/13.png',
    k2vip = 'https://web-resource.mc-cdn.cn/web/vip/9.png',
    k3vip = 'https://web-resource.mc-cdn.cn/web/vip/10.png',
    k4vip = 'https://web-resource.mc-cdn.cn/web/vip/15.png',
    k1bgbc = 'https://web-resource.mc-cdn.cn/web/vip/11.png',
    k4bgbc = 'https://web-resource.mc-cdn.cn/web/vip/17.png',
    jjtk = 'https://web-resource.mc-cdn.cn/web/vip/16.png',
    jhk = 'https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/icon_01.png',
    dtjq = 'http://exam-room.mc-cdn.cn/exam-room/2022/07/01/17/cb19d41f6b1840038909d5244a8d9d20.png',
    jj5tj = 'https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/icon_02.png',
    kq3tj = 'https://web-resource.mc-cdn.cn/web/vip/score12/5.png',
    qyzbk = 'https://web-resource.mc-cdn.cn/web/vip/car/jk_qkm_05.png',
    // 目前只有被隐藏的状态（所以随便找的一张图）
    ztmgg = 'https://web-resource.mc-cdn.cn/web/vip/car/jk_qkm_05.png',
    jhkelder = 'http://exam-room.mc-cdn.cn/exam-room/2022/07/26/15/9c4e9fdffac04af8aa59bf2513688680.png',
    khcheKebgbc = 'http://exam-room.mc-cdn.cn/exam-room/2022/07/28/10/5aa9ead6fe1c4ec49a7f07ebaaf060fa.png'
}

export enum promiseList {
    bgbc = 'bgbc',
    kqmj = 'kqmj',
    zskcmn = 'zskcmn',
    jj500t = 'jj500t',
    bxbd = 'bxbd',
    k1vip = 'k1vip',
    k2vip = 'k2vip',
    k3vip = 'k3vip',
    k4vip = 'k4vip',
    jhk = 'jhk',
    jjtk = 'jjtk',
    jpzbk = 'jpzbk',
    dtjq = 'dtjq',
    jj5tj = 'jj5tj',
    kq3tj = 'kq3tj',
    qyzbk = 'qyzbk',
    ztmgg = 'ztmgg',
    sjkj = 'sjkj'
}

export function getSessionListByTiku() {
    return getSessionList().then(data => {
        const orginEliteGoods = data.sessionMap[sessionMap[URLCommon.tiku]].goodsEntityList;
        const renderEliteGoods = [];
        for (let i = 0; i < orginEliteGoods.length; i++) {
            if (orginEliteGoods[i].uniqKey !== 'goods-fallible') {
                renderEliteGoods.push(orginEliteGoods[i]);
            }
        }

        return renderEliteGoods;
    });
}