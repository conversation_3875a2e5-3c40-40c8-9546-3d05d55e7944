/*
 * ------------------------------------------------------------------
 * 定位相关
 * ------------------------------------------------------------------
 */

import { getSystemInfo } from ':common/core';
import { getCityName } from ':common/utils';

export async function isHubei() {

    const baseParams = await getSystemInfo();
    const userCity = baseParams._userCity || '';
    const cityCode = baseParams._cityCode || '';
    const gpsCity = baseParams._gpsCity || '';

    return userCity.indexOf('42') === 0 || cityCode.indexOf('42') === 0 || gpsCity.indexOf('42') === 0;
}

export async function getCurrentCityName() {
    const { _userCity, _cityCode } = await getSystemInfo();
    const cityCode = _userCity || _cityCode;
    const cityName = await getCityName(+cityCode);
    return cityName || '';
}