/*
 * ------------------------------------------------------------------
 * 缓存在本地相关的东西
 * ------------------------------------------------------------------
 */

import { getCache, saveCache } from ':common/core';

export const toggleStatus = async (key: string) => {
    let status = Number(await getCache(key) || 0);

    status++;

    await saveCache({
        key,
        value: String(status)
    });
};

// 页面tabIndex下一个页面存储，到上一个页面读取
export const setTabIndex = async (tabIndex) => {
    await saveCache({
        key: 'tabIndex',
        value: tabIndex
    });
};

export const getTabIndex = async () => {

    const tabIndex = await getCache('tabIndex');
    setTabIndex('');
    return tabIndex && +tabIndex;

};