import { getAuthToken, getCache, webClose } from ':common/core';
import { pauseAllVideos } from './dom';
import { reload } from './jump';
import { onPageHide, onPageShow } from './page_status_switch';

export const BUY_STATUS = 'buyStatus';

/**
 * 页面show时，购买状态变化需要关闭
*/
const buyStatusChange = async () => {
    const preBuyStatus = await getCache(BUY_STATUS);
    const preLoginStatus = await getAuthToken();
    onPageShow(async () => {
        const buyStatus = await getCache(BUY_STATUS);
        const loginStatus = await getAuthToken();
        setTimeout(() => {
            // 由于鸿蒙系统的微信支付成功回调慢，购买完成后会触发onPageShow,导致页面提前关闭，所以这里需要判断一下visibilityState的显示状态
            if (document.visibilityState === 'visible') {
                // 购买成功会触发status的show，需要延时处理，购买成功在show里面才会跳转status页面
                // eslint-disable-next-line max-len
                if ((location.href.indexOf('/status.html?') === -1 && location.href.indexOf('/ke3route.html?') === -1) && preBuyStatus !== buyStatus) {
                    webClose();
                    return;
                }
                // 必须先判断购买再判断登录
                if (preLoginStatus !== loginStatus) {
                    setTimeout(() => {
                        reload();
                    }, 1700);
                }
            }
        }, 500);

    });
};

const pauseVideo = () => {
    onPageHide(() => {
        pauseAllVideos();
    });
};

export const registeredPageHide = () => {
    pauseVideo();
};

export const registeredPageShow = async () => {
    buyStatusChange();
};