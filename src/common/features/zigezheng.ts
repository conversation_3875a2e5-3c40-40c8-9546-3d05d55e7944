/*
 * ------------------------------------------------------------------
 * 资格证相关
 * ------------------------------------------------------------------
 */

import { CarType } from ':common/env';
import { GroupKey } from ':store/goods';

export const zigezhengGroupKeyObj: Record<string, GroupKey> = {
    [CarType.KEYUN]: GroupKey.KeYun,
    [CarType.HUOYUN]: GroupKey.HuoYun,
    [CarType.WEIXIAN]: GroupKey.WeiXian,
    [CarType.JIAOLIAN]: GroupKey.JiaoLian,
    [CarType.CHUZU]: GroupKey.ChuZu,
    [CarType.WANGYUE]: GroupKey.WangYue,
    [CarType.WEIXIAN_YAYUN]: GroupKey.WeiX<PERSON>a<PERSON>un,
    [CarType.CHACHE]: GroupKey.ChaChe,
    [CarType.WEIXIAN_ZHUANGXIE]: GroupKey.WeiXianZhuangXie,
    [CarType.BAOZHA]: GroupKey.BaoZha,
    [CarType.BAOZHA_YAYUN]: GroupKey.BaoZhaYaYun,
    [CarType.BAOZHA_ZHUANGXIE]: GroupKey.BaoZhaZhuangXie,
    [CarType.JIAOLIAN_ZAIJIAOYU]: GroupKey.JiaoLianZaiJiaoyu,
    [CarType.MULTI_WVR]: GroupKey.WuRenJi,
    [CarType.MULTI_BVR]: GroupKey.WuRenJi,
    [CarType.MULTI_COACH]: GroupKey.WuRenJi,
    [CarType.FIXED_WVR]: GroupKey.WuRenJi,
    [CarType.FIXED_BVR]: GroupKey.WuRenJi,
    [CarType.FIXED_COACH]: GroupKey.WuRenJi,
    [CarType.HELICOPTER_WVR]: GroupKey.WuRenJi,
    [CarType.HELICOPTER_BVR]: GroupKey.WuRenJi,
    [CarType.HELICOPTER_COACH]: GroupKey.WuRenJi,
    [CarType.VTOL_WVR]: GroupKey.WuRenJi,
    [CarType.VTOL_BVR]: GroupKey.WuRenJi,
    [CarType.VTOL_COACH]: GroupKey.WuRenJi
};

export const zigezhengGroupKeyObjAlone: Record<string, GroupKey> = {
    [CarType.KEYUN]: GroupKey.KeYunAlone,
    [CarType.HUOYUN]: GroupKey.HuoYunAlone,
    [CarType.WEIXIAN]: GroupKey.WeiXianAlone,
    [CarType.JIAOLIAN]: GroupKey.JiaoLianAlone,
    [CarType.CHUZU]: GroupKey.ChuZuAlone,
    [CarType.WANGYUE]: GroupKey.WangYueAlone,
    [CarType.WEIXIAN_YAYUN]: GroupKey.WeiXianYaYunAlone,
    [CarType.CHACHE]: GroupKey.ChaCheAlone,
    [CarType.WEIXIAN_ZHUANGXIE]: GroupKey.WeiXianZhuangXieAlone,
    [CarType.BAOZHA]: GroupKey.BaoZhaAlone,
    [CarType.BAOZHA_YAYUN]: GroupKey.BaoZhaYaYunAlone,
    [CarType.BAOZHA_ZHUANGXIE]: GroupKey.BaoZhaZhuangXieAlone,
    [CarType.JIAOLIAN_ZAIJIAOYU]: GroupKey.JiaoLianZaiJiaoyuAlone
};

export const zigezhengTextObj: Record<string, string> = {
    [CarType.KEYUN]: '客运资格证',
    [CarType.HUOYUN]: '货运资格证',
    [CarType.WEIXIAN]: '危险品资格证',
    [CarType.JIAOLIAN]: '教练员资格证',
    [CarType.CHUZU]: '出租车资格证',
    [CarType.WANGYUE]: '网约车资格证',
    [CarType.WEIXIAN_YAYUN]: '危险品押运资格证',
    [CarType.CHACHE]: '叉车资格证',
    [CarType.WEIXIAN_ZHUANGXIE]: '危险品装卸资格证',
    [CarType.BAOZHA]: '爆炸品资格证',
    [CarType.BAOZHA_YAYUN]: '爆炸品押运资格证',
    [CarType.BAOZHA_ZHUANGXIE]: '爆炸品装卸资格证',
    [CarType.JIAOLIAN_ZAIJIAOYU]: '教练员再教育资格证',
    [CarType.MULTI_WVR]: '无人机',
    [CarType.MULTI_BVR]: '无人机',
    [CarType.MULTI_COACH]: '无人机',
    [CarType.FIXED_WVR]: '无人机',
    [CarType.FIXED_BVR]: '无人机',
    [CarType.FIXED_COACH]: '无人机',
    [CarType.HELICOPTER_WVR]: '无人机',
    [CarType.HELICOPTER_BVR]: '无人机',
    [CarType.HELICOPTER_COACH]: '无人机',
    [CarType.VTOL_WVR]: '无人机',
    [CarType.VTOL_BVR]: '无人机',
    [CarType.VTOL_COACH]: '无人机'
};

export const zigezhengTextMap: Record<string, string> = {
    [CarType.KEYUN]: '客运',
    [CarType.HUOYUN]: '货运',
    [CarType.WEIXIAN]: '危险品',
    [CarType.JIAOLIAN]: '教练员',
    [CarType.CHUZU]: '出租车',
    [CarType.WANGYUE]: '网约车',
    [CarType.WEIXIAN_YAYUN]: '危险品押运',
    [CarType.CHACHE]: '叉车',
    [CarType.WEIXIAN_ZHUANGXIE]: '危险品装卸',
    [CarType.BAOZHA]: '爆炸品',
    [CarType.BAOZHA_YAYUN]: '爆炸品押运',
    [CarType.BAOZHA_ZHUANGXIE]: '爆炸品装卸',
    [CarType.JIAOLIAN_ZAIJIAOYU]: '教练员再教育',
    [CarType.MULTI_WVR]: '无人机',
    [CarType.MULTI_BVR]: '无人机',
    [CarType.MULTI_COACH]: '无人机',
    [CarType.FIXED_WVR]: '无人机',
    [CarType.FIXED_BVR]: '无人机',
    [CarType.FIXED_COACH]: '无人机',
    [CarType.HELICOPTER_WVR]: '无人机',
    [CarType.HELICOPTER_BVR]: '无人机',
    [CarType.HELICOPTER_COACH]: '无人机',
    [CarType.VTOL_WVR]: '无人机',
    [CarType.VTOL_BVR]: '无人机',
    [CarType.VTOL_COACH]: '无人机'
};
