import { URLParams } from ':common/env';
import { trackExit } from ':common/stat';
import { hiddenIOSPayButton } from './ios_pay';

const delRepeatUrlParams = (params = {}, addUrlParams = true) => {
    let urlParams;
    if (addUrlParams) {
        urlParams = {
            ...URLParams,
            ...params
        };
    } else {
        urlParams = {
            ...params
        };
    }

    let str = '';
    for (const key in urlParams) {
        if (urlParams[key]) {
            str += `${key}=${encodeURIComponent(urlParams[key])}&`;
        }
    }

    return str.replace(/[&]$/, '');

};
const dealUrl = (url) => {

    // 解决相对路径的跳转
    if ((/^\.\/([^/]+)\.html$/).test(url)) {

        const goName = RegExp.$1;

        url = location.origin + location.pathname.replace(/\/[^/]+(\.html)/g, `/${goName}.html`);

    }

    const urlObj = new URL(url);

    const urlParams = {};

    const baseUrl = urlObj.origin + urlObj.pathname;

    if (urlObj.search) {
        const urlSearch = urlObj.search.split('?')[1];

        urlSearch.split('&').forEach(item => {
            const [key, value] = item.split('=');
            urlParams[decodeURIComponent(key)] = decodeURIComponent(value);
        });
    }

    return {
        baseUrl,
        urlParams,
        hashParamsStr: urlObj.hash
    };
};

// 离开当前页面需要把底部按钮隐藏（因为ios的底部按钮跟webview是同级的，跳转页面必须自己隐藏按钮）
export const replace = (url, params = {}) => {
    hiddenIOSPayButton();
    trackExit();
    const { baseUrl,
        urlParams,
        hashParamsStr
    } = dealUrl(url);

    location.replace(`${baseUrl}?${delRepeatUrlParams({ ...urlParams, ...params })}${hashParamsStr}`);
    throw new Error('不再继续往下走打点');

};

// 如果下个页面有支付按钮会重新设置，如果没有，请在下个页面自己设置隐藏支付按钮，因为在这里设置，可能是跳转协议，会导致回来按钮不见了
export const navigateTo = (url, params = {}) => {
    // hiddenIOSPayButton();
    trackExit();
    const { baseUrl,
        urlParams,
        hashParamsStr
    } = dealUrl(url);

    location.href = `${baseUrl}?${delRepeatUrlParams({ ...urlParams, ...params })}${hashParamsStr}`;
    throw new Error('不再继续往下走打点');
};

export const reload = () => {
    trackExit();
    setTimeout(() => {
        window.location.reload();
        throw new Error('不再继续往下走打点');
    }, 200);
};

export default {
    replace,
    navigateTo,
    reload,
    dealUrl,
    delRepeatUrlParams
};