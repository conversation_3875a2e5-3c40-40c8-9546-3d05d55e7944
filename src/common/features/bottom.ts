/*
 * ------------------------------------------------------------------
 * 底部支付按钮设置
 * 
 * https://mckj.feishu.cn/docs/doccnQ4QaaKdQ4LijdKum3WMeJb#IVJJt0
 * ------------------------------------------------------------------
 */

import { PAY_GUIDE_URL, PROTOCOL1_URL } from ':common/navigate';
import { GroupKey } from ':store/goods';
import { IOSGoodsInfo } from './ios_pay';
import Texts from ':common/features/texts';

export const defaultButtonConfig = {
    protocolText1: '开通前请阅读',
    protocolText2: '《' + Texts.productName + '会员协议》',
    protocolUrl: PROTOCOL1_URL,
    rightBottomText: '支付教程',
    rightBottomUrl: PAY_GUIDE_URL,
    cornerRadius: 4
};

export enum typeCode {
    type1 = 1,
    type2 = 2,
    type3 = 3,
    type4 = 4,
    type5 = 5,
    type6 = 6,
    type7 = 7,
}

interface CommonButtonConfig {
    /** 按钮样式，传0不展示 */
    type: number,
    /** 支付逻辑，1:表示不立即调起支付，需等待我这边处理前续操作逻辑再通知客户端发起支付。0:表示立即调起支付 */
    process?: 0 | 1,
    groupKey: GroupKey,
    protocolText1?: string,
    protocolText2?: string,
    protocolUrl?: string,
    protocolRequired?: boolean,
    rightBottomText?: string,
    rightBottomUrl?: string,
    // 购买按钮右上角提示条, 字典结构，便于扩展
    tag?: {
        text: string
    },
    // 左上和右上圆角
    cornerRadius?: number,
    /** 直接传给ios商品信息 */
    goodsInfo?: IOSGoodsInfo,
    /** 动画效果 */
    animate?: {
        price: number;
        toPrice: number;
    }
}

interface ButtonConfig1 extends CommonButtonConfig {
    type: 1
    price: string,
    validDays: string,
    title: string,
}

interface ButtonConfig2 extends CommonButtonConfig {
    type: 2
    price: string,
    title: string,
    subtitle: string
}

interface ButtonConfig3 extends CommonButtonConfig {
    type: 3
    title: string,
    subtitle?: string
}

interface ButtonConfig4 extends CommonButtonConfig {
    type: 4
    title: string,
    subtitle: string
}

interface ButtonConfig5 extends CommonButtonConfig {
    type: 5
    price: string,
    originalPrice: string
    discount: string
    validDays: string,
    title: string,
    subtitle: string
}

interface ButtonConfig6 extends CommonButtonConfig {
    type: 6
    price: string,
    /** 这个是给老版本fallback用的，type为3时需要 */
    validDays: string,
    originalPrice: string
    discount: string
    title: string,
    subtitle: string
}

interface ButtonConfig7 extends CommonButtonConfig {
    type: 7
    title: string,
    subtitle: string
}

export type ButtonConfig = ButtonConfig1 | ButtonConfig2 | ButtonConfig3
    | ButtonConfig4 | ButtonConfig5 | ButtonConfig6 | ButtonConfig7;

/** 根据商品信息计算按钮配置 */
export function getBtnConfigByGoods(type: ButtonConfig['type']) {
    switch (type) {
        case 1:
            return {};
        case 2:
            return {};
        case 3:
            return {};
        case 4:
            return {};
        case 5:
            return {};
        case 6:
            return {};
        case 7:
            return {};
        default:
            throw new Error('未知的type');
    }
}
