/*
 * ------------------------------------------------------------------
 * 犹豫用户挽留逻辑
 * ------------------------------------------------------------------
 */

import { getAuthToken, webClose } from ':common/core';
import { ABTestKey, ABTestType, CarType, KemuType, PageName, URLCommon } from ':common/env';
import { dateFormat, objToParams } from ':common/utils';
import { getAbtest, getVipPurchaseActivityType, hostApi } from ':store/chores';
import { GroupKey } from ':store/goods';
import { ActivityType, squirrelPrice } from ':store/newGoods';
import once from 'lodash/once';

/** 提前请求犹豫用户判断，避免返回时延迟过长 */
export const preJudgeHesitateUserPersuade = once(async function (isKemuAll = false) {
    if (
        URLCommon.tiku === CarType.CAR &&
        (isKemuAll || URLCommon.kemu === KemuType.Ke1 || URLCommon.kemu === KemuType.Ke4) &&
        URLCommon.isNormal
    ) {
        // 只处理小车正常模式
    } else {
        return false;
    }

    // 只处理已登录用户
    const authToken = await getAuthToken();
    if (!authToken) {
        return false;
    }

    const provide = await hostApi();
    if (!provide) {
        return false;
    }

    const { strategy } = await getAbtest(URLCommon.tiku);
    console.log('犹豫用户abtest值', strategy[ABTestKey.key26]);
    const plan = strategy[ABTestKey.key26] || ABTestType.A;
    if (plan === ABTestType.A) {
        return false;
    }

    const { value } = await getVipPurchaseActivityType({
        tiku: URLCommon.tiku,
        kemu: isKemuAll ? KemuType.Ke1 : URLCommon.kemu
    }).catch(() => {
        console.error('犹豫用户接口报错');
        return {
            value: 0
        };
    });
    if (value !== 1) {
        return false;
    }

    // 每个商品每天只出现1次
    const cacheKey = `hesitate-${dateFormat(new Date(), 'yyyy-MM-dd')}-plan-${plan}`;
    if (localStorage.getItem(cacheKey)) {
        return false;
    } else {
        localStorage.setItem(cacheKey, '1');
    }

    // eslint-disable-next-line no-nested-ternary
    const groupKey = isKemuAll ? GroupKey.ChannelKemuAll : URLCommon.kemu === KemuType.Ke1 ? GroupKey.ChannelKe1 : GroupKey.ChannelKe4;

    // 秒杀活动没配置或已过期则不展示
    if (plan === ABTestType.B) {
        try {
            const [goodsInfo] = await squirrelPrice({ groupKeys: [groupKey], activityType: ActivityType.fs });
            if (goodsInfo.activityType !== ActivityType.fs) {
                return false;
            }
        } catch {
            return false;
        }
    }

    return {
        plan,
        groupKey
    };
});

/** 是否弹出犹豫用户VIP促销弹窗 */
export const hesitateUserPersuade = once(async function (isKemuAll = false) {
    return false;
    const preJudge = await preJudgeHesitateUserPersuade(isKemuAll);

    if (!preJudge) {
        return false;
    }

    const { plan, groupKey } = preJudge;

    // 此处打开vip半截弹窗，可以直接调webClose关闭
    location.href = `http://jiakao.nav.mucang.cn/vip/new-vip?${objToParams({
        openType: 'dialog',
        page: `https://laofuzi.kakamobi.com/jkbd-vip/index/hesitate.html?groupKey=${groupKey}&plan=${plan}&fromPage=${PageName}`
    })}`;
    webClose();

    return true;
});