/*
 * ------------------------------------------------------------------
 * 原生dom操作的一些帮助方法
 * ------------------------------------------------------------------
 */

import { Platform } from ':common/env';
import createEventBus from ':common/eventbus';

/* 滚动到可视区域 */
export function scrollIntoView(dom: HTMLElement, options?: {
    topThreshold?: number,
    leftThreshold?: number,
    bottomThreshold?: number,
    rightThreshold?: number,
    center?: boolean
}) {
    const {
        topThreshold = 0,
        leftThreshold = 0,
        bottomThreshold = 0,
        rightThreshold = 0,
        center = false
    } = options || {};

    const scroller = dom.parentElement;

    const { scrollTop = 0, scrollLeft = 0 } = scroller;
    const scrollBottom = scrollTop + scroller.clientHeight;
    const scrollRight = scrollLeft + scroller.clientWidth;

    const { offsetTop, offsetLeft } = dom;
    const offsetBottom = offsetTop + dom.offsetHeight;
    const offsetRight = offsetLeft + dom.offsetWidth;

    const scrollOptions: { top?: number; left?: number; } = { top: 0, left: 0 };

    if (offsetTop - topThreshold < scrollTop) {
        scrollOptions.top = offsetTop - topThreshold - scrollTop;
    } else if (offsetBottom + bottomThreshold > scrollBottom) {
        scrollOptions.top = offsetBottom + bottomThreshold - scrollBottom;
    }

    if (offsetLeft - leftThreshold < scrollLeft) {
        scrollOptions.left = offsetLeft - leftThreshold - scrollLeft;
    } else if (offsetRight + rightThreshold > scrollRight) {
        scrollOptions.left = offsetRight + rightThreshold - scrollRight;
    }

    if (center) {
        scrollOptions.top = ((offsetTop + offsetBottom) / 2) - ((scrollTop + scrollBottom) / 2);
        scrollOptions.left = ((offsetLeft + offsetRight) / 2) - ((scrollLeft + scrollRight) / 2);
    }

    scrollOptions.top += scrollTop;
    scrollOptions.left += scrollLeft;

    if (scroller.scrollTo) {
        scroller.scrollTo({
            ...scrollOptions,
            behavior: 'smooth'
        });
    } else {
        // 低版本没有scroll方法
        scroller.scrollTop = scrollOptions.top;
        scroller.scrollLeft = scrollOptions.left;
    }
}

/* 监听输入框输入 */
export function onInput(inputElement: HTMLInputElement, handler: (e: Event) => void) {
    let inputLock = false;

    inputElement.addEventListener('compositionstart', function () {
        inputLock = true;
    });

    inputElement.addEventListener('compositionend', function (event) {
        inputLock = false;
        handler(event);
    });

    inputElement.addEventListener('input', function (event) {
        if (!inputLock) {
            handler(event);
        }
    });
}

/** 模拟toast */
export function makeToast(message: string, foz = '0.14rem', time = 2000) {
    return new Promise((resolve) => {
        if (document.getElementById('myToast')) {
            document.body.removeChild(document.getElementById('myToast'));
        }

        const div: HTMLDivElement = document.createElement('div');
        div.innerText = message;

        div.setAttribute('id', 'myToast');

        div.style.position = 'fixed';
        div.style.left = '50%';
        div.style.top = '50%';
        div.style.transform = 'translate(-50%, -50%)';
        div.style.webkitTransform = 'translate(-50%, -50%)';
        div.style.background = 'rgba(0, 0, 0, 0.7)';
        div.style.zIndex = '9999';
        div.style.padding = '10px 20px';
        div.style.borderRadius = '6px';
        div.style.textAlign = 'center';
        div.style.color = '#ffffff';
        div.style.maxWidth = '90%';
        div.style.minWidth = '60%';
        div.style.fontSize = foz;
        div.style.lineHeight = '1.5';

        document.body.appendChild(div);
        setTimeout(function () {
            div.remove();
            resolve('');
        }, time);
    });
}

/** 暂停所有视频 */
export function pauseAllVideos() {
    console.log('pauseAllVideos, 暂停所有视频');
    const $videos = document.querySelectorAll('video');
    for (let i = 0; i < $videos.length; i++) {
        $videos[i].pause();
    }
}

/** 
 * 监听dom滚动
 * 
 * @param scroller 滚动容器
 * @param threshold 阀值
 * @param scrollDownFn 向下滚动到大于阀值时触发此函数
 * @param scrollUpFn 向上滚动到小于阀值时触发此函数
 * @param scrollFn 每次滚动都触发此函数
 */
export function listenScroll(scroller: HTMLElement, threshold: number, scrollDownFn: AnyFunc, scrollUpFn: AnyFunc, scrollFn?: (scrollTop: number) => void) {
    let prevScrollTop = scroller.scrollTop;
    scroller.addEventListener('scroll', function () {
        const scrollTop = scroller.scrollTop;

        if (scrollTop >= threshold && prevScrollTop < threshold) {
            scrollDownFn();
        } else if (scrollTop < threshold && prevScrollTop >= threshold) {
            scrollUpFn();
        }
        scrollFn?.(scrollTop);

        prevScrollTop = scrollTop;
    });
}

/**
 * 滚动到顶部
 *  @param scroller 滚动容器
*/
export function scrollTop(scroller: HTMLElement, height?: number) {
    scroller.style.overflowY = 'hidden';

    // 回到滚动的顶部
    scroller.scrollTop = height || 0;

    scroller.style.overflowY = 'scroll';
}

/** 加载脚本 */
export function loadScript(src: string) {
    const script = document.createElement('script');
    return new Promise<any>((resolve, reject) => {
        script.onload = resolve;
        script.onerror = reject;
        script.src = src;
        document.body.appendChild(script);
    });
}

/**
 * 促销动画
 * 
*/

export function couponAnimate(params: {
    zIndex?: number,
    // 优惠券动画目标选择器
    couponTargetDomSelect: string
    // 比价动画目标选择器
    compareTargetDomSelect: string
    // 优惠券数据
    couponData: any
    // 比价数据
    compareData: any
    // 商品数据
    goodsData: any
    // 比价商品数据
    compareGoodsData: any
    // 比价动画类型 1竖着居中动画，2横着居中动画 3竖着底部动画 4横着底部动画
    compareAnimateType?: 1 | 2 | 3 | 4
}) {
    const { couponData, compareData = {}, zIndex, goodsData, compareAnimateType = 1, compareGoodsData } = params;

    function couponAnimation() {

        const $mask = document.createElement('div');
        const $body = document.querySelector('body');

        $mask.style.position = 'fixed';
        $mask.style.zIndex = zIndex ? String(zIndex) : '1001';
        $mask.style.top = '0';
        $mask.style.bottom = '0';
        $mask.style.left = '0';
        $mask.style.right = '0';
        $mask.style.background = 'rgba(0, 0, 0, 0.3)';
        $mask.setAttribute('skip', 'true');

        $body.append($mask);

        const { couponTargetDomSelect } = params;

        const targetDom = document.querySelector(couponTargetDomSelect);

        const domRect = targetDom && targetDom.getBoundingClientRect();

        const $animate = document.createElement('div');
        $animate.style.position = 'absolute';
        $animate.style.width = '200px';
        $animate.style.height = '200px';
        $animate.style.top = domRect?.top ? domRect.top + (domRect.height / 2) + 'px' : '50%';
        $animate.style.left = domRect?.left ? domRect.left + (domRect.width / 2) + 'px' : '50%';
        $animate.style.transform = 'translate(-50%, -50%) scale(0)';

        const $priceBox = document.createElement('div');
        const $unit = document.createElement('span');
        const $price = document.createElement('span');

        $priceBox.style.position = 'absolute';
        $priceBox.style.bottom = '58px';
        $priceBox.style.left = '44px';
        $priceBox.style.width = '90px';
        $priceBox.style.textAlign = 'center';
        $priceBox.style.color = 'white';

        $unit.textContent = '￥';

        if (+goodsData?.payPrice < +couponData.priceCent) {
            $price.textContent = Math.ceil(+goodsData.payPrice) + '';
        } else {
            $price.textContent = couponData.priceCent;
        }

        $animate.style.background = 'url(http://exam-room.mc-cdn.cn/exam-room/2023/09/20/16/08b286b0a4ef4a8bbe33ef22aaae84c7.png) no-repeat center center/cover';

        if (+$price.textContent > 99) {
            $unit.style.fontSize = '20px';
            $price.style.fontSize = '40px';
        } else {
            $unit.style.fontSize = '28px';
            $price.style.fontSize = '52px';
        }

        $animate.style.animation = 'couponAnimate 1.2s';

        $animate.addEventListener('animationend', () => {
            $mask.remove();
        });

        $priceBox.append($unit);
        $priceBox.append($price);
        $animate.append($priceBox);
        $mask.append($animate);
    }

    function compareAnimationVertical() {
        const { compareTargetDomSelect } = params;

        const parentDom = document.querySelector(compareTargetDomSelect);

        if (!parentDom) {
            return;
        }
        const targetDom = parentDom.querySelector('.animate-box');
        const domRect = parentDom && parentDom.getBoundingClientRect();

        const $animate = document.createElement('div');

        $animate.style.background = 'url(http://exam-room.mc-cdn.cn/exam-room/2023/12/05/18/2f57b495345746c8a0209a4610b5607f.png) no-repeat center center/100% 100%';
        $animate.style.display = 'flex';
        $animate.style.flexDirection = 'column';
        $animate.style.justifyContent = 'center';
        $animate.style.position = 'absolute';
        if (compareAnimateType === 1) {
            $animate.style.top = '50%';
            $animate.style.left = '50%';
            $animate.style.transform = 'translate(-50%, -50%) scale(0)';
        }

        if (compareAnimateType === 3) {
            $animate.style.bottom = '0';
            $animate.style.left = '50%';
            $animate.style.transform = 'translateX(-50%) scale(0)';
        }

        $animate.style.backgroundColor = 'red';
        $animate.style.minWidth = `${Math.max(domRect.width, 122)}px`;
        $animate.style.minHeight = `${Math.max(domRect.height, 141)}px`;
        $animate.style.borderRadius = `${8}px`;
        $animate.style.boxSizing = 'content-box';
        $animate.style.padding = '24px 8px 8px';
        $animate.style.zIndex = '10';

        if (compareAnimateType === 1) {
            $animate.style.animation = 'compareBoxAnimate 3s';
        }

        if (compareAnimateType === 3) {
            $animate.style.animation = 'compareBoxAnimateBottom 3s';
        }

        const $rightIcon = document.createElement('div');
        $rightIcon.style.position = 'absolute';
        $rightIcon.style.top = '0';
        $rightIcon.style.right = '0';
        $rightIcon.style.borderRadius = '0 8px 0 8px';
        $rightIcon.style.background = '#FFEF96';
        $rightIcon.style.fontSize = '12px';
        $rightIcon.style.color = '#644B00';
        $rightIcon.style.padding = '2px 4px';
        $rightIcon.textContent = `立省${compareData.diffPrice}元`;

        setTimeout(() => {
            $rightIcon.style.animation = 'compareRightIconAnimate 0.5s';
        }, 2100);

        const $name = document.createElement('div');
        $name.style.color = 'white';
        $name.style.textAlign = 'center';
        $name.style.fontSize = '14px';
        $name.style.fontWeight = '700';
        $name.textContent = `${compareGoodsData.name}`;

        const $compare = document.createElement('div');
        $compare.style.color = 'white';
        $compare.style.textAlign = 'center';
        $compare.style.marginTop = '10px';
        $compare.style.fontWeight = '700';
        $compare.style.fontSize = '18px';
        $compare.style.animation = 'compareTxtAnimate 1s';
        $compare.style.animationDelay = '0.6s';
        $compare.style.animationFillMode = 'forwards';
        $compare.textContent = `分开买${compareData.allPrice}`;

        const $realBox = document.createElement('div');
        $realBox.style.width = '0';
        $realBox.style.height = '0';
        $realBox.style.margin = '10px auto 0';
        $realBox.style.position = 'relative';
        $realBox.style.animation = 'compareRealBoxAnimate 1s';
        $realBox.style.animationDelay = '1.1s';
        $realBox.style.animationFillMode = 'forwards';

        const $whiteBox = document.createElement('div');
        $whiteBox.style.position = 'absolute';
        $whiteBox.style.top = '50%';
        $whiteBox.style.left = '50%';
        $whiteBox.style.transform = 'translate(-50%,-50%)';
        $whiteBox.style.width = '110px';
        $whiteBox.style.height = '50px';
        $whiteBox.style.opacity = '0';
        $whiteBox.style.backgroundColor = 'white';
        $whiteBox.style.borderRadius = '8px';
        $whiteBox.style.boxSizing = 'border-box';
        $whiteBox.style.display = 'flex';
        $whiteBox.style.flexDirection = 'column';
        $whiteBox.style.justifyContent = 'center';
        $whiteBox.style.alignItems = 'center';
        $whiteBox.style.overflow = 'hidden';
        $whiteBox.style.animation = 'compareWhiteBoxAnimate 1s';
        $whiteBox.style.animationDelay = '1.1s';
        $whiteBox.style.animationFillMode = 'forwards';

        $realBox.append($whiteBox);

        const $goodIcon = document.createElement('div');
        $goodIcon.style.width = '19px';
        $goodIcon.style.height = '18px';
        $goodIcon.style.background = 'url(http://exam-room.mc-cdn.cn/exam-room/2023/12/05/10/bc44e62052a74fbd84639be719e96192.png) no-repeat center center/cover';

        $whiteBox.append($goodIcon);

        const $txt = document.createElement('div');
        $txt.style.fontSize = '18px';
        $txt.style.fontWeight = '700';
        $txt.style.textWrap = 'nowrap';
        $txt.style.whiteSpace = 'nowrap';
        $txt.style.color = '#F51C95';
        $txt.style.marginTop = '5px';
        $txt.textContent = `现价仅￥${compareGoodsData.payPrice}`;
        $whiteBox.append($txt);

        $animate.append($rightIcon);
        $animate.append($name);
        $animate.append($compare);
        $animate.append($realBox);
        targetDom.append($animate);

    }

    function compareAnimationHorizontal() {
        const { compareTargetDomSelect } = params;

        const parentDom = document.querySelector(compareTargetDomSelect);

        if (!parentDom) {
            return;
        }
        const targetDom = parentDom.querySelector('.animate-box');

        const domRect = parentDom && parentDom.getBoundingClientRect();

        const $animate = document.createElement('div');

        $animate.style.background = 'url(http://exam-room.mc-cdn.cn/exam-room/2023/12/05/18/2f57b495345746c8a0209a4610b5607f.png) no-repeat center center/100% 100%';
        $animate.style.display = 'flex';
        $animate.style.flexDirection = 'column';
        $animate.style.justifyContent = 'center';
        $animate.style.position = 'absolute';
        if (compareAnimateType === 2) {
            $animate.style.top = '50%';
            $animate.style.left = '50%';
            $animate.style.transform = 'translate(-50%,-50%) scale(0)';
        }

        if (compareAnimateType === 4) {
            $animate.style.bottom = '0';
            $animate.style.left = '50%';
            $animate.style.transform = 'translateX(-50%) scale(0)';
        }

        $animate.style.backgroundColor = 'red';
        $animate.style.minWidth = `${Math.max(domRect.width, 168)}px`;
        $animate.style.minHeight = `${Math.max(domRect.height, 80)}px`;
        $animate.style.borderRadius = `${8}px`;
        $animate.style.boxSizing = 'content-box';
        $animate.style.padding = '24px 8px 8px';
        $animate.style.zIndex = '10';

        if (compareAnimateType === 2) {
            $animate.style.animation = 'compareHorizontaBoxAnimate 3s';
        }

        if (compareAnimateType === 4) {
            $animate.style.animation = 'compareHorizontaBoxAnimateBottom 3s';
        }

        const $rightIcon = document.createElement('div');
        $rightIcon.style.position = 'absolute';
        $rightIcon.style.top = '0';
        $rightIcon.style.right = '0';
        $rightIcon.style.borderRadius = '0 8px 0 8px';
        $rightIcon.style.background = '#FFEF96';
        $rightIcon.style.fontSize = '12px';
        $rightIcon.style.color = '#644B00';
        $rightIcon.style.padding = '2px 4px';
        $rightIcon.textContent = `立省${compareData.diffPrice}元`;

        setTimeout(() => {
            $rightIcon.style.animation = 'compareRightIconAnimate 0.5s';
        }, 2100);

        const $name = document.createElement('div');
        $name.style.color = 'white';
        $name.style.textAlign = 'center';
        $name.style.fontSize = '14px';
        $name.style.fontWeight = '700';
        $name.textContent = `${compareGoodsData.name}`;

        const $compare = document.createElement('div');
        $compare.style.color = 'white';
        $compare.style.textAlign = 'center';
        $compare.style.marginTop = '10px';
        $compare.style.fontWeight = '700';
        $compare.style.fontSize = '18px';
        $compare.style.animation = 'compareTxtAnimate 1s';
        $compare.style.animationDelay = '0.6s';
        $compare.style.animationFillMode = 'forwards';
        $compare.textContent = `分开买${compareData.allPrice}`;

        const $realBox = document.createElement('div');
        $realBox.style.width = '0';
        $realBox.style.height = '0';
        $realBox.style.margin = '10px auto 0';
        $realBox.style.position = 'relative';
        $realBox.style.animation = 'compareHorizontaRealBoxAnimate 1s';
        $realBox.style.animationDelay = '1.1s';
        $realBox.style.animationFillMode = 'forwards';

        const $whiteBox = document.createElement('div');
        $whiteBox.style.position = 'absolute';
        $whiteBox.style.top = '50%';
        $whiteBox.style.left = '50%';
        $whiteBox.style.transform = 'translate(-50%,-50%)';
        $whiteBox.style.width = '143px';
        $whiteBox.style.height = '28px';
        $whiteBox.style.opacity = '0';
        $whiteBox.style.backgroundColor = 'white';
        $whiteBox.style.borderRadius = '14px';
        $whiteBox.style.boxSizing = 'border-box';
        $whiteBox.style.display = 'flex';
        $whiteBox.style.justifyContent = 'center';
        $whiteBox.style.alignItems = 'center';
        $whiteBox.style.overflow = 'hidden';
        $whiteBox.style.animation = 'compareHorizontalWhiteBoxAnimate 1s';
        $whiteBox.style.animationDelay = '1.1s';
        $whiteBox.style.animationFillMode = 'forwards';

        $realBox.append($whiteBox);

        const $goodIcon = document.createElement('div');
        $goodIcon.style.width = '19px';
        $goodIcon.style.height = '18px';
        $goodIcon.style.background = 'url(http://exam-room.mc-cdn.cn/exam-room/2023/12/05/10/bc44e62052a74fbd84639be719e96192.png) no-repeat center center/cover';

        $whiteBox.append($goodIcon);

        const $txt = document.createElement('div');
        $txt.style.fontSize = '18px';
        $txt.style.fontWeight = '700';
        $txt.style.textWrap = 'nowrap';
        $txt.style.whiteSpace = 'nowrap';
        $txt.style.color = '#F51C95';
        $txt.style.marginTop = '5px';
        $txt.textContent = `现价仅￥${compareGoodsData.payPrice}`;
        $whiteBox.append($txt);

        $animate.append($rightIcon);
        $animate.append($name);
        $animate.append($compare);
        $animate.append($realBox);
        targetDom.append($animate);

    }

    // 可能没有dom  所以延时获取一下
    setTimeout(() => {
        if (!Platform.isIOS && (couponData?.code || couponData?.couponCode)) {
            couponAnimation();
        } else if (compareData?.diffPrice) {
            if (compareAnimateType === 2 || compareAnimateType === 4) {
                compareAnimationHorizontal();
            }

            if (compareAnimateType === 1 || compareAnimateType === 3) {
                compareAnimationVertical();
            }
        }

    }, 500);

}