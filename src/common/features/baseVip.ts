/*
 * ------------------------------------------------------------------
 * vip base.ts  只提供调用方法 
 * ------------------------------------------------------------------
 */

import { getSessionExtra, GroupKey, GroupComparePrice, GoodsInfo, getGroupSessionInfo } from ':store/goods';
import { Application } from '@simplex/simple-core';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { BUYED_URL, openAuth } from ':common/navigate';
import jump from './jump';
import { formatPrice } from ':common/utils';
import { typeCode } from './bottom';
import { onPageShow } from './page_status_switch';
import { getTabIndex } from './cache';
import isNumber from 'lodash/isNumber';
import { PayType, Platform } from ':common/env';
import { iosPay } from './ios_pay';
import { trackGoPay } from ':common/stat';
import { getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from './pay';

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
}

export default class BaseVip extends Application {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog
    };
    declare state: State
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey] || {};
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom(config?: { stat: any }) {
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const fragmentName1 = '底部吸底按钮';
        let bottomType: typeCode = typeCode.type4;

        // 全科并且有活动的时候按钮不同
        if (nowGoodInfo.inActivity) {
            bottomType = typeCode.type5;
        }

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1,
                    ...config?.stat
                });
                break;
            case typeCode.type5:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${nowGoodInfo.inActivity.discountedPrice}元`,
                    price: this.showPrice,
                    originalPrice: '日常价￥' + nowGoodInfo.inActivity.preDiscountPrice,
                    fragmentName1,
                    ...config?.stat
                });
                break;
            default:
                break;
        }
    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    jump.replace(BUYED_URL);
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }
            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool
            });
            this.setPageInfo();
        });

    }
    getCoupon = async (goodsInfoList?) => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};
        const list = goodsInfoList || goodsInfoPool;

        list.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[list[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel(goodsInfoList?) {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};
        const list = goodsInfoList || goodsInfoPool;

        list.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[list[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    // 比价必须在商品详情之后
    async getComparePrice(goodsInfoList?) {
        const { goodsInfoPool } = this.state;
        const list = goodsInfoList || goodsInfoPool;

        await GroupComparePrice({ groupKeys: list.map(item => item.groupKey).join(',') }).then(comparePricePool => {
            this.setState({ comparePricePool });
        });

    }
    pay = async (stat: PayStatProps) => {
        console.error('子类需要实现这个方法');
    }
    onPay = async (config: { stat: PayStatProps }) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo?.couponCode,
            ...config.stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(config.stat);
                },
                ...config.stat
            });
        });
    }
    onPayBtnCall = (config?: { stat: any }) => {
        const { tabIndex, goodsInfoPool } = this.state;

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                ...config?.stat
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            // 点击支付按钮打点
            trackGoPay({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                ...config?.stat
            });

            this.children.payDialog.show({
                groupKey: this.nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.onPay({ stat: config?.stat });
                },
                ...config?.stat
            });

        }
    }
    tabChangeCall(tabIndex) {
        console.error('子类需要实现这个方法');
    }
    goAuth = async (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
}
