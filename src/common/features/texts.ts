import { CarType, KemuType, URLCommon, URLParams } from '../env';

/** 科目文案 */
const kemuTxt = {
    [KemuType.Ke1]: '科一',
    [KemuType.Ke2]: '科二',
    [KemuType.Ke3]: '科三',
    [KemuType.Ke4]: '科四'
};

/** 车型文案 */
const carStyleTxt = {
    [CarType.CAR]: '小车',
    [CarType.BUS]: '客车',
    [CarType.TRUCK]: '货车',
    [CarType.MOTO]: '摩托车',
    [CarType.GUACHE]: '轻型牵引挂车'
};

/** 车执照类型文案 */
const carStyleLicenseTxt = {
    [CarType.CAR]: 'C1/C2/C3',
    [CarType.BUS]: 'A1/A3/B1',
    [CarType.TRUCK]: 'A2/B2',
    [CarType.MOTO]: 'D/E/F',
    [CarType.GUACHE]: 'C6'
};

const TEMPLATE_KEY_MAP = {
    template_index: 'template_index'
};

/**
 * 产品名称
*/
const productName = URLParams._product === '驾考宝典助手' ? '驾考宝典' : (URLParams._product || '驾考宝典');
/**
 * 已购买页文案，便于打点
 */
const BUYED_PAGE = '已购买页';
const currentCarStyleTxt = URLCommon.isZigezheng ? '资格证' : carStyleTxt[URLCommon.tiku];
const currentKemuTxt = URLCommon.isZigezheng ? '资格证' : kemuTxt[URLCommon.kemu];
const currentcarStyleLicenseTxt = carStyleLicenseTxt[URLCommon.tiku];

window.vAssets = window.vAssets || {
    'https://maiche.jmtv.com.cn': 'https://hw-jiakao-video.jiakaobaodian.com'
};

const HOSTTXT = {
    maiche: 'https://maiche.jmtv.com.cn'
};

const TVHOSTMAP = {
    maiche: window.vAssets[HOSTTXT.maiche]
};

export default {
    currentcarStyleLicenseTxt,
    currentCarStyleTxt,
    currentKemuTxt,
    productName,
    kemuTxt,
    BUYED_PAGE,
    TVHOSTMAP,
    TEMPLATE_KEY_MAP
};