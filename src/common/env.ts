/*
 * ------------------------------------------------------------------
 * 环境上下文相关
 * ------------------------------------------------------------------
 */

const userAgent = navigator.userAgent.toLowerCase();

/** app支持的特性 */
export const Features = (window.mucang && window.mucang.features) || [];

/** 控制是否有挽留弹窗 */
export const persuadeDialogAllow = true;

/** 车型 */
export enum ABTestType {
    A = 'a',
    B = 'b',
    C = 'c'
}

export enum ABTestKey {
    key1 = 'vip-ke1-session',
    key2 = 'vip-ke1-group',
    key3 = 'vip-ke1-session-500',
    key4 = 'vip-kemuall-hc-head',
    key5 = 'vip-kemuall-moto-head',
    key6 = 'vip-kemuall-kc-head',
    key7 = 'vip-kou12-abtest',
    key8 = 'vip-ke2-moto',
    /**
     * 客车科一科四头部
     */
    key9 = 'vip-ke1ke4-bus',
    /**
     * 货车科一科四头部
     */
    key10 = 'vip-ke1ke4-truck',
    /**
     * 摩托科一科四头部 
     * */
    key11 = 'vip-ke1ke4-moto',
    /**
    * 小車全科页面展示 
    * */
    key12 = 'vip-quanke-group',
    /**
     * 优秀学员小车a/b test
     */
    key13 = 'vip-excellent-sale',
    /* 
     * qiaoji.html货车展示
    */
    key14 = 'vip-sujipage',
    /**
    * 优秀学员货车a/b test
    */
    key15 = 'vip-excellent-sale-hc',
    /**
     * 科三科四组合包a/b test
     */
    key16 = 'vip-ke3ke4',
    /**
     * 全科升级页a/b test
     */
    key17 = 'vip-quanke-upgrade',
    key18 = 'vip-sjkj-rights',

    // 路线视频考场详情页（未购）
    key19 = 'ke3-routevedio-pay',
    // 小车科三VIP落地页进行abtest
    key20 = 'ke3-vip-pay',
    // 货车考前密卷abtest
    key21 = 'vip-hc-kqmj',
    // 路线视频考场详情页（未购）
    key22 = 'ke3-routevedio-pay2',
    // 路线视频考场详情页视频类型
    key23 = 'ke3-routevideo-freevideo',
    // moto第二个商品是否是短时提分
    key24 = 'moto-ke1ke4',
    // 巧记展示不同的页面
    key25 = 'jk-jiqiao',
    // 犹豫用户促销
    key26 = 'vip-sale',
    // 科一月卡和半年卡默认选中
    key27 = 'vip-sale-month',
    // 科二月卡和半年卡默认选中
    key28 = 'vip-ke2sale-month',
     // 是否展示月卡功能
    key29 = 'vip-ke1ke4-session1',
    // 是否进服务端推算商品页
    key30 = 'predict-ke14-vip',
    // 是否展示月卡功能
    key31 = 'vip-ke1ke4-session' 
}

/** 车型 */
export enum CarType {
    CAR = 'car',
    TRUCK = 'truck',
    BUS = 'bus',
    MOTO = 'moto',
    GUACHE = 'light_trailer',
    // 资格证
    KEYUN = 'keyun',
    HUOYUN = 'huoyun',
    WEIXIAN = 'weixian',
    JIAOLIAN = 'jiaolian',
    CHUZU = 'chuzu',
    WANGYUE = 'wangyue',
    WEIXIAN_YAYUN = 'weixian_yayun',
    CHACHE = 'chache',
    WEIXIAN_ZHUANGXIE = 'weixian_zhuangxie',
    BAOZHA = 'baozha',
    BAOZHA_YAYUN = 'baozha_yayun',
    BAOZHA_ZHUANGXIE = 'baozha_zhuangxie',
    JIAOLIAN_ZAIJIAOYU = 'jiaolian_zaijiaoyu',
    // 无人机相关（资格证的一种）
    MULTI_WVR = 'multi_wvr',
    MULTI_BVR = 'multi_bvr',
    MULTI_COACH = 'multi_coach',
    FIXED_WVR = 'fixed_wvr',
    FIXED_BVR = 'fixed_bvr',
    FIXED_COACH = 'fixed_coach',
    HELICOPTER_WVR = 'helicopter_wvr',
    HELICOPTER_BVR = 'helicopter_bvr',
    HELICOPTER_COACH = 'helicopter_coach',
    VTOL_WVR = 'vtol_wvr',
    VTOL_BVR = 'vtol_bvr',
    VTOL_COACH = 'vtol_coach'
}

/** 科目 */
export enum KemuType {
    Ke0 = 0,
    Ke1 = 1,
    Ke2 = 2,
    Ke3 = 3,
    Ke4 = 4
}

/** 语言*/
export enum LangType {
    // 中文
    ZH = 'zh',
    // 英文
    EN = 'en',
    // 朝鲜语
    KO = 'ko',
    // 维语
    UG = 'ug'
}

// 推荐商品入口命名
export enum RecKey {
    INDEX = 'index'
} 

/** url参数，统一从这里取 */
export const URLParams = (function () {
    return window.location.search.substring(1).split('&').reduce<Record<string, string>>((obj, segment) => {
        if (!segment) {
            return obj;
        }
        const [key, value] = segment.split('=');
        // iOS需要decode两遍
        obj[decodeURIComponent(key)] = decodeURIComponent(decodeURIComponent(value));
        return obj;
    }, {});
})();
/** 平台判断 */
export const Platform = {
    isWeiXin: !!userAgent.match(/MicroMessenger/i),
    isAndroid: userAgent.indexOf('android') > -1,
    isHarmony: userAgent.indexOf('openharmony') > -1,
    isMuCang: userAgent.indexOf('mucang') > -1,
    isIOS: userAgent.indexOf('iphone') > -1 || userAgent.indexOf('ipad') > -1,
    /** VIVO平台 */
    isVIVO: (() => {
        const manufacturer = (URLParams._manufacturer && URLParams._manufacturer.toLowerCase()) || '';
        return manufacturer.indexOf('vivo') > -1;
    })(),
    isWeiyu: URLParams._appName === 'jiakaobaodianuygur',
    isXueTang: URLParams._appName === 'jiakaoxuetang' || URLParams._appName === 'jiakaoduoyuyan',
    isJiakao: URLParams._appName === 'jiakaozhushou' || URLParams._appName === 'jiakaobaodian',
    isSupportWebp: (() => {
        if ((userAgent.indexOf('iphone') > -1 || userAgent.indexOf('ipad') > -1) && +URLParams._systemVersion?.split('.')[0] < 14) {
            return false;
        }
        return true;
    })(),
    // 智慧驾校
    isZhiHui: URLParams._appName === 'jiakaozhihui',
    isNoProtocol: URLParams._appName === 'jiakaobaodianuygur' || URLParams._appName === 'jiakaoduoyuyan' || URLParams._appName === 'jiakaoxuetang' || URLParams._appName === 'jiakao3d'
};

// eslint-disable-next-line max-len
const zigezhengArr = [CarType.KEYUN, CarType.HUOYUN, CarType.WEIXIAN, CarType.JIAOLIAN, CarType.CHUZU, CarType.WANGYUE, CarType.WEIXIAN_YAYUN, CarType.CHACHE, CarType.WEIXIAN_ZHUANGXIE, CarType.BAOZHA, CarType.BAOZHA_YAYUN, CarType.BAOZHA_ZHUANGXIE, CarType.JIAOLIAN_ZAIJIAOYU, CarType.MULTI_WVR, CarType.MULTI_BVR, CarType.MULTI_COACH, CarType.FIXED_WVR, CarType.FIXED_BVR, CarType.FIXED_COACH, CarType.HELICOPTER_WVR, CarType.HELICOPTER_BVR, CarType.HELICOPTER_COACH, CarType.VTOL_WVR, CarType.VTOL_BVR, CarType.VTOL_COACH];

const wurenjiArr = [CarType.MULTI_WVR, CarType.MULTI_BVR, CarType.MULTI_COACH, CarType.FIXED_WVR, CarType.FIXED_BVR, CarType.FIXED_COACH, CarType.HELICOPTER_WVR, CarType.HELICOPTER_BVR, CarType.HELICOPTER_COACH, CarType.VTOL_WVR, CarType.VTOL_BVR, CarType.VTOL_COACH];

/** 通用URL参数及判断 */
export const URLCommon = {
    /** 题库，即车型 */
    tiku: (URLParams.carStyle || CarType.CAR) as CarType,
    /** 科目 */
    kemu: (+URLParams.kemuStyle >= 5 ? KemuType.Ke1 : (+URLParams.kemuStyle || KemuType.Ke1)) as KemuType,

    /** 满分学习场景 */
    isScore12: URLParams.sceneCode === '102' && URLParams.patternCode !== '102',
    /** 长辈版模式 */
    isElder: URLParams.patternCode === '102',
    /** 正常版模式 */
    isNormal: URLParams.sceneCode !== '102' && URLParams.patternCode !== '102',
    /** 是否资格证 */
    isZigezheng: zigezhengArr.indexOf(URLParams.carStyle as CarType) > -1,
    /** 是否无人机 */
    isWurenji: wurenjiArr.indexOf(URLParams.carStyle as CarType) > -1,
    /** 是否是3d,只有3d才有_myVer，因为驾考可能打开3d的sdk，_appName还是jiakaobaodian，所以appName已经不能判断包了 */
    is3DSingle: (() => {
        URLParams.sceneCode = URLParams.sceneCode || '101';
        URLParams.patternCode = URLParams.patternCode || '101';
        return URLParams._appName === 'jiakao3d';
    })(),
    is3D: (() => {
        URLParams.sceneCode = URLParams.sceneCode || '101';
        URLParams.patternCode = URLParams.patternCode || '101';
        return !!URLParams._myVer;
    })()
};

/** 版本判断 */
export const Version = {
    /**
     * 用来判断客户端支持的木仓协议的版本
     * 
     * 5: 
     *  - 新增 Vip.setting.switchType
     */
    bizVersion: +URLParams.bizVersion,
    /**
    * 用来判断客户端新商业化ios是否可以自己下单
    * 
    */
    h5IsSquirrel: +URLParams.h5IsSquirrel
};

/** 支付方式 */
export enum PayType {
    Alipay = 1,
    Weixin = 2,
    ApplePay = 3,
    Harmony = 8,
    // 他人帮忙付
    OtherPay = 100
}

let payType: PayType = PayType.Harmony;

export function setPayType(paramsPayType: PayType) {
    payType = paramsPayType;
}

export function getPayType() {
    return payType;
}

/** 页面名称 */
export let PageName = '未知页';
/** 设置页面名称，请尽早设置 */
export function setPageName(pageName = '未知页') {
    PageName = pageName;
}
export function getPageName() {
    return PageName;
}
