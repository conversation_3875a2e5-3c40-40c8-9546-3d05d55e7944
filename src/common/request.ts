/*
 * ------------------------------------------------------------------
 * 网络请求
 * ------------------------------------------------------------------
 */

import { MCBaseStore } from '@simplex/simple-base';
import { Platform, URLParams } from './env';
import { getAuthToken } from './core';
import { makeToast } from './features/dom';
import { sign } from './utils';
import { trackEvent } from './stat';

type HostName = 'sirius' | 'misc' | 'activity' | 'config' | 'swallow' | 'jiakao3d' | 'squirrel' | 'cheyouquan' | 'monkey' | 'jiakao' | 'tiku' | 'panda' | 'pony' | 'parrot' | 'rights' | 'koala' | 'eagle';

interface RequestOptions {
    hostName?: HostName,
    url: string,
    method?: 'GET' | 'POST',
    headers?: Record<string, any>,
    data?: Record<string, any>
    noToast?: true
}

const signMap: Record<HostName, string> = {
    'sirius': '*#06#c2uXpo9IeKaIkpyJdXipfGxs',
    'misc': '*#06#j5moQpWNkIhrjaSFgodFh52T',
    'activity': '*#06#j5moQpWNkIhrjaSFgodFh52T',
    'config': '*#06#d3pycm9DSYd6lndDckVwkzyZ',
    'swallow': '*#06#j5moQpWNkIhrjaSFgodFh52T',
    'jiakao3d': '*#06#bIWiiqmXc3lspqhJj5NIipya',
    'squirrel': '*#06#iKVuc32KRW12cqg8QnGkdX16',
    'cheyouquan': '',
    'monkey': '*#06#PGuPbJiIkz2PeItsc5qKhItG',
    'jiakao': '',
    'tiku': '*#06#i4mleXFFkIlCqXWal3eCQkN6',
    'panda': '*#06#l5J2nW13l3qEfHdubKKmPJyj',
    'parrot': '*#06#b4dEqYuWaohyRJN4eW9zfEJ5',
    'pony': '*#06#bJaWjoOnhaN9pXCCbqiSoqd2',
    'rights': '*#06#R31tjnFuiaJubpKojalHbnmG',
    'koala': '*#06#p0mJa5qSmnE9nG5wa3aZSY6C',
    'eagle': '*#06#p4VEPGttj3CPPXyWQ5GkmZlF'
};

const resignMap: Record<HostName, string> = {
    'sirius': 'hello',
    'misc': 'debug',
    'activity': 'hello',
    'config': '',
    'swallow': 'hello1',
    'jiakao3d': 'xx',
    'squirrel': 'helloworld',
    'cheyouquan': '',
    'monkey': 'debug',
    'jiakao': 'debug',
    'tiku': '_debug',
    'panda': 'debug',
    'pony': 'hello',
    'parrot': 'hello',
    'rights': 'hello',
    'koala': 'hello',
    'eagle': 'debug'
};

/** 发起网络请求 */
export async function request({ hostName = 'sirius', url, method = 'GET', headers, data = {}, noToast }: RequestOptions): Promise<any> {
    /*
     | 处理参数
     */

    const authToken = await getAuthToken();
    if (authToken && !data.authToken) {
        data.authToken = authToken;
    }
    data.from = URLParams.from || 'unknown';

    if (!Platform.isMuCang) {
        data = {
            ...(() => {
                const whiteUrlParams = {};
                for (const key in URLParams) {
                    if (!(/^_/).test(key)) {
                        whiteUrlParams[key] = URLParams[key];
                    }
                }
                return whiteUrlParams;
            }),
            ...data,
            _r: sign(1),
            resign: resignMap[hostName]
        };
    }

    return new Promise((resovle, reject) => {
        (new (MCBaseStore.extend({
            url: `${hostName}://${url}`,
            sign: signMap[hostName],
            headers,
            method: method.toLocaleUpperCase(),
            errorToast: false,
            type: 'online'
        }))()).request(data).then((res) => {
            // console.info('[response]', url, res);
            resovle(res);
        }).fail((errorCode, error) => {
            if (!noToast) {
                // 由于打点中会调用这2个接口会造成死循环
                // if (url !== 'api/open/sys/provide.htm' && url !== 'api/open/ab-test/get-all-ab-test.htm' && url !== 'api/web/v4/config/get.htm') {
                //     trackEvent({
                //         PageName: '公共页',
                //         fragmentName1: 'h5页面',
                //         actionType: '触发',
                //         actionName: '接口报错',
                //         targetContent: JSON.stringify({
                //             url,
                //             params: data,
                //             error
                //         }),
                //         debug: location.href
                //     });
                // }
                makeToast(error.statusText || '');
            }
            reject(error);
        });
    });
}
