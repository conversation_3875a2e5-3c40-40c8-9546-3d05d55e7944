/*
 * ------------------------------------------------------------------
 * 跳转、资源链接相关
 * ------------------------------------------------------------------
 */

import { URLParams } from ':common/env';
import { getAuthToken, openVipWebView, openWeb } from ':common/core';
import { login } from './features/login';
import { replace } from './features/jump';
import { getFromPathCode } from './stat';
import Texts from ':common/features/texts';
export const DIANPING_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/dianping.html';
export const DETAIL_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/detail.html';
export const STATUS_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/status.html';
export const COURSE_TEACH_URL = 'https://laofuzi.kakamobi.com/personal-training-live/';
export const ROUTE_STATUS_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/new/routeStatus.html';
export const BUYED_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/buyed.html';
export const SCORE12_BUYED_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/score12buySuccess.html';
export const DTJQ_PAGE_BUYED_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/dtjqPageSuccess.html';
export const PROTOCOL1_URL = 'https://laofuzi.kakamobi.com/protocol/protocol.html?protocolKey=jkbdVIP';
export const PROTOCOL2_URL = 'https://laofuzi.kakamobi.com/protocol/protocol.html?protocolKey=jkbdke3video';
export const PROTOCOL3_URL = 'https://laofuzi.kakamobi.com/protocol/protocol.html?protocolKey=jkbd3dHuiyuan';
export const PROTOCOL4_URL = 'https://laofuzi.kakamobi.com/protocol/protocol.html?protocolKey=studentMemberProtect';
export const PROTOCOL5_URL = 'https://laofuzi.kakamobi.com/protocol/protocol.html?protocolKey=jkbdPrivateAgreement';
export const BASEINFO_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/baseinfo.html';
export const INFO_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/insure/info.html';
export const BGBC_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/insure/bgbc.html';
export const RECOMMEND_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/recommend.html';
export const PAY_GUIDE_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/payguide.html';
export const QIAOJIINTRO_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/qiaojiintro.html';
export const COUPON_DETAIL_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/couponDetail.html';
export const STUDENT = 'https://laofuzi.kakamobi.com/jkbd-vip/index/student.html';
export const ADV_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/adv.html';
export const CLEAN_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/clean.html';
// 'https://laofuzi.kakamobi.com/jkbd-vip/index/couponDetail.html';
// 'http://*************:8080/couponDetail.html';
export const PLIVATE_TEACH_HOMEWORK = 'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-manual-score/plivateTeachHomeWork.html';
export const AUTH_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/detail.html';
export const HELP_VIP = 'https://laofuzi.kakamobi.com/jkbd-qa/index.html';
export const CHECK_VIP = 'https://laofuzi.kakamobi.com/jkbd-qa/vipCheck.html';
export const KQFD = 'https://laofuzi.kakamobi.com/jkbd-vip/index/kqfd.html';
export const KQFD_HISTORY = 'https://laofuzi.kakamobi.com/jkbd-vip/index/kqfdHistory.html';
export const EX_CHANGE_COUPON = 'https://laofuzi.kakamobi.com/jkbd-vip/index/exchangeCoupon.html';
export const KQFD_HISTORY_DIALOG = 'https://laofuzi.kakamobi.com/jkbd-vip/index/kqfdHistoryDialog.html';
export const ZXGK_HISTORY = 'https://laofuzi.kakamobi.com/jkbd-vip/index/zxgkHistory.html';
export const ZXGK_HISTORY_DIALOG = 'https://laofuzi.kakamobi.com/jkbd-vip/index/zxgkHistoryDialog.html';
export const ZDST_HISTORY = 'https://laofuzi.kakamobi.com/jkbd-vip/index/zdstHistory.html';
export const ZDST_HISTORY_DIALOG = 'https://laofuzi.kakamobi.com/jkbd-vip/index/zdstHistoryDialog.html';

export const VIP_LIVE = 'https://laofuzi.kakamobi.com/jiakaobaodian-zhibojian/vip-live.html';
export const SECRET = 'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-xgksmj/skill.html';

export const OUTLIMIT = 'https://laofuzi.kakamobi.com/jkbd-vip/index/outLimit.html';
/** 精简500，考试，密卷跳转地址 */
export const JING_JIANG = 'http://jiakao.nav.mucang.cn/difficultQuickPractice';
export const EXAM_JUAN =
    'http://jiakao.nav.mucang.cn/doExam?type=kaochangkaoshi&from=' + (URLParams.from || '');
export const MI_JIANG = 'http://jiakao.nav.mucang.cn/vip/difficultPractice';
/**
 * 落地页顶部视频  
*/
export const QK_VIDEO = `${Texts.TVHOSTMAP.maiche}/2024/01/09/4acfc7b9e0664fbcaa47e7947b2ddabc.high.mp4`;
export const TRUCK_QK_VIDEO = `${Texts.TVHOSTMAP.maiche}/2022-08-31/4019bdbcd23840d68c3ca6af76bea8f1.middle.mp4`;
export const CAR_KE1_VIDEO = `${Texts.TVHOSTMAP.maiche}/lsan/2023/12/21/674aec22e4b9447ebe94bc4e409cfcb9.higher.mp4`;
export const CAR_KE4_VIDEO = `${Texts.TVHOSTMAP.maiche}/2023/02/07/b63ee7fb9d324e04a95e79024ff90f47.middle.mp4`;
// `${Texts.TVHOSTMAP.maiche}/2023/02/07/b92181dc141f4fa580a0a03f873e0a00.middle.mp4`;
export const CAR_KE1AND4_VIDEO = '';
export const CAR_SESSION500_VIDEO = `${Texts.TVHOSTMAP.maiche}/2022/03/23/c7ea80f6a0564c3fbc53e7bd01fbb73e.middle.mp4`;
export const ELDER_QIAOJI = 'http://upload-video.mucang.cn/knowhere/2022/05/20/c1b965e12eb746a2851ac90b9bcc9d46.middle.mp4';

/**
 * 小窗视频  
*/

export const MOVE_GOODS_KEMU2 = `${Texts.TVHOSTMAP.maiche}/lsan/2024/03/12/71ff40ddc9e541c1bf335d7bc3d875dd.low.mp4`;
export const MOVE_GOODS_KEMU3 = `${Texts.TVHOSTMAP.maiche}/lsan/2024/03/12/802f69b88747447996d43bfad267e50f.middle.mp4`;

/** 已购买页学习步骤跳转链接（如精简500题等）科一科四 */
export const JING_JIAN = 'http://jiakao.nav.mucang.cn/difficultQuickPractice';
export const REAL_ROOM = 'http://jiakao.nav.mucang.cn/doExam?type=kaochangkaoshi&from=' + (URLParams.from || '');
export const MI_JUAN = 'http://jiakao.nav.mucang.cn/vip/difficultPractice';
export const FAMOUS_TEACHER = 'http://jiakao.nav.mucang.cn/famousTeacher';
export const SCORE12 = 'http://jiakao.nav.mucang.cn/vip/new-vip?from=92&page=%2fjkbd-vip%2fkouman12%2fbuy.html&pagename=score12';
/** 已购买学习步骤跳转链接 科二科三 */
export const STEP1_CAR = 'http://jiakao.nav.mucang.cn/exam-project-detail?kemu=kemu2&tiku=car&articleId=1542051&projectId=144';
export const STEP1_TRUCK = 'http://jiakao.nav.mucang.cn/exam-project-detail?kemu=kemu2&tiku=truck&articleId=536134&projectId=31';
export const STEP1_BUS = 'http://jiakao.nav.mucang.cn/exam-project-detail?kemu=kemu2&tiku=bus&articleId=536140&projectId=54';
export const STEP1_MOTO = 'http://jiakao.nav.mucang.cn/exam-project-detail?kemu=kemu2&tiku=moto&articleId=536140&projectId=54';
export const STEP2_CAR = 'http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fk2home%3ffrom%3d0039';
export const STEP2_TRUCK = 'http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fdk2home%3ffrom%3d0038%26licenseType%3d3';
export const STEP2_BUS = 'http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fdk2home%3ffrom%3d0105%26licenseType%3d4';
export const STEP3_BUS = 'http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fdk2home%3ffrom%3d0104%26licenseType%3d4';
export const STEP1_K3_CAR = 'http://jiakao.nav.mucang.cn/exam-project-detail?kemu=kemu3&tiku=car&articleId=1456426&projectId=127';
export const STEP1_K3_TRUCK = 'http://jiakao.nav.mucang.cn/exam-project-detail?kemu=kemu3&tiku=truck&articleId=536099&projectId=82';
export const STEP1_K3_MOTO = 'http://jiakao.nav.mucang.cn/exam-project-detail?kemu=kemu3&tiku=moto&articleId=536140&projectId=54';
export const STEP2_K3_CAR = 'http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fk3home%3ffrom%3d0040';
export const STEP2_K3_TRUCK = 'http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fdk3home%3ffrom%3d0303%26licenseType%3d3';
export const STEP3_K3_TRUCK = 'http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fdk3home%3ffrom%3d0304%26licenseType%3d3';
export const STEP4_K3_CAR = 'http://jiakao.nav.mucang.cn/examRouteLineVideo';
export const STEP1_K3_BUS = 'http://jiakao.nav.mucang.cn/exam-project-detail?kemu=kemu3&tiku=bus&articleId=536099&projectId=16';
export const STEP2_K3_BUS = 'http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fdk3home%3ffrom%3d0303%26licenseType%3d2';
export const STEP3_K3_BUS = 'http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fdk3home%3ffrom%3d0304%26licenseType%3d2';
export const STEP1_ZHIBOKE = 'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-must-learning/index.html';
/** 已购买页补偿信息补偿记录页面 */
export const LIPEI_RECORD_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/insure/compen.html';
export const OLD_LIPEI_RECORD_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/vip-lipei/list/list.html';
export const LIPEI_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/insure/compensation.html';
export const OLD_LIPEI_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/vip-lipei/index.html';
/** 已购买页长辈版学习步骤跳转 */
export const TOP_LESSON = 'http://jiakao.nav.mucang.cn/topLesson/detail';
export const AGED_500 = 'http://jiakao.nav.mucang.cn/aged_500';
export const MYERROR = 'http://jiakao.nav.mucang.cn/myError';
export const EXAM_RECORD = 'http://jiakao.nav.mucang.cn/record';
export const ICON_SHORTHAND = 'http://jiakao.nav.mucang.cn/icon-shorthand';
export const KNOWLEDGE_LIST = 'http://jiakao.nav.mucang.cn/knowledge-list';
// 考试分析页
export const PASS_ANALYSIS = 'https://laofuzi.kakamobi.com/jkbd-vip/index/passAnalysis.html';
/** 考前两小时试题合集页 */
export const PRACTICE_ALL = 'https://laofuzi.kakamobi.com/q-collection/practice.html';
/** 跳到客服帮助页 */
export async function goHelp() {
    const authToken = await getAuthToken();
    if (authToken) {
        openWeb({
            url: 'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-downApp/jkbdgw.html?open=true'
        });
    } else {
        await login();
    }
}

/** 转驾考宝典会员协议页面 */
export function openProtocol() {
    openWeb({
        url: PROTOCOL1_URL,
        title: URLParams._product + '会员协议'
    });
}

/** 转驾考宝典权益页面 */
export function openAuth(config: {
    groupKeys: string,
    groupKey: string
    authId: string
}) {
    openVipWebView({
        url: `${AUTH_URL}?groupKeys=${config.groupKeys}&groupKey=${config.groupKey}&code=${config.authId}&fromPathCode=${getFromPathCode()}&from=${URLParams.from}`
    });
}

export const changeSceneScore12 = () => {
    openWeb({
        url: 'http://jiakao.nav.mucang.cn/switchType?kemu=kemu1&sceneCode=102&backhome=0'
    });

    setTimeout(() => {
        replace(SCORE12_BUYED_URL, {
            sceneCode: 102
        });
    }, 500);
};
