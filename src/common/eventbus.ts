/*
 * ------------------------------------------------------------------
 * 事件管理
 * ------------------------------------------------------------------
 */

/**
 * 创建事件发射器
 * 
 * @param emitLatest 监听的时候是否告诉我最近的事件，用来实现类似store的效果
 */
export default function createEventBus<EventMap extends Record<string, any> = unknown>(emitLatest?: boolean) {
    type EventName = keyof EventMap;
    type Payload<K extends EventName> = EventMap[K];
    type CallBack<K extends EventName> = (e: EventMap[K]) => any
    type CallBacks<K extends EventName> = Array<CallBack<K>>

    const callbacks: { [K in EventName]?: CallBacks<K> } = {};
    const payloads: { [K in EventName]?: Payload<K> } = {};

    const eventbus = {
        on<K extends EventName>(eventName: K, callback: CallBack<K>): void {
            if (!callbacks[eventName]) {
                callbacks[eventName] = [];
            }
            if (emitLatest && eventName in payloads) {
                callback(payloads[eventName]);
            }
            (callbacks[eventName] as CallBacks<K>).push(callback);
        },
        off<K extends EventName>(eventName: K, callback: CallBack<K>): void {
            const arr = callbacks[eventName];
            if (!arr) {
                return;
            }
            const idx = (arr as CallBacks<K>).indexOf(callback);
            if (idx !== -1) {
                arr.splice(idx, 1);
                if (!arr.length) {
                    delete callbacks[eventName];
                }
            }
        },
        emit<K extends EventName>(eventName: K, e: EventMap[K]) {
            const arr = callbacks[eventName] || [];
            payloads[eventName] = e;
            (arr as CallBacks<K>).forEach(callback => callback(e));
        }
    };

    return eventbus;
}
