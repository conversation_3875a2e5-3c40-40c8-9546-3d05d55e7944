/*
 * ------------------------------------------------------------------
 * 业务无关的工具方法
 * ------------------------------------------------------------------
 */
import once from 'lodash/once';
import { Platform, URLCommon, URLParams } from './env';

/** url签名 */
export function sign(a): string {
    const c = Math.abs(parseInt((new Date().getTime() * Math.random() * 10000) + '')).toString();
    let d = 0;
    for (let b = 0; b < c.length; b++) {
        d += parseInt(c[b]);
    }
    const e = ((f) => {
        return (g, h) => {
            return ((h - 0 + g.length) <= 0) ? g : (f[h] || (f[h] = Array(h + 1).join('0'))) + g;
        };
    })([]);

    d += c.length;
    d = e(d, 3 - d.toString().length);
    return a.toString() + c + d;
}

/** 生成UUID */
export function getUUID() {
    const S4 = function () {
        // eslint-disable-next-line no-bitwise
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    };

    return (S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4());
}

/** 时间展示 */
export function dateFormat(dateValue: string | number | Date, formatStr = 'yyyy-MM-dd hh:mm', showDayType = 24) {
    let timestamp = new Date(dateValue);
    let fmt = formatStr;
    let k;
    const week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const o = {
        'M+': timestamp.getMonth() + 1,
        'd+': timestamp.getDate(),
        'h+': timestamp.getHours() % showDayType,
        'H+': timestamp.getHours(),
        'm+': timestamp.getMinutes(),
        's+': timestamp.getSeconds(),
        'q+': Math.floor((timestamp.getMonth() + 3) / 3),
        'S': timestamp.getMilliseconds(),
        'W+': week[timestamp.getDay()]
    };

    if (!dateValue) {
        return '';
    }

    if (typeof timestamp !== 'object') {
        timestamp = new Date(timestamp);
    }
    fmt = fmt || 'yyyy-MM-dd';

    if ((/(y+)/).test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (timestamp.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            // eslint-disable-next-line eqeqeq
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)));
        }
    }
    return fmt;

}

/** 时间段展示 */
export function dateDiffFormat(timestamp: number, formatStr = 'yyyy-MM-dd hh:mm') {
    const eightHours = 8 * 60 * 60 * 1000;
    return dateFormat(timestamp - eightHours, formatStr);
}

/** 价格展示 */
export function formatPrice(price: number) {
    let fmtPic;
    if (price % 100 > 0) {
        if ((price % 100) % 10 > 0) {
            fmtPic = (price / 100).toFixed(2);
        } else {
            fmtPic = (price / 100).toFixed(1);
        }
    } else {
        fmtPic = (price / 100).toFixed(0);
    }
    return fmtPic;
}

/** 取数字对应的中文表示 */
export function numZh(num: number): string {
    const dict = ['十', '一', '二', '三', '四', '五', '六', '七', '八', '九'];

    const tens = Math.floor(num / 10);
    const ones = num % 10;

    const t = tens ? (tens === 1 ? '' : dict[tens]) + dict[0] : '';
    const o = ones ? dict[ones] : '';

    return t + o;
}

/** 将木仓协议的调用转为promise形式 */
export function promisify(mcprotocolFn: AnyFunc = () => false) {
    return function (config?: any) {
        return new Promise<any>(resolve => mcprotocolFn({ ...config, callback: resolve }));
    };
}

/** 实现精准的读秒, 请在tick函数中自己取当前时间 */
export function ticker(tick: AnyFunc, duration = 1000) {
    let handle: number;
    function timer(time) {
        cancelAnimationFrame(handle);
        handle = requestAnimationFrame(timer);
        tick(time);
    }
    const interval = setInterval(timer, duration);
    handle = requestAnimationFrame(timer);

    return () => {
        clearInterval(interval);
        cancelAnimationFrame(handle);
    };
}

/** 将参数拼接成url需要的格式 */
export function objToParams(obj) {
    return Object.keys(obj).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`).join('&');
}

/**
 * 将16进制的颜色转为10进制的
 * @example `#ff0000` -> [255, 0, 0]
 */
export function colorHex2Dec(color: string) {
    color = color.substring(1);
    const r = color.substring(0, 2);
    const g = color.substring(2, 4);
    const b = color.substring(4, 6);

    return [parseInt(r, 16), parseInt(g, 16), parseInt(b, 16)];
}

/** 计算字符串中的中文个数，非中文算半个，如字母和数字 */
export function zhCount(str: string): number {
    const len = str.length;
    let count = 0;
    for (let i = 0; i < len; i++) {
        const charCode = str.charCodeAt(i);
        if (charCode >= 0x4e00 && charCode <= 0x9fa5) {
            count += 1;
        } else {
            count += 0.5;
        }
    }
    return count;
}

interface cityData {
    initial: Array<any>
}

// async function loadCityData(src): Promise<cityData> {
//     return new Promise((resolve) => {
//         window.getCDNCityData = function (cityData: cityData) {
//             resolve(cityData);
//         };
//         const script = document.createElement('script');
//         script.src = src;
//         document.body.appendChild(script);
//     });
// }

const loadCityData = once(async (src): Promise<cityData> => {
    return new Promise((resolve) => {
        window.getCDNCityData = function (cityData: cityData) {
            resolve(cityData);
        };
        const script = document.createElement('script');
        script.src = src;
        document.body.appendChild(script);
    });
});

export async function getCityName(code) {
    const cityMap = {};

    const cityData = await loadCityData('https://share-m.kakamobi.com/activity.kakamobi.com/qichebaojiazhijia-base-common/get-city/data/city.js');

    const initial = cityData.initial;

    initial.forEach(item => {
        item[1].forEach(icdata => {
            icdata.citys.forEach(city => {
                cityMap[city.code] = city.name;
            });
        });
    });

    return cityMap[code];
}

export const deleteEmpty = (obj) => {
    for (const key in obj) {
        if (obj[key] === null || obj[key] === undefined) {
            delete obj[key];
        }
    }
    return obj;
};
// 倒计时，大于3天，小于3天的不同展示
export const getHMT = (leftTime: number) => {
    let day: any = Math.floor((leftTime) / (60 * 60 * 1000 * 24));

    let hour: any = Math.floor((leftTime - (day * 60 * 60 * 1000 * 24)) / (60 * 60 * 1000) % 24);
    let min: any = Math.floor((leftTime - (day * 60 * 60 * 1000 * 24) - (hour * 60 * 60 * 1000)) / (60 * 1000));
    let sec: any = Math.floor((leftTime - (day * 60 * 60 * 1000 * 24) - (hour * 60 * 60 * 1000) - (min * 60 * 1000)) / (1000));

    if (hour < 10) {
        hour = '0' + hour;
    }
    if (min < 10) {
        min = '0' + min;
    }
    if (sec < 10) {
        sec = '0' + sec;
    }

    if (day <= 0) {
        return hour + ':' + min + ':' + sec;
    } else if (day >= 3) {
        day += '天';
        return day + hour + ':' + min + ':' + sec;
    }
    return ((day * 24) + parseInt(hour)) + ':' + min + ':' + sec;
};

/** 从文案中提取价格 */
export function getPrice(str: string) {
    return Number(str.match(/(\d+)/)[1]);
}

export function timeout(duration: number) {
    return new Promise(resolve => setTimeout(resolve, duration));
}

/**
 * 格式化图片（ios13以下不支持webp，服务端返回的图片很多是通过webp压缩的）
*/

export function calcImg(imgUrl) {

    if (!Platform.isSupportWebp) {
        return imgUrl.replace(/\.(jpg|png|jpeg)!(.+)/, '.$1');
    }
    return imgUrl;
}
// 剩余天数
export const getHasDay = (leftTime: number) => {
    const day: any = Math.floor(leftTime / (60 * 60 * 1000 * 24));
    return day;
};
export function clacRem(number: number) {
    // const pxFontSize = document.querySelector('html').style.fontSize;
    // const baseFontSize = +pxFontSize.split('px')[0];
    // return number / 100;
    const maxWidth = URLParams.passShowType === 'dialog' ? 480 : 1200;
    return (number / 750) * Math.min(document.documentElement.clientWidth, maxWidth) * 2;
}

export function dayDiff(date: number) {
    const dateNow = new Date();
    dateNow.setHours(0);
    dateNow.setMinutes(0);
    dateNow.setSeconds(0);
    dateNow.setMilliseconds(0);

    const dateOther = new Date(date);
    dateOther.setHours(0);
    dateOther.setMinutes(0);
    dateOther.setSeconds(0);
    dateOther.setMilliseconds(0);

    const time = dateOther.getTime() - dateNow.getTime();
    const dayMills = 24 * 60 * 60 * 1000;
    return time / dayMills;
}
export const formatNumber = (n: number) => {
    const s = n.toString();
    return s[1] ? s : '0' + s;
};

export function showClock(time: number, format = 'mm:ss') {
    const hh = formatNumber(Math.floor(time / 3600));
    const mm = formatNumber(Math.floor(Math.floor(time % 3600) / 60));
    const ss = formatNumber(Math.floor(time % 60));
    let str = '';

    if (format.indexOf('hh') > -1) {
        str += hh + ':';
    }
    if (format.indexOf('mm') > -1) {
        str += mm + ':';
    }
    if (format.indexOf('ss') > -1) {
        str += ss;
    }
    return str;
}
export function handleIosZigezhen(url: string) {
    // 兼容ios资格证全局科目的问题，学习步骤协议跳转带了kemuStyle=1,ios不识别导致报错，前端兼容下
    if (URLCommon.isZigezheng && Platform.isIOS) {
        location.href = url;
        return true;
    }
    return false;

}

export function mergeObjects(obj1, obj2 = {}) {
    for (const key in obj2) {
        if (obj2[key] !== null && obj2[key] !== undefined) {
            obj1[key] = obj2[key];
        }
    }
    return obj1;
}