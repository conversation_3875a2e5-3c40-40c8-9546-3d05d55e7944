/*
 * ------------------------------------------------------------------
 * 函数式编程相关
 * ------------------------------------------------------------------
 */

export function constValue<T>(value: T) {
    return function () {
        return value;
    };
}

export function id<T = any>() {
    return function (value: T) {
        return value;
    };
}

export function partial<L extends unknown[], R extends unknown[], T>(fn: (...args: [...L, ...R]) => T, ...largs: L) {
    return function (...rargs: R) {
        return fn(...largs, ...rargs);
    };
}

export function memoArgs<T extends AnyFunc>(fn: T, context?: any) {
    let lastArgs;

    const ret: T & { invokeLastCall: () => any } = function (...args) {
        lastArgs = args;
        fn.apply(context, args);
    } as any;

    ret.invokeLastCall = function () {
        if (lastArgs) {
            return ret(lastArgs);
        }
        throw new Error('函数之前未被调用过');
    };
    return ret;
}

// 根据参数缓存，给request用，防止重复请求
export function memoizeParams<T extends AnyFunc>(fn: T) {
    const cache = {};
    return function (...args) {
        // 使用args作为缓存键
        const key = JSON.stringify(args);
        // 检查缓存中是否有结果
        if (cache[key]) {
            return cache[key];
        }
        // 执行函数并缓存结果
        const result = fn.apply(this, args);
        cache[key] = result;
        return result;
    } as T;
}