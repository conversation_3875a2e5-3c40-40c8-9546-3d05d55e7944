/* eslint-disable */
(function () {
    var VERSION = '2308101500'
    var Timer = 3000
    var TimerMore = 7000
    // 1024 * 5
    var ResUrlLimit = 5120
    // 最大错误上报数
    var errLimit = 100
    var isLocal = false; !!window.location.href.match(/192\.168|localhost|172\.20|127\.0\.0\.1/gi)

    var pgUrl = window.location.href.replace(/(\?|#).*/gi, '').replace(/.*\/\//gi, '')
    var pathUrl = (pgUrl.slice(0, pgUrl.lastIndexOf('/') + 1) || '').replace(/\/+$/gi, '')

    var UASTR = window.navigator.userAgent || '';
    var isInApp = (!!(UASTR.toLowerCase().match(/mucang/g) || window.MCProtocolIosExecute || window.getMucangIOSWebViewData)) && !UASTR.toLowerCase().match(/\.mucang\./g);

    // 不计入统计的指标
    var excludeKeys = ['transfersize', 'decodedbodysize', 'encodedbodysize']
    var MCPerf = function () {
        var that = this
        var listen
        // 是否发送 ready 请求
        var readySent = 0;

        window.MCPerfV = VERSION;

        that.reset()
        // 简单判断是否是vue项目
        that.isVue = false

        listen = function () {
            if (that.isListening || !that.isVue) {
                clearTimeout(that.readyTimer)

                return false;
            }

            that.isListening = true

            clearTimeout(that.readyTimer)
            // 不延时会获取不到正确的hash url
            that.readyTimer = setTimeout(function () {
                that.reset()
                that.init('ready')

                setTimeout(function () {
                    that.init('timeout')
                }, Timer);

                that.isListening = false
            }, 1000)
        }

        // ready 上报
        document.addEventListener('readystatechange', function (event) {
            var docStr
            var headStr

            // 只执行一次
            if (document.readyState === 'interactive' || document.readyState === 'complete') {
                readySent++
            }

            // 只执行一次
            if (readySent === 1) {
                docStr = (document.body && document.body.innerHTML) || ''
                headStr = (document.head && document.head.innerHTML) || ''

                that.isVue = !!docStr.match(/chunk-.*\.js/gi) ||
                    !!headStr.match(/paas/) ||
                    !!headStr.match(/.vue.*?.js/)
                that.init('ready')

                setTimeout(function () {
                    that.init('timeout')
                }, Timer);

                // 资源加载监听
                that.observer();

                if (that.isVue) {
                    // vue3 hash 优先使用 history 实现，所以重写 history 以便监听
                    history.pushState = that.his('pushState')
                    window.addEventListener('pushState', function () {
                        listen()
                    })
                }
            }
        });

        // 兼容vue2 hashchange
        window.addEventListener('hashchange', function () {
            listen()
        })
    };
    var TrackErr = function () {
        if (!isLocal) {
            this.init();
        }
    };

    // 格式化数字结果
    MCPerf.prototype.format = function (n) {
        return parseInt((n || 0).toFixed(0))
    }
    // 数据和状态重置
    MCPerf.prototype.reset = function () {
        var that = this;

        // 保存已经上报的记录，防止手动调用pageChange或report时，资源重复上报
        that.resAllLen = 0
        // 网页性能已经上报了几次，最多只能上报2次：ready 和 Timer 后各一次
        that.pageReportedIdx = 0
        // 所有内存存储的资源
        that.allRess = []
        // 清空定时器
        that.readyTimer = that.obsTimer = null
        // 清空performance缓冲区资源
        window.performance.clearResourceTimings();
    }
    // 重新定义 history，以便拦截路由变化
    MCPerf.prototype.his = function (type) {
        try {
            var before = history[type];

            return function () {
                var event = new Event(type)

                event.arguments = arguments
                window.dispatchEvent(event)

                return before.apply(this, arguments)
            }
        } catch (error) {
            console.log(error);
        }
    }
    // 资源监听
    MCPerf.prototype.observer = function () {
        var that = this;

        if (that.Observer) {
            return false;
        }

        that.obsTimer = null
        // 所有内参存储的资源
        that.allRess = that.allRess || []

        try {
            that.Observer = new PerformanceObserver(function (list) {
                var tmpRes = list.getEntriesByType('resource') || [];

                that.allRess = that.allRess.concat(tmpRes)
                clearTimeout(that.obsTimer)
                that.obsTimer = setTimeout(function () {
                    that.init('report')
                }, Timer + TimerMore)
            });

            that.Observer.observe({
                entryTypes: ['resource']
            });

            // 浏览器保存的资源信息是有大小限制的，超过一定数量，剩下的资源就算加载，也不会被记录 performance.getEntries() 就获取不到了。 chrome 中，最大缓冲资源数是 250个
            // 对缓冲区进行监听，当缓冲区满了之后，就清空资源
            window.performance.addEventListener('resourcetimingbufferfull', function () {
                window.performance.clearResourceTimings();
                that.resAllLen = 0
                that.allRess = []
            });

        } catch (error) {
            console.warn('PerformanceObserver not support');
        }
    }
    // 初始化
    MCPerf.prototype.init = function (type) {
        var that = this
        var perf
        var params = {
            strs: {},
            numbers: {}
        }
        var t = {}

        var resAll
        var isReady = type === 'ready'
        var isManualReport = type === 'report'
        var isTimeout = type === 'timeout'
        var isResource = isTimeout || isManualReport

        var i
        var len
        var resItem
        var resPath = []
        var resTime = []

        var w;
        // 是否有页面资源数据
        var hasRes = false
        // 是否有页面具体性能指标数据
        var hasPerf = false

        var idRegx
        var defaultPage = 'index.html'

        // domReady 值来源于 DomContentLoadedEventEnd
        // onload 值来源于 LoadEventEnd
        var timeoutKeys = ['domContentLoadedEventEnd', 'loadEventEnd']

        if (isLocal) {
            return false;
        }

        // report 转换为 timeout，保持事件简洁
        if (isManualReport) {
            type = 'timeout'
        }

        try {
            perf = that.pageReportedIdx < 2 ? window.performance.getEntriesByType('navigation') : []
            t = (perf && perf[0]) || {}
            resAll = isReady ? [] : that.allRess

            // 当前页面不带 ? 或 # 且不带 xxx.html 的 url。保证 id 和服务器管理系统 domain 字段一致
            params.strs.id = pathUrl
            // 当前页面不带 ? 或 # 的 url
            params.strs.page = that.isVue ? window.location.href.replace(/\?.*\#/gi, '#').replace(/.*\/\/|\?.*/gi, '') : pgUrl
            // 版本号
            params.strs.v = VERSION

            idRegx = new RegExp(pathUrl, 'ig')
            // 为减小page参数长度，去掉id和page的相同部分，在后台数据报告显示时再拼接
            params.strs.page = ((params.strs.page || '').replace(idRegx, '').replace(/^\//gi, '')
                || defaultPage)
            // 是否从本地缓存加载的网页。该字段只单纯上报，【数据报告】里不做任何数据分析 (20220803 增加)
            params.strs.localCached = that.checkCache(pathUrl, ((pgUrl || '').replace(idRegx, '') || defaultPage))

            // 单位毫秒
            if (!isManualReport) {
                for (w in t) {
                    if (typeof t[w] === 'number' && excludeKeys.indexOf(w.toLowerCase()) === -1) {
                        var timeoutKeyVal = 0

                        hasPerf = true

                        // 如果跟网页加载完成相关的指标值取不到，则认为超过3秒，即给个默认值
                        if (isTimeout && timeoutKeys.indexOf(w) !== -1 && !t[w]) {
                            timeoutKeyVal = (Timer + 1)
                        }

                        // 如果指标取不到（比如网速很慢的情况下，3秒后取指标大概率值为0），给个超过3秒的默认值
                        params.numbers['page_' + w] = that.format(t[w] || timeoutKeyVal)
                    }
                }
            }

            if (isResource && resAll && resAll.length) {
                var tmpRelAllLen = resAll.length
                var resAllParam
                var tmpResAll = []

                // 扔掉已统计的资源
                tmpResAll = resAll.slice(that.resAllLen, resAll.length)

                // 接收和处理APP内通过木仓协议发送的请求
                if (that.resApiOpen && that.resApiOpen.length) {
                    tmpResAll = tmpResAll.concat(that.resApiOpen)
                    // 置空
                    that.resApiOpenIds = []
                    that.resApiOpen = []
                }

                for (i = 0, len = tmpResAll.length; i < len; i++) {
                    var resItemName
                    var resExcludeReg = new RegExp('favicon\\.ico|oort-shipper\\.|perf.*\\.js|api\/paas\/core\/comet\/notice\.htm|.*\\.htm', 'gi')
                    // 资源捕获采用域名白名单
                    var whiteDomainReg = new RegExp('.*(((kakamobi|mucang|jiakaobaodian|jiaxiaozhijia|xiaozhu2|pingxing|jiaxiaobang|tufu|baodianjiaoyu|gongkaobaodian|jiakaogongju|jiakaoxinxi|kaozhengbaodian|baodian|mc-cdn|futurephantom)\.(com|cn|net)))', 'gi');
                    var resParam
                    var resItemDuration

                    resItem = tmpResAll[i] || {}
                    resItemName = (resItem.name || '')
                    // 如果没有取到耗时，给个默认值 Timer+1 秒
                    resItemDuration = that.format(resItem.duration || (Timer + TimerMore + 1))

                    resParam = encodeURIComponent(resItemName.match(/\?/gi) ? resItemName.replace(/.*\?/gi, '') : '')

                    // 删除资源路径http https前缀和所有参数，保持路径简洁
                    resItemName = resItemName.replace(/.*\/\//gi, '').replace(/(\?|#).*/gi, '')

                    if (resItemName && resItem.entryType === 'resource' && !resItemName.match(resExcludeReg) && resItemName.match(whiteDomainReg) && resItemDuration > 0) {
                        that.resAllLen = tmpRelAllLen
                        resPath.push(resItemName)
                        resTime.push(resItemDuration)

                        // 上报资源中接口类型的请求参数，便于数据报告中复现和定位接口问题
                        if (resItemName.match(/\.htm/gi)) {
                            resAllParam = resAllParam || {}
                            resAllParam[resItemName] = resAllParam[resItemName] || []
                            if (resParam && resAllParam[resItemName].indexOf(resParam) === -1) {
                                // 防止超长引起 oort 服务器错误
                                resAllParam[resItemName].push((resParam.length > ResUrlLimit) ? (resParam.slice(0, ResUrlLimit) + '...') : resParam)
                            }
                        }
                    }
                }

                if (resPath.length) {
                    hasRes = true
                    params.strs.res = JSON.stringify(resPath)
                    params.strs.resTime = JSON.stringify(resTime)
                    params.strs.resParam = resAllParam ? JSON.stringify(resAllParam) : ''
                }
            }

            // 存在有用数据才上报
            if (hasRes || hasPerf) {
                that.pageReportedIdx++
                that.send(params, type)
            }
        } catch (error) {
            console.log(error)
        }

        return null;
    }
    /**
     * 检测网页是否从缓存加载
     */
    MCPerf.prototype.checkCache = function (path, pg) {
        var cacheTime = +new Date()
        // 页面是否有变化
        var isChanged = false
        // path 转换为 localStorage 的 key
        var pathKey = (path || '').replace(/\//gi, '-').replace(/\./gi, '_').replace(/\:/gi, '__')
        // 缓存结果
        var cacheRst
        // 当前页面的 performance 数据
        var pathPerfRst
        // 当前页面 transferSize
        var pathTransSize
        // 当前页面 encodedBodySize
        var pathBodySize
        var isExpired = this.clearCache(pathKey)

        // 是否走的缓存
        this.isCached = this.isCached || null

        if (path && pg && this.isCached === null) {
            pathPerfRst = performance.getEntriesByType('navigation')[0] || {}

            pathTransSize = pathPerfRst.transferSize
            pathBodySize = pathPerfRst.encodedBodySize

            cacheRst = JSON.parse(localStorage.getItem(pathKey) || '{}')
            cacheRst[pg] = parseInt(cacheRst[pg] || '0')

            // 取不到 transferSize 和 encodedBodySize 就认为没缓存
            if ((pathTransSize || pathTransSize === 0) && (pathBodySize || pathBodySize === 0)) {
                // 没有 encodedBodySize 或者 2个 encodedBodySize 不等，说明页面有更新
                if (!cacheRst[pg + '_body'] || cacheRst[pg + '_body'] !== pathBodySize) {
                    isChanged = true
                }

                // 如果页面重新发布了，说明没有缓存了
                if (isChanged) {
                    this.isCached = false
                    cacheRst[pg + '_body'] = pathBodySize
                    if (isExpired) {
                        cacheRst.time = cacheTime
                    }
                    localStorage.setItem(pathKey, JSON.stringify(cacheRst))
                } else {
                    // pathTransSize 小于 cacheRst[pg] 的一半，说明从缓存加载的
                    this.isCached = cacheRst[pg] && pathTransSize < (cacheRst[pg] / 2)

                    // 对应页面key的值没有，或者对应页面的transferSize大于缓存的值，则更新缓存
                    if (!cacheRst[pg] || !this.isCached) {
                        cacheRst[pg] = pathTransSize
                        if (isExpired) {
                            cacheRst.time = cacheTime
                        }
                        localStorage.setItem(pathKey, JSON.stringify(cacheRst))
                    }
                }
            }

        }

        return this.isCached ? 'true' : 'false'
    }
    // 清除 checkCache 产生的过旧缓存
    MCPerf.prototype.clearCache = function (localKey) {
        var timeNow = +new Date()
        // 最大缓存7天
        var timeLimit = 7 * 24 * 60 * 60 * 1000;

        var localVal
        var localTime

        var lKey
        var cacheExpired = false

        try {
            if (localKey) {
                localVal = JSON.parse(localStorage.getItem(localKey) || '{}')
                localTime = localVal.time || ''

                if (!localTime || ((timeNow - localTime) > timeLimit)) {
                    cacheExpired = true

                    for (lKey in localStorage) {
                        if (lKey && lKey.match(/_kakamobi_com-|_jiakaobaodian_com-|_mucang_cn-/gi)) {
                            localStorage.removeItem(lKey)
                        }
                    }
                }
            }
        } catch (error) {
            console.log(error);
        }

        return cacheExpired
    }

    // 提供主动上报功能：解决vue类项目hash路由页面切换后的资源加载统计问题
    // 2022年05月11日 采用 PerformanceObeserver 方案后，已无需该方案
    MCPerf.prototype.pageChange = function () {
        console.warn('MCPerf pageChange event listener is no needed any more');
    }
    // 获取url参数
    MCPerf.prototype.getURLParams = function (keyFilter) {
        var params = {}

        window.location.href.replace(/[#|?&]+([^=#|&]+)=([^#|&]*)/gi, function (m, key, value) {
            if (value === 'null' || value === '(null)' || value === 'undefined') {
                value = ''
            }

            params[key] = decodeURIComponent(value)

            if (keyFilter && key && key.indexOf(keyFilter) === -1) {
                delete params[key]
            }
        })

        return params
    }
    // 获取系统平台
    MCPerf.prototype.getPlatform = function () {
        var platform = ''

        if (window.navigator.userAgent.indexOf('Android') !== -1) {
            platform = 'Android'
        } else if (window.navigator.userAgent.indexOf('iPhone') !== -1) {
            platform = 'iPhone'
        } else if (window.navigator.userAgent.indexOf('Windows') !== -1) {
            platform = 'Windows'
        } else if (window.navigator.userAgent.indexOf('iPad') !== -1) {
            platform = 'iPad'
        } else {
            platform = 'Other'
        }

        return platform.toLocaleLowerCase()
    }
    // 生成唯一用户id
    MCPerf.prototype.genUId = function () {
        // eslint-disable-next-line func-style
        function S4() {
            // eslint-disable-next-line no-bitwise
            return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
        }

        return (S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4())
    }
    // 检测或新建用户唯一id
    MCPerf.prototype.setAppUser = function (name) {
        var urlParams = this.getURLParams('_') || {}
        var urlValue = urlParams[name] || ''
        var localName = ['MCH5Perf', name].join('_')
        var appUser = window.localStorage.getItem(localName)

        if (!appUser || urlValue) {
            appUser = urlValue || this.genUId()
            window.localStorage.setItem(localName, appUser)
        }

        return appUser
    }
    // H5签名
    MCPerf.prototype.sign = function (a) {
        var c = Math.abs(parseInt(new Date().getTime() * Math.random() * 10000))
            .toString();
        var d = 0;
        var b;
        var e;

        for (b = 0;
            b < c.length;
            b++) {
            d += parseInt(c[b]);
        }
        e = (function (f) {
            return function (g, h) {
                return ((h - '' + g.length) <= 0) ? g : (f[h] || (f[h] = Array(h + 1)
                    .join(0))) + g;
            };
        })([]);
        d += c.length;
        d = e(d, 3 - d.toString().length);

        return a.toString() + c + d;
    }
    // 对象转url参数
    MCPerf.prototype.object2param = function (obj) {
        var arr = []

        for (var key in obj) {
            if (key) {
                arr.push(key + '=' + encodeURIComponent(obj[key]))
            }
        }
        return arr.join('&')
    }
    // 发送数据
    MCPerf.prototype.send = function (data, evt) {
        var that = this
        var params
        var urlParams = that.getURLParams('_') || {}
        var localAppBaseParams = JSON.parse(localStorage.getItem('MC_G_ABP') || '{}') || {}
        var dataToSend = []

        var platform = that.getPlatform() || ''

        // 数据上报 oort group 恒定为 MCH5Perf，随意修改会导致接口无法获取数据
        var name = 'MCH5Perf'

        if (!urlParams._deviceId && localAppBaseParams._deviceId) {
            urlParams = localAppBaseParams
        }

        // 删除会影响请求的基础参数
        delete urlParams._r
        delete urlParams._v

        urlParams._platform = platform
        urlParams._appName = urlParams._appName || ''
        urlParams._appUser = urlParams._appUser || that.setAppUser('_appUser')
        urlParams._deviceId = urlParams._deviceId || that.setAppUser('_deviceId')
        // _productCategory 必须固定写 MCH5Perf，这样数据会全进入 oort other 表。
        // _productCategory 如果根据 url 参数获取，上传数据会分散到不同表，导致数据报告界面无法显示所有上报数据。
        urlParams._productCategory = name
        // id和page相同，但_product不同会算多条记录；所以为了让id和page相同时只有唯一记录，_product固定为MCH5Perf
        // 2022-08-01 15:12:45 _product 改为动态
        urlParams._product = urlParams._product || name
        urlParams._r = that.sign(1)

        params = that.object2param(urlParams)

        dataToSend = {
            group: name,
            event: name + '_' + evt,
            timestamp: new Date().getTime(),
            properties: data || {}
        }

        // app 内通过 app 发请求
        // if (isInApp) {
        //     return that.sendByApp(dataToSend);
        // }
        console.log(data, 33333333333)
        that.ajax({
            method: 'post',
            data: {
                data: JSON.stringify(data)
            },
            url: 'http://172.20.104.21:3500/upimage?' + params,
            dataType: 'json',
            async: true,
            success: function (ret) { },
            error: function () { }
        })
    }
    // 执行 app 协议
    MCPerf.prototype.execApp = function (group, modules, method, userParams) {
        try {
            var GetSystem = function () {
                if (
                    isInApp ||
                    window.MCProtocolIosExecute ||
                    window.MCProtocolAndroidExecute ||
                    window.mcAndroidWebview1 ||
                    window.getMucangIOSWebViewData
                ) {
                    if (navigator.userAgent.indexOf('iPhone') > -1 || navigator.userAgent.indexOf('iPad') > -1) {
                        return 'iphone';
                    }
                    if (navigator.userAgent.indexOf('Android') > -1) {
                        return 'android';
                    }
                    return 'ipad';
                }
                return 'other';
            };
            var BuildUrl = function (group, modules, method) {
                if (modules) {
                    return 'https://' + group.toLowerCase() + '.luban.mucang.cn/' + modules.toLowerCase() + '/' + method;
                }
                return 'https://' + group.toLowerCase() + '.luban.mucang.cn/' + method;
            };
            var ObjectToParams = function (params) {
                var paramString = [];
                var e;
                var paramsType;
                for (e in params) {
                    paramsType = typeof params[e];
                    if (paramsType === 'string' || paramsType === 'number') {
                        paramString.push(e + '=' + encodeURIComponent(params[e]));
                    } else {
                        paramString.push(e + '=' + encodeURIComponent(JSON.stringify(params[e])));
                    }
                }
                return paramString;
            };
            var ParseParams = function (params) {
                var paramString = [];
                var e;
                if (params) {
                    for (e in params) {
                        switch (e) {
                            case 'config':
                                paramString = paramString.concat(ObjectToParams(params[e]));
                                break;
                            case 'callback':
                            case 'callbackName':
                                break;
                            default:
                                if (typeof params[e] === 'object') {
                                    paramString.push(e + '=' + encodeURIComponent(JSON.stringify(params[e])));
                                } else {
                                    paramString.push(e + '=' + encodeURIComponent(params[e]));
                                }
                        }
                    }
                    return paramString.join('&');
                }
                return '';
            };
            var GetCallbackName = function (params) {
                var e;
                if (params) {
                    for (e in params) {
                        if (e === 'callbackName') {
                            return params[e];
                        }
                    }
                }
                return null;
            };
            var GetCallback = function (params) {
                var e;
                if (params) {
                    for (e in params) {
                        if (e === 'callback') {
                            return params[e];
                        }
                    }
                }
                return null;
            };
            var IosExecute = function (url, params, callback, callbackName) {
                if (window.MCProtocolIosExecute) {
                    return window.MCProtocolIosExecute(url + (params ? '?' + params : ''), BuildCallback(callback, callbackName));
                }
                return window.getMucangIOSWebViewData(url + (params ? '?' + params : ''), BuildCallback(callback, callbackName));
            };
            var AndroidExecute = function (url, params, callback, callbackName) {
                // 木仓小程序兼容木仓协议，但是木仓协议不能运行小程序事件
                if (window.MCProtocolAndroidExecute) {
                    return window.MCProtocolAndroidExecute.exec(url + (params ? '?' + params : ''), BuildCallback(callback, callbackName) || 'NULL');
                }
                return window.mcAndroidWebview1 && window.mcAndroidWebview1.getMucangWebViewData(url + (params ? '?' + params : ''), BuildCallback(callback, callbackName) || 'NULL');
            };
            var BuildCallback = function (callback, callbackName) {
                return null;
            };

            var url = BuildUrl(group, modules, method, userParams);
            var params = ParseParams(userParams);
            var callback = GetCallback(userParams);
            var callbackName = GetCallbackName(userParams);

            switch (GetSystem()) {
                case 'iphone':
                    return IosExecute(url, params, callback, callbackName);
                case 'android':
                    return AndroidExecute(url, params, callback, callbackName);
                default:
                    BuildCallback(callback, callbackName);
            }
        } catch (error) {
            console.log('execApp err', error);
        }
    }
    // 通过 app 发送打点数据
    MCPerf.prototype.sendByApp = function (data) {
        var that = this;

        that.execApp('Core', 'System', 'alert', {
            title: '标题',
            message: '这是一个提示'
        })

        that.execApp('Core', 'System', 'stat', {
            eventId: data.group,
            eventName: data.event,
            eventLabel: data.event,
            properties: data.properties,
            needCommon: data.needCommon || 1
        })
    }
    MCPerf.prototype.ajax = function (obj) {
        var xhr = new XMLHttpRequest()

        // eslint-disable-next-line func-style
        function callback() {
            if (xhr.status === 200) {
                try {
                    obj.success(JSON.parse(xhr.responseText))
                } catch (e) {
                    console.log(e)
                }
            } else {
                obj.error(xhr.status, xhr.statusText)
            }
        }

        if (typeof obj.data === 'object' && obj.contentType !== false) {
            obj.data = this.object2param(obj.data)
        }

        if (obj.method === 'get') {
            obj.url = obj.url.indexOf('?') === -1 ? obj.url + '?' + obj.data : obj.url + '&' + obj.data;
        }
        if (obj.async === true) {
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    callback()
                }
            }
        }

        xhr.open(obj.method, obj.url, true)
        xhr.withCredentials = true
        if (obj.contentType !== false) {
            xhr.setRequestHeader('Content-Type', obj.contentType || 'application/x-www-form-urlencoded')
        }

        if (obj.method === 'post') {
            xhr.send(obj.data)
        } else {
            xhr.send(null)
        }

        if (obj.async === false) {
            callback()
        }
    }
    TrackErr.prototype.init = function () {
        var that = this
        // 发生错误时的环境信息
        var client = {
            a: navigator.userAgent,
            // 会带有问号参数，需转义
            b: encodeURIComponent(window.location.href),
            c: +new Date()
        }

        // 同步代码异常报错
        window.addEventListener('error', function (e) {
            var excludeMsgs = ['Script error.', 'ResizeObserver loop limit exceeded', 'ResizeObserver loop completed with undelivered notifications.'];

            e = e || {};
            e.message = e.message || '';

            if (errLimit-- < 0 || !e.message || excludeMsgs.indexOf(e.message) !== -1 || (e.source && e.source.indexOf('chrome-extension://') !== -1)) {
                console.log('此种错误过滤掉');
            } else {
                that.send({
                    type: 'onerror',
                    data: {
                        message: e.message || '',
                        source: encodeURIComponent(e.filename || ''),
                        lineno: e.lineno || '',
                        colno: e.colno || '',
                        error: e.error,
                        client: client
                    }
                })
            }
        });

        // 未处理的Promise错误
        window.addEventListener('unhandledrejection', function (e) {
            if (errLimit-- > 0) {
                that.send({
                    type: 'unhandledrejection',
                    data: {
                        message: e.reason || '',
                        client: client
                    }
                })
            }
        });
    };

    TrackErr.prototype.send = function (data) {
        // 根据错误生成id
        var getErrId = function (errObj) {
            var errTmpId = ''
            var errKey
            var errItem

            errObj = errObj || {}

            for (errKey in errObj) {
                errItem = errObj[errKey] || ''
                errTmpId += encodeURIComponent(typeof errItem === 'object' ? JSON.stringify(errItem) : errItem)
            }

            return errTmpId
        }

        // 已上报过的错误ids
        this.errIdsSended = this.errIdsSended || []

        data = data || {}
        data.data = data.data || {}
        data.data.message = JSON.stringify(data.data.message || '')
        data.data.v = VERSION

        // 防止超长引起 oort 服务器错误
        if (data.data.message.length > ResUrlLimit) {
            data.data.message = data.data.message.slice(0, ResUrlLimit) + '...'
        }

        // 当前准备上报的错误id
        this.curErrId = getErrId(data.data)

        // 优化上报规则
        if (this.curErrId && this.errIdsSended.indexOf(this.curErrId) !== -1) {
            return false;
        }

        if (this.curErrId) {
            this.errIdsSended.push(this.curErrId)
        }

        window.MCPerf.send({
            strs: {
                id: pathUrl,
                page: pgUrl,
                type: JSON.stringify([data.type || '']),
                error: JSON.stringify([data.data || ''])
            }
        }, 'error')
    };

    window.MCPerf = new MCPerf()
    new TrackErr()

})();