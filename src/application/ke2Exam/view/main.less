.ke2-exam-page{
    height: 100%;
    display: flex;
    flex-direction: column;
    .header {
        position: absolute;
        z-index: 1000;
        top: 0;
        left: 0;
        right: 0;
    }
    .main{
        background: #1A1B28;
        flex: 1;
        height: 0;
        overflow: auto;
        -webkit-overflow-scrolling: touch;
        
        .top{
            width: 375px;
            height: 447px; 
            padding: 192px 24px 0 25px;
            display: flex;
            justify-content:space-between;
            position: relative;
            &::after{
                content: " ";
                width:100%;
                height: 100%;
                background: url(../images/header.png) no-repeat;
                background-size: 375px 447px;
                position:absolute;
                top: 0;
                left:0;
                right: 0;
                pointer-events: none;
            }
            .video-wraper{
                width: 326px;
                height: 183px;
           
                position:absolute;
                top: 192px;
                left: 50%;
                transform: translateX(-50%);
                video{
                    width: 100%;
                    height: 100%;
                }
                .video-cover{
                    position:absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    width: 326px;
                    height: 183px;
                    background:url(../images/1.jpg) no-repeat;
                    background-size: 100% 100%;
                    &::before{
                        content: "";
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 50px;
                        height:50px;
                        background: url(../images/2.png) no-repeat;
                        background-size: 100% 100%;
                    }
                }
            }
            .cityname{
                position: absolute;
                top: 60px;
                left: 52px;
                font-size: 16px;
                line-height:26px;
                color: #5B2816;
                height: 26px;
                text-align:center;
                padding:0 12px;
                background:linear-gradient(90deg,#F9E5D5 0%,#DD9E75 100%);
                border-radius:3px;
            }
            .title{
                width: 227px;
                height: 42px;
                background: url(../images/title.png) no-repeat;
                background-size: 100% 100%;
                text-align:center;
                font-size: 22px;
                color: #FFFFFF;
                line-height: 42px;
                position:absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: 0;
                z-index: 1;
            }
        }
        .wraper{
            .title{
                margin: 0px auto;
                width: 227px;
                height: 42px;
                background: url(../images/title.png) no-repeat;
                background-size: 100% 100%;
                text-align:center;
                font-size: 22px;
                color: #FFFFFF;
                line-height: 42px;
            }
            .cjwt{
                margin-top:7px;
            }
            .content{
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                padding: 0 15px;
                margin-top: 20px;
                .item{
                    width: 166px;
                    height: 125px;
                    border-radius:2px;
                    overflow: hidden;
                    position: relative;
                    margin-bottom: 15px;
                    border: .5px solid #8C8D93;
                    .img-box{
                        width: 100%;
                        height: 100%;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                        .default{
                            width: 100%;
                            height: 100%;
                            background:url(../images/11.png) no-repeat;
                            background-size: 100% 100%;
                        }
                        .vip-tag{
                            position: absolute;
                            top: 0;
                            right: 0;
                            width: 33px;
                            height: 39px;
                            background:url(../images/8.png) no-repeat;
                            background-size: 100% 100%;
                        }
                        .not-vip-tag{
                            position: absolute;
                            top: 0;
                            right: 0;
                            width: 33px;
                            height: 39px;
                            background:url(../images/9.png) no-repeat;
                            background-size: 100% 100%; }
                    }
                    .name{
                        position:absolute;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        font-size: 13px;
                        line-height: 14px;
                        color: #FFFFFF;
                        width: 166px;
                        background:url(../images/name.png) no-repeat;
                        background-size: 100% 100%;
                        min-height: 30px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        color: #FFFFFF;
                        padding:2px 8px;
                        display: flex;
                        align-items: center;
                    }
                }
               
            }
            
        }
        .more{
            padding-right: 20px;
            line-height: 18px;
            font-size: 13px;
            font-weight: 400;
            margin: 10px auto 10px;
            color: #fff;
            display: inline-block;
            position: relative;
            left: 50%;
            transform:translateX(-50%);
            &:before {
                content: '';
                position:absolute;
                top: 50%;
                transform: translateY(-50%);
                right: 0;
                width:11px;
                height: 11px;
                background: url(../images/3.png) no-repeat right center;
                background-size:11px 11px;
            }
        }
        .rotate{
            &:before {
                content: '';
                position:absolute;
                top: 50%;
                right: 0;
                width:11px;
                height: 11px;
                background: url(../images/3.png) no-repeat right center;
                background-size:11px 11px;
                transform: translateY(-50%)  rotate(180deg) ;
            }
        }
        .img4{
            width: 345px;
            height: 320px;
            background: url(../images/4.png) no-repeat;
            background-size: 100% 100%;
            margin: 20px auto 0;
        }
        .img5{
            width: 345px;
            height: 320px;
            background: url(../images/5.png) no-repeat;
            background-size: 100% 100%;
            margin: 20px auto 0;
        }
        .img6{
            width: 345px;
            height: 320px;
            background: url(../images/6.png) no-repeat;
            background-size: 100% 100%;
            margin: 20px auto 0;
        }
        .img7{
            width: 345px;
            height: 320px;
            background: url(../images/7.png) no-repeat;
            background-size: 100% 100%;
            margin: 20px auto;
            position: relative;
        }
        .img8{
            width: 345px;
            height: 320px;
            background: url(../images/b8.png) no-repeat;
            background-size: 100% 100%;
            margin: 20px auto;
            position: relative;
           
        }
        .img9{
            width: 345px;
            height: 320px;
            background: url(../images/b9.png) no-repeat;
            background-size: 100% 100%;
            margin: 20px auto 30px;
            position: relative;
            .bc{
                position:absolute;
                top:270px;
                left: 50%;
                transform:translateX(-50%);
                width:210px;
                height:40px;
            }
        }
    }
    .buy-button{
        margin:0 5px;
    }
    .modal{
        position: fixed;
        top: 0;
        bottom:0;
        left:0;
        right: 0;
        z-index: 1;
        background: rgba(0,0,0,.3);
        .embeded{
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background:#fff;
        }
        .ios-height{
            height:280px;
        }
        .android-height{
            height:410px;
        }
    }
    .persuade-content{
        width: 100%;
        height:154px;
        position:relative;
        &:before{
            content: "";
            position:absolute;
            top: 0;
            left: -15px;
            width: 320px;
            height:100%;
            background: url(../images/13.png) no-repeat;
            background-size:320px 100%;
        }
        &:after {
            content: "";
            position:absolute;
            top: 0;
            left:-25px;
            right: 0;
            bottom: 0;
            width: 339px;
            height: 154px;
            background:url(../images/12.gif) no-repeat;
            background-size: cover;
        }
    }
}
