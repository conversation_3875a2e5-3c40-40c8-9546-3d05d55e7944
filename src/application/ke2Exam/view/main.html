<import name="style" content="./main" />

<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="header" content=":component/header/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />

<div class="ke2-exam-page">

    <div class="header">
        <com:header title="{{self.title}}" theme="black" endTheme="black" qaKey="{{self.qaKey}}"
            back="{{self.onBackClick}}" />
    </div>

    <div class="main" ref="scroller">

        <div class="top">
            <div class="video-wraper">
                <video ref="video" id="video" webkit-playsinline x5-playsinline loop
                    src="http://upload-video.mucang.cn/knowhere/2022/06/02/88c1046712ad449ea187d8e307a587a5.high.mp4"
                    type="video/mp4" playsinline="true" sp-on:click="onPause"
                    poster="https://jiakao-web.mc-cdn.cn/jiakao-web/2022/06/08/11/800d8a324d224326ba128cd98c24e925.jpg" />
                <div class="video-cover {{state.hideCover?'hide':''}}" sp-on:click="onPlay">
                </div>
            </div>
            <div class="cityname">{{state.cityName || '本地'}}</div>
            <div class="title">科二考场列表</div>
        </div>

        <div class="wraper">
            <div class="content">
                <sp:each for="{{state.showAll ?  state.videoList : state.videoList.slice(0,8)}}" value="item">
                    <div class="item" ref="item-btn" data-isvip="{{item.hasPermission ? 'true' :'false'}}">
                        <div class="img-box">
                            <sp:if value="item.cover">
                                <img src="{{item.cover}}" alt="">
                                <sp:else />
                                <div class="default"></div>
                            </sp:if>
                            <sp:if value="{{item.hasPermission}}">
                                <div class="not-vip-tag"></div>
                                <sp:else />
                                <div class="vip-tag"></div>
                            </sp:if>
                        </div>
                        <div class="name">
                            <div class="text" ref="name">
                                {{item.name}}
                            </div>
                        </div>
                    </div>
                </sp:each>

            </div>
        </div>
        <sp:if value="{{state.videoList.length > 6}}">
            <div class="more {{state.showAll && 'rotate'}}" sp-on:click="onShowMore">{{state.showAll ? '收起' :'查看更多考场'}}
            </div>
        </sp:if>

        <div class="img4" key="img4" />
        <div class="img5" key="img5" />
        <div class="img6" key="img6" />
        <div class="img7" />
        <div class="img8" />
        <div class="img9">
            <div class="bc" sp-on:click="goAuth"></div>
        </div>

        <div class="wraper">
            <div class="title cjwt">常见问题</div>
            <com:commonQuestion type="10"></com:commonQuestion>
        </div>
    </div>

    <div class="buy-button">
        <com:buyButton>
            <div sp:slot="couponEntry">
                {{self.currentCoupon.hint}}
            </div>
        </com:buyButton>
    </div>

    <div class="{{state.showEmbeded ? '' : 'hide'}} modal">
        <div class="embeded">
            <div ref="embeded" skip="true" class="{{Platform.isIOS ? 'ios-height' : 'android-height'}}"></div>
        </div>
    </div>

    <com:persuadeDialog>
        <div sp:slot="text-body">
            <div class="persuade-content">
            </div>
        </div>
    </com:persuadeDialog>
    <com:payDialog />
    <com:expiredDialog />
</div>
