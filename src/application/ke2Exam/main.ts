/*
 * ------------------------------------------------------------------
 * 科二考场
 * ------------------------------------------------------------------
 */

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { PayBoundType, startSiriusPay } from ':common/features/pay';
import { PayType, persuadeDialogAllow, Platform, setPageName, URLCommon } from ':common/env';
import { trackEvent, trackExit, trackPageLoad } from ':common/stat';
import { GoodsInfo } from ':store/goods';
import Ke23D from ':application/ke23d/main';
import PersuadeDialogCom from ':component/persuadeDialog/main';
import { onWebBack } from ':common/features/persuade';
import noop from 'lodash/noop';
import PayDialog from ':component/payDialog/main';
import { listenScroll } from ':common/features/dom';
import Header from ':component/header/main';
import { getSystemInfo, openWeb, webClose } from ':common/core';
import { getSceneList } from ':store/chores';
import { openAuth } from ':common/navigate';
import { getCityName } from ':common/utils';

interface State {
    goodsInfo: GoodsInfo;
    showEmbeded: boolean;

    showAll?: boolean,
    cityName?: string,
    videoList?: {
        cover: string,
        hasPermission: boolean,
        name: string
    }[],
    showTitle: boolean,
    hideCover: boolean
}

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        persuadeDialog: PersuadeDialogCom;
        payDialog: PayDialog;
        header: Header;
    };

    ke23d: Ke23D;

    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            goodsInfo: null,
            showEmbeded: false,

            showTitle: false,
            hideCover: true
        };
    }

    get currentGoods() {
        return this.state.goodsInfo;
    }

    get title() {
        if (!this.state.showTitle) {
            return ' ';
        }
        return this.currentGoods.name;
    }
    async didMount() {
        setPageName('科二考场前置H5页');
        const { _userCity } = await getSystemInfo();
        const cityName = await getCityName(_userCity);
        this.setState({
            cityName
        });
        this.getSceneList(_userCity);
        // 装载
        const ke23d = this.ke23d = new Ke23D({
            target: this.getDOMNode().embeded as HTMLElement
        });
        this.ke23d.props.from = 'ke2_exam';
        this.ke23d.props.fragmentName1 = '未知片段';
        this.ke23d.props.fragmentName2 = '选择支付方式弹窗';
        listenScroll(
            this.getDOMNode().scroller as HTMLElement,
            200,
            () => {
                this.setState({ showTitle: true });
            },
            () => {
                this.setState({ showTitle: false });
            },
            (scrollTop) => {
                this.children.header.setScrollBg(scrollTop);
            }
        );

        ke23d.on('goodsInfo', goodsInfo => {
            this.setState({ goodsInfo });
            this.setPayment();

            trackPageLoad();
        });
        ke23d.on('close', goodsInfo => {
            this.setState({ showEmbeded: false, goodsInfo });
            this.setPayment();
        });

        ke23d.render();

        this.event.on('item-btn', 'click', (e) => {
            trackEvent({
                fragmentName1: '考场列表',
                actionName: '考场卡片',
                actionType: '点击'
            });
            const isVip = e.refTarget.dataset.isvip;
            if (isVip === 'true') {
                openWeb({
                    url: 'http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fk2realhome%3ffrom%3d0207'
                });
            } else {
                this.ke23d.setPayment({ propFragmentName1: '考场列表' });
                this.setState({ showEmbeded: true });
            }
        });

        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

        this.onPlay();
    }

    async getSceneList(cityCode = '420100') {
        const videoList = await getSceneList(cityCode);
        console.log(videoList, '125');

        this.setState({
            videoList
        });
    }

    onShowMore() {
        const { showAll } = this.state;
        this.setState({
            showAll: !showAll
        });
    }

    private setPayment() {
        const { goodsInfo } = this.state;
        this.children.buyButton.setPay({
            androidPay: noop,
            iosPaySuccess: noop,
            intercepter: this.interceptPay
        });

        if (!goodsInfo.bought) {
            this.children.buyButton.setButtonConfig({
                groupKey: this.currentGoods.groupKey,
                type: 1,
                title: '确认协议并支付',
                price: this.currentGoods.payPrice,
                validDays: this.currentGoods.validDays,
                fragmentName1: '吸底按钮'
            });
        } else {
            this.children.buyButton.hideButton();
        }

    }

    interceptPay = () => {
        trackEvent({
            fragmentName1: '吸底按钮',
            actionType: '点击',
            actionName: '去支付'
        });
        this.setState({ showEmbeded: true });
        this.ke23d.setPayment({ propFragmentName1: '吸底按钮' });
        return true;
    };

    async pay(stat: PayStatProps) {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.currentGoods.groupKey,
            sessionIds: this.currentGoods.sessionIds,
            activityType: this.currentGoods.activityType,
            ...stat
        }).catch(async () => {
            this.children.payDialog.show({
                groupKey: this.currentGoods.groupKey,
                payPrice: this.currentGoods.payPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        
        });
    }

    async goAuth() {
        openAuth({
            groupKeys: [this.state.goodsInfo].map(item => item.groupKey).join(','),
            groupKey: this.currentGoods.groupKey,
            authId: 'bgbc'
        });
    }

    onBackClick = () => {
        this.goBackPage();
    }

    private flag = false;
    /** 返回按钮点击事件 */
    goBackPage() {
        if (persuadeDialogAllow && !this.flag && Platform.isAndroid && !this.currentGoods.bought) {
            this.flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: this.currentGoods,
                groupKey: this.currentGoods.groupKey,
                payPrice: this.currentGoods.payPrice,
                title: `真的要放弃${this.currentGoods.name}吗？`,
                txt1: '倒车入库看点位\n太重要了',
                txt2: '3D模拟挺有意思\n好用',
                txt3: '居然可以看到\n本地的考场',
                txt4: URLCommon.tiku === 'truck' ? '朋友推荐的说好用' : '第一次没考过\n真的补偿了40块钱',
                tag: { text: '考不过补偿40元' },
                kemu: URLCommon.kemu
            }).then(payType => {
                if (payType === false) {
                    trackExit();
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            trackExit();
            webClose();
        }
    }

    onPlay() {
        (document.getElementById('video') as HTMLVideoElement)
            ?.play();
        this.setState({
            hideCover: true
        });
    }
    onPause() {
        (document.getElementById('video') as HTMLVideoElement)
            ?.pause();
        this.setState({
            hideCover: false
        });
    }
}
