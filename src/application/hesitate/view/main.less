.hesitate-page {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    max-width: 375px;
    margin: 0 auto;

    .close {
        position: absolute !important;
        top: 15px;
        right: 15px;
        width: 30px;
        height: 30px;
        background: url("../images/close.png");
        background-size: cover;
    }

    .banner {
        width: 375px;
        height: 163px;
        background-image: url("../images/banner.png");
        background-size: cover;
        margin-bottom: 5px;
    }

    .plan-b {
        margin: 0 auto;
        width: 335px;
        height: 162px;
        background: linear-gradient(135deg, #feeacd, #fed9ad);
        border-radius: 17px;
        box-shadow: 0px -2px 0px 0px #f8d09d inset;
        position: relative;
        overflow: hidden;

        .goods-name {
            position: relative;
            margin-top: 26px;
            margin-left: 26px;
            font-size: 20px;
            font-weight: bold;
            color: #7f310a;
            line-height: 28px;
        }

        .goods-desc {
            position: relative;
            color: #7f310a;
            white-space: nowrap;
            margin-top: 19px;
            padding-left: 24px;

            .i1 {
                font-size: 23px;
                // line-height: 32px;
                font-weight: bold;
            }

            .i2 {
                font-size: 53px;
                line-height: 74px;
                font-weight: bold;
            }

            .i3 {
                margin-left: 12px;
                font-size: 15px;
                // line-height: 21px;
                font-weight: bold;
            }

            .i4 {
                font-size: 20px;
                // line-height: 28px;
                font-weight: bold;
            }

            .i5 {
                font-size: 11px;
                // line-height: 21px;
            }
        }

        .goods-tips {
            position: absolute;
            top: 73px;
            right: 20px;
            width: 113px;
            height: 27px;
            background: rgba(221, 117, 66, 0.18);
            border-radius: 20px;
            font-size: 13px;
            font-weight: bold;
            color: #252422;
            line-height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .goods-deco1 {
            position: absolute;
            left: -20px;
            top: -127px;
            width: 198px;
            height: 217px;
            background: linear-gradient(142deg, rgba(255, 247, 233, 0.00) 64%, #feebce 84%);
            border-radius: 50%;
        }

        .goods-deco2 {
            position: absolute;
            left: 280px;
            top: 111px;
            width: 200px;
            height: 217px;
            opacity: 0.42;
            background: linear-gradient(121deg, #feebce 24%, rgba(255, 247, 233, 0.00) 46%);
            border-radius: 50%;
        }
    }

    .plan-c {
        display: flex;
        flex-direction: column;
        margin: 0 auto;
        width: 339px;
        height: 155px;
        background: linear-gradient(0deg, rgba(254, 250, 244, 0.00) 48%, #fff4e1 86%);
        border: 1px solid #fff3e1;
        border-radius: 14px;

        .goods-name {
            margin-top: 25px;
            margin-left: 15px;
            font-size: 18px;
            font-weight: bold;
            color: #333333;
            line-height: 25px;
            position: relative;
            align-self: flex-start;

            i {
                position: absolute;
                left: 100%;
                top: 50%;
                transform: translateX(4px) translateY(-50%);
                display: block;
                width: 140px;
                height: 19px;
                background-image: url('../images/flag.png');
                background-size: cover;
            }
        }

        .items {
            margin-top: 17px;
            display: flex;
            justify-content: space-around;

            .item {
                width: 36px;

                i {
                    margin-bottom: 5px;
                    display: block;
                    width: 36px;
                    height: 36px;
                    background-size: cover;
                }

                img {
                    margin-bottom: 5px;
                    display: block;
                    width: 36px;
                    height: 36px;
                }

                span {
                    width: 36px;
                    font-size: 12px;
                    color: #464646;
                    line-height: 17px;
                    white-space: nowrap;
                    display: flex;
                    justify-content: center;
                }
            }

            .item:nth-child(1) i {
                background-image: url('../images/item1.png');
            }

            .item:nth-child(2) i {
                background-image: url('../images/item2.png');
            }

            .item:nth-child(3) i {
                background-image: url('../images/item3.png');
            }

            .item:nth-child(4) i {
                background-image: url('../images/item4.png');
            }
        }
    }

    .countdown {
        margin-top: 13px;
        margin-bottom: 15px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        color: #979495;
        line-height: 25px;
    }

    .flex1 {
        flex: 1;
    }

    .buy-btn {
        padding: 0 5px;
        flex-shrink: 0;
    }

}

body {
    max-width: none;
    background: linear-gradient(180deg, #ffe6d6, #ffffff 34%);
    border-radius: 25px 25px 0px 0px;
}