<import name="style" content="./main" />

<import name="buyButton" content=":component/buyButton/main" />
<import name="ticker" content="../component/ticker/main" />

<div class="hesitate-page">
    <div class="hotArea close" ref="close"></div>
    <div class="banner"></div>

    <sp:if value="{{state.plan === 'b'}}">

        <div class="plan-b">
            <div class="goods-deco1"></div>
            <div class="goods-deco2"></div>
            <div class="goods-name">{{self.displayName}}</div>
            <div class="goods-desc">
                <span class="i1">限时立减</span>
                <span class="i2">{{self.discountPrice}}</span>
                <span class="i1">元</span>
                <span class="i3">仅需¥</span>
                <span class="i4">{{state.goodsInfo.payPrice || '--'}}</span>
                <span class="i5">/{{state.goodsInfo.validDays}} 天</span>
            </div>
            <div class="goods-tips">即将恢复为¥{{self.originalPrice}}</div>
        </div>

        <sp:elseif value="{{state.plan === 'c'}}" />

        <div class="plan-c">
            <div class="goods-name">
                <span>{{self.displayName}}</span>
                <i></i>
            </div>
            <div class="items">
                <sp:if value="{{state.isKemuAll}}">
                    <div class="item" data-key="k1vip" sp-on:click="gotoRightDetail">
                        <img src="https://web-resource.mc-cdn.cn/web/vip/13.png" />
                        <span>科一vip</span>
                    </div>
                    <div class="item" data-key="k2vip" sp-on:click="gotoRightDetail">
                        <img src="https://web-resource.mc-cdn.cn/web/vip/9.png" />
                        <span>科二vip</span>
                    </div>
                    <div class="item" data-key="k3vip" sp-on:click="gotoRightDetail">
                        <img src="https://web-resource.mc-cdn.cn/web/vip/10.png" />
                        <span>科三vip</span>
                    </div>
                    <div class="item" data-key="k4vip" sp-on:click="gotoRightDetail">
                        <img src="https://web-resource.mc-cdn.cn/web/vip/15.png" />
                        <span>科四vip</span>
                    </div>

                    <sp:else />

                    <div class="item" data-key="jj500t" sp-on:click="gotoRightDetail"><i></i><span>精简题库</span></div>
                    <div class="item" data-key="dtjq" sp-on:click="gotoRightDetail"><i></i><span>答题技巧</span></div>
                    <div class="item" data-key="zskcmn" sp-on:click="gotoRightDetail"><i></i><span>真实考场模拟</span></div>
                    <div class="item" data-key="kqmj" sp-on:click="gotoRightDetail"><i></i><span>考前秘卷</span></div>
                </sp:if>
            </div>
        </div>
    </sp:if>

    <div class="countdown">优惠马上消失</div>
    <com:ticker />
    <div class="flex1"></div>
    <div class="buy-btn">
        <com:buyButton />
    </div>
</div>