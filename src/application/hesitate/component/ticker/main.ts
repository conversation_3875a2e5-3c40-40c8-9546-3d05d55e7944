/*
 * ------------------------------------------------------------------
 * 像计时器这种不停刷新state的，性能会很卡，只能单独做成一个组件，局部更新这个组件
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
    timerTips: string[];
}

export default class extends Component<State> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            timerTips: []
        };
    }

    public setTips(timerTips: string[]) {
        this.setState({
            timerTips
        });
    }
}
