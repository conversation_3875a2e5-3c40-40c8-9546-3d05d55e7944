/*
 * ------------------------------------------------------------------
 * 犹豫用户促销
 * ------------------------------------------------------------------
 */

import { CarType, KemuType, setPageName, URLCommon, URLParams } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import Ticker from './component/ticker/main';
import { GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackEvent } from ':common/stat';
import { showSetting } from ':common/features/embeded';
import { ActivityType, newGetGroupSessionInfo } from ':store/newGoods';
import { openAuth } from ':common/navigate';
import { webClose } from ':common/core';
import { dateDiffFormat, ticker } from ':common/utils';

interface State {
    plan: string,
    kemu: KemuType,
    tiku: CarType,
    goodsInfo: GoodsInfo,
    isKemuAll: boolean,
}

const originGroupKey = URLParams.groupKey as GroupKey;

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        ticker: Ticker;
    };

    get displayName() {
        switch (originGroupKey) {
            case GroupKey.ChannelKe1:
                return '科一VIP会员';
            case GroupKey.ChannelKe4:
                return '科四VIP会员';
            case GroupKey.ChannelKemuAll:
                return '全科VIP会员';
            default:
                return '未知商品';
        }
    }

    get originalPrice() {
        return this.state.goodsInfo.originalPrice || '--';
    }

    get discountPrice() {
        return this.state.goodsInfo.originalPrice ?
            String(Math.round(+this.state.goodsInfo.originalPrice - +this.state.goodsInfo.payPrice)) : '--';
    }

    $constructor() {
        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            plan: URLParams.plan,
            kemu,
            tiku,
            goodsInfo: {} as any,
            isKemuAll: originGroupKey === GroupKey.ChannelKemuAll
        };

    }
    async didMount() {
        showSetting({
            iosH: 440,
            androidH: 560
        });
        ensureSiriusBound();

        if (this.state.plan === 'b') {
            const fragmentName1 = '秒杀弹窗';
            const groupKey = originGroupKey;
            setPageName(URLParams.fromPage || '未知页');
            trackEvent({
                fragmentName1,
                actionType: '出现',
                groupKey,
                payStatus: '2'
            });

            newGetGroupSessionInfo({ groupKeys: [groupKey], activityType: ActivityType.fs }).then(([goodsInfo]) => {
                this.setPayment(goodsInfo, fragmentName1);
            });
        } else if (this.state.plan === 'c') {
            const groupKeyMapper = {
                [GroupKey.ChannelKe1]: GroupKey.ChannelKe1Sale,
                [GroupKey.ChannelKe4]: GroupKey.ChannelKe4Sale,
                [GroupKey.ChannelKemuAll]: GroupKey.ChannelKemuAllSale
            } as Record<GroupKey, GroupKey>;

            const fragmentName1 = '短时VIP弹窗';
            const groupKey = groupKeyMapper[originGroupKey];
            setPageName(URLParams.fromPage || '未知页');
            trackEvent({
                fragmentName1,
                actionType: '出现',
                groupKey,
                payStatus: '2'
            });

            newGetGroupSessionInfo({ groupKeys: [groupKey] }).then(([goodsInfo]) => {
                this.setPayment(goodsInfo, fragmentName1, true);
            });
        }

        this.setTimer()();

        this.event.on('close', 'click', () => {
            webClose();
        });
    }

    setTimer() {
        // 倒计时
        const endTime = new Date().getTime() + (15 * 60 * 1000);

        return () => {
            let diffBefore: string;
            const cancel = ticker(() => {
                const due = endTime - new Date().getTime();
                if (due <= 0) {
                    cancel();
                    webClose();
                    return;
                }

                const diff = dateDiffFormat(due, 'HHmmss');

                if (diff === diffBefore) {
                    return;
                }
                diffBefore = diff;

                this.children.ticker.setTips(diff.split(''));
            });
        };
    }

    gotoRightDetail(e) {
        const rightsIntroduceCode = e.refTarget.getAttribute('data-key');

        openAuth({
            authId: rightsIntroduceCode,
            groupKeys: this.state.goodsInfo.groupKey,
            groupKey: this.state.goodsInfo.groupKey
        });
    }

    /** 设置支付参数 */
    public setPayment(goodsInfo: GoodsInfo, fragmentName1: string, showValidDays = false) {
        // 有小概率跳进这，直接关掉
        if (goodsInfo.bought) {
            webClose();
            return;
        }

        this.setState({ goodsInfo });
        this.children.buyButton.setPay({
            androidPay: this.pay.bind(this),
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => newBuySuccess({ groupKey: this.state.goodsInfo.groupKey }, 2),
            isInDialog: false,
            getIOSGoods() {
                return goodsInfo;
            }
        });
        this.children.buyButton.setButtonConfig({
            groupKey: this.state.goodsInfo.groupKey,
            type: 3,
            title: `¥${goodsInfo.payPrice} 立即秒杀`,
            subtitle: showValidDays ? '有效期' + this.state.goodsInfo.validDays + '天' : '',
            fragmentName1,
            actionName: '去支付'
        });
    }

    /** 发起支付 */
    public pay(stat: PayStatProps) {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.state.goodsInfo.groupKey,
            sessionIds: this.state.goodsInfo.sessionIds,
            activityType: this.state.goodsInfo.activityType,
            ...stat
        }, false).then(() => newBuySuccess({ groupKey: this.state.goodsInfo.groupKey }, 2));
    }
}
