/*
 * ------------------------------------------------------------------
 * 驾考讲堂引导
 * ------------------------------------------------------------------
 */

import { setStatusBarTheme } from ':common/core';

import { Application } from '@simplex/simple-core';
import View from './view/main.html';

export default class extends Application {
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
        };

    }
    didMount() {
        setStatusBarTheme('dark');
    }
}
