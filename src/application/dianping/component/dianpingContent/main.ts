/*
 * ------------------------------------------------------------------
 * 点评页
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { URLParams } from ':common/env';
import { openWeb } from ':common/core';
interface State {
    kemu: string | number
}
export default class extends Component<State> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            kemu: URLParams.kemuStyle || 1
        };
    }

    willReceiveProps() {
        return true;
    }
    didMount() {
        this.event.on('intro', 'click', () => {
            if (+this.state.kemu === 2) {
                openWeb({
                    url: 'http://saturn.nav.mucang.cn/tag/detail?tagId=28777&selectTab=3'
                });
               
            } else if (+this.state.kemu === 3) {
                openWeb({
                    url: 'http://saturn.nav.mucang.cn/tag/detail?tagId=28779&selectTab=3'
                });
            } else {
                openWeb({
                    url: 'http://saturn.nav.mucang.cn/tag/detail?tagId=37456'
                });
            }
        });
    }
}
