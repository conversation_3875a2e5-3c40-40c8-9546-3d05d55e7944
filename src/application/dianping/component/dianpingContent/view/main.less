.dianping-content {

    .body-panel {
        flex: 1;
        -webkit-flex: 1;
        overflow-y: scroll;
        -webkit-overflow-scrolling: touch;
        box-sizing: border-box;
        padding-bottom: 15px;
    }

    .sec-w {
        .sec-dp {
            background: #fff;
            border-radius: 10px;
            padding: 0 23px 10px 21px;
        }

        .dp-hd {
            display: block;
            width: 148px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            margin: 0 auto;
            color: #ffffff;
            font-size: 16px;
            background: linear-gradient(0deg, #D87447 0%, #F2B082 100%);
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
        }

        .dp-li {
            display: flex;
            display: -webkit-flex;
            padding: 24px 0;
            border-bottom: 1px dashed #D4D4D4;
            box-sizing: border-box;

            &:nth-last-child(1) {
                border: none;
            }

            .avatar {
                width: 26px;
                height: 26px;
                border-radius: 100%;
            }

            .content {
                -webkit-flex: 1;
                flex: 1;
                padding-left: 11px;
            }

            .p1 {
                font-size: 14px;
                color: #6b6870;
                padding: 2px 0 8px 0;

            }

            .p2 {
                font-size: 14px;
                color: #494455;
                line-height: 20px;
            }
        }

        .sec-qa {
            background: #fff;
            border-radius: 10px;
            padding: 0 21px 20px 18px;
        }

        .qa-hd {
            display: block;
            width: 128px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            margin: 0 auto;
            color: #ffffff;
            font-size: 16px;
            background: linear-gradient(0deg, #D87447 0%, #F2B082 100%);
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
        }

        .qa-li {
            display: flex;
            display: -webkit-flex;
            padding: 12px 0;
            box-sizing: border-box;

            .icon {
                width: 18px;
                height: 18px;
                border-radius: 100%;
            }

            .content {
                -webkit-flex: 1;
                flex: 1;
                padding-left: 5px;
            }

            .p1 {
                font-size: 14px;
                color: #494455;
                padding: 0 0 8px 0;
                font-weight: bold;
            }

            .p2 {
                font-size: 14px;
                color: #75717d;
                line-height: 20px;
            }
        }
    }

    .sec-dp-w {
        padding: 30px 15px 20px 15px;
    }

    .sec-qa-w {
        padding: 30px 15px 20px 15px;
    }

    .sec-wenda-w {}

    .sec-wenda {
        background: #ffffff;
        border-radius: 10px;
        position: relative;
    }

    .wenda-t {
        padding: 0 15px;
        height: 49px;

        .sp1 {
            color: #7F3511;
            font-size: 19px;
            padding-right: 22px;
        }

        .sp2 {
            color: #6E6E6E;
            font-size: 13px;
        }
    }

    .wenda-li {
        padding: 0 15px 15px 15px;
        border-bottom: 10px solid #F2F2F2;

        .div1 {
            padding: 15px 0;
            border-bottom: 1px solid #F2F2F2;

            p {
                color: #494455;
                font-size: 15px;
                line-height: 22px;
                font-weight: bold;
                padding-left: 30px;
                background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/q.png) no-repeat left top;
                background-size: 22px 22px;
            }
        }

        .div2 {
            padding-top: 18px;

            img {
                width: 22px;
                height: 22px;
                border-radius: 100%;
                margin: 0;
            }

            b {
                color: #6E6E6E;
                font-size: 14px;
                padding-left: 8px;
                flex: 1;
            }

            span {
                color: #7F3511;
                font-size: 13px;
                line-height: 14px;
                padding: 2px 10px;
                background-color: #FFF0E5;
                border-bottom-left-radius: 4px;
                border-top-right-radius: 4px;
            }
        }

        .div3 {
            color: #494455;
            font-size: 14px;
            padding: 9px 0 0 30px;
            line-height: 1.4;
        }
    }

    .webkit-flex-center {
        display: flex;
        align-items: center;
        justify-content: space-between;
        -webkit-display: flex;
        -webkit-align-items: center;
        -webkit-justify-content: space-between;
    }



    .pay-btn-w {
        position: absolute;
        left: 0;
        right: 0;
        top: 5px;
        width: 100%;
        height: 50px;
        color: #FFF;
        background: linear-gradient(90deg, #F95B38 0%, #E83E30 100%);
        z-index: 1;
        display: flex;
        display: -webkit-flex;
        align-items: baseline;
        -webkit-align-items: baseline;
        justify-content: center;
        -webkit-justify-content: center;
        box-sizing: border-box;
        padding: 15px 0 0 146px;

        span {
            font-size: 18px;
            font-weight: bold;
        }
    }

    .proto-coupon {
        padding-left: 15px;
    }

    .hide {
        display: none;
    }
}