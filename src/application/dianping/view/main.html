<import name="style" content="./main" module="S" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />

<import name="dianpingContent" content=":application/dianping/component/dianpingContent/main" />
<import name="kemuPassRateReminder" content=":component/kemuPassRateReminder/main" />
<div class="page-container">
    <div class=":header">
        <com:header title="{{self.title}}" theme="white" endTheme="white" back="{{self.onBackClick}}">
            <div sp:slot="right"></div>
        </com:header>
    </div>

    <div class="{{S.main}} {{S.content}}">
        <com:kemuPassRateReminder />
        <com:dianpingContent/>
    </div>

    <div class="{{S.footer}}">
        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元':''}}
            </div>
        </com:buyButton>
    </div>
    <com:payDialog />
    <com:expiredDialog />
</div>