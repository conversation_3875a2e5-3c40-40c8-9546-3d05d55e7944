/*
 * ------------------------------------------------------------------
 * 点评页
 * ------------------------------------------------------------------
 */
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import ExpiredDialog from ':component/expiredDialog/main';
import PayDialog from ':component/payDialog/main';
import { ensureSiriusBound, getDefaultPayType, PayBoundType, startSiriusPay } from ':common/features/pay';
import { BUYED_URL } from ':common/navigate';
import { PayType } from ':common/env';
import { iosBuySuccess } from ':common/features/ios_pay';
import Header from ':component/header/main';
import { trackPageLoad } from ':common/stat';
import { webClose, setStatusBarTheme, goBack } from ':common/core';
import { URLParams, setPageName } from ':common/env';
import { Coupon, getBestCoupon, goodsInfoWithCoupon } from ':common/features/coupon';
import jump from ':common/features/jump';
import { onWebBack } from ':common/features/persuade';
const groupKey = URLParams.groupKey as GroupKey;
interface State {
    goodsList: GoodsInfo[];
    couponPool: object,
    tabIndex: number;
    backType: string,
}
export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        expiredDialog: ExpiredDialog;
        payDialog: PayDialog;
        header: Header;
    }

    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            goodsList: [],
            couponPool: {},
            tabIndex: 0,
            backType: URLParams.backType
        };
    }
    get pageName() {
        return 'VIP问答页';
    }
    get currentGoods() {
        return this.state.goodsList[this.state.tabIndex];
    }
    get title() {
        return ' ';
    }
    /**
   * 如果有优惠券的价格为0的就显示0
   * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
  */
    get showPrice() {
        const { tabIndex, goodsList, couponPool } = this.state;
        const nowGoodInfo = goodsList[tabIndex];
        const nowPayPrice = nowGoodInfo.payPrice;

        if (couponPool[this.currentGoods.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(nowGoodInfo, { code: couponPool[nowGoodInfo.groupKey].couponCode, price: couponPool[nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.currentGoods.groupKey];
    }

    async didMount() {
        setStatusBarTheme('dark');
        setPageName(this.pageName);

        ensureSiriusBound({ groupKey, type: PayBoundType.GoStatusPage });
        await this.fetchGoodsInfo();

        trackPageLoad();

        this.appProxy();

    }
    appProxy() {
        onWebBack(async () => {
            goBack();
        });
    }
    private async fetchGoodsInfo() {
        const [goodsInfo] = await getGroupSessionInfo({
            groupKeys: [groupKey]
        });
        if (goodsInfo.expired) {
            this.children.expiredDialog.show({ time: goodsInfo.expiredTime });
        }
        // 主商品已购买，直接跳走
        if (goodsInfo.bought) {
            jump.replace(BUYED_URL);
            return;
        }
        const goodsList = [goodsInfo];
        this.setState({ goodsList });
        this.setPayment();
        setTimeout(async () => {
            await this.getCoupon();
            this.setPayment();
        }, 60);
    }
    getCoupon = async () => {
        const { goodsList } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsList.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsList[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    private setPayment() {
        this.children.buyButton.setPay({
            androidPay: this.pay.bind(this),
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => iosBuySuccess({ groupKey: this.currentGoods.groupKey })
        });
        this.children.buyButton.setButtonConfig({
            groupKey: this.currentGoods.groupKey,
            type: 1,
            title: '确认协议并支付',
            price: this.showPrice,
            validDays: this.currentGoods.validDays,
            fragmentName1: '底部吸底按钮'
        });
    }
    /** 发起支付 */
    async pay(stat: PayStatProps) {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.currentGoods.groupKey,
            sessionIds: this.currentGoods.sessionIds,
            activityType: this.currentGoods.activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }).catch(async (err) => {
            console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: this.currentGoods.groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    onBackClick = () => {
        if (this.state.backType === 'close') {
            webClose();
        } else {
            goBack();
        }
    }
}
