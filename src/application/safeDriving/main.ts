/*
 * ------------------------------------------------------------------
 * 模拟考试购买弹窗单次售卖(进入这个页面肯定都是已登录的)
 * ------------------------------------------------------------------
 */
import { CarType, KemuType, setPageName, URLCommon, URLParams } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayTypeCom from ':component/payType/main';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { webClose } from ':common/core';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackGoPay } from ':common/stat';
import { Coupon, getBestCoupon, goodsInfoWithCoupon } from ':common/features/coupon';
import { setEmbeddedHeight } from ':common/features/embeded';
import { iosDialogBuySuccess } from ':common/features/ios_pay';

interface State {
    URLCommon: typeof URLCommon,
    kemu: KemuType,
    tiku: CarType,
    tabIndex: number,
    goodsDetail: any;
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object
}

const docWidth = 345;
const fragmentName1 = URLParams.fragmentName1 || '底部吸底按钮';

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton,
        payType: PayTypeCom;
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    get pageName() {
        return '3D项目详情页';
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (this.nowCouponInfo?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: this.nowCouponInfo.couponCode, price: this.nowCouponInfo.priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }

    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {
        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;
        const goodsInfoPool: GoodsInfo[] = [];

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        const goodsDetail = JSON.parse(URLParams.goodsInfo || '{"channelCode": "channel_safe_driving_1","videoCount":0,"totalVideoCount":0,"showPackGoods":false,"has3dExperience":true}');

        goodsInfoPool.push({
            groupKey: goodsDetail.channelCode
        } as GoodsInfo);
        if (goodsDetail.showPackGoods) {
            goodsInfoPool.push({
                groupKey: 'channel-safe-driving-all' as GroupKey
            } as GoodsInfo);
        }

        this.state = {
            URLCommon,
            kemu,
            tiku,
            goodsDetail,
            tabIndex: 0,
            goodsInfoPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {}
        };

    }

    didMount() {
        setPageName(this.pageName);

        // trackGoPay({
        //     groupKey: this.nowGoodInfo.groupKey,
        //     fragmentName1,
        //     payPathType: 0
        // });
        this.setPageInfo();
        this.getGoodInfo();
        this.appEventProxy();
        this.resetWindow(docWidth);
        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: async () => {
                iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey, goUse: true });
            },
            isInDialog: true
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey
        }, true);
    }
    appEventProxy() {
        setEmbeddedHeight(345 / 303);
    }
    resetWindow(docWidth) {
        const docEl = document.documentElement;
        const resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize';
        const reCalc = function () {
            const clientWidth = docEl.clientWidth;
            if (!clientWidth) {
                setTimeout(function () {
                    reCalc();
                }, 50);
                return;
            }
            window.baseFontSize = 100 * (clientWidth / docWidth);
            docEl.style.fontSize = window.baseFontSize + 'px';
        };

        window.addEventListener(resizeEvt, reCalc, false);
        document.addEventListener('DOMContentLoaded', reCalc, false);
    }
    onTabClick(e) {
        const idx = +e.refTarget.getAttribute('data-idx');
        this.tabChangeCall(idx);
    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        this.setState({
            tabIndex
        }, () => {
            this.setPageInfo();
        });

    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const { tabIndex, goodsInfoPool, labelPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];

        let tag = null;
        const label = labelPool[nowGoodInfo.groupKey]?.label;

        if (label) {
            tag = {
                text: label
            };
        }

        this.children.buyButton.setButtonConfig({
            groupKey: nowGoodInfo.groupKey,
            type: 7,
            title: `¥ ${this.showPrice}元确认协议并支付`,
            subtitle: '有效期' + nowGoodInfo.validDays + '天',
            fragmentName1,
            fragmentName2: '支付弹窗',
            tag: tag
        });

    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            if (goodsListInfo[0].bought) {
                webClose();
                return;
            }

            goodsListInfo.forEach((goodInfo) => {
                newGoodsPool.push(goodInfo);
            });
            this.setState({
                goodsInfoPool: newGoodsPool
            });
            // 先展示页面，再去请求无关的信息
            await Promise.all([
                this.getCoupon(),
                this.getLabel(),
                this.getComparePrice()
            ]);
            this.setPageInfo();
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool, tiku } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice
                    };
                }
            });
            this.setState({ comparePricePool });
        });
    }

    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(async () => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey, goUse: true }, 2);
        });
    }

    onCloseClick = () => {
        webClose();
    }
}