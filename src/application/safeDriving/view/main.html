<import name="style" content="./main" module="S" />
<import name="style" content="./override" />
<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="PriceTag" content=":component/priceTag/main" />

<div class=":safe-driving :container {{Platform.isIOS ? S['ios-container']: ''}}">
    <div class=":close" sp-on:click="onCloseClick"></div>

    <div class=":title"></div>

    <sp:if value="{{state.goodsInfoPool.length===2}}">

        <!-- 两个商品 -->
        <div class=":cards">
            <div class=":card {{state.tabIndex===0?S['active']:S['normal']}}" sp-on:click="onTabClick" data-idx="0">
                <div class=":name">{{state.goodsInfoPool[0].name}}</div>
                <div class=":desc">{{state.goodsDetail.videoCount}}课时&nbsp;{{state.goodsInfoPool[0].validDays}}天有效</div>
                <div class=":price">{{state.goodsInfoPool[0].payPrice || '--'}}元立即开通</div>
            </div>
            <div class=":card {{state.tabIndex===1?S['active']:S['normal']}}" sp-on:click="onTabClick" data-idx="1">
                <div class=":name">{{state.goodsInfoPool[1].name}}</div>
                <div class=":desc">{{state.goodsDetail.totalVideoCount}}课时&nbsp;{{state.goodsInfoPool[1].validDays}}天有效
                </div>
                <div class=":desc :desc2" sp:if="{{state.comparePricePool[state.goodsInfoPool[1].groupKey]}}">
                    分开买需要{{state.comparePricePool[state.goodsInfoPool[1].groupKey].allPrice}}元</div>
                <div class=":price">{{state.goodsInfoPool[1].payPrice || '--'}}元优惠开通</div>
                <com:PriceTag goodsInfo="{{state.goodsInfoPool[1]}}" comparePriceMap="{{state.comparePricePool}}"
                    labelMap="{{state.labelPool}}" />
            </div>
        </div>

        <sp:else />

        <!-- 单个商品 -->
        <div class=":card-single">
            <div class=":panel">
                <div>
                    <div class=":name">{{state.goodsInfoPool[0].name}}</div>
                    <div class=":desc">{{state.goodsDetail.videoCount}}课时&nbsp;{{state.goodsInfoPool[0].validDays}}天有效
                    </div>
                </div>
                <div class=":divider"></div>
                <div class=":price">{{state.goodsInfoPool[0].payPrice || '--'}}元</div>
            </div>
            <div class=":pics">
                <div class=":pic">
                    <div class=":pic1"></div>
                    <div>看真实案例</div>
                </div>
                <sp:if value="{{state.goodsDetail.has3dExperience}}">
                    <div class=":arrow"></div>
                    <div class=":pic">
                        <div class=":pic2"></div>
                        <div>3D驾驶体验</div>
                    </div>
                </sp:if>
                <div class=":arrow"></div>
                <div class=":pic">
                    <div class=":pic3"></div>
                    <div>专属讲解</div>
                </div>
            </div>
        </div>

    </sp:if>

    <div class=":footer">
        <div class=":pay-type {{Platform.isIOS && 'hide'}}">
            <com:payType theme="mnks" />
        </div>
        <div class=":buy-btn">
            <com:buyButton noPayType="true">
                <div sp:slot="couponEntry">
                    {{self.nowCouponInfo.couponCode?'已优惠' +
                    self.nowCouponInfo.priceCent + '元':''}}
                </div>
            </com:buyButton>
        </div>
    </div>
</div>