html,
body {
    height: 100vh;
    overflow: hidden;
    max-width: 100% !important;
}

.safe-driving {
    &.container {
        position: relative;
        height: 100vh;
        background: url(../images/mnks_3.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
    }

    &.ios-container {
        background: url(../images/mnks_3_ios.png) no-repeat;
        background-size: 100% 100%;
        padding-bottom: 110px;
    }

    .close {
        position: absolute;
        right: 0;
        top: 0;
        padding: 10px;
        width: 24px;
        height: 24px;
        background: url(../images/close.png) no-repeat center;
        background-size: 24px 24px;
        z-index: 1;
        box-sizing: content-box;
    }

    .title {
        margin-top: 10px;
        margin-left: 15px;
        width: 117px;
        height: 20px;
        background-image: url(../images/title.png);
        background-size: cover;
    }

    .cards {
        margin-top: 22px;
        display: flex;
        justify-content: center;
        flex: 1;

        .card {
            width: 154px;
            height: 90px;
            border-radius: 4px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;

            &.normal {
                background: #ffffff;
                box-shadow: 0 0 0 1px #f4c2a2;
                color: #843405;
            }

            &.active {
                background: linear-gradient(113deg, #353b4e 10%, #1d222b 91%);
                color: #FFFFFF;

                &:before {
                    content: '已选';
                    font-size: 10px;
                    text-align: center;
                    color: #211b1b;
                    line-height: 14px;
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 30px;
                    background: linear-gradient(131deg, #ffddc6 6%, #f9bf98 90%);
                    border-radius: 4px 0px 4px 0px;
                }
            }

            .price {
                position: absolute;
                left: 50%;
                top: 100%;
                transform: translateX(-50%) translateY(-50%);
                line-height: 20px;
                background: #ff5959;
                border-radius: 2px;
                padding: 0 7px;
                font-size: 11px;
                color: #ffffff;
                white-space: nowrap;
            }

            .name {
                margin-top: 12px;
                font-size: 15px;
                font-weight: bold;
                line-height: 21px;
            }

            .desc {
                margin-top: 4px;
                font-size: 12px;
                line-height: 17px;
            }

            .desc2 {
                margin-right: 1px;
            }
        }

        .card+.card {
            margin-left: 10px;
        }
    }

    .card-single {
        flex: 1;

        .panel {
            margin: 10px auto 0;
            width: 305px;
            height: 66px;
            color: #ffffff;
            background: linear-gradient(113deg, #353b4e 10%, #1d222b 91%);
            border-radius: 4px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            &:before {
                content: '已选';
                font-size: 10px;
                text-align: center;
                color: #211b1b;
                line-height: 14px;
                position: absolute;
                left: 0;
                top: 0;
                width: 30px;
                background: linear-gradient(131deg, #ffddc6 6%, #f9bf98 90%);
                border-radius: 4px 0px 4px 0px;
            }

            .divider {
                width: 1PX;
                height: 46px;
                background: rgba(255, 255, 255, 0.20);
                margin-left: 26px;
                margin-right: 23px;
            }

            .name {
                font-size: 15px;
                font-weight: bold;
                line-height: 21px;
            }

            .desc {
                margin-top: 4px;
                font-size: 12px;
                line-height: 17px;
            }

            .price {
                font-size: 20px;
                font-weight: bold;
                line-height: 28px;
            }
        }

        .pics {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            counter-reset: picNum;

            .pic {
                width: 32px;
                display: flex;
                flex-direction: column;
                align-items: center;
                font-size: 12px;
                color: #8f3112;
                line-height: 17px;
                white-space: nowrap;
                counter-increment: picNum;

                >:first-child {
                    width: 32px;
                    height: 32px;
                    margin-bottom: 2px;
                }

                >:last-child::before {
                    content: counter(picNum) ".";
                }
            }

            .pic1 {
                background-image: url(../images/pic1.png);
                background-size: cover;
            }

            .pic2 {
                background-image: url(../images/pic2.png);
                background-size: cover;
            }

            .pic3 {
                background-image: url(../images/pic3.png);
                background-size: cover;
            }

            .arrow {
                margin: 10px 32px;
                width: 7px;
                height: 11px;
                background-image: url(../images/arrow.png);
                background-size: 100% 100%;
            }
        }
    }

    .footer {
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        width: 100%;

        .pay-type {
            margin-bottom: -5px;
        }

        .buy-btn {
            width: 100%;
        }
    }

}