<import name="style" content="./main" module="S" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import
    name="onlineDialog"
    content=":application/kqfd/component/onlineDialog/main"
/>
<div class=":zdsthistory-index-dialog">
    <div class=":head-bg">
        <div class=":index-top-time">
            <sp:if value="{{state.lessonSchedule.length<=0}}">
                暂无直播
                <sp:else />
                直播时间:{{self.newSchelTime}}
            </sp:if>
        </div>
    </div>
    <div class=":img2-box">
        <div class=":content">
            <div class=":step2-box">
                <div class=":step2-title"></div>
                <div class=":zhibo-box">
                    <sp:each for="state.lessonSchedule">
                        <div class=":zhibo-item-box">
                            <div class=":time-title">{{$value.title}}</div>
                            <sp:each
                                for="$value.liveDataList"
                                value="$sonitem"
                                index="$sonIndex"
                            >
                                <div
                                    class=":zhibo-item"
                                    data-liveSessionId="{{$sonitem.liveSessionId}}"
                                    data-fragment="直播时间表"
                                    sp-on:click="onPay"
                                >
                                    <div class=":title">
                                        <span class=":num"
                                            >第{{Tools.numZh($sonIndex+1)}}场</span
                                        >
                                        <span class=":right-day">
                                            <sp:if
                                                value="{{Tools.dateFormat($sonitem.beginTime,
                                                                            'MM月dd日')==Tools.dateFormat(new Date(),
                                                                            'MM月dd日')}}"
                                            >
                                                今天
                                                <sp:else />
                                                {{Tools.dateFormat($sonitem.endTime,
                                                'MM月dd日')}}
                                            </sp:if>
                                            <span class=":right-time">
                                                {{Tools.dateFormat($sonitem.beginTime,
                                                'HH:mm')}}-{{Tools.dateFormat($sonitem.endTime,
                                                'HH:mm')}}
                                            </span>
                                        </span>

                                        <sp:if value="{{$sonitem.status==1}}">
                                            <span class=":zhizho1">直播中</span>
                                        </sp:if>
                                        <sp:if value="{{$sonitem.status==2}}">
                                            <sp:if
                                                value="{{$sonitem.subscribeStatus==1}}"
                                            >
                                                <span class=":zhizho2">
                                                    已预约
                                                </span>
                                                <sp:else />
                                                <span
                                                    class=":zhizho2"
                                                    data-liveSessionId="{{$sonitem.liveSessionId}}"
                                                    data-index="{{$index}}"
                                                    data-sonIndex="{{$sonIndex}}"
                                                    sp-on:click="makeappointment"
                                                >
                                                    预约直播
                                                </span>
                                            </sp:if>
                                        </sp:if>
                                        <sp:if value="{{$sonitem.status==3}}">
                                            <span class=":zhizho3">看回放</span>
                                        </sp:if>
                                    </div>
                                    <div class=":zhibo-item-content">
                                        {{$sonitem.liveContent}}
                                    </div>
                                </div>
                            </sp:each>
                        </div>
                    </sp:each>
                </div>
            </div>
        </div>
    </div>

    <div class=":loading-more">
        <span sp:if="{{state.hasMore}}" class=":loading"></span>
        {{state.loadTextTips}}
    </div>
    <com:onlineDialog></com:onlineDialog>
</div>

