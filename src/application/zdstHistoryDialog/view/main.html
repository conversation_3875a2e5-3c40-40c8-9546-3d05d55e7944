<import name="style" content="./main" />

<import
    name="indexPage"
    content=":application/zdstHistoryDialog/component/index/main"
/>
<import name="loading" content=":application/buyed/components/loading/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="loadingmore" content=":component/loading-more/main" />


<div class="page-container page-zdsthistory-dialog">
    <div class="body-panel">
         <com:loadingmore scroll="{{self.pageScroll}}" loadData="{{self.requestData}}">
        <com:indexPage
            expireTimeString="{{state.expireTimeString}}"
            hasPromission="{{state.hasPromission}}"
            goodsInfoPool="{{state.goodsInfoPool}}"
        ></com:indexPage>
        </com:loadingmore>
    </div>
  
    <com:loading />
    <com:expiredDialog />
</div>
