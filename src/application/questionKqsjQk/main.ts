/*
 * ------------------------------------------------------------------
 * ios考前密卷
 * ------------------------------------------------------------------
 */

import { PayType, setPageName, URLCommon, URLParams } from ':common/env';
import { trackPageLoad } from ':common/stat';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayTypeComponent from ':component/payType/main';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { iosDialogBuySuccess } from ':common/features/ios_pay';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { typeCode } from ':common/features/bottom';
import { openWeb, webClose } from ':common/core';
import { showSetting } from ':common/features/embeded';
import { formatPrice } from ':common/utils';
import { getLessionInfo } from ':store/chores';
import { newGetGroupSessionInfo, squirrelPrice } from ':store/newGoods';

const fragmentName1 = '错题弹窗';

interface State {
    tabIndex: number
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object
    practiceType: string
    showConfig: {
        title: string,
        subtitle: string
    }
}

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeComponent
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        const goodsInfoPool = [{
            groupKey: URLParams.groupKey
        } as GoodsInfo];

        this.state = {
            tabIndex: 0,
            goodsInfoPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            practiceType: URLParams.practiceType,
            showConfig: {
                title: '----',
                subtitle: '----'
            }
        };
    }
    async didMount() {
    
        // 注册底部支付方法
        this.children.buyButton.setPay({
            isInDialog: true,
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey
        });
        this.appProxy();
        this.getShowCoure();
        await this.getGoodInfo();

        setPageName(URLParams.fromPage || '未知');

        trackPageLoad({
            fragmentName1
        });
    }
    appProxy() {
        showSetting({
            iosH: 250,
            androidH: 400
        });
    }
    // 获取展示课程相关信息
    getShowCoure() {
        const { practiceType } = this.state;

        getLessionInfo(practiceType).then(async data => {
            const groupKeys = [];
            data.lessonConfig.forEach(item => {
                if (item.channelCode) {
                    groupKeys.push(item.channelCode);
                }
            });
            const goodsPriceConfig = await this.recommengVipGoodsInfo(groupKeys);
            data.lessonConfig.forEach((res) => {
                res.price = goodsPriceConfig[res.channelCode]?.payPrice || res.price / 100;
            });
            this.setState({
                showConfig: data
            });
        });
    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const bottomType: typeCode = typeCode.type2;

        switch (bottomType) {
            case typeCode.type2:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '升级全科VIP解锁所有直播课',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    price: this.showPrice,
                    fragmentName1,
                    payPathType: 0
                });
                break;
            default:
                break;
        }
    }
    async recommengVipGoodsInfo(groupKeys: GroupKey[]) {
        const goodsConfig = {};
        const goodsListInfo = await squirrelPrice({ groupKeys });
        goodsListInfo && goodsListInfo.forEach((res) => {
            goodsConfig[res.channelCode] = res;
        });
        return goodsConfig;
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            this.setState({
                goodsInfoPool: goodsListInfo
            });

            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getComparePrice();

                this.setPageInfo();
            }, 60);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice,
                        groupItems: item.groupItems
                    };
                }
            });
            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey }, 2);
        });
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);
        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    goLesson(e) {
        const url = e.refTarget.getAttribute('data-url');

        openWeb({
            url: url
        });

    }
    close() {
        webClose();
    }
}
