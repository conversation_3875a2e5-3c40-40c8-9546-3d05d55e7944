<import name="style" content="./main" />

<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />

<div class="page-qkupgrade page-questionKqsjQk">
    <div class="close" sp-on:click="close"></div>
    <sp:if value="state.showConfig.title">
        <div class="title">{{state.showConfig.title}}</div>
    </sp:if>
    <sp:if value="state.showConfig.subtitle">
        <div class="dec">{{state.showConfig.subtitle}}</div>
    </sp:if>
    <sp:if
        value="state.showConfig.lessonConfig && state.showConfig.lessonConfig.length"
    >
        <div class="course-box">
            <sp:each for="state.showConfig.lessonConfig">
                <div
                    class="course-item"
                    sp-on:click="goLesson"
                    data-url="{{$value.url}}"
                >
                    <div
                        class="bg-img"
                        style="background-image:url({{Tools.calcImg($value.cover)}})"
                    ></div>
                    <div class="course-item-bg">
                        <div class="info">
                            <span class="unit">¥</span>
                            <span class="price">{{$value.price}}</span>
                            <span>单独购买</span>
                        </div>
                    </div>
                </div>
            </sp:each>
        </div>
    </sp:if>

    <com:buyButton>
        <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
            {{self.nowCouponInfo.couponCode?'已优惠' + self.nowCouponInfo.priceCent
            + '元 >':'领取优惠券'}}
        </div>
    </com:buyButton>
</div>
