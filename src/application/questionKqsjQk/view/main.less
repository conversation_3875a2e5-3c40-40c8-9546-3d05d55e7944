.page-questionKqsjQk {
    position: relative;
    box-sizing: border-box;
    padding: 10px 0px;
    background-color: #fff;
    box-shadow: 0px 1px 0px 0px #FFFFFF;
    border-radius: 6px 6px 0px 0px;

    .close {
        position: absolute;
        z-index: 10;
        width: 40px;
        height: 40px;
        top: 0px;
        right: 0px;
        background: url(../images/close.png) no-repeat center center/20px 20px;
    }

    >.title {
        text-align: center;
        padding: 15px 15px 0;
        font-size: 20px;
        font-weight: 600;
    }

    >.dec {
        padding: 0 15px;
        margin-top: 3px;
        font-size: 13px;
        text-align: center;
    }

    .course-box {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        width: 100%;
        overflow-x: auto;
        overflow-y: visible;
        margin: 20px 0 0;
        padding: 0 15px;

        .course-item {
            flex-shrink: 0;
            width: 109px;
            height: 154px;
            margin-right: 7px;
            border-radius: 4px;
            overflow: hidden;
            position: relative;

            &:last-of-type {
                margin-right: 0;
            }

            .bg-img {
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 99px;
                height: 120px;
                margin: 0 auto;
                background-size: cover;
                background-position: center center;
                background-repeat: no-repeat;
                border-radius: 4px;
                z-index: 1;
            }

            .course-item-bg {
                background: #FFEBD5;
                width: 100%;
                height: 123px;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                position: absolute;
                left: 0;
                right: 0;
                bottom: 0;

                .info {
                    padding-bottom: 8px;
                    font-size: 12px;
                    color: #AA4120;
                    text-align: center;

                    .unit {
                        font-weight: bold;
                        margin-right: 2px;
                    }

                    .price {
                        font-size: 18px;
                        font-weight: bold;
                    }
                }
            }
        }
    }

    .paytype-box {
        padding: 0 15px;
    }

    .buy-footer {
        .coupon-pick {
            background-color: transparent;
        }
    }
}
