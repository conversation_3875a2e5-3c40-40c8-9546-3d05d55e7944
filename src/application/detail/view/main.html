<import name="style" content="./main" module="S" />

<import name="header" content=":component/header/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />

<div class=":container {{URLCommon.isElder?S.elder:''}}">
    <div class=":top">
        <com:header title="VIP权益介绍" theme="black" back="{{self.backCall}}"
            ><div sp:slot="right" class=":right"></div>
        </com:header>
        <div class=":list">
            <sp:each for="state.salesRights">
                <div
                    class=":item {{state.salesIndex===$index&&S['item--active']}}"
                    sp-on:click="onSalesTabClick"
                    data-idx="{{$index}}"
                    id="sales{{$index}}"
                >
                    <div
                        class=":item__icon"
                        style="background-image: url({{$value.icon}});background-size: 100% 100%;"
                    />
                    <div class=":item__txt">{{$value.name}}</div>
                </div>
            </sp:each>
        </div>
    </div>
    <div class=":main">
        <div
            class="swiper-container"
            skip="{{state.skipSwiper ? 'true' : 'false'}}"
        >
            <div class="swiper-wrapper">
                <sp:each for="state.salesRights">
                    <div class="swiper-slide">
                        <div class=":content">
                            <div class=":content__title">{{$value.name}}</div>
                            <div class=":content__subtitle">
                                {{$value.lightSpot}}
                            </div>
                            <div class=":content__introduce">
                                <div class=":content__textarea">{{#$value.introduce}}</div>
                            </div>
                        </div>
                    </div>
                </sp:each>
            </div>
        </div>
    </div>
    <div class=":footer">
        <div class=" {{state.goodsList.length > 1?'':'hide'}}">
            <com:bottomTabs
                comparePricePool="{{state.comparePriceMap}}"
                labelPool="{{state.labelMap}}"
                tabIndex="{{state.tabIndex}}"
                goodsList="{{state.goodsList}}"
                tabChange="{{self.onBottomTabChange}}"
            />
        </div>
        <com:buyButton>
            <div sp:slot="couponEntry" sp-on:click="onCouponEntryClick">
                {{self.currentCoupon.hint}}
            </div>
        </com:buyButton>
    </div>
    <com:payDialog />
    <com:expiredDialog />
</div>
