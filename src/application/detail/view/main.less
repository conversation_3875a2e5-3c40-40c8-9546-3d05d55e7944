html,
body {
    height: 100vh;
    overflow: hidden;
}

.container {
    height: 100vh;
    display: flex;
    flex-direction: column;

    .top {
        background: linear-gradient(131deg, #343238 0%, #28262b 100%);

        :global(.top-header .header) {
            max-width: unset;
        }
    }

    .right {
        width: 25px;
    }

    .list {
        padding: 10px 11px 0;
        display: flex;
        width: 100%;
        overflow: auto;
        -webkit-overflow-scrolling: touch;
    }

    .item {
        flex-shrink: 0;
        width: 80px;
        white-space: nowrap;
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #ffffff;

        &--active {
            color: #fcd2b5;
            position: relative;

            &:after {
                content: "";
                position: absolute;
                left: 50%;
                bottom: 0;
                transform: translateX(-50%);
                width: 20px;
                height: 2px;
                background: #ffffff;
                border-radius: 1px;
            }
        }

        &__icon {
            width: 36px;
            height: 36px;
        }

        &__txt {
            margin-top: 6px;
            margin-bottom: 10px;
            font-size: 13px;
            line-height: 18px;
        }
    }

    .main {
        background: #fff2eb;
        flex: 1;
        height: 0;
        display: flex;
        flex-direction: column;
    }

    .content {
        height: 100%;
        display: flex;
        flex-direction: column;
        background: #ffffff;
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
        border-radius: 6px 6px 0px 0px;
        border: 1px solid #fbf5db;
        padding-top: 20px;


        &__title {
            font-size: 17px;
            font-family: PingFangSC-Semibold, PingFang SC, sans-serif;
            font-weight: 600;
            color: #333333;
            line-height: 24px;
            text-align: center;
            flex-shrink: 0;
        }

        &__subtitle {
            margin-top: 3px;
            font-size: 11px;
            color: #e49f6d;
            line-height: 16px;
            text-align: center;
            flex-shrink: 0;
        }

        &__introduce {
            flex: 1;
            overflow-y: auto;
        }

        &__textarea {
            -webkit-overflow-scrolling: touch;
            padding: 15px;
            font-size: 13px;
            color: #6e6e6e;
            line-height: 18px;
            word-break: break-all;

            img {
                width: 100%;
            }

            li {
                list-style: unset;
            }

            ul,
            ol {
                padding-left: 1em;
            }
        }
    }

    .footer {
        position: relative;
        z-index: 1;
        background: white;
    }

    &.elder {
        .item {
            &__txt {
                font-size: 15px;
            }
        }

        .content {
            &__title {
                font-size: 20px;
            }

            &__subtitle {
                font-size: 14px;
            }

            &__textarea {
                font-size: 16px;
                line-height: 1.3;
            }
        }
    }
}

:global(.swiper-container) {
    margin-top: 15px;
    flex: 1;
    height: 0;
    display: flex;
    flex-direction: column;
    position: relative;
}

:global(.swiper-wrapper) {
    flex: 1;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

:global(.swiper-slide) {
    height: 100%;
    width: 3.125rem !important;
    transition: 300ms;
    transform: scale3d(0.9, 0.95, 1) translate3d(0, 6%, 0) !important;
}

:global(.swiper-slide-active),
:global(.swiper-slide-duplicate-active) {
    transform: scale(1) !important;
}

body {
    max-width: unset !important;
}