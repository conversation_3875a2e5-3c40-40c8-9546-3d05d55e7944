/*
 * ------------------------------------------------------------------
 * VIP权益子页面 
 * ------------------------------------------------------------------
 */

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { PayType, setPageName, URLParams } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsExtra, GoodsInfo, GroupKey } from ':store/goods';
import { ensureSiriusBound, getDefaultPayType, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackPageLoad, trackExit } from ':common/stat';
import { iosBuySuccess } from ':common/features/ios_pay';
import { batchGetBestCoupon, couponInfo2Coupon, Coupons, couponWithHint, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import ExpiredDialog from ':component/expiredDialog/main';
import { BUYED_URL } from ':common/navigate';
import { getSalesRightsList, SalesRights } from ':store/sales_rights';
import Swiper from 'swiper';
import 'swiper/swiper-bundle.css';
import { scrollIntoView } from ':common/features/dom';
import PayDialog from ':component/payDialog/main';
import jump from ':common/features/jump';
import { goBack } from ':common/core';
import { onWebBack } from ':common/features/persuade';
import { setTabIndex } from ':common/features/cache';

const groupKey = URLParams.groupKey as GroupKey;
const groupKeys = URLParams.groupKeys.split(',') as GroupKey[];
const code = URLParams.code;
setPageName('VIP权益介绍子页');

interface State {
    tabIndex: number;
    goodsList: GoodsInfo[];
    coupons: Coupons;
    labelMap: Partial<Record<GroupKey, GoodsExtra>>;
    comparePriceMap: Partial<Record<GroupKey, { diffPrice: string; allPrice: string }>>;
    salesRights: SalesRights[];
    skipSwiper: boolean;
    salesIndex: number;
}

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        expiredDialog: ExpiredDialog;
        payDialog: PayDialog;
    }

    swiper: Swiper;

    $constructor() {

        this.$super({
            name: module.id,
            target: document.body,
            view: View
        });

        this.state = {
            tabIndex: -1,
            goodsList: [],
            coupons: {},
            labelMap: {},
            comparePriceMap: {},
            salesRights: [],
            skipSwiper: false,
            salesIndex: -1
        };
    }

    get currentGoods() {
        return goodsInfoWithCoupon(this.state.goodsList[this.state.tabIndex], this.currentCoupon);
    }

    get currentCoupon() {
        return couponWithHint(this.state.coupons[this.state.goodsList[this.state.tabIndex]?.groupKey], true);
    }

    async didMount() {
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

        trackPageLoad({ groupKey });
        this.fetchGoodsInfo();
    }

    /** 首次全部请求完 */
    private salesRightsCache: Promise<SalesRights[][]> = null;

    private async fetchSalesRights() {
        // 先重置
        if (this.state.skipSwiper) {
            await new Promise(resolve => this.setState({ salesIndex: -1, skipSwiper: false }, resolve));
        }
        const firstRun = !this.salesRightsCache;

        let salesRights: SalesRights[];

        if (!this.salesRightsCache) {
            const fetchers = this.state.goodsList.map(item => getSalesRightsList({ groupKey: item.groupKey }));
            const currentFetcher = fetchers[this.state.tabIndex];

            salesRights = await currentFetcher;

            // 拉取其他tab的权限
            this.salesRightsCache = Promise.all(fetchers);
        } else {
            const cache = await this.salesRightsCache;
            salesRights = cache[this.state.tabIndex];
        }

        // 进来默认选中code对应的，切换的时候默认选中第一个
        const salesIndex = firstRun ? Math.max(salesRights.findIndex(s => s.code === code), 0) : 0;
        this.setState({ salesRights, skipSwiper: true }, () => {
            this.setSwiper();
            setTimeout(() => this.switchSalesTab(salesIndex));
        });
    }

    private setSwiper() {
        this.swiper?.destroy();
        this.swiper = new Swiper('.swiper-container', {
            initialSlide: 0,
            slidesPerView: 'auto',
            centeredSlides: true
        });
        this.swiper.on('slideChange', (swiper) => {
            this.switchSalesTab(swiper.activeIndex);
        });
    }

    private async fetchGoodsInfo() {
        const goodsList2 = await getGroupSessionInfo({ groupKeys });
        const goodsList: GoodsInfo[] = [];
        goodsList2.forEach(goodsInfo => {
            // 全科升级相当于未购买
            if (goodsInfo.groupKey === GroupKey.ChannelKemuAll && goodsInfo.bought && goodsInfo.upgrade) {
                goodsInfo.name = '升级全科目VIP';
                goodsInfo.bought = false;
            }
        });
        goodsList2.forEach((goodsInfo, index) => {
            // 处理第一个商品
            if (index === 0) {
                ensureSiriusBound({ groupKey: goodsInfo.groupKey, type: PayBoundType.GoStatusPage });
                if (goodsInfo.expired) {
                    this.children.expiredDialog.show({ time: goodsInfo.expiredTime });
                }
                if (goodsInfo.bought) {
                    jump.replace(BUYED_URL);
                    return;
                }
            }
            // 商品未购买才push
            if (!goodsInfo.bought) {
                goodsList.push(goodsInfo);
            }
        });
        this.setState({
            goodsList
        });

        // 选中指定的groupKey
        const tabIndex = goodsList.findIndex(item => item.groupKey === groupKey);
        this.switchTab(tabIndex);

        // 拉取不重要的信息
        setTimeout(() => {
            this.fetchCoupon();
            this.fetchComparePrice();
            this.fetchLabel();
        }, 60);
    }

    private async fetchCoupon() {
        // 获取优惠券
        const coupons = await batchGetBestCoupon(this.state.goodsList);
        this.setState({ coupons });
        this.setPayment();
    }

    private async fetchComparePrice() {
        // 获取比价信息
        const comparePriceGoods = this.state.goodsList.filter((item) => !item.inActivity);
        const comparePriceList = await Promise.all(comparePriceGoods.map(item => comparePrice({
            groupKey: item.groupKey,
            upgradeStrategyCode: item.upgradeStrategyCode
        })));
        const comparePriceMap = comparePriceList.reduce((acc, item, index) => {
            if (item) {
                acc[comparePriceGoods[index].groupKey] = {
                    diffPrice: item.savePrice,
                    allPrice: item.allPrice
                };
            }
            return acc;
        }, {});
        this.setState({ comparePriceMap });
    }

    private async fetchLabel() {
        // 获取label信息
        const labelGoods = this.state.goodsList.filter((item) => !item.inActivity);
        const labelList = await Promise.all(labelGoods.map(item => getSessionExtra({
            groupKey: item.groupKey
        })));
        const labelMap = labelList.reduce((acc, item, index) => {
            acc[labelGoods[index].groupKey] = item;
            return acc;
        }, {});
        this.setState({ labelMap });
    }

    private switchTab(tabIndex: number) {
        if (tabIndex !== this.state.tabIndex) {
            this.setState({ tabIndex });
            this.setPayment();
            this.fetchSalesRights();
        }
    }

    private switchSalesTab(tabIndex: number) {
        this.setState({
            salesIndex: tabIndex
        }, () => {
            this.swiper.slideTo(tabIndex);
            const scroller = document.querySelector('#sales' + tabIndex);
            scroller && scrollIntoView(scroller as HTMLElement, { leftThreshold: 40, rightThreshold: 40, center: true });
        });
    }

    private setPayment() {
        this.children.buyButton.setPay({
            androidPay: this.pay.bind(this),
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => iosBuySuccess({ groupKey: this.currentGoods.groupKey })
        });
        this.children.buyButton.setButtonConfig({
            groupKey: this.currentGoods.groupKey,
            type: 4,
            title: '¥ ' + this.currentGoods.payPrice + ' 确认协议并支付',
            subtitle: '有效期' + this.currentGoods.validDays + '天',
            fragmentName1: '底部吸底按钮'
        });
    }

    /** 发起支付 */
    async pay(stat: PayStatProps) {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.currentGoods.groupKey,
            sessionIds: this.currentGoods.sessionIds,
            activityType: this.currentGoods.activityType,
            couponCode: this.currentCoupon.code,
            ...stat
        }).catch(async (err) => {
            console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: this.currentGoods.groupKey,
                payPrice: this.currentGoods.payPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }

    async onCouponEntryClick() {
        const couponInfo = await selectUserCoupon(this.currentGoods, this.currentCoupon?.code);

        if (couponInfo) {
            this.setState({
                coupons: {
                    ...this.state.coupons,
                    [this.currentGoods.groupKey]: couponInfo2Coupon(couponInfo)
                }
            });
            this.setPayment();
        }
    }

    onSalesTabClick(e) {
        const tabIndex = +e.refTarget.getAttribute('data-idx');
        this.switchSalesTab(tabIndex);
    }

    onBottomTabChange = (tabIndex: number) => {
        this.switchTab(tabIndex);
    }

    goBackPage = async () => {
        await setTabIndex(Math.max(this.state.tabIndex, 0));

        goBack();
    }
    backCall = () => {
        trackExit();
        this.goBackPage();
    };
}
