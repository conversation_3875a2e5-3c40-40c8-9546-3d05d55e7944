.common-question {
    .sec-w {
        padding: 30px 15px;

        .sec-dp {
            background: #fff;
            border-radius: 10px;
            padding: 0 23px 10px 21px;
        }

        .dp-hd {
            display: block;
            width: 148px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            margin: 0 auto;
            color: #ffffff;
            font-size: 16px;
            background: linear-gradient(0deg, #d87447 0%, #f2b082 100%);
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
        }

        .dp-li {
            display: flex;
            display: -webkit-flex;
            padding: 24px 0;
            border-bottom: 1px dashed #d4d4d4;
            box-sizing: border-box;

            &:nth-last-child(1) {
                border: none;
            }

            .avatar {
                width: 26px;
                height: 26px;
                border-radius: 100%;
            }

            .content {
                -webkit-flex: 1;
                flex: 1;
                padding-left: 11px;
            }

            .p1 {
                font-size: 14px;
                color: #6b6870;
                padding: 2px 0 8px 0;
            }

            .p2 {
                font-size: 14px;
                color: #494455;
                line-height: 20px;
            }
        }

        .sec-qa {
            background: #fff;
            border-radius: 10px;
            padding: 0 21px 20px 18px;
        }

        .qa-hd {
            display: block;
            width: 128px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            margin: 0 auto;
            color: #ffffff;
            font-size: 16px;
            background: linear-gradient(0deg, #d87447 0%, #f2b082 100%);
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
        }

        .qa-li {
            display: flex;
            display: -webkit-flex;
            padding: 12px 0;
            box-sizing: border-box;

            .icon {
                width: 18px;
                height: 18px;
                border-radius: 100%;
                background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/q.png) center center/cover;
            }

            .content {
                -webkit-flex: 1;
                flex: 1;
                padding-left: 5px;
            }

            .p1 {
                font-size: 14px;
                color: #494455;
                padding: 0 0 8px 0;
                font-weight: bold;
            }

            .p2 {
                font-size: 14px;
                color: #75717d;
                line-height: 20px;
            }
        }
    }
}
