/*
 * ------------------------------------------------------------------
 * vip常见问题
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    styleType: 1
    questionList: {
        title: string
        answer: string
    }[]
}

export default class CommonQuestion extends Component<unknown, Props> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

    }
    willReceiveProps() {
        return true;
    }
}
