/*
 * main
 *
 * name: xiao<PERSON>a
 * date: 16/3/24
 */
import { CarType, KemuType, URLCommon, URLParams } from ':common/env';
import { CAR_KE1_VIDEO, CAR_KE4_VIDEO } from ':common/navigate';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { promiseIconList, promiseList } from ':common/features/promise';
import { getDivisionInfo } from ':store/chores';
import { GoodsInfo, GroupKey } from ':store/goods';
import { mergeObjects } from ':common/utils';
import { openWeb } from ':common/core';

interface State {
    divisionInfo: any
}

interface Props {
    goodsList?: GoodsInfo[]
    currentIndex?: number
    goodsInfo?: GoodsInfo
    pageUiConfig: any
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    get pageUiMaterial() {
        const { pageUiConfig } = this.props;
        return JSON.parse(pageUiConfig || '{}');
    }
    get headUiConfig() {
        const { goodsInfo } = this.props;
        const info = goodsInfo;
        const config: any = {
            img: this.pageUiMaterial.topImg,
            video: this.pageUiMaterial.topVideoUrl
        };

        if (info.headConfig?.img) {
            mergeObjects(config, {
                img: info.headConfig.img,
                bgc: info.headConfig.bgc,
                video: info.headConfig.video,
                transparent: true
            });
        }

        return config;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            divisionInfo: {}
        };
    }
    didMount(): void {
        this.getDivision();
    }
    getDivision() {
        getDivisionInfo({
            type: 20
        }).then(info => {
            this.setState({
                divisionInfo: info
            });
        });
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    onGoActionUrl(e) {
        const url = e.refTarget.getAttribute('data-url');

        openWeb({
            url
        });
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
