<import name="style" content="./main" />
<import name="pageHeader" content=":application/car/component/page-header/main" />
<import name="cards" content=":application/car/component/cards/main" />
<import name="standbyGoods" content=":application/car/component/standbyGoods/main" />
<import name="payType" content=":component/payType/main" />
<import name="studentGuide" content=":component/studentGuide/main" />
<import name="wenda" content=":component/wenda/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />
<import name="commonQuestion" content=":application/configPages/components/commonQuestion/main" />
<import name="buchangTips" content=":component/buchangTips/main" />
<import name="Count" content=":component/count/main" />


<div class="panel-carkemu14">
    <com:pageHeader 
        goAuth="{{props.goAuth}}"
        iconList="{{self.pageUiMaterial.topIconList}}"
        uiConfig="{{self.headUiConfig}}"
    />
    <sp:if value="{{self.pageUiMaterial.headBuchangTips}}">
        <com:buchangTips />
    </sp:if>

    <sp:if value="{{props.middleTab && props.goodsList.length}}">
        <div class="ipad-box">
            <div class="card-box phone-box"
                style="background: linear-gradient({{self.headUiConfig.bgc}} 50%, #f2f2f2 51%, #f2f2f2 100%);">
                <com:cards comparePricePool="{{props.comparePricePool}}" standbyPool="{{props.standbyPool}}"
                    goodsList="{{props.goodsList}}" tabChange="{{props.tabChange}}" tabIndex="{{props.currentIndex}}" />
            </div>
        </div>
    </sp:if>

    <sp:if value="props.standbyPool.length">
        <div class="ipad-box">
            <div class="phone-box">
                <com:standbyGoods changeGoods="{{props.changeGoods}}" standbyPool="{{props.standbyPool}}"
                    standbyDefaultTab="{{props.standbyDefaultTab}}" />
            </div>
        </div>
    </sp:if>

    <div class="ipad-box">
        <div class="phone-box">
            <sp:if value="props.goodsInfo.inActivity && !props.goodsInfo.upgrade">
                <div class="activity-buy-btn" sp-on:click="pay" data-fragment="主图">
                    <div class="div1">
                        <p class="p1">
                            <span class="sp1"></span>
                            <span class="sp2">比分开买节省{{props.comparePricePool[props.goodsInfo.groupKey].diffPrice}}元</span>
                        </p>
                        <p class="p2">
                            <span class="sp3">
                                日常价
                                <i class="i1">&nbsp; ¥ &nbsp;</i>
                                <b class="b1">{{props.goodsInfo.inActivity.preDiscountPrice}}</b>
                            </span>
                            <span class="sp4">
                                特惠价
                                <i class="i2">&nbsp; ¥ &nbsp;</i>
                                <b class="b2">{{props.payPrice || '--'}}</b>
                            </span>
                        </p>
                    </div>
                    <div class="div2">
                        <div class="p3">限时特惠</div>
                        <div class="p4">
                            <com:Count name="Count" startTime="{{props.goodsInfo.inActivity.discountStartTime}}"
                                endTime="{{props.goodsInfo.inActivity.discountEndTime}}" />
                        </div>
                    </div>
                </div>
            <sp:else/>
                <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                    <div class="buy-btn">
                        确认协议并支付 ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
                        '--'}}
                    </div>
                    <i class="label">{{props.labelPool[props.groupKey].label}}</i>
                </div>
                <div class="protocol-box">
                    <com:middleProtocol groupKey="{{props.groupKey}}" couponPool="{{props.couponPool}}" />
                </div>
            </sp:if>

            <sp:if value="{{state.divisionInfo.imageUrl}}">
                <img style="width: 284px;height: 2px;display: block;margin: 15px auto;"
                    src="{{state.divisionInfo.imageUrl}}" alt="">
            </sp:if>

            <div class="step-box">

                <sp:if value="!props.isHubei && !Platform.isXueTang">
                    <div class="wenda-box">
                        <com:wenda name="wenda" groupKey="{{props.groupKey}}" type="1" />
                    </div>
                </sp:if>

                <sp:each for="{{self.pageUiMaterial.imgList}}">
                    <sp:if value="{{$value.uniqkey}}">
                        <img src="{{$value.url}}" alt="" key="{{$value.url}}" sp-on:click="goAuth" />
                    <sp:elseif value="{{$value.activeUrl}}" />
                        <img src="{{$value.url}}" alt="" sp-on:click="onGoActionUrl" data-url="{{$value.actionUrl}}" />
                    <sp:else/>
                        <img src="{{$value.url}}" alt="" />
                    </sp:if>
                </sp:each>
            </div>

            <sp:if value="{{self.pageUiMaterial.commonQuestions && self.pageUiMaterial.commonQuestions.questionList}}">
                <com:commonQuestion styleType="{{self.pageUiMaterial.commonQuestions.styleType}}"
                    questionList="{{self.pageUiMaterial.commonQuestions.questionList}}" />
            </sp:if>
        </div>
    </div>
</div>
