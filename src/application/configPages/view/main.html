<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="buyDialogOrderSign" content=":component/buyDialogOrderSign/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />

<import name="standbyGoods" content=":application/car/component/standbyGoods/main" />

<import name="TemplateHome" content=":application/configPages/components/template_home/main" />

<div class="page-container page-car">
    <div class="page-header">
        <com:header title="{{(state.showFooter && state.prevScrollTop > 200)?self.nowGoodInfo.name: ' '}}" theme="black"
            endTheme="black" scrollTop="{{state.prevScrollTop}}" back="{{self.backCall}}" />
    </div>

    <div class="body-panel" sp-on:scroll="pageScroll">
        <sp:each for="{{state.config.goodsUiList}}">
            <div
                class="{{self.nowGoodInfo.groupKey === $value.channelCode?'show':'hide'}}">
                <sp:if
                    value="{{state.goodsUiConfigPool[$value.channelCode].templateKey === Texts.TEMPLATE_KEY_MAP.template_index}}">
                    <com:TemplateHome
                        name="{{$value.channelCode}}"
                        pageUiConfig="{{state.goodsUiConfigPool[$value.channelCode].data}}"
                        middleTab="{{state.config.middleTab}}"
                        goodsInfo="{{state.goodsInfoPool[$index]}}"
                        currentIndex="{{state.tabIndex}}"
                        goodsList="{{state.goodsInfoPool}}"
                        labelPool="{{state.labelPool}}"
                        comparePricePool="{{state.comparePricePool}}"
                        couponPool="{{state.couponPool}}"
                        standbyPool="{{state.tabIndex === 0?state.standbyPool:[]}}"
                        standbyDefaultTab="{{state.config.standbyDefaultTab}}"
                        payPrice="{{self.showPrice}}"
                        groupKey="{{$value.channelCode}}"
                        isHubei="{{state.isHubei}}"
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}"
                        tabChange="{{self.tabChangeCall}}"
                        changeGoods="{{self.changeGoods}}"
                    />
                </sp:if>
            </div>
        </sp:each>

    </div>

    <div class="{{state.showFooter ? '': 'hide'}}">
        <sp:if value="{{state.config.modelKey}}">
            <com:standbyGoods changeGoods="{{self.changeGoods}}" standbyDefaultTab="{{state.config.standbyDefaultTab}}"
                standbyPool="{{state.tabIndex === 0?state.standbyPool:[]}}"
                className="type1" />
        </sp:if>
        <div class="footer {{state.goodsInfoPool.length > 1?'':'hide'}}">
            <com:bottomTabs tabIndex="{{state.tabIndex}}" labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}" goodsList="{{state.goodsInfoPool}}"
                tabChange="{{self.tabChangeCall}}" standbyPool="{{state.tabIndex === 0?state.standbyPool:[]}}" />
        </div>
        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
            </div>
        </com:buyButton>
    </div>
    <!-- 所有商品信息加载完成才能加载这个组件，内部有根据商品判断 -->
    <sp:if value="state.showSignModal">
        <com:buyDialogOrderSign goodsInfoPool="{{state.goodsInfoPool}}" standbyPool="{{state.standbyPool}}"
            labelPool="{{state.labelPool}}" comparePricePool="{{state.comparePricePool}}"
            couponPool="{{state.couponPool}}" close="{{self.closeSignModal}}" />
    </sp:if>

    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
</div>
