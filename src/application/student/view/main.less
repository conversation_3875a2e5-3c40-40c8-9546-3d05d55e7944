.page-stu {
    background: #f6f6f6;
    height: 100%;

    .page-header {
        position: absolute;
        z-index: 1000;
        top: 0;
        left: 0;
        width: 100%;
        // background: #ffffff;
        // padding-bottom: 5px;
    }

    .body-panel {
        flex: 1;
        overflow-y: scroll;
        width: 100%;

        .top {
            background-color: #67dbff;
            background-image: url(../images/1.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            height: 640px;
            width: 100%;
            position: relative;

            .rule {
                background: url(../images/2.png) no-repeat;
                background-size: 100% 100%;
                height: 69px;
                width: 28px;
                position: absolute;
                top: 139px;
                right: 0;
            }

            .share {
                background: url(../images/3.png) no-repeat;
                background-size: 100% 100%;
                height: 41px;
                width: 51px;
                position: absolute;
                top: 223px;
                right: 0;
            }

            .trans {
                width: 375px;
                height: 63px;
                border: 1px oslid blue;
                background: linear-gradient(180deg, #67dbff, #f6f6f6);
                position: absolute;
                bottom: -20px;
            }
        }

        .stu-content {
            position: relative;
            display: flex;
            flex-direction: column;
            margin-top: -255px;

            img {
                width: 100%;
            }
        }

        .desc-wrap {
            width: 355px;
            height: 365px;
            background: url(../images/4.png) no-repeat;
            background-size: 100% 100%;
            margin: 0 auto;
            margin-left: 12px;

            &.desc-wrap1 {
                width: 355px;
                height: 443px;
                background: url(../images/6.png) no-repeat;
                background-size: 100% 100%;
            }

            .p1 {
                margin: 24px auto 0 auto;
                width: 144px;
                height: 21px;
                background: url(../images/7.png) no-repeat;
                background-size: 100% 100%;
            }

            .p2 {
                font-size: 13px;
                text-align: center;
                color: #6e6e6e;
                line-height: 18px;
                margin-top: 5px;
            }

            .divider {
                width: 305px;
                height: 1px;
                border-top: 1px dashed #e8e8e8;
                margin: 10px auto 0 auto;
            }

            .desc {
                display: flex;
                justify-content: center;
                align-items: end;
                padding-top: 12px;
            }

            .desc1 {
                display: flex;
                flex-direction: column;
                align-items: center;

                .sp1 {
                    font-size: 11px;
                    text-decoration: line-through;
                    text-align: center;
                    color: #a0a0a0;
                    line-height: 12px;
                }

                .sp2 {
                    i {
                        font-size: 18px;
                        text-align: center;
                        color: #fe328a;
                        line-height: 25px;
                    }

                    font-size: 48px;
                    font-weight: bold;
                    text-align: center;
                    color: #fe328a;
                    letter-spacing: -0.5px;
                    margin-top: 5px;
                    margin-left: -10px;
                }

                .sp3 {
                    width: 62px;
                    height: 25px;
                    background: #feec0d;
                    font-size: 12px;
                    text-align: center;
                    color: #060809;
                    line-height: 25px;
                    font-weight: bold;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    border-radius: 6px;
                    margin-top: 5px;

                    i {
                        width: 0;
                        height: 0;
                        border-bottom: 6px solid #feec0d;
                        border-right: 6px solid transparent;
                        border-left: 6px solid transparent;
                        position: absolute;
                        top: -6px;
                        margin-left: 24px;
                    }
                }
            }

            .divider2 {
                width: 2px;
                height: 98px;
                border-left: 1px solid #e8e8e8;
                margin: 0 9px;
            }

            .desc2 {
                .ul1 {
                    display: flex;

                    li {
                        width: 52px;
                        height: 55px;
                        border: 1px solid #ffd4e3;
                        border-radius: 6px;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-around;
                        align-items: center;
                        margin: 0 2px;
                        padding-top: 8px;

                        .dp1 {
                            width: 47px;
                            height: 17px;
                            font-size: 12px;
                            font-weight: bold;
                            color: #6e6e6e;
                            line-height: 17px;
                            text-align: center;
                        }

                        .dp2 {
                            width: 30px;
                            height: 22px;
                            font-size: 16px;
                            color: #a97777;
                            line-height: 16px;
                            text-align: center;

                            i {
                                font-size: 11px;
                            }
                        }
                    }
                }

                .ul2 {
                    padding-left: 15px;

                    li {
                        list-style-type: disc;
                        font-size: 13px;
                        font-weight: bold;
                        color: #464646;
                        line-height: 18px;
                        padding-top: 7px;
                    }
                }
            }

            .p3 {
                width: 322px;
                height: 36px;
                background: rgba(255, 243, 247, 0.75);
                border-radius: 5px;
                font-size: 15px;
                font-weight: bold;
                text-align: center;
                color: #fe328a;
                line-height: 36px;
                margin: 12px auto 0 auto;
            }

            .p4 {
                height: 18px;
                font-size: 13px;
                text-align: center;
                color: #6e6e6e;
                line-height: 18px;
                text-align: center;
                margin-top: 10px;
            }

            .stu-info {
                display: flex;
                padding: 20px 0 0 15px;

                .info1 {
                    width: 48px;
                    height: 48px;
                    border-radius: 100%;
                    overflow: hidden;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .info2 {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    padding-left: 10px;

                    .ip1 {
                        display: flex;

                        span {
                            font-size: 16px;
                            font-weight: bold;
                            text-align: left;
                            color: #060809;
                            line-height: 22px;
                        }

                        label {
                            width: 72px;
                            height: 18px;
                            background: url(../images//5.png) no-repeat;
                            background-size: 100% 100%;
                        }
                    }

                    .ip2 {
                        width: 111px;
                        height: 24px;
                        background: linear-gradient(256deg, #fd71cb 5%, #ffa1dd 85%);
                        font-size: 12px;
                        text-align: center;
                        color: #ffffff;
                        line-height: 24px;
                        letter-spacing: 0.4px;
                        border-radius: 5px;
                        position: relative;

                        i {
                            width: 0;
                            height: 0;
                            border-bottom: 10px solid #ffa1dd;
                            border-right: 10px solid transparent;
                            border-left: 10px solid transparent;
                            position: absolute;
                            bottom: 0px;
                            left: -10px;
                        }
                    }

                }
            }
        }

        .kemuall-1 {
            position: relative;
            padding: 30px 15px 40px 15px;
        }

        .kemuall-2 {
            position: relative;
            padding: 0 15px;
        }

        .kemuall-3 {
            position: relative;
            padding: 0 15px;
            margin-top: 25px;
        }

        .kemuall-4 {
            position: relative;
            padding: 0 15px;
            margin-top: 25px;
        }

        .kemuall-5 {
            position: relative;
            padding: 0 15px;
            margin-top: 30px;

            .img {
                width: 325px;
                height: 57px;
            }
        }

        .kemuall-9 {
            padding: 30px 15px 0 15px;
        }
    }

    .footer {
        position: relative;
        z-index: 10;
        border-radius: 16px 16px 0 0;
        padding: 5px 24px 5px 24px;
        background: #ffffff;
        padding-bottom: calc(~"10px + constant(safe-area-inset-bottom)/2"
            ) !important;
        /* 兼容 iOS < 11.2 */
        padding-bottom: calc(~"10px + env(safe-area-inset-bottom)/2");

        .buy-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding-bottom: 10px;

            .goods-button {
                width: 140px;
                height: 59px;
                // background: url(../images/button-left.png) no-repeat center;
                background-size: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
            }

            .goods-button01 {
                width: 190px;
                height: 59px;
                // background: url(../images/button-right.png) no-repeat center;
                background-size: 100%;
                margin-left: 15px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
            }

            .goods-button02 {
                width: 335px;
                height: 52px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
                background: linear-gradient(90deg, #fe7b8b 10%, #f1341e 53%, #fbc0b8 93%);
                border-radius: 26px;
                box-shadow: 1px 1px 3px 0px rgba(254, 144, 161, 0.78);
            }

            .button-xuan-fu {
                position: absolute;
                top: 0px;
                right: 5px;
                transform: translateY(-100%);
                width: 116px;
                height: 39px;
                text-align: center;
                padding-top: 9px;
                font-size: 12px;
                color: #6E2F1C;
                // background: url(../images/<EMAIL>) no-repeat center center/cover;
            }

            .goods-button-title {
                font-size: 15px;
                font-family: PingFangSC, PingFangSC-Semibold;
                font-weight: 600;
                color: #ffffff;
                line-height: 20px;
            }

            .goods-button-desc {
                font-size: 11px;
                font-family: PingFangSC, PingFangSC-Regular;
                font-weight: 400;
                color: #ffffff;
                line-height: 14px;
            }
        }
    }

    .btns {
        display: flex;
        padding: 20px 14px 0 14px;
        justify-content: space-between;
        align-items: center;

        .btn1 {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            color: #fe328a;
            line-height: 22px;
            padding: 13px 10px;
            border-radius: 48px;
            border: 1px solid #fe328a;
        }

        .btn2 {
            width: 210px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            color: #ffffff;
            line-height: 22px;
            background-color: #fe328a;
            border-radius: 48px;
            padding: 13px 0;
            position: relative;

            .tips {
                position: absolute;
                top: 0;
                right: 0;
                transform: translateY(-60%);
                width: 120px;
                height: 22px;
                background: #feec0d;
                border-radius: 33px 33px 33px 2px;
                font-size: 12px;
                color: #0D2531;
            }
        }


    }

    .proto-wrap {
        padding: 5px 15px 0 15px;
    }

    .proto {
        padding: 8px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .pay-guide {
            font-size: 12px;
            color: #333333;
        }
    }

    .footer {
        .btns {
            padding: 15px 0 20px 0;
        }

        .btns1 {
            padding: 5px 0 5px 0;
        }
    }

    .hide {
        display: none;
    }
}