<import name="style" content="./main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="header" content=":component/header/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />

<import name="loading" content=":application/buyed/components/loading/main" />
<import name="identify" content=":application/student/components/identify/main" />
<import name="rule" content=":application/student/components/rule/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />

<div class="page-container page-stu">
    <div class="page-header">
        <com:header title="全科VIP学生卡" theme="white" endTheme="white" scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}" />
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <div class="top">
            <div class="rule" sp-on:click="showRuleDialog"></div>
            <div class="share" sp-on:click="share"></div>
            <div class="trans"></div>
        </div>
        <div class="stu-content" ref="stuContent">
            <div class="desc-wrap {{state.identified ? 'desc-wrap1' : ''}}">
                <p class="p1"></p>
                <p class="p2">一站式解锁从科一到科四所有付费权益</p>
                <p class="divider"></p>
                <div class="desc">
                    <div class="desc1">
                        <span class="sp1">原价¥98</span>
                        <span class="sp2"><i>¥</i><b>{{state.goodsInfoPool[0].payPrice}}</b></span>
                        <span class="sp3">立减30元<i></i></span>
                    </div>
                    <div class="divider2"></div>
                    <div class="desc2">
                        <ul class="ul1">
                            <li>
                                <p class="dp1">科一VIP</p>
                                <p class="dp2"><i>¥</i>50</p>
                            </li>
                            <li>
                                <p class="dp1">科二VIP</p>
                                <p class="dp2"><i>¥</i>40</p>
                            </li>
                            <li>
                                <p class="dp1">科三VIP</p>
                                <p class="dp2"><i>¥</i>60</p>
                            </li>
                            <li>
                                <p class="dp1">科四VIP</p>
                                <p class="dp2"><i>¥</i>50</p>
                            </li>
                        </ul>
                        <ul class="ul2">
                            <li>加赠所有付费课程,无需二次付费</li>
                            <li>{{state.goodsInfoPool[0].validDays}}天有效期</li>
                        </ul>
                    </div>
                </div>
                <p class="p3">一站式学习，考不过最高补偿140元</p>
                <sp:if value="state.identified">
                    <div class="stu-info">
                        <div class="info1">
                            <img src="{{state.userData.avatar}}" alt="">
                        </div>
                        <div class="info2">
                            <p class="ip1"><span>{{state.userData.nickname}}</span><label></label></p>
                            <p class="ip2">已获得专属优惠<i></i></p>
                        </div>
                    </div>
                </sp:if>
                <div class="btns" ref="centerButton">
                    <div class="btn1" sp-on:click="share">分享给同学</div>
                    <div class="btn2" sp-on:click="btnOperation" data-fragment="主图">
                        {{state.identified ? '¥' +
                        state.goodsInfoPool[0].payPrice + '优惠开通全科VIP >' :
                        '认证完成即可68元开通'}}
                        <div class="tips" sp:if="state.identified">比正常价优惠30元</div>
                    </div>
                </div>
                <sp:if value="Platform.isAndroid && state.identified">
                    <com:payType theme="stud" />
                </sp:if>
                <sp:if value="!state.identified">
                    <p class="p4">限18-22周岁完成认证的用户专享</p>
                </sp:if>
            </div>
            <sp:if value="state.identified">
                <div class="proto-wrap">
                    <div class="proto">
                        <com:readProtocol theme="stud" />
                        <sp:if value='{{Platform.isIOS}}'>
                            <div class="pay-guide" sp-on:click="gotoPayGuide">支付教程 ></div>
                        </sp:if>
                    </div>
                </div>
            </sp:if>
            <div class="kemuall-1" key="kemuall-1">
                <img src="https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/img-title-zbkbxbd.png" />
            </div>
            <div class="kemuall-2">
                <img src="https://web-resource.mc-cdn.cn/web/vip/car/step2.png" />
            </div>
            <div class="kemuall-3">
                <img src="https://web-resource.mc-cdn.cn/web/vip/car/step3.png" />
            </div>
            <div class="kemuall-4">
                <img src="https://web-resource.mc-cdn.cn/web/vip/car/step4.png" />
            </div>
            <div class="kemuall-5">
                <img src="https://web-resource.mc-cdn.cn/web/vip/car/step5.png" />
            </div>

            <div sp-on:click="goAuth" class="kemuall-9" data-uniqkey="bgbc">
                <img src="https://web-resource.mc-cdn.cn/web/vip/t4.png" />
            </div>

            <com:commonQuestion type="16" />
        </div>

    </div>
    <div class="footer {{state.showFooter ? '': 'hide'}}">
        <sp:if value="Platform.isAndroid && state.identified">
            <com:payType theme="stud" />
        </sp:if>
        <div class="btns {{state.identified ? 'btns1' : ''}}">
            <div class="btn1" sp-on:click="share">分享给同学</div>
            <div class="btn2" sp-on:click="btnOperation" data-fragment="底部吸底按钮">
                {{state.identified ? '¥' + state.goodsInfoPool[0].payPrice +
                '优惠开通全科VIP >' : '认证完成即可68元开通'}}
                <div class="tips" sp:if="state.identified">比正常价优惠30元</div>
            </div>
        </div>
        <sp:if value="state.identified">
            <div class="proto">
                <com:readProtocol theme="stud" />
                <sp:if value='{{Platform.isIOS}}'>
                    <div class="pay-guide" sp-on:click="gotoPayGuide">支付教程 ></div>
                </sp:if>
            </div>
        </sp:if>

    </div>
    <com:payDialog />

    <com:buyButton />
    <com:loading />
    <com:expiredDialog />
    <sp:if value="state.showIdentifyDialog">
        <com:identify hideIdentifyDialog="{{self.hideIdentifyDialog.bind(self)}}"
            identifiedBuy="{{self.identifiedBuy.bind(self)}}" payPrice="{{state.goodsInfoPool[0].payPrice}}"
            confirmSubmitSuccess="{{self.confirmSubmitSuccess}}" />
    </sp:if>
    <sp:if value="state.showRule">
        <com:rule hideRuleDialog="{{self.hideRuleDialog.bind(self)}}"/>
    </sp:if>

</div>