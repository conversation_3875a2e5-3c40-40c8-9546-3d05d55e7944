/*
 * ------------------------------------------------------------------
 * 全科学生活动
 * ------------------------------------------------------------------
 */

import { KemuType, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import { getGroupSessionInfo, GoodsInfo, GroupKey, comparePrice } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import { newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackEvent, trackGoPay, trackPageLoad } from ':common/stat';
import Header from ':component/header/main';
import { checkReaded } from ':common/features/agreement';
import { queryAuthRes, Authenticate, IdentifiedStatus } from ':store/student';
import { getAuthToken, openVipWebView, setStatusBarTheme } from ':common/core';
import { login } from ':common/features/login';
import Loading from ':application/buyed/components/Loading/main';
import { MCProtocol } from '@simplex/simple-base';
import { BUYED_URL, PAY_GUIDE_URL } from ':common/navigate';
import jump from ':common/features/jump';

URLCommon.kemu = KemuType.Ke1;
URLParams.kemuStyle = String(KemuType.Ke1);

const identifiedStatusMap = {
    [IdentifiedStatus.noIdentified]: '未认证',
    [IdentifiedStatus.identifieding]: '认证中',
    [IdentifiedStatus.identified]: '认证通过',
    [IdentifiedStatus.noPass]: '认证不通过'
};

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    expireTimeString: string,
    prevScrollTop: number,
    showIdentifyDialog: boolean,
    identified: boolean
    showFooter: boolean,
    showRule: boolean,
    userData: {
        nickname: string,
        avatar: string
    }
}

let timer;
let showFooterHeight = 400;
export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog,
        header: Header,
        loading: Loading
    };
    get nowGoodInfo() {
        console.log('now  goods info');
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey] || {};
    }

    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            goodsInfoPool: [
                {
                    payPrice: '--',
                    validDays: '--',
                    groupKey: GroupKey.ChannelKemuAllStudent
                } as GoodsInfo
            ],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            expireTimeString: '',
            prevScrollTop: 0,
            showIdentifyDialog: false,
            identified: false,
            showFooter: false,
            showRule: false,
            userData: {
                nickname: '***',
                avatar: 'https://jiakao-audit.image.mucang.cn/jiakao-audit/2022/01/24/11/8a66545b8d3a49f5af0b3dc1c5fd87f5.png'
            }
        };

    }
    async didMount() {
        setStatusBarTheme('dark');
        setPageName('全科VIP学生会员页');
        this.settingShare();
        // 页面进出时长打点
        trackPageLoad();
        this.setShowFooterHeight();
        await this.getGoodInfo();
        // await this.getComparePrice();
        const authToken = await getAuthToken();
        if (authToken) {
            const authRes: Authenticate = await queryAuthRes();
            const identified = (authRes.authStatus === IdentifiedStatus.identified);
            this.setState({
                identified: identified
            });
            // 如果是未登录时点击按钮调起登陆，登陆成功后判断是否展示认证弹窗
            if (window.localStorage.getItem('unloginIndentifyBtnClicked') === 'true' && !identified) {
                window.localStorage.setItem('unloginIndentifyBtnClicked', '');
                this.setState({
                    showIdentifyDialog: true
                });
            }
            this.getUserData();
        }
        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: async () => {
                iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });
    }
    getUserData() {
        MCProtocol.Core.User.get((data: any) => {
            this.setState({
                userData: data.data
            });
        });
    }
    settingShare() {
        MCProtocol.Core.Share.setting({
            channel: 'qq,weixin_friend,weixin_moment,sina',
            type: '',
            shareData: {
                title: '【学生专享】驾考宝典全科VIP会员立减30%！',
                description: '仅限18-22岁周岁学生专享优惠，一站式解锁四科VIP；',
                url: `https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-share-vip/student.html?carStyle=${URLCommon.tiku}&kemuStyle=${URLCommon.kemu}`,
                iconUrl: 'https://sirius.mc-cdn.cn/mc-sirius/2022/10/25/17/0fe99e07f50c4c04a7b7b7fac2b08fc6.png'

            }
        });
    }
    setShowFooterHeight() {
        const centerButtonDom = this.getDOMNode().centerButton as HTMLElement;
        const stuContentDom = this.getDOMNode().stuContent as HTMLElement;
        showFooterHeight = centerButtonDom.offsetTop + stuContentDom.offsetTop;
    }
    pageScroll(e) {
        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = +e.refTarget.scrollTop;

            if (prevScrollTop > showFooterHeight) {
                this.setState({
                    prevScrollTop,
                    showFooter: true
                });
            } else {
                this.setState({
                    prevScrollTop,
                    showFooter: false
                });
            }

        }, 60);
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });
        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                if (index === 0 && goodInfo.bought) {
                    jump.replace(BUYED_URL);
                    return;
                }
                newGoodsPool.push(goodInfo);
            });
            this.setState({
                goodsInfoPool: newGoodsPool
            });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice,
                        groupItems: item.groupItems
                    };
                }
            });
            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: '',
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
        });
    }
    btnOperation = async (e) => {
        e.stopPropagation();
        if (this.state.identified) {
            this.buttonBuy(e);
        } else {
            const authToken = await getAuthToken();
            trackEvent({
                fragmentName1: '认证按钮',
                actionName: '去认证',
                actionType: '点击'
            });

            if (authToken) {
                this.setState({
                    showIdentifyDialog: true
                });
            } else {
                window.localStorage.setItem('unloginIndentifyBtnClicked', 'true');
                login();
            }
        }
    }
    hideIdentifyDialog = (e) => {
        this.setState({
            showIdentifyDialog: false
        });
    }
    showRuleDialog = (e) => {
        this.setState({
            showRule: true
        });
    }
    hideRuleDialog = (e) => {
        this.setState({
            showRule: false
        });
    }
    buttonBuy = (e) => {
        checkReaded(() => {
            this.payBtnCall(e);
        });
    }
    identifiedBuy(e) {
        checkReaded(() => {
            this.payBtnCall(e);
        });
    }
    payBtnCall = (e) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });
        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1
            });
        } else {
            this.pay({ fragmentName1 });
        }
    }
    share() {
        trackEvent({
            fragmentName1: '主图',
            fragmentName2: '分享入口',
            actionName: '分享',
            actionType: '点击'
        });

        MCProtocol.Core.Web.menu();
    }
    confirmSubmitSuccess = async () => {
        const authRes: Authenticate = await queryAuthRes();
        const identified = (authRes.authStatus === IdentifiedStatus.identified);
        this.setState({
            identified: identified
        });
    }
    gotoPayGuide() {
        openVipWebView({
            url: PAY_GUIDE_URL
        });
    }
}
