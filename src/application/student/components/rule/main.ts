import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    hideRuleDialog(e?: Event)
}

export default class extends Component<Props> {
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });
    }
    willReceiveProps() {
        return true;
    }
    async didMount() {
        return true;
    }
  
    close(e) {
        this.props.hideRuleDialog(e);
    }
}
