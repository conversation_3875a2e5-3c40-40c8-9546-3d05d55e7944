.rule-w {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 20;
}

.rule-content {
    width: 330px;
    // background: url(../images/1.png) no-repeat left top;
    // background-size: 330px 138px;
    background-color: #fff;
    border-radius: 19px;
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    height: 80%;
    overflow: hidden;

    .close {
        position: absolute;
        top: 0px;
        right: 0px;
        width: 44px;
        height: 44px;
        z-index: 30;
        background: url(../images/2.png) no-repeat center center;
        background-size: 22px 22px;
    }

    .rule-w {
        background: url(../images/1.png) no-repeat left top;
        background-size: 330px 138px;
        background-color: #fff;
        height: 100%;
        padding: 20px;
        display: flex;
        flex-direction: column;
        .title{
            flex-shrink: 0;
            padding-bottom: 10px;
        }
        .content{
            overflow-y: scroll;
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }
    }

    h1 {
        font-size: 20px;
        color: #333333;
        line-height: 1.4;
        text-align: center;
    }

    h3 {
        font-size: 16px;
        color: #333333;
        line-height: 1.4;
        padding: 20px 0 10px 0;
    }

    p {
        font-size: 14px;
        color: #333333;
        line-height: 1.4;
        padding-left: 15px;
    }
}