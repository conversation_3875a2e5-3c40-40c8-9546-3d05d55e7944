import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { makeToast } from ':common/features/dom';
import { submitAuthApply } from ':store/student';
import { openVipWebView, openWeb } from ':common/core';
import { PAY_GUIDE_URL, PROTOCOL1_URL, PROTOCOL4_URL, PROTOCOL5_URL } from ':common/navigate';

interface State {
    step: string,
    errorMsg?: string
    readed: boolean,
    userName: string,
    idCardNum: string,
    schoolName: string
}

interface Props {
    hideIdentifyDialog(e?: Event),
    identifiedBuy(e?: Event),
    confirmSubmitSuccess(),
    payPrice: string
}

export default class extends Component<State, Props> {
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            step: 'form',
            userName: '',
            idCardNum: '',
            schoolName: '',
            readed: false
        };
    }
    willReceiveProps() {
        return true;
    }
    async didMount() {
        return true;
    }
    readProto() {
        this.setState({
            readed: !this.state.readed
        });
    }

    submitInfo() {
        if (!this.state.readed) {
            makeToast('请先阅读并同意《个人信息保护声明》《隐私政策》');
            return;
        }

        const dom = this.getDOMNode();
        const userName = (dom.userName as HTMLInputElement).value;
        const idCardNum = (dom.idCardNum as HTMLInputElement).value;
        const schoolName = (dom.schoolName as HTMLInputElement).value;

        if (!userName) {
            makeToast('请输入姓名！');
            return;
        }
        if (!(/^[\u4e00-\u9fa5]{2,4}$/).test(userName)) {
            makeToast('请输入2-4个字的中文！');
            return;
        }
        if (!idCardNum) {
            makeToast('请输入身份证号！');
            return;
        }

        if (schoolName.length > 15) {
            makeToast('学校名称不能大于15个字！');
            return;
        }

        if (!this.isIdentityNumber(idCardNum)) {
            makeToast('请输入正确的身份证号码！');
            return;
        }

        this.setState({
            userName,
            idCardNum,
            schoolName,
            step: 'confirm'
        });
    }

    modify() {
        this.setState({
            step: 'form'
        }, () => {
            const dom = this.getDOMNode();
            (dom.userName as HTMLInputElement).value = this.state.userName;
            (dom.idCardNum as HTMLInputElement).value = this.state.idCardNum;
            (dom.schoolName as HTMLInputElement).value = this.state.schoolName;
        });
    }

    async confirmSubmit() {
        const { confirmSubmitSuccess } = this.props;
        const res = await submitAuthApply({
            userName: this.state.userName,
            idCardNum: this.state.idCardNum,
            schoolName: this.state.schoolName
        });

        confirmSubmitSuccess && confirmSubmitSuccess();

        if (res.authResult) {
            this.setState({
                step: 'success'
            });
        } else {
            this.setState({
                step: 'fail',
                errorMsg: res.errorMsg
            });
        }
    }

    buy(e) {
        this.props.identifiedBuy(e);
    }

    isIdentityNumber(num: string) {

        num = num.toUpperCase();

        // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X。
        if (!((/(^\d{15}$)|(^\d{17}([0-9]|X)$)/).test(num))) {
            return false;
        }

        // 校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
        // 下面分别分析出生日期和校验位
        const len = num.length;
        let re;

        if (len === 15) {

            re = new RegExp(/^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/);
            const arrSplit = num.match(re);

            // 检查生日日期是否正确
            const dtmBirth = new Date('19' + arrSplit[2] + '/' + arrSplit[3] + '/' + arrSplit[4]);

            const bGoodDay = (dtmBirth.getFullYear() === Number(arrSplit[2])) && ((dtmBirth.getMonth() + 1) === Number(arrSplit[3])) && (dtmBirth.getDate() === Number(arrSplit[4]));

            if (!bGoodDay) {

                return false;

            } else {

                // 将15位身份证转成18位
                // 校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
                const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
                const arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
                let nTemp = 0;
                num = num.substr(0, 6) + '19' + num.substr(6, num.length - 6);
                for (let i = 0; i < 17; i++) {
                    nTemp += Number(num.substr(i, 1)) * arrInt[i];
                }

                num += arrCh[nTemp % 11];

                return true;

            }
        }

        if (len === 18) {

            re = new RegExp(/^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/);
            const arrSplit = num.match(re);

            // 检查生日日期是否正确
            const dtmBirth = new Date(arrSplit[2] + '/' + arrSplit[3] + '/' + arrSplit[4]);
            const bGoodDay: any = (dtmBirth.getFullYear() === Number(arrSplit[2])) && ((dtmBirth.getMonth() + 1) === Number(arrSplit[3])) && (dtmBirth.getDate() === Number(arrSplit[4]));

            if (!bGoodDay) {

                return false;

            } else {

                // 检验18位身份证的校验码是否正确。
                // 校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
                const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
                const arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
                let nTemp = 0;

                for (let i = 0; i < 17; i++) {
                    nTemp += Number(num.substr(i, 1)) * arrInt[i];
                }

                const valnum = arrCh[nTemp % 11];

                if (valnum !== num.substr(17, 1)) {
                    return false;
                }

                return true;

            }

        }

        return false;

    }
    goProtocol(e) {
        const type = +e.refTarget.getAttribute('data-type');

        if (type === 1) {
            openWeb({
                url: PROTOCOL4_URL
            });
        }
        if (type === 2) {
            openWeb({
                url: PROTOCOL5_URL
            });
        }
    }
    close(e) {
        this.setState({
            step: 'form'
        });
        this.props.hideIdentifyDialog(e);
    }
    gotoPayGuide() {
        openVipWebView({
            url: PAY_GUIDE_URL
        });
    }
}
