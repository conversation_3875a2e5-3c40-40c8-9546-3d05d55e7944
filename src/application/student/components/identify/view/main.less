.identify-w {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1002;
}

.id-content {
    width: 330px;
    background: url(../images/1.png) no-repeat left top;
    background-size: 330px 138px;
    background-color: #fff;
    border-radius: 19px;
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    padding: 20px;

    &.nobg{
        background: #fff;
    }

    .close {
        position: absolute;
        top: 7px;
        right: 7px;
        width: 44px;
        height: 44px;
        z-index: 20;
        background: url(../images/2.png) no-repeat center center;
        background-size: 22px 22px;
    }
}

.id-form {
    .title {
        font-size: 23px;
        font-weight: 800;
        color: #333333;
        line-height: 32px;
        text-align: center;
        padding: 20px 0 10px 0;
        position: relative;

        i {
            width: 93px;
            height: 13px;
            background: #ffe54b;
            position: absolute;
            right: 30px;
            bottom: 10px;
            z-index: -1;
        }
    }

    .form-item {
        display: flex;
        height: 48px;
        align-items: center;
        justify-content: start;
        border-bottom: 1px solid rgba(81, 13, 44, 0.10);

        label {
            font-size: 14px;
            color: #333333;
            line-height: 24px;
            width: 84px;
            height: 24px;
        }

        input {
            font-size: 14px;
            height: 26px;
            border: 0;
            background: transparent;
        }
    }

    .p1 {
        font-size: 12px;
        color: #999999;
        line-height: 17px;
        margin-top: 10px;
    }

    .p2 {
        font-size: 12px;
        color: #fe328a;
        line-height: 17px;
        margin-top: 10px;
        background: #fff3f7;
        border-radius: 5px;
        padding: 6px;
    }

    .submit {
        width: 280px;
        height: 49px;
        background: url(../images/3.png) no-repeat;
        background-size: 100% 100%;
        border-radius: 19px;
        margin: 15px auto 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        font-weight: bold;
        text-align: center;
        color: #ffffff;
        line-height: 24px;
        text-shadow: 0px 1px 1px 0px rgba(200, 0, 0, 0.32);
    }

    .proto {
        padding-top: 6px;
        height: 40px;

        

        .sp1 {
            font-size: 11px;
            color: #750201;
            line-height: 16px;
            padding: 3px 0 3px 15px;

            &.checked {
                background: url(../images/4.png) no-repeat left center;
                background-size: 12px 12px;
            }
    
            &.unchecked {
                background: url(../images/8.png) no-repeat left center;
                background-size: 12px 12px;
            }
        }

        .sp2 {
            font-size: 11px;
            color: #FE328A;
            line-height: 16px;
            padding: 3px 0;
        }

        .sp3 {
            font-size: 11px;
            color: #FE328A;
            line-height: 16px;
            padding: 3px 0;
        }
    }
}

.id-confirm {
    .title {
        font-size: 23px;
        font-weight: 800;
        color: #333333;
        line-height: 32px;
        text-align: center;
        padding: 20px 0 10px 0;
        position: relative;

        i {
            width: 93px;
            height: 13px;
            background: #ffe54b;
            position: absolute;
            right: 35px;
            bottom: 10px;
            z-index: -1;
        }
    }

    .con {
        display: flex;
        height: 40px;
        align-items: center;
        justify-content: start;

        label {
            font-size: 14px;
            color: #333333;
            line-height: 20px;
        }

        span {
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            padding-left: 15px;
        }
    }

    .p1 {
        font-size: 12px;
        color: #fe328a;
        line-height: 17px;
        margin-top: 10px;
        background: #fff3f7;
        border-radius: 5px;
        padding: 6px;
    }

    .btns {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 18px;

        .btn1 {
            font-size: 16px;
            text-align: center;
            font-weight: bold;
            color: #fe328a;
            line-height: 22px;
            padding: 12px 10px;
            border-radius: 48px;
            border: 1px solid #fe328a;
        }

        .btn2 {
            width: 190px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            color: #ffffff;
            line-height: 22px;
            background-color: #fe328a;
            border-radius: 48px;
            padding: 12px 0;
        }
    }

}

.id-success {
    .f1 {
        width: 104px;
        height: 101px;
        background: url(../images/6.png) no-repeat;
        background-size: 100% 100%;
        margin: 10px auto 0 auto;
    }

    .f2 {
        font-size: 23px;
        font-weight: 800;
        text-align: center;
        color: #333333;
        line-height: 32px;
        margin: 10px;
    }

    .f3 {
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        color: #333333;
        line-height: 22px;
        margin-top: 5px;
    }

    .f4 {
        font-size: 12px;
        font-weight: bold;
        text-align: center;
        color: #999999;
        line-height: 16px;
        margin-top: 5px;
    }

    .paytype-w {
        padding: 10px;
    }

    .proto {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 10px;

        .pay-guide {
            font-size: 12px;
            color: #333333;
        }
    }

    .f5 {
        width: 280px;
        height: 49px;
        font-weight: bold;
        background: url(../images/3.png) no-repeat;
        background-size: 100% 100%;
        border-radius: 19px;
        margin: auto 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        text-align: center;
        color: #ffffff;
        line-height: 24px;
        text-shadow: 0px 1px 1px 0px rgba(200, 0, 0, 0.32);
    }
}

.id-fail {
    .f1 {
        width: 104px;
        height: 101px;
        background: url(../images/7.png) no-repeat;
        background-size: 100% 100%;
        margin: 10px auto 0 auto;
    }

    .f2 {
        font-size: 23px;
        font-weight: 800;
        text-align: center;
        color: #333333;
        line-height: 32px;
        margin: 10px;
    }

    .f3 {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
        color: #333333;
        line-height: 22px;
        margin-top: 5px;
    }

    .f4 {
        width: 280px;
        height: 49px;
        background: url(../images/3.png) no-repeat;
        background-size: 100% 100%;
        border-radius: 19px;
        margin: 30px auto 10px auto;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        text-align: center;
        font-weight: bold;
        color: #ffffff;
        line-height: 24px;
        text-shadow: 0px 1px 1px 0px rgba(200, 0, 0, 0.32);
    }
}