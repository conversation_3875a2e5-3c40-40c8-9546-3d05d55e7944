<import name="style" content="./main" module="S" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="payType" content=":component/payType/main" />
<div class=":identify-w">

    <div class=":id-content {{(state.step == 'form' || state.step == 'success') ? '' : S.nobg}}">
        <div class=":close" sp-on:click="close"></div>
        <sp:if value="state.step == 'form'">
            <div class=":id-form">
                <h1 class=":title">驾考宝典会员学生认证<i></i></h1>
                <div class=":form-item" skip="true">
                    <label>真实姓名</label>
                    <input type="text" ref="userName" placeholder="请输入您的姓名" value="" />
                </div>
                <div class=":form-item" skip="true">
                    <label>身份证号</label>
                    <input type="text" ref="idCardNum" placeholder="请输入身份证号" value="" />
                </div>
                <div class=":form-item" skip="true">
                    <label>学校(选填)</label>
                    <input type="text" ref="schoolName" placeholder="请输入学校名称" />
                </div>
                <p class=":p1">以上信息仅用于学生卡认证，认证通过后即可享有专属优惠；</p>
                <p class=":p2">您的账号将永久绑定该信息，不可解绑哦，请在绑定前认真确认自己提交的信息～</p>

                <div class=":submit" sp-on:click="submitInfo">提交认证</div>

                <div class=":proto">
                    <span class=":sp1 {{state.readed ? S.checked : S.unchecked}}" sp-on:click="readProto">我已阅读并同意</span>
                    <span class=":sp2" data-type="1" sp-on:click="goProtocol">《个人信息保护声明》</span>
                    <span class=":sp3" data-type="2" sp-on:click="goProtocol">《隐私政策》</span>
                </div>
            </div>
        </sp:if>

        <sp:if value="state.step == 'confirm'">
            <div class=":id-confirm">
                <h1 class=":title">请再次确认您的信息<i></i></h1>
                <div class=":con">
                    <label>真实姓名</label>
                    <span>{{state.userName}}</span>
                </div>
                <div class=":con">
                    <label>身份证号</label>
                    <span>{{state.idCardNum}}</span>
                </div>
                <div class=":con">
                    <label>学&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;校</label>
                    <span>{{state.schoolName}}</span>
                </div>
                <p class=":p1">您的账号将永久绑定该信息，不可解绑哦，请在绑定前认真确认自己提交的信息～</p>
                <div class=":btns">
                    <div class=":btn1" sp-on:click="modify">我要修改</div>
                    <div class=":btn2" sp-on:click="confirmSubmit">信息正确，提交认证</div>
                </div>
            </div>
        </sp:if>

        <sp:if value="state.step == 'success'">
            <div class=":id-success">
                <div class=":f1"></div>
                <div class=":f2">认证成功</div>
                <div class=":f3">尊敬的驾考宝典用户，您已完成学生认证～</div>
                <div class=":f4">已获得学生全科VIP专属优惠，赶快去解锁吧。</div>

                <div class=":paytype-w">
                    <sp:if value="Platform.isAndroid">
                        <com:payType theme="stud" />
                    </sp:if>
                </div>
                <div class=":f5" sp-on:click="buy" data-fragment="认证成功弹窗">{{props.payPrice}}元优惠开通全科VIP&nbsp;&gt;</div>
                <div class=":proto">
                    <com:readProtocol theme="stud" />
                    <sp:if value='{{Platform.isIOS}}'>
                        <div class=":pay-guide" sp-on:click="gotoPayGuide">支付教程 ></div>
                    </sp:if>
                </div>
            </div>
        </sp:if>

        <sp:if value="state.step == 'fail'">
            <div class=":id-fail">
                <div class=":f1"></div>
                <div class=":f2">认证失败</div>
                <div class=":f3">{{state.errorMsg}}</div>
                <div class=":f4" sp-on:click="modify">修改认证信息</div>
            </div>
        </sp:if>
    </div>


</div>