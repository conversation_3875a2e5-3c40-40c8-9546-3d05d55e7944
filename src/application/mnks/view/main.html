<import name="style" content="./main" />
<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />

<import name="old" content=":application/mnks/component/old/main" />
<import name="com" content=":application/mnks/component/com/main" />
<import name="k2com" content=":application/mnks/component/k2com/main" />

<div class="container">
    <div class="close" sp-on:click="onCloseClick"></div>

    <sp:if value="URLCommon.kemu === KemuType.Ke2">
        <div class="com-body-box">
            <com:k2com tabIndex="{{state.tabIndex}}" goodsList="{{state.goodsInfoPool}}">
                <div sp:slot="bottomTabs">
                    <div class="{{state.goodsInfoPool.length > 1?'':'hide'}}">
                        <com:bottomTabs tabIndex="{{state.tabIndex}}" labelPool="{{state.labelPool}}"
                            goodsList="{{state.goodsInfoPool}}" comparePricePool="{{state.comparePricePool}}"
                            tabChange="{{self.tabChangeCall}}" />
                    </div>
                </div>
            </com:k2com>
        </div>
        <sp:elseif value="Version.bizVersion > 2" />
        <div class="com-body-box">
            <com:com tabIndex="{{state.tabIndex}}" goodsList="{{state.goodsInfoPool}}">
                <div sp:slot="bottomTabs">
                    <div class="{{state.goodsInfoPool.length > 1?'':'hide'}}">
                        <com:bottomTabs tabIndex="{{state.tabIndex}}" labelPool="{{state.labelPool}}"
                            goodsList="{{state.goodsInfoPool}}" comparePricePool="{{state.comparePricePool}}"
                            tabChange="{{self.tabChangeCall}}" />
                    </div>
                    <sp:if value="{{state.divisionInfo.imageUrl}}">
                        <img style="width: 284px;height: 2px;display: block;margin: 10px auto 5px;"
                            src="{{state.divisionInfo.imageUrl}}" alt="">
                    </sp:if>
                </div>
            </com:com>
        </div>
        <sp:else />
        <div class="com-body-box">
            <com:old />
        </div>
    </sp:if>
    <div class="footer">
        <div class="pay-type {{Platform.isIOS && 'hide'}}">
            <com:payType theme="mnks" />
        </div>
        <div class="buy-btn">
            <com:buyButton noPayType="true" protocolText2="{{URLCommon.kemu === KemuType.Ke2?'《会员协议》':''}}"
                protocolUrl="{{URLCommon.kemu === KemuType.Ke2?self.protocolUrl:''}}">
                <div sp:slot="couponEntry" class="go_coupon">
                    {{self.nowCouponInfo.couponCode?'已优惠' +
                    self.nowCouponInfo.priceCent + '元':''}}
                </div>
            </com:buyButton>
        </div>
    </div>
</div>
