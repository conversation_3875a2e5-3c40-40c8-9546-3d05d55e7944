html,
body {
    height: 100vh;
    overflow: hidden;
    max-width: 100% !important;
}

.container {
    position: relative;
    height: 100vh;
}

.com-body-box {
    height: 100%;
}

.close {
    position: absolute;
    right: 0;
    top: 0;
    padding: 10px;
    width: 24px;
    height: 24px;
    background: url(../images/close.png) no-repeat center;
    background-size: 24px 24px;
    z-index: 1;
    box-sizing: content-box;
}

.footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .buy-footer {
        padding: 0 20px;
        background-color: transparent;

        .other-info {
            height: 32px;
        }
    }

    .tag {
        color: #6F2117 !important;
        background: linear-gradient(90deg, #FFD878 0%, #FFC400 100%) !important;
        border-radius: 33px 33px 33px 2px !important;
        top: -22px !important;
        transform: scale3d(0.85, 0.85, 0.85);
    }
}

.pay-type {
    margin-bottom: -5px;
}

.buy-btn {
    width: 100%;
}

.coupon-pick {
    background: transparent !important;
    color: #AE8A79 !important;
}

.bottom-tabs {
    background-color: transparent !important;
    padding: 0 !important;

    .hd-tabs {
        display: flex;
        justify-content: space-between;
        -webkit-justify-content: space-between;
        padding: 10px 15px 0 15px;

        .hd-tab {
            position: relative;
            font-weight: bold;
            color: #843405;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center !important;
            box-sizing: border-box;
            width: 154px !important;
            height: 42px !important;
            padding: 5px 0;
            font-size: 14px;
            border-radius: 5px;
            background-color: #FFFFFF;
            border: 1px solid #F4C2A2;

            &.active {
                color: #ffffff;
                background: linear-gradient(113deg, #353B4E 0%, #1D222B 100%);
                border: none;
            }
        }
    }

    .time-tip {
        transform: scale(0.85) !important;
    }
}
