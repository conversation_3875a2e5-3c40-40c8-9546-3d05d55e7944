.k2-c-container {
    background: #1f2027;
    display: flex;
    flex-direction: column;
    height: 100%;

    .content {
        width: 100%;
        height: 100%;
        margin: 0 auto;
        background: url(../images/mnks_3.png) no-repeat;
        background-size: 100% 100%;

        box-sizing: border-box;
        position: relative;
    }

    .title {
        width: 281px;
        height: 23px;
        margin: 15px 0 0 15px;
        transform-origin: left top;
        background: url(../images/t1.png) no-repeat;
        background-size: 125px 20px;
        background-position: center left;
        padding-left: 136px;
        font-size: 13px;
        color: #8F3112;
        display: flex;
        align-items: center;

    }

    .ht {
        font-size: 13px;
        color: #8F3112;
        line-height: 15px;
        text-align: center;
        padding-top: 13px;
    }

    .icons-w {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 10px 10px 0 10px;

        .icon-c {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;


            label {
                width: 32px;
                height: 32px;
                background-size: 100% 100%;
                margin-bottom: 5px;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    right: 0;
                    width: 6.5px;
                    height: 12px;
                    background: url(../images/arrow.png) no-repeat center center/cover;
                    transform: translate(28px, -50%);
                }
            }

            span {
                font-size: 12px;
                color: #8F3112;
                line-height: 1;
            }

            .text {
                font-size: 10px;
                line-height: 16px;
                color: #8F3112;
            }

            .i1_1 {
                background-image: url(../images/i1-1.png);
            }

            .i1_2 {
                background-image: url(../images/i1-2.png);
            }

            .i1_3 {
                background-image: url(../images/i1-3.png);

                &::after {
                    background: linear-gradient(76deg, #ab6035 16%, #91431f 90%);
                    width: 1px;
                    height: 24px;
                }
            }

            .i1_4 {
                background-image: url(../images/i1-4.png);

                &::after {
                    background: none;
                }
            }


            .i2_1 {
                background-image: url(../images/i2-1.png);
            }

            .i2_2 {
                background-image: url(../images/i2-2.png);
            }

            .i2_3 {
                background-image: url(../images/i2-3.png);

                &::after {
                    background: linear-gradient(76deg, #ab6035 16%, #91431f 90%);
                    width: 1px;
                    height: 24px;
                }
            }

            .i2_4 {
                background-image: url(../images/i2-4.png);

                &::after {
                    background: none;
                }
            }
        }
    }

    .single {
        .ht {
            font-size: 15px;
            font-weight: bold;
            color: #843926;
            line-height: 21px;
            padding-top: 25px;
        }

        .icons-w {
            padding: 15px 0 22px 0;
        }
    }
}

.k2-ios-container {
    .content {
        background: url(../images/mnks_3_ios.png) no-repeat;
        background-size: 100% 100%;
    }

    .ht {
        padding: 10px;
    }
}
