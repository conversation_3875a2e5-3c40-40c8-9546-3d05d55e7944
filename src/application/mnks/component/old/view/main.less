.content {
    background: #1f2027;
    width: 100%;
    height: 100%;
    position: relative;
    background: url(../images/bg.png) no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
    // &.kemu1 {
    //   background: url(http://exam-room.mc-cdn.cn/exam-room/2022/02/11/13/110b13a72ea742229c2a44762e734f02.png) no-repeat;
    //   background-size: 100% 100%;
    // }

    // &.kemu4 {
    //   background: url(http://exam-room.mc-cdn.cn/exam-room/2022/02/11/13/367fbcbedc824b26af58cac9f9153e78.png) no-repeat;
    //   background-size: 100% 100%;
    // }

    // &.moto {
    //   background: url(../images/mnks_3_moto.png) no-repeat;
    //   background-size: 100% 100%;

    //   &.kemu1 {
    //     background: url(http://exam-room.mc-cdn.cn/exam-room/2022/02/28/18/cb9d3c6ed2214156928d263fef5cc087.png) no-repeat;
    //     background-size: 100% 100%;
    //   }

    //   &.kemu4 {
    //     background: url(http://exam-room.mc-cdn.cn/exam-room/2022/02/28/18/7bca53ad87dd41cbad5dd54ba1e99210.png) no-repeat;
    //     background-size: 100% 100%;
    //   }
    // }

    // &.mf {
    //   background: url(../images/mnks_3_score12.png) no-repeat;
    //   background-size: 100% 100%;
    // }
    .title {
        width: 190px;
        height: 40px;
        background: url(../images/hd.png) no-repeat;
        background-size: 100% 100%;
        font-size: 20px;
        font-weight: bold;
        color: #843405;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .p1 {
        font-size: 12px;
        color: #8F3112;
        line-height: 16px;
        text-align: center;
        margin-top: 4px;
    }

    .p2-w {
        display: flex;
        margin-top: 8px;
    }

    .p2 {
        font-size: 13px;
        font-weight: bold;
        color: #843926;
        line-height: 18px;
        text-align: center;
        background: #FFEADE;
        margin: 0 auto;
        padding: 0 2px;
    }

    .icons-w {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 10px 10px 0 10px;

        .icon-c {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;

            label {
                width: 32px;
                height: 32px;
                background-size: 100% 100%;
                margin-bottom: 5px;
            }

            span {
                font-size: 12px;
                color: #8F3112;
                line-height: 1.5;
            }

            .i1_1 {
                background-image: url(../images/i1_1.png);
            }

            .i1_2 {
                background-image: url(../images/i1_2.png);
            }

            .i1_3 {
                background-image: url(../images/i1_3.png);
            }

            .i1_4 {
                background-image: url(../images/i1_4.png);
            }

        }
    }
}
