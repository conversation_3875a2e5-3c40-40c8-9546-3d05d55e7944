<import name="style" content="./main" />

<div
    class="c-container {{URLCommon.isScore12?'sorce12':''}} {{Platform.isIOS ? 'ios-container': 'android-container'}}  ">
    <div class="content">
        <sp:if value="URLCommon.isScore12">
            <div class="title score12"></div>
            <sp:else />
            <!-- 资格证不能显示图片 -->
            <div class="title {{URLCommon.isZigezheng?'zigezheng':''}} {{ state.kemu == 1 ? 'title1': 'title4'}}"></div>
        </sp:if>

        <sp:slot name="bottomTabs"></sp:slot>

        <div class="{{props.tabIndex == 0 ? '': 'hide'}}  {{props.goodsList.length === 1 ? 'single': ''}}">
            <sp:if value="props.goodsList[props.tabIndex].groupKey != GroupKey.ChannelKou12Short">
                <h3 class="ht"><i class="i1"></i>- 限时附赠的权益 -<i class="i2"></i></h3>
            </sp:if>
            <div class="icons-w">
                <!-- 摩托车扣满12分 -->
                <sp:if value="[GroupKey.MotoChannelKou12].indexOf(props.goodsList[props.tabIndex].groupKey) > -1">
                    <div class="icon-c">
                        <label class="i1_6"></label>
                        <span>精简200题</span>
                        <p class="text">学得快</p>
                    </div>
                    <div class="icon-c">
                        <label class="i1_2"></label>
                        <span>答题技巧</span>
                        <p class="text">记得牢</p>
                    </div>
                    <div class="icon-c">
                        <label class="i1_3"></label>
                        <span>考前两套卷</span>
                        <p class="text">冲高分</p>
                    </div>
                    <!-- 扣满12分小车客车货车都是600题 且是双行文字 -->
                    <sp:elseif value="URLCommon.isScore12" />
                    <div class="icon-c">
                        <label class="i1_5"></label>
                        <span>精简题库</span>
                        <p class="text">学得快</p>
                        <sp:if value="URLCommon.tiku === CarType.TRUCK || URLCommon.tiku === CarType.BUS">
                            <div class="icon-multi"></div>
                        </sp:if>
                    </div>
                    <div class="icon-c">
                        <label class="i1_2"></label>
                        <span>答题技巧</span>
                        <p class="text">记得牢</p>
                    </div>
                    <div class="icon-c">
                        <label class="i1_3"></label>
                        <span>考前三套卷</span>
                        <p class="text">冲高分</p>
                    </div>
                    <sp:else />

                    <div class="icon-c">
                        <!-- 非扣满12分 客货车是600题 -->
                        <sp:if value="URLParams.carStyle == CarType.BUS || URLParams.carStyle == CarType.TRUCK">
                            <label class="i1_5"></label>
                            <sp:else />
                            <label class="i1_1"></label>
                        </sp:if>
                        <span>精简题库</span>
                    </div>
                    <sp:if value="!URLCommon.isWurenji">
                        <div class="icon-c">
                            <label class="i1_2"></label>
                            <span>答题技巧</span>
                        </div>
                    </sp:if>
                    <div class="icon-c">
                        <label class="i1_3"></label>
                        <span>考前秘卷</span>
                    </div>
                    <!-- 扣满12分和资格证没有考不过补偿 -->
                    <sp:if value="!URLCommon.isScore12 && !URLCommon.isZigezheng">
                        <div class="icon-c">
                            <label class="i1_4"></label>
                            <span>考不过补偿</span>
                        </div>
                    </sp:if>
            </div>
        </div>

        <!-- 摩托车扣满12分短时提分场景 -->
        <sp:if value="[GroupKey.MotoChannelKou12Short].indexOf(props.goodsList[props.tabIndex].groupKey) > -1">
            <div class="score12-wraper">
                <div class="l icons-w">
                    <div class="icon-c">
                        <label class="i1_6"></label>
                        <span>精简200题</span>
                        <p class="text">学得快</p>
                    </div>
                    <div class="icon-c">
                        <label class="i1_2"></label>
                        <span>答题技巧</span>
                        <p class="text">记得牢</p>
                    </div>
                    <div class="icon-c">
                        <label class="i1_3"></label>
                        <span>考前两套卷</span>
                        <p class="text">冲高分</p>
                    </div>
                </div>
                <div class="icon">+</div>
                <div class="r">
                    <div class="live-icon"></div>
                    <span>精品直播课</span>
                    <p>讲师带学</p>
                </div>
            </div>
        </sp:if>

        <!-- 扣满12场景 小车客车货车短时提分 -->
        <sp:if
            value="[GroupKey.ChannelKou12Short,GroupKey.HcChannelKou12Short,GroupKey.KcChannelKou12Short].indexOf(props.goodsList[props.tabIndex].groupKey) > -1">
            <div class="score12-wraper">
                <div class="l icons-w">
                    <div class="icon-c">
                        <label class="i1_1"></label>
                        <span>精简600题</span>
                        <p class="text">学得快</p>
                        <sp:if value="URLCommon.tiku === CarType.TRUCK || URLCommon.tiku === CarType.BUS">
                            <div class="icon-multi"></div>
                        </sp:if>
                    </div>
                    <div class="icon-c">
                        <label class="i1_2"></label>
                        <span>答题技巧</span>
                        <p class="text">记得牢</p>
                    </div>
                    <div class="icon-c">
                        <label class="i1_3"></label>
                        <span>考前三套卷</span>
                        <p class="text">冲高分</p>
                    </div>
                </div>
                <div class="icon">+</div>
                <div class="r">
                    <div class="live-icon"></div>
                    <span>精品直播课</span>
                    <p>讲师带学</p>
                </div>
            </div>
        </sp:if>

        <!-- 小车科四短时提分 -->
        <div class="{{[GroupKey.ChannelKe4Short].indexOf(props.goodsList[props.tabIndex].groupKey) > -1 ? '': 'hide'}}">
            <h3 class="ht"><i class="i1"></i>- 限时附赠的权益 -<i class="i2"></i></h3>
            <div class="icons-w">
                <div class="icon-c">
                    <label class="i2_1"></label>
                    <span>直播课</span>
                </div>
                <div class="icon-c">
                    <label class="i1_1"></label>
                    <span>精简题库</span>
                </div>
                <div class="icon-c">
                    <label class="i1_2"></label>
                    <span>答题技巧</span>
                </div>
                <div class="icon-c">
                    <label class="i1_3"></label>
                    <span>考前秘卷</span>
                </div>
                <!-- 扣满12分和资格证没有考不过补偿 -->
                <sp:if value="!URLCommon.isScore12 && !URLCommon.isZigezheng">
                    <div class="icon-c">
                        <label class="i1_4"></label>
                        <span>考不过补偿</span>
                    </div>
                </sp:if>
            </div>
        </div>
        <!-- 小车，客，货车全科 -->
        <div
            class="{{props.goodsList[props.tabIndex].groupKey === GroupKey.ChannelKemuAll || props.goodsList[props.tabIndex].groupKey === GroupKey.HcChannelKemuAll || props.goodsList[props.tabIndex].groupKey === GroupKey.KcChannelKemuAll? '': 'hide'}}">
            <h3 class="ht">- 一站通关高效学 -</h3>
            <div class="icons-w">
                <div class="icon-c">
                    <label class="i2_1"></label>
                    <span>直播课</span>
                </div>
                <div class="icon-c">
                    <label class="i2_2"></label>
                    <span>科一VIP</span>
                </div>
                <div class="icon-c">
                    <label class="i2_3"></label>
                    <span>科二VIP</span>
                </div>
                <div class="icon-c">
                    <label class="i2_4"></label>
                    <span>科三VIP</span>
                </div>
                <div class="icon-c">
                    <label class="i2_5"></label>
                    <span>科四VIP</span>
                </div>
            </div>
        </div>
        <!-- 客车货车科一科四组合包 -->
        <div class="{{props.goodsList[props.tabIndex].groupKey === GroupKey.KcChannelKe1Ke4Group||props.goodsList[props.tabIndex].groupKey === GroupKey.HcChannelKe1Ke4Group ? '': 'hide'}}">
            <h3 class="ht">- 科一科四组合VIP权益 -</h3>
            <div class="icons-w">
                <div class="icon-c">
                    <label class="i3_1"></label>
                    <span>科一VIP</span>
                </div>
                <div class="icon-c">
                    <label class="i3_2"></label>
                    <span>科一考不过补偿</span>
                </div>
                <div class="icon-c">
                    <label class="i3_3"></label>
                    <span>科四VIP</span>
                </div>
                <div class="icon-c">
                    <label class="i3_4"></label>
                    <span>科四考不过补偿</span>
                </div>
            </div>
        </div>

        <!-- 摩托科一科四组合包 -->
        <div class="{{props.goodsList[props.tabIndex].groupKey === GroupKey.MotoChannelKe1Ke4Group ? '': 'hide'}}">
            <h3 class="ht">- 科一科四组合VIP权益 -</h3>
            <div class="icons-w">
                <div class="icon-c">
                    <label class="i3_1"></label>
                    <span>科一VIP</span>
                </div>
                <div class="icon-c">
                    <label class="i3_2"></label>
                    <span>科一考不过补偿</span>
                </div>
                <div class="icon-c">
                    <label class="i3_3"></label>
                    <span>科四VIP</span>
                </div>
                <div class="icon-c">
                    <label class="i3_4"></label>
                    <span>科四考不过补偿</span>
                </div>
            </div>
        </div>
    </div>

</div>
