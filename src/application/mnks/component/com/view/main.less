.c-container {
    background: #1f2027;
    display: flex;
    flex-direction: column;
    height: 100%;

    .content {
        width: 100%;
        height: 100%;
        margin: 0 auto;
        background: url(../images/mnks_3.png) no-repeat;
        background-size: 100% 100%;

        box-sizing: border-box;
        position: relative;

        .show {
            display: block!important;
        }

        .add-90day-sign {
            position: static;
            height: 20px;
            background: linear-gradient(90deg, #ff4a40, rgba(49, 56, 69, 0.00));
            text-align: center;
            justify-content: center;
            transform: translateY(100%);

            .icon {
                display: none;
            }

            .txt {
                color: white;
            }
        }
    }

    .title {
        width: 281px;
        height: 23px;
        margin: 15px 0 0 15px;
        transform-origin: left top;

        &.title1 {
            background: url(../images/t1.png) no-repeat;
            background-size: 100% 100%;
        }

        &.title4 {
            background: url(../images/t4.png) no-repeat;
            background-size: 100% 100%;
        }

        &.zigezheng {
            width: 243px;
            height: 22px;
            background: url(../images/mnks_5.png) no-repeat;
            background-size: 100% 100%;
        }

        &.score12 {
            width: 243px;
            height: 22px;
            background: url(../images/mnks_5.png) no-repeat;
            background-size: 100% 100%;
        }
    }

    .ht {
        font-size: 13px;
        color: #8F3112;
        line-height: 15px;
        text-align: center;
        padding-top: 8px;
    }

    .icons-w {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 5px 10px 0 10px;

        .icon-c {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;

            .icon-multi {
                width: 41px;
                height: 15px;
                background: url(http://exam-room.mc-cdn.cn/exam-room/2022/07/12/11/baa34cadd2524f2088f3bad93e562116.png) no-repeat center center/cover;
                position: absolute;
                right: -20px;
                top: -8px;
                animation: shakeX 1.5s infinite;
            }

            label {
                width: 32px;
                height: 32px;
                background-size: 100% 100%;
                margin-bottom: 5px;
            }

            span {
                font-size: 12px;
                color: #8F3112;
                line-height: 1;
            }

            .text {
                font-size: 10px;
                line-height: 16px;
                color: #8F3112;
            }

            .i1_1 {
                background-image: url(../images/i1_1.png);
            }

            .i1_2 {
                background-image: url(../images/i1_2.png);
            }

            .i1_3 {
                background-image: url(../images/i1_3.png);
            }

            .i1_4 {
                background-image: url(../images/i1_4.png);
            }

            .i1_5 {
                background: url(../images/jkbd__vip_jj600.png) no-repeat;
                background-size: 100% 100%;
            }

            .i1_6 {
                background: url(../images/i1_6.png) no-repeat;
                background-size: 100% 100%;
            }

            .i2_1 {
                background-image: url(../images/i2_1.png);
            }

            .i2_2 {
                background-image: url(../images/i2_2.png);
            }

            .i2_3 {
                background-image: url(../images/i2_3.png);
            }

            .i2_4 {
                background-image: url(../images/i2_4.png);
            }

            .i2_5 {
                background-image: url(../images/i2_5.png);
            }

            .i3_1 {
                background-image: url(../images/i1_3.png);
            }

            .i3_2 {
                background-image: url(../images/i1_4.png);
            }

            .i3_3 {
                background-image: url(../images/i2_5.png);
            }

            .i3_4 {
                background-image: url(../images/i1_4.png);
            }
        }
    }

    .single {
        .ht {
            font-size: 15px;
            font-weight: bold;
            color: #843926;
            line-height: 21px;
            padding-top: 25px;

            .i1 {
                display: inline-block;
                width: 69px;
                height: 7px;
                background: url(../images/i1.png) no-repeat;
                background-size: 100% 100%;
            }

            .i2 {
                display: inline-block;
                width: 69px;
                height: 7px;
                background: url(../images/i2.png) no-repeat;
                background-size: 100% 100%;
            }
        }

        .icons-w {
            padding: 15px 0 22px 0;
        }
    }

    .score12-wraper {
        margin-top: 9px;
        display: flex;
        align-items: center;
        justify-content: center;

        .l {
            width: 227px;
            padding-top: 23px;
            height: 90px;
            position: relative;
            border-radius: 10px;
            border: 1px solid #DE9971;

            &::before {
                content: '限时附赠的VIP所有权益';
                text-align: center;
                font-size: 11px;
                font-weight: 500;
                color: #FCDECE;
                position: absolute;
                top: -1px;
                left: -1px;
                width: 145px;
                height: 22px;
                line-height: 22px;
                background: url(../images/1.png) no-repeat;
                background-size: 100% 100%;
            }

            .icon-c {
                position: relative;

                .i1_1 {
                    background: url(../images/jkbd__vip_jj600.png) no-repeat;
                    background-size: 100% 100%;
                }

                label {
                    width: 30px;
                    height: 30px;
                }

                span {
                    font-size: 11px;
                }
            }
        }

        .icon {
            font-size: 20px;
            font-weight: 600;
            color: #A34E2E;
            margin: 0 3px;
        }

        .r {
            width: 70px;
            height: 90px;
            background: linear-gradient(149deg, #AB6035 0%, #CC7E5A 100%);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;

            .live-icon {
                width: 37px;
                height: 37px;
                background: url(../images/live.png) no-repeat center center;
                background-size: 100% 100%;
                margin-top: 12px;
            }

            span {
                font-size: 11px;
                color: #FFEDE3;
                line-height: 16px;
                margin-top: 2px;
            }

            p {
                font-size: 10px;
                line-height: 14px;
                color: #FFEDE3;
            }
        }
    }

}

.ios-container {
    .content {
        background: url(../images/mnks_3_ios.png) no-repeat;
        background-size: 100% 100%;

        .add-90day-sign {
            position: static;
            height: 20px;
            background: linear-gradient(90deg, #ff4a40, rgba(49, 56, 69, 0.00));
            text-align: center;
            justify-content: center;
            transform: translateY(160%);

            .icon {
                display: none;
            }

            .txt {
                color: white;
            }
        }
    }

    .ht {
        padding: 10px;
    }
}

.sorce12.android-container {
    .content {
        background: url(../images/mnks_3_android.png) no-repeat;
        background-size: 100% 100%;
    }
}


@keyframes shakeX {

    0%,
    to {
        transform: translateX(0)
    }

    50% {
        transform: translateX(0)
    }

    55%,
    65%,
    75%,
    85%,
    95% {
        transform: translateX(-5px)
    }

    60%,
    70%,
    80%,
    90% {
        transform: translateX(5px)
    }
}
