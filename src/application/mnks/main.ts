/*
 * ------------------------------------------------------------------
 * 模拟考试购买弹窗
 * ------------------------------------------------------------------
 */
import { CarType, KemuType, PayType, Platform, setPageName, URLCommon, URLParams, Version } from ':common/env';
import { formatPrice } from ':common/utils';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayTypeCom from ':component/payType/main';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { iosDialogBuySuccess } from ':common/features/ios_pay';
import { webClose } from ':common/core';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackDialogShow, trackEvent, trackGoPay } from ':common/stat';
import { Coupon, getBestCoupon, goodsInfoWithCoupon } from ':common/features/coupon';
import { setEmbeddedHeight } from ':common/features/embeded';
import { zigezhengGroupKeyObj } from ':common/features/zigezheng';
import { PROTOCOL3_URL } from ':common/navigate';
import { getDivisionInfo } from ':store/chores';

if (URLCommon.kemu === KemuType.Ke2) {
    URLParams.fragmentName1 = '浮窗模式';
}

interface State {
    URLCommon: typeof URLCommon,
    kemu: KemuType,
    tiku: CarType,
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    prevScrollTop: number
    comparePricePool: object,
    divisionInfo: any
}

let docWidth: number;

// 以前的版本不支持设置宽高比，是写死的高度
if (Version.bizVersion >= 2) {
    docWidth = 345;
} else {
    docWidth = 304;
}
export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton,
        payType: PayTypeCom;
    };
    get protocolUrl() {
        return PROTOCOL3_URL;
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    get pageName() {
        return URLCommon.kemu === KemuType.Ke2 ? '3DVIP购买页' : '真实考场前置页';
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }

    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {
        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;
        const goodsInfoPool: GoodsInfo[] = [];
        let tabIndex;

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        if (URLCommon.is3DSingle) {
            if (URLCommon.kemu === KemuType.Ke2) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe2
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe2Ke3Group
                } as GoodsInfo);
            } else {
                switch (tiku) {
                    case CarType.CAR:
                        if (kemu === 1) {
                            goodsInfoPool.push({
                                groupKey: GroupKey.ChannelKe1D3
                            } as GoodsInfo);
                        } else {
                            goodsInfoPool.push({
                                groupKey: GroupKey.ChannelKe4D3
                            } as GoodsInfo);
                        }
                        break;
                    case CarType.TRUCK:
                        if (kemu === 1) {
                            goodsInfoPool.push({
                                groupKey: GroupKey.HcChannelKe1D3
                            } as GoodsInfo);
                        } else {
                            goodsInfoPool.push({
                                groupKey: GroupKey.HcChannelKe4D3
                            } as GoodsInfo);
                        }

                        break;
                    case CarType.BUS:
                        if (kemu === 1) {
                            goodsInfoPool.push({
                                groupKey: GroupKey.KcChannelKe1D3
                            } as GoodsInfo);
                        } else {
                            goodsInfoPool.push({
                                groupKey: GroupKey.KcChannelKe4D3
                            } as GoodsInfo);
                        }
                        break;
                    default: break;
                }
            }

        } else if (URLCommon.isElder) {
            goodsInfoPool.push({
                groupKey: kemu === 1 ? GroupKey.ElderChannelKe1 : GroupKey.ElderChannelKe4
            } as GoodsInfo);

            if (kemu === KemuType.Ke1) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKemuAll
                } as GoodsInfo);
            }
        } else if (URLCommon.isZigezheng) {
            goodsInfoPool.push({
                groupKey: zigezhengGroupKeyObj[tiku]
            } as GoodsInfo);
        } else if (URLCommon.isScore12) {
            switch (tiku) {
                case CarType.CAR:
                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKou12
                    } as GoodsInfo);
                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKou12Short
                    } as GoodsInfo);
                    break;
                case CarType.TRUCK:
                    goodsInfoPool.push({
                        groupKey: GroupKey.HcChannelKou12
                    } as GoodsInfo);
                    goodsInfoPool.push({
                        groupKey: GroupKey.HcChannelKou12Short
                    } as GoodsInfo);
                    break;
                case CarType.BUS:
                    goodsInfoPool.push({
                        groupKey: GroupKey.KcChannelKou12
                    } as GoodsInfo);
                    goodsInfoPool.push({
                        groupKey: GroupKey.KcChannelKou12Short
                    } as GoodsInfo);
                    break;
                case CarType.MOTO:
                    goodsInfoPool.push({
                        groupKey: GroupKey.MotoChannelKou12
                    } as GoodsInfo);
                    goodsInfoPool.push({
                        groupKey: GroupKey.MotoChannelKou12Short
                    } as GoodsInfo);
                    break;
                default: break;
            }
        } else {
            // 虽然驾考没有科二模拟考试， 但是能打开驾考3d，但是又算在驾考里面，所以要在这里重新加一次商品
            // eslint-disable-next-line no-lonely-if
            if (URLCommon.kemu === KemuType.Ke2) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe2
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe2Ke3Group
                } as GoodsInfo);
            } else {
                switch (tiku) {
                    case CarType.CAR:

                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.ChannelKe1 : GroupKey.ChannelKe4
                        } as GoodsInfo);

                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.ChannelKemuAll : GroupKey.ChannelKe4Short
                        } as GoodsInfo);

                        break;
                    case CarType.TRUCK:
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.HcChannelKe1 : GroupKey.HcChannelKe4
                        } as GoodsInfo);

                        if (kemu === KemuType.Ke1) {
                            goodsInfoPool.push({
                                groupKey: GroupKey.HcChannelKe1Ke4Group
                            } as GoodsInfo);
                            // goodsInfoPool.push({
                            //     groupKey: GroupKey.HcChannelKemuAll
                            // } as GoodsInfo);
                        }

                        break;
                    case CarType.BUS:
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.KcChannelKe1 : GroupKey.KcChannelKe4
                        } as GoodsInfo);
                        if (kemu === KemuType.Ke1) {
                            goodsInfoPool.push({
                                groupKey: GroupKey.KcChannelKe1Ke4Group
                            } as GoodsInfo);
                            // goodsInfoPool.push({
                            //     groupKey: GroupKey.KcChannelKemuAll
                            // } as GoodsInfo);
                        }
                        break;
                    case CarType.MOTO:
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.MotoChannelKe1 : GroupKey.MotoChannelKe4
                        } as GoodsInfo);

                        goodsInfoPool.push({
                            groupKey: GroupKey.MotoChannelKe1Ke4Group
                        } as GoodsInfo);
                        if (kemu === KemuType.Ke1) {
                            tabIndex = 1;
                        }
                        break;
                    case CarType.GUACHE:
                        // 挂车只有科四
                        goodsInfoPool.push({
                            groupKey: GroupKey.GcChannelKe4
                        } as GoodsInfo);

                        break;
                    default: break;
                }
            }
        }

        this.state = {
            URLCommon,
            kemu,
            tiku,
            tabIndex: tabIndex || 0,
            goodsInfoPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            prevScrollTop: 0,
            divisionInfo: {}
        };

    }
    willMount() {
        this.getGoodInfo();
    }
    didMount() {
        const { tabIndex, goodsInfoPool } = this.state;
        setPageName(this.pageName);

        if (URLCommon.kemu === KemuType.Ke2) {
            trackDialogShow({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                fragmentName1: URLParams.fragmentName1,
                payPathType: 0
            });
        } else {
            trackGoPay({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                fragmentName1: URLParams.fragmentName1,
                payPathType: 0
            });
        }

        this.getDivision();

        this.setPageInfo();
        this.appEventProxy();
        this.resetWindow(docWidth);
        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                // 可能从3d打开或者驾考sdk打开3d
                if (URLCommon.kemu === KemuType.Ke2) {
                    webClose();
                    return;
                }
                iosDialogBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
            },
            isInDialog: URLCommon.kemu !== KemuType.Ke2
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey
        });

    }
    getDivision() {
        getDivisionInfo({
            type: 20
        }).then(info => {
            this.setState({
                divisionInfo: info
            });
        });
    }
    appEventProxy() {
        if (URLCommon.isScore12 && Platform.isAndroid) {
            setEmbeddedHeight(345 / 353);
        } else {
            setEmbeddedHeight(345 / 333);
        }
    }
    resetWindow(docWidth) {
        const docEl = document.documentElement;
        const resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize';
        const reCalc = function () {
            const clientWidth = docEl.clientWidth;
            if (!clientWidth) {
                setTimeout(function () {
                    reCalc();
                }, 50);
                return;
            }
            window.baseFontSize = 100 * (clientWidth / docWidth);
            docEl.style.fontSize = window.baseFontSize + 'px';
        };

        window.addEventListener(resizeEvt, reCalc, false);
        document.addEventListener('DOMContentLoaded', reCalc, false);
    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        this.setState({
            tabIndex
        }, () => {
            if (URLCommon.kemu === KemuType.Ke2) {
                trackEvent({
                    fragmentName1: URLParams.fragmentName1,
                    groupkey: this.nowGoodInfo.groupKey,
                    actionType: '点击',
                    actionName: '商品'
                });
            }
            this.setPageInfo();
        });

    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const fragmentName1 = URLParams.fragmentName1 || '底部吸底按钮';
        const { tabIndex, goodsInfoPool, labelPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];

        let tag = null;
        const label = labelPool[nowGoodInfo.groupKey]?.label;

        if (label) {
            tag = {
                text: label
            };
        }

        this.children.buyButton.setButtonConfig({
            groupKey: nowGoodInfo.groupKey,
            type: 7,
            title: `¥ ${this.showPrice}元确认协议并支付`,
            subtitle: '有效期' + nowGoodInfo.validDays + '天',
            fragmentName1,
            fragmentName2: URLCommon.kemu === KemuType.Ke2 ? '' : '支付弹窗',
            tag: tag
        });

    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        let { tabIndex } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {

                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });
            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }
            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool
            });
            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getLabel();
                await this.getComparePrice();

                this.setPageInfo();
            }, 60);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool, tiku } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice
                    };
                }
            });
            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            // 可能从3d打开或者驾考sdk打开3d
            if (URLCommon.kemu === KemuType.Ke2) {
                webClose();
                return;
            }
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey }, 2);
        });
    }

    onCloseClick = () => {
        if (URLCommon.kemu === KemuType.Ke2) {
            trackEvent({
                fragmentName1: URLParams.fragmentName1,
                actionType: '点击',
                actionName: '关闭'
            });
        }
        webClose();
    }
}
