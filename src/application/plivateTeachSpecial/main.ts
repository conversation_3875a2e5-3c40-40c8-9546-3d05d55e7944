/*
 * ------------------------------------------------------------------
 * 废弃，项目迁移到manual-score(填写成绩)
 * ------------------------------------------------------------------
 */

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { getCourseNote, kq2hList } from ':store/chores';
import { openWeb, setStatusBarTheme } from ':common/core';
import { URLCommon, URLParams } from ':common/env';

interface State {
    title: string
    courseData: any
}
export default class extends Application<State> {
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            title: URLParams.title,
            courseData: {}
        };
    }
    didMount() {

        setStatusBarTheme('dark');
        this.getCourseNote();
    }

    getCourseNote() {
        getCourseNote({
            lessonId: URLParams.relatedLessonId
        }).then(data => {
            data.lessonResources = data.lessonResources?.length ? data.lessonResources : ['http://exam-room.mc-cdn.cn/exam-room/2023/06/28/16/03d72f3bcd0a486f9a202fab02d273a7.png'];
            this.setState({
                courseData: data
            });
        });
    }
}
