/*
 * ------------------------------------------------------------------
 * 全科vip
 * ------------------------------------------------------------------
 */

import { CarType, KemuType, persuadeDialogAllow, Platform, setPageName, URLCommon } from ':common/env';
import { formatPrice } from ':common/utils';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import BuyDialogOrderSign from ':component/buyDialogOrderSign/main';
import { getGroupSessionInfo, getSessionE<PERSON>tra, GoodsInfo, GroupComparePrice, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { typeCode } from ':common/features/bottom';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import { webClose } from ':common/core';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackExit, trackGoPay, trackPageLoad } from ':common/stat';
import { onWebBack } from ':common/features/persuade';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { BUYED_URL, openAuth } from ':common/navigate';
import { isHubei } from ':common/features/locate';
import jump from ':common/features/jump';
import { couponAnimate } from ':common/features/dom';
import { hesitateUserPersuade } from ':common/features/hesitate';

interface State {
    kemu: KemuType,
    tiku: CarType,
    isHubei: boolean,
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    prevScrollTop: number
    showSignModal: boolean
}

// 标记是否展示过挽留弹窗
let flag = false;
let timer;

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog,
        buyDialogOrderSign: BuyDialogOrderSign
    };

    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    get pageName() {
        return 'VIP通用页';
    }

    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;

            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    get qaKey() {
        return 'qaKey6';
    }
    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {
        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;
        const goodsInfoPool: GoodsInfo[] = [];
        let tabIndex;

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        switch (tiku) {
            case CarType.CAR:
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKemuAll
                } as GoodsInfo);

                break;
            case CarType.TRUCK:
                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKemuAll
                } as GoodsInfo);
                break;
            case CarType.BUS:
                goodsInfoPool.push({
                    groupKey: GroupKey.KcChannelKemuAll
                } as GoodsInfo);
                break;
            case CarType.MOTO:
                goodsInfoPool.push({
                    groupKey: GroupKey.MotoChannelKemuAll
                } as GoodsInfo);
                break;
            default: break;
        }

        this.state = {
            kemu,
            isHubei: true,
            tiku,
            tabIndex: tabIndex || 0,
            goodsInfoPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            prevScrollTop: 0,
            showSignModal: false
        };

    }

    async didMount() {
        const { tabIndex, goodsInfoPool } = this.state;
        // 优先展示底部按钮再加载数据；
        this.setPageInfo();

        setPageName(this.pageName);

        await this.getGoodInfo();

        // 页面进出时长打点
        trackPageLoad();

        // 判断是否是湖北
        isHubei().then(isHubei => {
            this.setState({
                isHubei
            });
        });

        // app代理方法
        this.appEventProxy();

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: goodsInfoPool[tabIndex].groupKey
        });

    }
    appEventProxy() {
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const fragmentName1 = '底部吸底按钮';
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        let bottomType: typeCode = typeCode.type4;

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });

        // 全科并且有活动的时候按钮不同
        if (nowGoodInfo.inActivity && !nowGoodInfo.upgrade) {
            bottomType = typeCode.type5;
        }

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1
                });
                break;
            case typeCode.type5:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${nowGoodInfo.inActivity.discountedPrice}元`,
                    price: this.showPrice,
                    originalPrice: ('日常价￥' + nowGoodInfo.inActivity.preDiscountPrice),
                    fragmentName1
                });
                break;
            default:
                break;
        }
    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;

            this.setState({
                prevScrollTop
            });
        }, 10);
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                // 如果第一个商品已购买并且不升级就跳走
                if (index === 0 && goodInfo.bought && !goodInfo.upgrade) {
                    jump.replace(BUYED_URL);
                    return;
                }
                newGoodsPool.push(goodInfo);
            });
            this.setState({
                goodsInfoPool: newGoodsPool
            });

            if (newGoodsPool[0].upgrade) {
                setPageName('VIP升级页');
            }

            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getLabel();
                await this.getComparePrice();

                couponAnimate({
                    couponTargetDomSelect: '.coupon-position-bottom',
                    compareTargetDomSelect: '',
                    couponData: this.nowCouponInfo,
                    compareData: this.state.comparePricePool[this.nowGoodInfo.groupKey],
                    goodsData: this.nowGoodInfo,
                    compareGoodsData: this.state.comparePricePool[this.nowGoodInfo.groupKey]
                });

                this.setPageInfo();
                this.setState({ showSignModal: true });
            }, 60);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;

        await GroupComparePrice({ groupKeys: goodsInfoPool.map(item => item.groupKey).join(',') }).then(comparePricePool => {
            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat

        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    payBtnCall = (e) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');

        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });

        }
    }
    goAuth = (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });
    }
    // 退出页面的回调
    async goBackPage() {
        const { tabIndex, goodsInfoPool, kemu, labelPool } = this.state;
        const nowGoodInfo = goodsInfoPool[tabIndex];

        if (this.children.buyDialogOrderSign.getStatus()) {
            this.children.buyDialogOrderSign.close();
            return;
        }

        if (!this.nowGoodInfo.upgrade && await hesitateUserPersuade(true)) {
            return;
        }

        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: nowGoodInfo,
                groupKey: nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                title: `真的要放弃${nowGoodInfo.name}吗？`,
                txt1: '懒人必备',
                txt2: '省不少时间',
                txt3: '后悔开晚了',
                txt4: '简单好记',
                tag: {
                    text: labelPool[nowGoodInfo.groupKey]?.label
                },
                kemu
            }).then(payType => {
                if (payType === false) {
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            webClose();
        }
    }
    backCall = () => {
        this.goBackPage();
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    // 离开当前页面
    leavePageCall = () => {
        // 退出当前页面的打点
        setPageName(this.pageName);
        trackExit();
    }
    closeSignModal = () => {
        this.setPageInfo();
    }
}
