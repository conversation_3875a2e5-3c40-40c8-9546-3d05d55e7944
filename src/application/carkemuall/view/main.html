<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="buyDialogOrderSign" content=":component/buyDialogOrderSign/main" />
<import
    name="MotoKemuAll"
    content=":application/moto/component/MotoKemuAll/main"
/>
<import
    name="CarKemuAll"
    content=":application/car/component/CarKemuAll/main"
/>
<import
    name="CarKemuAllUpgrade"
    content="../component/upgrade/main"
/>
<import
    name="TruckKemuAll"
    content=":application/khche/component/TruckKemuAll/main"
/>
<import
    name="BusKemuAll"
    content=":application/khche/component/BusKemuAll/main"
/>
<import
    name="MotoKemuAll"
    content=":application/moto/component/MotoKemuAll/main"
/>


<div class="page-container page-carkemuall">
    <div class="page-header">
        <com:header
            title="{{state.prevScrollTop > 200?self.nowGoodInfo.name: ' '}}"
            theme="black"
            endTheme="black"
            qaKey="{{self.qaKey}}"
            scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}"
        />
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <!-- 小车全科 -->
        <sp:if value="self.getGroupKeyInfo(GroupKey.ChannelKemuAll).payPrice">
            <div
                class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.ChannelKemuAll?'show':'hide'}}"
            >
                <sp:if value="!URLCommon.isElder && self.getGroupKeyInfo(GroupKey.ChannelKemuAll).upgrade">
                     <com:CarKemuAllUpgrade
                        goodsInfo="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll)}}"
                        comparePricePool="{{state.comparePricePool}}"
                        labelPool="{{state.labelPool}}"
                        groupKey="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll).groupKey}}"
                        payPrice="{{self.showPrice}}"
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}"
                    />
                    <sp:else/>
                    <com:CarKemuAll
                        tiku="{{state.tiku}}"
                        showActiveBtn="{{true}}"
                        goodsInfo="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll)}}"
                        comparePricePool="{{state.comparePricePool}}"
                        groupKey="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll).groupKey}}"
                        payPrice="{{self.showPrice}}"
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}"
                    />
                </sp:if>
            </div>
        </sp:if>

        <!-- 货车全科 -->
        <sp:if value="self.getGroupKeyInfo(GroupKey.HcChannelKemuAll).payPrice">
            <div
                class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.HcChannelKemuAll?'show':'hide'}}"
            >
                <com:TruckKemuAll
                    goodsInfo="{{self.getGroupKeyInfo(GroupKey.HcChannelKemuAll)}}"
                    comparePricePool="{{state.comparePricePool}}"
                    tiku="{{state.tiku}}"
                    payPrice="{{self.showPrice}}"
                    payBtnCall="{{self.payBtnCall}}"
                    goAuth="{{self.goAuth}}"
                />
            </div>
        </sp:if>
        <!-- 客车全科 -->
        <sp:if value="self.getGroupKeyInfo(GroupKey.KcChannelKemuAll).payPrice">
            <div
                class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.KcChannelKemuAll?'show':'hide'}}"
            >
                <com:BusKemuAll
                    goodsInfo="{{self.getGroupKeyInfo(GroupKey.KcChannelKemuAll)}}"
                    comparePricePool="{{state.comparePricePool}}"
                    tiku="{{state.tiku}}"
                    payPrice="{{self.showPrice}}"
                    payBtnCall="{{self.payBtnCall}}"
                    goAuth="{{self.goAuth}}"
                />
            </div>
        </sp:if>

        <!-- 摩托全科VIP -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.MotoChannelKemuAll).payPrice"
        >
            <div
                class="{{self.nowGoodInfo&&self.nowGoodInfo.groupKey === GroupKey.MotoChannelKemuAll?'show':'hide'}}"
            >
                <com:MotoKemuAll
                    goodsInfo="{{self.nowGoodInfo}}"
                    labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>
    </div>

    <div class="footer-box">
        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon coupon-position-bottom" sp-on:click="goCoupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
            </div>
        </com:buyButton>
    </div>
    <!-- 所有商品信息加载完成才能加载这个组件，内部有根据商品判断 -->
    <sp:if value="state.showSignModal">
        <com:buyDialogOrderSign goodsInfoPool="{{state.goodsInfoPool}}" labelPool="{{state.labelPool}}"
            comparePricePool="{{state.comparePricePool}}" couponPool="{{state.couponPool}}"
            close="{{self.closeSignModal}}" />
    </sp:if>
    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
</div>
