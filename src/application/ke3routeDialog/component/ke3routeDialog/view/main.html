<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="payType" content=":component/payType/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />

<div class="route-pay-dialog">
    <div class="hedaer-wrap">
        <div class="content">
            <p class="title1">立即解锁</p>
        </div>
        <label class="close" sp-on:click="close"></label>
    </div>
    <sp:if value="state.goodsInfoPool[0].bought && (state.goodsInfoPool[1].bought && state.goodsInfoPool[1].upgrade)">
        <div class="part-c">
            <div class="pay-con">
                <div class="good-item1 {{state.tabIndex == 1 ? 'active': ''}}" sp-on:click="tabChange" data-type="1">
                    <div class="good1">
                        <p class="p1">
                            {{state.goodsInfoPool[1].name}}
                            <span class="compare_price {{state.goodsInfoPool[1].upgrade?'upgrade':''}}"></span>
                        </p>
                        <p class="p2">
                            <i class="price">¥</i><label class="price">{{state.goodsInfoPool[1].payPrice ||
                                '--'}}</label>
                        </p>
                    </div>
                    <div class="good2 {{state.interact?'interact':''}}">
                        <div class="auth-item">
                            <div class="img"></div>
                            <div class="auth-info">
                                <div class="auth-dec">考场实拍</div>
                            </div>
                        </div>
                        <sp:if value="state.interact">
                            <div class="auth-item">
                                <div class="img">
                                </div>
                                <div class="auth-info">
                                    <div class="auth-dec">互动视频</div>
                                </div>
                            </div>
                            <sp:else />
                            <div class="auth-item">
                                <div class="img"></div>
                                <div class="auth-info">
                                    <div class="auth-dec">3D练车</div>
                                </div>
                            </div>
                        </sp:if>
                        <div class="auth-item">
                            <div class="img">
                                <sp:if value="state.metaLightEmulatorTitle">
                                    <div class="tip"></div>
                                </sp:if>
                            </div>
                            <div class="auth-info">
                                <div class="auth-dec">灯光模拟</div>
                            </div>
                        </div>
                        <sp:if value="state.hasAnyTdRoute">
                            <div class="auth-item">
                                <div class="img"
                                    style="background:url(http://exam-room.mc-cdn.cn/exam-room/2024/06/19/09/5adf05ec6d67408f8398f34527f2a413.png)
                                    no-repeat center
                                    center/cover">
                                    <div class="tip"
                                        style="background:url(http://exam-room.mc-cdn.cn/exam-room/2024/06/19/09/9882fb71e8804aeb967acbb7d59e21df.png)
                                        no-repeat center center/100% 100%"
                                        ></div>
                                </div>
                                <div class="auth-info">
                                    <div class="auth-dec">3D真实考场</div>
                                </div>
                            </div>
                        <sp:elseif value="state.interact" />
                            <div class="auth-item">
                                <div class="img"></div>
                                <div class="auth-info">
                                    <div class="auth-dec">3D练车</div>
                                </div>
                            </div>
                        <sp:else />
                            <div class="auth-item">
                                <div class="img"></div>
                                <div class="auth-info">
                                    <div class="auth-dec">必考项目</div>
                                </div>
                            </div>
                        </sp:if>
                    </div>
                </div>
            </div>
        </div>
        <sp:else />
        <div class="part-c">
            <div class="pay-con">
                <div class="good-item {{state.tabIndex == 0 ? 'active': ''}}" sp-on:click="tabChange" data-type="0">
                    <div class="good1">
                        <p class="p1">{{state.goodsInfoPool[0].name}}视频包</p>
                    </div>
                    <div class="good2">
                        <p class="p1">
                            <i class="price">¥</i><label class="price">{{state.goodsInfoPool[0].payPrice ||
                                '--'}}</label>
                        </p>
                    </div>
                </div>
                <sp:if value="state.goodsInfoPool[1]">
                    <div class="good-item1 {{state.tabIndex == 1 ? 'active': ''}}" sp-on:click="tabChange"
                        data-type="1">
                        <div class="good1">
                            <p class="p1">
                                {{state.goodsInfoPool[1].name}}
                                <span class="compare_price {{state.goodsInfoPool[1].upgrade?'upgrade':''}}"></span>
                            </p>
                            <p class="p2">
                                <i class="price">¥</i><label class="price">{{state.goodsInfoPool[1].payPrice ||
                                    '--'}}</label>
                            </p>
                        </div>
                        <div class="good2 {{state.interact?'interact':''}}">
                            <div class="auth-item">
                                <div class="img"></div>
                                <div class="auth-info">
                                    <div class="auth-dec">考场实拍</div>
                                </div>
                            </div>
                            <sp:if value="state.interact">
                                <div class="auth-item">
                                    <div class="img">
                                    </div>
                                    <div class="auth-info">
                                        <div class="auth-dec">互动视频</div>
                                    </div>
                                </div>
                                <sp:else />
                                <div class="auth-item">
                                    <div class="img"></div>
                                    <div class="auth-info">
                                        <div class="auth-dec">3D练车</div>
                                    </div>
                                </div>
                            </sp:if>
                            <div class="auth-item">
                                <div class="img">
                                    <sp:if value="state.metaLightEmulatorTitle">
                                        <div class="tip"></div>
                                    </sp:if>
                                </div>
                                <div class="auth-info">
                                    <div class="auth-dec">灯光模拟</div>
                                </div>
                            </div>
                            <sp:if value="state.hasAnyTdRoute">
                                <div class="auth-item">
                                    <div class="img" style="background:url(http://exam-room.mc-cdn.cn/exam-room/2024/06/19/09/5adf05ec6d67408f8398f34527f2a413.png)
                                    no-repeat center
                                    center/cover">
                                        <div class="tip" style="background:url(http://exam-room.mc-cdn.cn/exam-room/2024/06/19/09/9882fb71e8804aeb967acbb7d59e21df.png)
                                        no-repeat center center/100% 100%"></div>
                                    </div>
                                    <div class="auth-info">
                                        <div class="auth-dec">3D真实考场</div>
                                    </div>
                                </div>
                                <sp:elseif value="state.interact" />
                                <div class="auth-item">
                                    <div class="img"></div>
                                    <div class="auth-info">
                                        <div class="auth-dec">3D练车</div>
                                    </div>
                                </div>
                                <sp:else />
                                <div class="auth-item">
                                    <div class="img"></div>
                                    <div class="auth-info">
                                        <div class="auth-dec">必考项目</div>
                                    </div>
                                </div>
                            </sp:if>
                        </div>
                    </div>
                </sp:if>

                <sp:if value="state.goodsInfoPool[2]">
                    <div class="good-item2 {{state.tabIndex == 2 ? 'active': ''}}" sp-on:click="tabChange"
                        data-type="2">
                        <div class="goods">
                            <p class="p1">
                                {{state.goodsInfoPool[2].name}}
                                <span class="compare_price"></span>
                            </p>
                            <p class="p2">
                                <i class="price">¥</i><label class="price">{{state.goodsInfoPool[2].payPrice ||
                                    '--'}}</label>
                            </p>
                        </div>
                        <div class="equity">
                            <sp:each for="state.comparePricePool[state.goodsInfoPool[2].groupKey].groupItems"
                                value="item" index="i">
                                <sp:if value="item.price && i<3">
                                    <div class="item">
                                        <div class="info">
                                            <div class="name">
                                                {{item.name}}
                                            </div>

                                            <div class="prize">
                                                单卖￥{{item.price}}
                                            </div>
                                        </div>
                                    </div>
                                    <sp:elseif value="i<3" />
                                    <div class="item">
                                        <div class="dec">
                                            {{item.name}}{{item.description}}
                                        </div>
                                    </div>
                                </sp:if>
                            </sp:each>
                        </div>
                    </div>
                </sp:if>
            </div>
        </div>
    </sp:if>

    <com:buyButton protocolUrl="{{self.protocolUrl}}">
        <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
            {{self.nowCouponInfo.couponCode?('已优惠' + self.nowCouponInfo.priceCent + '元'):'领取优惠券'}}
        </div>
    </com:buyButton>
    <com:payDialog />
</div>
