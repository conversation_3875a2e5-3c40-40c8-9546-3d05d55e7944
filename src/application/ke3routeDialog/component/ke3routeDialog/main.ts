/*
 * ------------------------------------------------------------------
 * 科三路线
 * ------------------------------------------------------------------
 */
import { MCProtocol } from '@simplex/simple-base';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PayTypeComponent from ':component/payType/main';
import { getAuthToken, getSystemInfo, webClose } from ':common/core';
import { LangType, PayType, Platform, URLCommon, URLParams, Version } from ':common/env';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackGoPay } from ':common/stat';
import { PROTOCOL1_URL, PROTOCOL2_URL } from ':common/navigate';
import { comparePrice, getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { typeCode } from ':common/features/bottom';
import { iosBuySuccess, iosDialogBuySuccess } from ':common/features/ios_pay';
import { getRouteVideo } from ':store/chores';
import { showSetting } from ':common/features/embeded';
import { getRouteCoupon } from ':store/coupon';
import { formatPrice } from ':common/utils';

enum typeMap {
    page = 'page',
    component = 'component'
}

const goodsHeight = {
    [GroupKey.ChannelKe3RouteMeta]: 91,
    [GroupKey.ChannelKe3RouteWeiyu]: 130,
    [GroupKey.ChannelKe3Group]: 130,
    [GroupKey.ChannelKe34]: 123,
    [GroupKey.ChannelKe3Route]: 130
};

interface State {
    tabIndex: number,
    placeInfo: {
        cityCode: string
        cityName: string
    }
    goodsInfoPool: GoodsInfo[],
    couponPool: any,
    hasAnyPermission: boolean
    comparePricePool: any,
    placeName: string,
    interact: boolean
    metaLightEmulatorTitle: boolean
    // 是否有真是考场
    hasAnyTdRoute: boolean
}
interface Props {
    type: typeMap,
    show: boolean
}

const fragmentName2 = '支付弹窗';

export default class extends Component<State, Props> {

    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog
        payType: PayTypeComponent
    };
    get protocolUrl() {
        return PROTOCOL1_URL;
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
    * 如果有优惠券的价格为0的就显示0
    * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
   */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            placeInfo: null,
            goodsInfoPool: [{
                groupKey: GroupKey.ChannelKe3RouteMeta
            } as GoodsInfo,
            {
                groupKey: (() => {
                    let key = GroupKey.ChannelKe3Group;
                    if (Platform.isWeiyu) {
                        key = GroupKey.ChannelKe3RouteWeiyu;
                        if (URLParams._lang && URLParams._lang === LangType.ZH) {
                            key = GroupKey.ChannelKe3Route;
                        }
                    }

                    return key;
                })()
            } as GoodsInfo,
            {
                groupKey: GroupKey.ChannelKe34
            } as GoodsInfo
            ],
            hasAnyPermission: false,
            comparePricePool: {},
            // 第一个商品名称（本来应该取商品名称的，但是这里是路线比较特殊）
            placeName: '',
            interact: false,
            metaLightEmulatorTitle: false,
            couponPool: {},
            hasAnyTdRoute: false
        };

    }
    async didMount() {

        if (this.props.type === typeMap.page) {
            trackGoPay({
                groupKey: this.nowGoodInfo.groupKey,
                payPathType: 0,
                fragmentName1: URLParams.fragmentName1
            });
        }
        await this.getVideoList();
        this.getGoodInfo();

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: this.nowGoodInfo.groupKey
        });
    }
    tabChange = (e) => {
        const index = +e.refTarget.getAttribute('data-type');

        this.setState({
            tabIndex: index
        });

        this.setPageInfo();
    }
    async getVideoList() {
        const systemInfo = await getSystemInfo();
        const userCityCode = systemInfo._userCity || systemInfo._cityCode;

        await getRouteVideo({
            cityCode: userCityCode,
            placeId: URLParams.placeId || ''
        }).then(data => {
            this.setState({
                placeInfo: {
                    cityCode: data.cityCode,
                    cityName: data.cityName
                },
                hasAnyPermission: data.hasAnyPermission,
                interact: Version.bizVersion > 14 && data.hasAnyPractice,
                placeName: data.placeName,
                metaLightEmulatorTitle: data.metaLightEmulatorTitle,
                hasAnyTdRoute: data.hasAnyTdRoute
            });
        });
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        let { tabIndex } = this.state;
        const groupKeys: GroupKey[] = [];
        const newGoodsPool = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo[0].bought = this.state.hasAnyPermission;

            if (goodsListInfo[0].bought && (goodsListInfo[1].bought && !goodsListInfo[1].upgrade)) {
                MCProtocol.Core.System.confirm({
                    title: '温馨提示',
                    message: '没有可购买的商品，可联系客服处理。',
                    action: '确定',
                    cancel: '取消',
                    callback: () => {
                        this.close();
                    }
                });
                return;
            }

            goodsListInfo.forEach((goodInfo, index) => {

                if (index === 0) {
                    goodInfo.name = this.state.placeName;
                }

                // 商品未购买才push
                if (index === 0 || !(goodInfo.bought && !goodInfo.upgrade)) {
                    newGoodsPool.push(goodInfo);
                }
            });

            if ((goodsListInfo[0].bought && (goodsListInfo[1].bought && goodsListInfo[1].upgrade)) || (+URLParams.tabIndex === 1 && newGoodsPool.length > 1)) {
                tabIndex = 1;
            }

            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool
            });
            if (this.props.type !== typeMap.component) {
                this.settingHeight();
            }

            if (this.props.type !== typeMap.component || this.props.show) {
                this.setPageInfo();
            }

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getComparePrice();

                if (this.props.type !== typeMap.component || this.props.show) {
                    this.setPageInfo();
                }
            }, 60);

        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });
            
            this.setState({ couponPool });
        });

    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice,
                        groupItems: item.groupItems
                    };
                }
            });

            this.setState({ comparePricePool });
        });
    }
    setPageInfo(stat?: { fragmentName1?: string, fragmentName2?: string }) {
        this.setBuyBottom(stat);
    }
    setBuyBottom(stat) {
        const fragmentName1 = URLParams.fragmentName1 || '底部吸底按钮';
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const bottomType: typeCode = typeCode.type4;

        this.children.buyButton.setPay({
            isInDialog: true,
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                if (this.state.tabIndex === 0) {
                    iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey, goUse: true, apiHost: 'squirrel' });
                } else {
                    iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
                }

            }
        });

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1,
                    fragmentName2,
                    protocolText2: '《考场视频会员协议》',
                    protocolUrl: this.protocolUrl,
                    goodsCityCode: this.nowGoodInfo.groupKey === GroupKey.ChannelKe3RouteMeta ? this.state.placeInfo?.cityCode : '',
                    extraInfo: JSON.stringify({
                        placeId: URLParams.placeId
                    }),
                    ...stat
                });
                break;
            default:
                break;
        }
    }
    settingHeight() {
        const { goodsInfoPool } = this.state;
        let iosH = 58;
        let androidH = 180;

        // 科三vip能被考场升级的场景，有2个商品  但是只展示一个商品，所以只要考场买了又没有跳走的情况，直接设置科三的高度
        if (goodsInfoPool[0].bought) {
            iosH += goodsHeight[goodsInfoPool[1].groupKey];
            androidH += goodsHeight[goodsInfoPool[1].groupKey];
            showSetting({
                iosH,
                androidH
            });
            return;
        }

        goodsInfoPool.forEach(item => {
            iosH += goodsHeight[item.groupKey];
            androidH += goodsHeight[item.groupKey];
        });

        showSetting({
            iosH,
            androidH
        });

    }
    pay = async (stat: PayStatProps) => {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo?.couponCode,
            goodsCityCode: this.nowGoodInfo.groupKey === GroupKey.ChannelKe3RouteMeta ? this.state.placeInfo?.cityCode : '',
            extraInfo: JSON.stringify({
                placeId: URLParams.placeId
            }),
            ...stat
        }, false).then(() => {

            if (this.state.tabIndex === 0) {
                newBuySuccess({ groupKey: this.nowGoodInfo.groupKey, goUse: true, apiHost: 'squirrel' }, 2);
            } else {
                newBuySuccess({ groupKey: this.nowGoodInfo.groupKey }, 2);
            }
        });
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    close() {
        const { type } = this.props;
        this.children.buyButton.hideButton();
        if (type === typeMap.component) {
            this.emit('close');
        } else {
            webClose();
        }
    }
    willReceiveProps() {
        return true;
    }
}
