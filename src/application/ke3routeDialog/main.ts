/*
 * ------------------------------------------------------------------
 * 科三路线
 * ------------------------------------------------------------------
 */
import { setPageName, URLParams } from ':common/env';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { showSetting } from ':common/features/embeded';
URLParams.noReplacePay = 'true';

interface State {

}
interface Props {
}

export default class extends Application<State, Props> {
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {

        };

    }
    async didMount() {
        setPageName(URLParams.fromPage || '路线视频路线详情页');
    }
}
