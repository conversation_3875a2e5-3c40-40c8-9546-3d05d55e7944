<import name="style" content="./main" />

<import name="indexPage" content=":application/zdstDialog/component/index/main" />
<import name="loading" content=":application/buyed/components/loading/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />


<div class="page-container page-zdst-dialog">
    <div class="body-panel" sp-on:scroll="pageScroll">
        <com:indexPage
            expireTimeString="{{state.expireTimeString}}"
            hasPromission="{{state.hasPromission}}"
            goodsInfoPool="{{state.goodsInfoPool}}"
            pageShow="{{self.pageShow}}"
        ></com:indexPage>
    </div>
  
    <com:loading />
    <com:expiredDialog />
</div>
