/*
 * main
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 16/3/24
 */
import { URLCommon, URLParams } from ':common/env';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import KqfdBuyDialog from ':application/kqfd/component/kqfdBuyDialog/main';
import { trackEvent, trackGoPay } from ':common/stat';
import { GoodsInfo } from ':store/goods';
import { getLessonList, getLiveLessonSchedule, getLiveWithPriority, subscribe } from ':store/kqfd';
import { dateFormat } from ':common/utils';
import OnlineDialog from ':application/kqfd/component/onlineDialog/main';
import { getAuthToken, openVipWebView, openWeb } from ':common/core';
import { KQFD_HISTORY, REAL_ROOM, SECRET, VIP_LIVE, ZDST_HISTORY, ZDST_HISTORY_DIALOG, ZXGK_HISTORY } from ':common/navigate';
import { makeToast } from ':common/features/dom';
import { onPageShow } from ':common/features/page_status_switch';
import jump from ':common/features/jump';

interface State {
    fragmentName1: string,
    lessonList: any[]
    lessonSchedule: any[]
}
interface Props {
    goodsInfoPool: GoodsInfo[],
    hasPromission: boolean,
    pageShow()
}

export default class extends Component<State, Props> {
    declare children: {
        kqfdBuyDialog: KqfdBuyDialog,
        onlineDialog: OnlineDialog
    }
    // 获取最新直播时间
    get newSchelTime() {
        const { lessonSchedule } = this.state;
        // 去最新的直播中，预约，回放
        const newZhiboData = [];
        lessonSchedule && lessonSchedule.forEach((res) => {
            newZhiboData.push(...res.liveDataList);
        });
        // status:1直播中，2预约，3回放
        const sortByData: any = newZhiboData && newZhiboData.sort((a, b) => {
            return +a.status - +b.status;
        });

        // eslint-disable-next-line max-len
        const timeString = dateFormat(sortByData[0]?.beginTime, 'MM.dd') + ' ' + dateFormat(sortByData[0]?.beginTime, 'HH:mm');
        return timeString;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            fragmentName1: '',
            lessonList: [],
            lessonSchedule: []
        };

    }
    willReceiveProps() {
        return true;
    }
    async didMount() {
        this.getLessonList();
        this.getZhiboData();
    }

    getLessonList() {
        getLessonList({
            page: 1,
            limit: 15,
            tagKey: 'ZDST'
        }).then((data: any) => {

            this.setState({
                lessonList: data.itemList
            });
        });
    }
    async getZhiboData() {
        const lessonSchedule: any = await getLiveLessonSchedule({
            limit: 2,
            lessonType: 3
        });

        this.setState({ lessonSchedule });

        if (lessonSchedule.length <= 0) {
            this.children.onlineDialog.show();
        }
    }
    onPay() {
        window.top.postMessage({
            type: 'pay',
            data: {}
        }, '*');
    }
    async makeappointment(e) {
        const { lessonSchedule } = this.state;
        e.stopPropagation();
        const lessonId = e.refTarget.getAttribute('data-liveSessionId');
        const index = e.refTarget.getAttribute('data-index');
        const sonIndex = e.refTarget.getAttribute('data-sonIndex');
        await subscribe({ lessonId });
        lessonSchedule[index].liveDataList[sonIndex].subscribeStatus = 1;

        this.setState({
            lessonSchedule
        });

    }
    async viewHistory() {
        jump.navigateTo(ZDST_HISTORY_DIALOG);
    }
}