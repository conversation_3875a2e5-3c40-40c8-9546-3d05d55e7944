/*
 * ------------------------------------------------------------------
 * 补偿信息
 * ------------------------------------------------------------------
 */

import { URLParams, Platform, setPageName } from ':common/env';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import Texts from ':common/features/texts';
import { trackEvent } from ':common/stat';
import { openVipWebView, openWeb, setStatusBarTheme } from ':common/core';
interface State {
    from: string,
    carStyle: string,
    carText: string,
    kemu: string | number,
    isIOS: boolean
}
export default class extends Application<State> {
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {
            from: URLParams.from || 'unknown',
            carStyle: URLParams.carStyle,
            carText: Texts.currentcarStyleLicenseTxt,
            kemu: URLParams.kemu || URLParams.kemuStyle,
            isIOS: Platform.isIOS
        };
    }
    didMount() {
        // 设置主题色
        setStatusBarTheme('dark');
        setPageName('补偿说明页');
        trackEvent({
            fragmentName1: '',
            actionName: '',
            actionType: '展示'
        });
        this.event.on('apply', 'click', () => {
            trackEvent({
                fragmentName1: '',
                actionName: '去申请',
                actionType: '点击'
            });
            if (this.state.isIOS) {
                if (Platform.isXueTang) {
                    openVipWebView({
                        url: 'https://laofuzi.kakamobi.com/jkbd-vip/insure/compensation.html?carStyle=' + this.state.carStyle + '&kemu=' + this.state.kemu
                    });
                    return;
                }
                openWeb({
                    url: 'http://jiakao.nav.mucang.cn/exam/compensation?page=' + encodeURIComponent('https://laofuzi.kakamobi.com/jkbd-vip/insure/compensation.html?carStyle=' + this.state.carStyle + '&kemu=' + this.state.kemu)
                });
            }

        });
    }

}
