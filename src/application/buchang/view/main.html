<import name="style" content="./main" module="S" />
<div class="{{S.pageContainer}}">
    <div class="{{S.buchangContentinfo}}">
        <div class=":buchang-content">
            <div class=":con">
                <h3>补偿步骤</h3>
                <p>1.点击底部的【去申请】；其次填写支付宝账号、上传模拟考试成绩，点击底部的【补充申请资料】按钮；</p>
                <p>2.在新页面中上传12123成绩单截图，点击【提交审核】即可完成补偿申请；</p>
                <p>3.提交申请后客服将在7-15个工作日完成补偿的处理，如符合补偿要求补偿金将发放至提交的支付宝账号。</p>
            </div>
            <div class=":con">
                <h3>补偿适用对象</h3>
                <p>1.中华人民共和国公民且持有有效居民身份证的用户；</p>
                <p>2.尚未获得中华人民共和国机动车驾驶证（包含{{state.carText}}）的用户；</p>
            </div>
            <div class=":con">
                <h3>补偿生效条件</h3>
                <p>1.购买驾考宝典科一VIP可申请科一补偿，购买科四VIP可申请科四补偿（备注：科四即科目三安全文明常识考试，下同）；</p>
                <p>2.购买VIP后填写并提交个人信息；</p>
                <p>3.补偿服务将在提交个人信息后次日0点开始生效。</p>
            </div>
            <div class=":con">
                <h3>补偿金额</h3>
                <sp:if value="state.carStyle !== 'light_trailer'">
                    <p>1.科一补偿50元，科四补偿50元；</p>
                </sp:if>
                <p>2.如有驾考宝典内其他追加补偿的活动补偿金额将可能增加，以活动规则为准。</p>

            </div>
            <div class=":con">
                <h3>补偿适用条件</h3>
                <p>1.用户需在补偿服务生效后申请补偿；</p>
                <p>2.用户需在补偿生效后再参加公安局车管所举办的机动车驾驶员考试{{state.carStyle == 'light_trailer' ? '科四' :
                    '科一/科四'}}，补偿生效前的考试无法获得补偿；</p>
                <p>3.用户的机动车驾驶员考试成绩{{state.carStyle == 'light_trailer' ? '科四' :
                    '科一/科四'}}为不合格才可申请补偿，参加考试前其在驾考宝典APP中需有2次以上（含2次）模拟考试成绩大于等于90分。</p>
            </div>
            <div class=":con">
                <h3>补偿有效期</h3>
                <p>1.补偿有效期与对应科目的VIP有效期一致，自VIP购买之日起180天有效；</p>
                <p>2.补偿需要在VIP有效期内发起，如VIP已过期则不可再申请补偿。</p>
            </div>
            <div class=":con">
                <h3>补偿发放方式</h3>
                <p>申请补偿时填写的支付宝账号为补偿发放的唯一渠道，补偿审核通过后将打款至该支付宝账号内，请注意查收；</p>
                <p>温馨提示：请务必填写用户本人实名认证的支付宝账号，如填写他人账号或错误账号将影响补偿的发放。</p>
            </div>

            <div class=":con">
                <h3>补偿发放次数</h3>
                <p>在符合补偿条件的情况下，一个会员有效期内每个科目仅可获得一次补偿。</p>
            </div>
        </div>
        <div class=":footer" ref="apply">去申请</div>
    </div>
</div>