.ke2_3d-page {
    position: relative;
    padding: 15px 15px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    max-width: 375px;
    margin: 0 auto;

    .title {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC, sans-serif;
        font-weight: 500;
        color: #333333;
        line-height: 22px;
        flex-shrink: 0;
    }

    .close {
        position: absolute !important;
        top: 8px;
        right: 10px;
        width: 20px;
        height: 20px;
        background: url("../images/close.png");
        background-size: cover;
    }

    .tabs-one-box {
        margin-top: 15px;
        height: 75px;
        background: url(../images/20.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        padding: 4px 15px 0 15px;

        .desc1 {
            font-size: 18px;
            font-weight: bold;
            color: #681309;
            line-height: 18px;
        }

        .desc2 {
            font-size: 14px;
            font-weight: 400;
            color: #9f5217;
            line-height: 20px;
            padding-top: 10px;
        }

        .price {
            flex: 1;
            text-align: right;
            padding-right: 10px;

            i {
                font-size: 18px;
                font-weight: 400;
                color: #681309;
                line-height: 25px;
                padding-right: 4px;
            }

            b {
                font-size: 30px;
                font-weight: bold;
                color: #681309;
                line-height: 36px;
            }
        }
    }

    .tabs-two-box {
        .tabs {
            margin-top: 25px;
            display: flex;
            justify-content: space-between;
        }

        .tab {
            width: 0;
            flex: 1;
            border-radius: 8px;
            text-align: center;
            position: relative;

            &:nth-of-type(1) {
                margin-right: 10px;
            }

            .top {
                line-height: 51px;
                font-size: 18px;
                font-family: PingFangSC-Semibold, PingFang SC, sans-serif;
                font-weight: 600;
                white-space: nowrap;
                border-radius: 8px 8px 0 0;

                &.smaller {
                    font-size: 15px;
                }
            }

            .bottom {
                line-height: 25px;
                font-size: 12px;
                font-family: PingFangSC-Medium, PingFang SC, sans-serif;
                font-weight: 500;
                border-radius: 0 0 8px 8px;
            }

            .label {
                position: absolute;
                right: 6px;
                top: -10px;
                width: 40px;
                height: 15px;
                background: url('../images/tag.png');
                background-size: 100% 100%;
            }

            &+& {
                margin-left: 11px;
            }

            &.tabNormal {
                box-shadow: 0 0 0 1px #f4c2a2;

                .top {
                    color: #171717;
                    background: #ffffff;
                }

                .bottom {
                    color: #aa4120;
                    background: #fef0e7;
                }
            }

            &.tabActive {
                .top {
                    color: #ffffff;
                    background: linear-gradient(113deg, #353b4e 0%, #1d222b 100%);
                }

                .bottom {
                    color: #aa4120;
                    background: linear-gradient(118deg, #f9dbc0 0%, #efaf8b 100%);
                }
            }
        }
    }

    .detail {
        margin-top: 15px;
        height: 0;
        flex: 1;

        &.no-top {
            margin-top: -10px;
        }

        .subtitle {
            margin-bottom: 10px;
            font-size: 15px;
            font-family: PingFangSC-Medium, PingFang SC, sans-serif;
            font-weight: bold;
            color: #692204;
            line-height: 21px;
            text-align: center;

            b {
                color: #f73b31;
            }
        }

        .showcase {
            display: flex;
            align-items: center;
            padding: 6px;
            padding-left: 0;
            background: linear-gradient(156deg,
                    #ffe6ca 0%,
                    #ffddb9 19%,
                    #fbdfbd 44%,
                    #facc91 100%);
            border-radius: 5px;
            border: 1px solid #ffffff;
            text-align: center;
            white-space: pre-wrap;

            .left {
                width: 70px;
                font-size: 12px;
                font-family: PingFangSC-Semibold, PingFang SC, sans-serif;
                font-weight: 600;
                color: #692204;
                line-height: 17px;
                display: flex;
                padding-top: 4px;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .price {
                    font-size: 16px;
                    color: #f73b31;
                    line-height: 22px;

                    span {
                        font-size: 11px;
                        line-height: 16px;
                    }
                }
            }

            .card {
                height: 86px;
                padding: 0 6px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background: linear-gradient(163deg, #fffbf6 0%, #fff7ee 100%);
                border-radius: 5px;
                border: 1px solid #ffcc8a;
                font-size: 13px;
                font-family: PingFangSC-Medium, PingFang SC, sans-serif;
                font-weight: 500;
                color: #a03c1c;
                width: 0;
                flex: 1;

                .top {
                    min-height: 32px;
                    line-height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .price {
                    font-size: 16px;
                    line-height: 22px;

                    span {
                        font-size: 11px;
                        line-height: 16px;
                    }
                }

                .desc {
                    font-size: 12px;
                    line-height: 22px;
                }
            }

            .circle {
                margin: 0 -5px;
                position: relative;
                width: 15px;
                height: 15px;
                background: url("../images/circle.png");
                background-size: cover;
            }
        }

        .sec1 {
            margin-top: 15px;
            height: 75px;
            background: url(../images/20.png) no-repeat;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            padding: 4px 15px 0 15px;

            .desc1 {
                font-size: 18px;
                font-weight: bold;
                color: #681309;
                line-height: 18px;
            }

            .desc2 {
                font-size: 14px;
                font-weight: 400;
                color: #9f5217;
                line-height: 20px;
                padding-top: 10px;
            }

            .price {
                flex: 1;
                text-align: right;
                padding-right: 10px;

                i {
                    font-size: 18px;
                    font-weight: 400;
                    color: #681309;
                    line-height: 25px;
                    padding-right: 4px;
                }

                b {
                    font-size: 30px;
                    font-weight: bold;
                    color: #681309;
                    line-height: 36px;
                }
            }
        }

        .sec2 {
            h3 {
                font-size: 16px;
                font-weight: bold;
                line-height: 21px;
                text-align: center;
                color: #692204;
            }

            .steps {
                display: flex;
                justify-content: space-around;
                padding-top: 10px;
            }

            .step {
                width: 78px;
                height: 86px;
                border-radius: 5px;
                position: relative;
                background: url(../images/13.png) no-repeat;
                background-size: 100% 100%;
                display: flex;
                flex-direction: column;
            }

            i {
                width: 33px;
                height: 15px;
                margin-top: 1px;
                margin-left: 1px;
            }

            .icon1 {
                background: url(../images/15.png) no-repeat;
                background-size: 100% 100%;
            }

            .icon2 {
                background: url(../images/16.png) no-repeat;
                background-size: 100% 100%;
            }

            .icon3 {
                background: url(../images/17.png) no-repeat;
                background-size: 100% 100%;
            }

            .icon4 {
                background: url(../images/21.png) no-repeat;
                background-size: 100% 100%;
            }

            span {
                font-size: 13px;
                color: #8c3418;
                font-weight: bold;
                line-height: 16px;
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                text-align: center;
                padding: 0 6px;
            }

            label {
                font-size: 12px;
                color: #a03c1c;
                line-height: 17px;
                text-align: center;
                padding: 3px 0;
            }
        }

        .addon {
            .title {
                margin-top: 20px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #671e02;
                line-height: 20px;
            }

            .content {
                margin-top: 10px;
                margin-bottom: 20px;
                padding: 0 10px;
                height: 45px;
                display: flex;
                align-items: center;
                background: url("../images/deco.png") no-repeat,
                    linear-gradient(133deg,
                        #fcf6ef 0%,
                        #fff7ee 35%,
                        #fef0e5 69%,
                        #fbeadc 100%);
                background-size: 65px 64px, auto;
                border-radius: 4px;
            }

            .check {
                color: #a2580c;
            }

            .name {
                margin-left: 10px;
                flex: 1;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #671e02;
                line-height: 22px;
                font-size: 16px;
            }

            .solo-price {
                font-size: 12px;
                color: #a2580c;
                line-height: 17px;
            }

            .addon-price {
                margin-left: 7px;
                font-size: 14px;
                color: #ee4034;
                line-height: 20px;
            }
        }

    }

    .pay-type {
        margin-bottom: -10px;
        margin-left: 10px;
        flex-shrink: 0;
    }

    .buy-btn {
        margin: 0 -10px;
        flex-shrink: 0;
    }

}

body {
    max-width: none;
    background: linear-gradient(180deg,
            #ffeddc 0%,
            #fffaf0 38%,
            #ffffff 73%,
            #ffffff 100%);
    box-shadow: 0px 1px 0px 0px #ffffff;
    border-radius: 6px 6px 0px 0px;
}
