<import name="style" content="./main" />

<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="Count" content=":component/count/main" />
<import name="cards" content=":component/buyDialogB/component/cards/main" />
<import name="standbyGoods" content=":application/car/component/standbyGoods/main" />

<div class="ke2_3d-page">
    <div class="title">购买</div>
    <div class="hotArea close" ref="close"></div>

    <!-- 显示单个Tab -->
    <sp:if value="state.goodsList.length===1">
        <div class="tabs-one-box">
            <div>
                <p class="desc1">{{state.goodsList[0].name}}</p>
                <sp:if value="state.goodsList[0].groupKey===GroupKey.ChannelKe2Asset">
                    <p class="desc2">
                        提前看考场，考试更有信心
                    </p>
                    <sp:else />
                    <p class="desc2">
                        {{URLCommon.tiku === CarType.MOTO
                            ?'快速学习科目二考试规则':'考不过补偿40元'}}
                    </p>
                </sp:if>
            </div>
            <div class="price">
                <i>¥</i><b>{{state.goodsList[0].payPrice || '--'}}</b>
            </div>
        </div>
    </sp:if>

    <sp:if value="state.goodsList.length===2">
        <div class="tabs-two-box">
            <div class="tabs">
                <sp:each for="state.goodsList">
                    <div class="tab {{$index===state.tabIndex?'tabActive':'tabNormal'}}" sp-on:click="onTabClick"
                        data-idx="{{$index}}">
                        <div class="top {{$value.name.length>=7&&'smaller'}}">
                            {{$value.name}}&nbsp;{{$value.payPrice}}元
                        </div>
                        <div class="bottom">{{self.getLabelTip($index)}}</div>
                        <sp:if value="$index===1">
                            <div class="label" />
                        </sp:if>
                    </div>
                </sp:each>
            </div>
        </div>
    </sp:if>

    <sp:if value="state.standbyPool.length && state.tabIndex === 0">
        <com:standbyGoods changeGoods="{{self.changeGoods}}" standbyPool="{{state.standbyPool}}" />
    </sp:if>

    <div class="detail {{state.standbyPool.length && state.tabIndex === 0?'no-top':''}}">
        <sp:if value="state.comparePricePool[self.currentGoods.groupKey]">
            <h3 class="subtitle">
                一起买更优惠
            </h3>
            <div class="showcase">
                <div class="left">
                    <span>比分开买<br />立省</span>
                    <span
                        class="price"><span>￥</span>{{state.comparePricePool[self.currentGoods.groupKey].diffPrice}}</span>
                </div>
                <sp:each for="state.comparePricePool[self.currentGoods.groupKey].groupItems">
                    <div class="card">
                        <span class="top">{{$value.name}}</span>
                        <sp:if value="$value.price">
                            <span class="price"><span>￥</span>{{$value.price}}</span>
                            <sp:else />
                            <span class="desc">{{$value.description}}</span>
                        </sp:if>
                    </div>
                    <div class="circle"
                        sp:if="{{$index!==state.comparePricePool[self.currentGoods.groupKey].groupItems.length-1}}" />
                </sp:each>
            </div>
            <sp:else />
            <div class="sec2">
                <sp:if value="self.currentGoods.groupKey===GroupKey.ChannelKe2Asset">
                    <h3>
                        - {{state.labelPool[self.currentGoods.groupKey].highlights.length + '大服务助你高效学习'}} -
                    </h3>
                    <sp:elseif value="!(state.standbyPool.length && state.tabIndex === 0)" />
                    <h3>
                        - {{URLCommon.tiku === CarType.MOTO ?
                                state.labelPool[self.currentGoods.groupKey].highlights.length + '大服务助你高效学习'
                                : (self.currentGoods.name || '') + ' 只需三步'}} -
                    </h3>
                </sp:if>
                <div class="steps">
                    <sp:each for="state.labelPool[self.currentGoods.groupKey].highlights">
                        <div class="step">
                            <i class="{{'icon' + ($index + 1)}}"></i>
                            <span>{{$value.highlight}}</span>
                            <label>{{#($value.description ||
                                        '&nbsp;')}}</label>
                        </div>
                    </sp:each>
                </div>
            </div>
        </sp:if>
    </div>

    <div class="buy-btn">
        <com:buyButton>
            <div class="bottom_coupon" sp:slot="couponEntry">{{self.currentCoupon.hint}}</div>
        </com:buyButton>
    </div>
</div>
