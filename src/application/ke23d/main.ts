/*
 * ------------------------------------------------------------------
 * 科目二3D购买弹窗
 * ------------------------------------------------------------------
 */

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { ABTestKey, ABTestType, CarType, PayType, setPageName, URLCommon, URLParams } from ':common/env';
import { comparePrice, ComparePriceInfo, getGroupSessionInfo, getSessionExtra, GoodsExtra, GoodsInfo, GroupKey, Kemu2SceneInfo } from ':store/goods';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackGoPay } from ':common/stat';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { iosBuySuccess } from ':common/features/ios_pay';
import { showSetting } from ':common/features/embeded';
import { BUYED_URL, openAuth } from ':common/navigate';
import { openVipWebView, webClose } from ':common/core';
import { Coupons, batchGetBestCoupon, goodsInfoWithCoupon, couponWithHint } from ':common/features/coupon';
import PayTypeCom from ':component/payType/main';
import jump from ':common/features/jump';
import { getAbtest, getKemu2ScenePermission } from ':store/chores';
import { couponAnimate } from ':common/features/dom';
import { onPageShow } from ':common/features/page_status_switch';
import { getTabIndex } from ':common/features/cache';
import isNumber from 'lodash/isNumber';

let fragmentName1 = URLParams.fragmentName1 || '未知片段';
let fragmentName2 = '支付弹窗';
let propsParams;

const groupKey = (() => {
    let groupKey;
    switch (URLCommon.tiku) {
        case CarType.CAR:
            groupKey = GroupKey.ChannelKe2;
            break;
        case CarType.MOTO:
            groupKey = GroupKey.MotoChannelKe2;
            break;
        case CarType.GUACHE:
            groupKey = GroupKey.GcChannelKe2;
            break;
        default:
            groupKey = GroupKey.ChannelKe2;
            break;
    }
    return groupKey;
})();

const bundleGroupKey = (() => {
    let groupKey;
    switch (URLCommon.tiku) {
        case CarType.CAR:
            groupKey = GroupKey.ChannelKe2Ke3GroupNew;
            break;
        case CarType.MOTO:
            groupKey = GroupKey.MotoChannelKe2Ke3Group;
            break;
        case CarType.GUACHE:
            groupKey = GroupKey.GcChannelKe2Ke4Group;
            break;
        default:
            groupKey = GroupKey.ChannelKe2Ke3Group;
            break;
    }
    return groupKey;
})();

const ke2Asset = GroupKey.ChannelKe2Asset;
const kemuAllGroupKey = GroupKey.ChannelKemuAll;

interface State {
    tabIndex: number;
    goodsList: GoodsInfo[];
    standbyPool: GoodsInfo[];
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    // 科二考场信息
    kemu2SceneInfo: Kemu2SceneInfo;
}

export default class Ke23D extends Application<State, {
    from?: string,
    fragmentName1?: string,
    fragmentName2?: string
}> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeCom;
    };

    $constructor(o) {
        this.$super({
            target: o.target || document.body,
            name: module.id,
            view: View
        });
        this.state = {
            tabIndex: 0,
            goodsList: [],
            standbyPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            kemu2SceneInfo: null
        };
        this.props = {};
    }

    getLabelTip($index: number) {
        if ($index === 0) {
            let title = '';
            switch (URLCommon.tiku) {
                case CarType.GUACHE:
                    title = '解锁科二必考项目视频';
                    break;
                case CarType.MOTO:
                    title = '科二难点一网打尽';
                    break;
                default:
                    title = '考不过补偿40元';
            }
            return title;
        }
        return this.state.goodsList[$index].groupKey === GroupKey.ChannelKemuAll ? '尊享从科一到科四付费权益' : '比分开买立省' + this.state.comparePricePool[this.state.goodsList[$index].groupKey].diffPrice + '元';
    }

    get currentGoods() {
        return goodsInfoWithCoupon(this.state.goodsList[this.state.tabIndex], this.currentCoupon);
    }

    get currentCoupon() {
        return couponWithHint(this.state.couponPool[this.state.goodsList[this.state.tabIndex]?.groupKey]);
    }

    /** 是否显示在h5页面中，还是单独的弹窗 */
    get isInH5Page() {
        return (this as any).target !== document.body;
    }
    async didMount() {
        this.windowResize();
        // ke2Exam 页面用到
        fragmentName1 = this.props.fragmentName1 || fragmentName1;
        fragmentName2 = this.props.fragmentName2 || fragmentName2;
        !this.isInH5Page && setPageName(URLParams.fromPage || '项目视频详情页');

        !this.isInH5Page && trackGoPay({
            groupKey,
            fragmentName1,
            payPathType: 0
        });

        this.event.on('close', 'click', () => {
            // 作为落地页的弹窗使用时，不直接关闭
            if (this.isInH5Page) {
                // 科二前置页关闭时需要知道关闭时选择的tab信息
                if (this.props.from !== 'ke2_exam') {
                    this.emit('close', null);
                } else {
                    this.emit('close', this.currentGoods);
                }
            } else {
                webClose();
            }
        });

        ensureSiriusBound({ groupKey, type: PayBoundType.GoLogin });

        await this.fetchGoodsInfo();

    }

    async fetchGoodsInfo() {
        const standbyPool: GoodsInfo[] = [];
        if (URLCommon.tiku === CarType.CAR) {
            // standbyPool.push({
            //     groupKey: GroupKey.ChannelKe2
            // } as GoodsInfo);
        }
        const standbyGoodsCount = standbyPool.length;
        let groupKeys = standbyPool.map(item => item.groupKey).concat([groupKey, bundleGroupKey, kemuAllGroupKey]);

        // 挂车没有全科
        if (URLCommon.tiku === CarType.GUACHE) {
            groupKeys = [groupKey, bundleGroupKey];
        }
        if (URLCommon.tiku === CarType.MOTO) {
            groupKeys = [groupKey, bundleGroupKey];
        }

        const infoList = await getGroupSessionInfo({
            groupKeys
        });
        const standbyGoodsListInfo = infoList.slice(0, standbyGoodsCount);
        const [goodsInfo, bundleGoodsInfo, kemuAllGoodsInfo] = infoList.slice(standbyGoodsCount);

        const goodsList = [];
        // 主商品已购买，直接跳走 （科二考场前置页面，不跳转已购页面）
        // 构建第一个商品
        if (goodsInfo.bought) {
            if (URLCommon.tiku === CarType.CAR && URLParams.k2AssetsId) {
                const kemu2SceneInfo = await getKemu2ScenePermission(URLParams.k2AssetsId);

                if (kemu2SceneInfo && !kemu2SceneInfo.hasKemu2TdPermission) {
                    const [ke2AssetInfo] = await getGroupSessionInfo({
                        groupKeys: [ke2Asset]
                    });

                    if (this.state.kemu2SceneInfo?.cityName) {
                        ke2AssetInfo.name = kemu2SceneInfo.cityName + ke2AssetInfo.name;
                    }

                    goodsList.push(ke2AssetInfo);

                    this.setState({ goodsList, kemu2SceneInfo });
                    this.switchTab(this.state.tabIndex);
                    return;
                } else {
                    openVipWebView({
                        url: BUYED_URL
                    });
                    return;
                }

            } else if (this.props.from !== 'ke2_exam') {
                openVipWebView({
                    url: BUYED_URL
                });
                return;
            }
        }

        goodsList.push(goodsInfo);

        // 全科可升级
        // 构建第二个商品
        if (kemuAllGoodsInfo?.upgrade) {
            kemuAllGoodsInfo.name = '升级全科目VIP';
            goodsList.push(kemuAllGoodsInfo);

        } else if ((bundleGoodsInfo && !bundleGoodsInfo.bought)) {
            goodsList.push(bundleGoodsInfo);
        }
        this.setState({
            goodsList,
            standbyPool: standbyGoodsCount ? [
                {
                    ...goodsList[0],
                    tempName: '连续包月'
                },
                ...standbyGoodsListInfo.map(goodInfo => ({
                    ...goodInfo,
                    tempName: '半年卡'
                }))
            ] : []
        });

        showSetting({
            iosH: 300,
            androidH: 430
        });

        this.switchTab(this.state.tabIndex);
        // 作为落地页的弹窗使用时，告诉落地页主商品信息
        this.isInH5Page && this.emit('goodsInfo', goodsInfo);

        setTimeout(async () => {
            await this.fetchCouponInfo(infoList);
            await this.fetchComparePriceInfo(infoList);
            await this.fetchLabelInfo(infoList);

            const { goodsList } = this.state;

            couponAnimate({
                couponTargetDomSelect: '.bottom_coupon',
                compareTargetDomSelect: `.tab-cards .${goodsList[2]?.groupKey}`,
                couponData: {
                    ...this.currentCoupon,
                    priceCent: this.currentCoupon.price
                },
                compareData: this.state.comparePricePool[goodsList[2]?.groupKey],
                compareGoodsData: goodsList[2],
                goodsData: this.currentGoods
            });
        }, 60);
    }

    async fetchCouponInfo(goodsList) {
        // 获取优惠券信息
        const couponPool = await batchGetBestCoupon(goodsList);
        this.setState({ couponPool });
        // 从科二前置页进来，不希望他再次设置。
        if (this.props.from !== 'ke2_exam') {
            this.setPayment();
        }
    }

    async fetchLabelInfo(goodsList) {

        const promiseList = [];
        const labelPool = {};

        goodsList.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsList[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });

    }

    async fetchComparePriceInfo(goodsList) {
        const promiseList = [];
        const comparePricePool = {};

        goodsList.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsList[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice,
                        groupItems: item.groupItems
                    };
                }
            });

            this.setState({ comparePricePool });
        });
    }

    switchTab = (tabIndex: number) => {
        if (tabIndex !== this.state.tabIndex) {
            this.setState({ tabIndex });
            this.setPayment();
        }
    }

    /** 设置支付参数 */
    public setPayment(params?: { propFragmentName1?: string }) {
        // switch 切换tab时需要
        if (this.props.from === 'ke2_exam' && params) {
            propsParams = params;
        }
        this.children.buyButton.setPay({
            androidPay: this.pay.bind(this),
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => iosBuySuccess({ groupKey: this.currentGoods.groupKey }),
            isInDialog: true
        });
        if (this.currentGoods.inActivity) {
            this.children.buyButton.setButtonConfig({
                groupKey: this.currentGoods.groupKey,
                type: 6,
                title: '确认协议并支付',
                subtitle: '有效期' + this.currentGoods.validDays + '天',
                price: this.currentGoods.payPrice,
                validDays: this.currentGoods.validDays,
                originalPrice: `日常价￥${this.currentGoods.inActivity.preDiscountPrice}`,
                discount: `已立减${this.currentGoods.inActivity.discountedPrice}元`,
                fragmentName1: params?.propFragmentName1 || propsParams?.propFragmentName1 || fragmentName1,
                fragmentName2,
                actionName: this.isInH5Page ? '确认支付' : '去支付'
            });
        } else {
            this.children.buyButton.setButtonConfig({
                groupKey: this.currentGoods.groupKey,
                type: 1,
                title: '确认协议并支付',
                price: this.currentGoods.payPrice,
                validDays: this.currentGoods.validDays,
                fragmentName1: params?.propFragmentName1 || propsParams?.propFragmentName1 || fragmentName1,
                fragmentName2,
                actionName: this.isInH5Page ? '确认支付' : '去支付'
            });
        }
    }

    /** 发起支付 */
    public async pay(stat: PayStatProps) {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.currentGoods.groupKey,
            sessionIds: this.currentGoods.sessionIds,
            activityType: this.currentGoods.activityType,
            goodsCityCode: this.currentGoods.groupKey === GroupKey.ChannelKe2Asset ? this.state.kemu2SceneInfo?.cityCode : '',
            couponCode: this.currentCoupon.code,
            ...stat
        }, false).then(() => newBuySuccess({ groupKey: this.currentGoods.groupKey }, 2));
    }

    onTabClick(e) {
        const idx = +e.refTarget.getAttribute('data-idx');
        this.switchTab(idx);
    }
    async gotoRightDetail(e) {
        const rightsIntroduceCode = e.refTarget.getAttribute('data-rightsIntroduceCode');

        const groupKeys = [];
        this.state.goodsList && this.state.goodsList.forEach((res) => {
            groupKeys.push([res.groupKey]);
        });
        openAuth({
            authId: rightsIntroduceCode,
            groupKeys: groupKeys.join(','),
            groupKey: this.currentGoods.groupKey
        });
        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.switchTab(tabIndex);
    }
    windowResize() {
        const reCalc = function () {
            const dimensionWidth = Math.min(document.documentElement.clientWidth, 480);

            const rate = dimensionWidth / Package.build.style.baseWidth;

            const baseFontSize = 100 * rate;
            window.baseFontSize = baseFontSize;

            document.documentElement.style.fontSize = baseFontSize + 'px';
        };
        setTimeout(() => {
            reCalc();
        }, 500);
    }
    changeGoods = (goodsInfo) => {
        const { goodsList } = this.state; 
        const tabIndex = 0;
        goodsList[tabIndex] = goodsInfo;

        this.setState({
            goodsList
        });
        this.setPayment();

    }
}
