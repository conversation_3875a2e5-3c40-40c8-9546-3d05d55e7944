<import name="style" content="./main" />
<import name="header" content=":component/header/main" />

<div class="page-plivateTeachHomeWork">
    <div class="page-header">
        <com:header title="私教班课后作业" back="{{self.backCall}}">
            <div sp:slot="right"></div>
        </com:header>
    </div>

    <sp:if value="{{state.homeWorkList && state.homeWorkList.length}}">
        <div class="tab-list">
            <sp:each for="{{state.homeWorkList}}">
                <div sp-on:click="onTabChange" data-idx="{{$index}}"
                    class="tab-item {{state.tabIndex === $index?'active':''}}">
                    {{Tools.dateFormat($value.beginTime, 'MM月dd日')}}</div>
            </sp:each>
        </div>
    </sp:if>

    <sp:if value="{{state.homeWorkList && state.homeWorkList[state.tabIndex]}}">
        <div class="homework-box">
            <div class="homework {{state.homeWorkList[state.tabIndex].coursePlaybackUrl?'':'noVideo'}}">
                <div class="title-box">
                    <div class="title">老师的要求</div>
                    <div class="title-dec">
                        1. 模拟考试要耐心做完再交卷，不要错了几题就放弃；<br />
                        2. 专项训练都是老师针对你薄弱知识点布置的。一定要完整答题；<br />
                        3.只有完成课后作业，才能巩固上课讲过的知识点，成绩才能提升。我们一起加油！
                    </div>
                </div>
                <div class="work-list">
                    <sp:each for="{{state.homeWorkList[state.tabIndex].courseHomeworkList}}" value="$sonItem">
                        <div class="work-item" data-type="{{$sonItem.type}}" data-tagid="{{$sonItem.bizValue}}"
                            data-questions="{{$sonItem.questionIds}}"
                            data-kemu="{{state.homeWorkList[state.tabIndex].kemu}}"
                            sp-on:click="onGoActive">
                            <div class="icon type{{$sonItem.type}}"></div>

                            <sp:if value="{{$sonItem.type === 3}}">
                                <div class="txt">扫清错题本</div>
                                <sp:elseif value="{{$sonItem.type === 1}}" />
                                <div class="txt">模拟考试（{{$sonItem.bizValue}}次）</div>
                                <sp:else />
                                <div class="txt">专项训练（{{$sonItem.bizName}}）</div>
                                <sp:if value="{{$sonItem.relatedLessonId}}">
                                    <div class="active-box" data-relatedlessonid="{{$sonItem.relatedLessonId}}"
                                        data-title="{{$sonItem.bizName}}" sp-on:click="goLookLesson">
                                        学习资料 >
                                    </div>
                                </sp:if>
                            </sp:if>
                        </div>
                    </sp:each>
                </div>
            </div>
            <sp:if value="{{state.homeWorkList[state.tabIndex].coursePlaybackUrl}}">
                <div class="look-video" data-url="{{state.homeWorkList[state.tabIndex].coursePlaybackUrl}}"
                    sp-on:click="onPlayVideo"></div>
            </sp:if>
        </div>
        <sp:elseif value="{{state.hasReq}}" />
        <div class="homework-list-empty">
            <div class="empty-img"></div>
            <div class="empty-dec">暂时还没有课后作业</div>
        </div>
    </sp:if>
</div>
