.page-plivateTeachHomeWork {
    height: 100vh;
    display: flex;
    flex-direction: column;

    .page-header {
        flex-shrink: 0;
        background-color: #fff;
    }

    .tab-list {
        flex-shrink: 0;
        padding: 10px 0 10px 15px;
        display: flex;
        white-space: nowrap;
        overflow-x: auto;

        .tab-item {
            width: 80px;
            height: 34px;
            color: #464646;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
            font-size: 14px;

            &.active {
                color: white;
                background-color: #04A5FF;
                border-radius: 17px;
            }
        }
    }

    .homework-box {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
        background-color: white;
        position: relative;

        .homework {
            max-height: 100%;
            overflow-y: auto;
            background: linear-gradient(180deg, #d9eeff, #f2fcff);
            border-radius: 10px;
            padding: 15px 15px 70px;

            &.noVideo{
                padding: 15px;
            }

            .title-box {
                .title {
                    font-size: 14px;
                    color: #464646;
                    line-height: 20px;
                }

                .title-dec {
                    margin-top: 5px;
                    font-size: 13px;
                    line-height: 18px;
                    color: #333;
                }
            }

            .work-list {
                margin-top: 3px;

                .work-item {
                    margin-top: 12px;
                    height: 85px;
                    background: #ffffff;
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    padding-left: 20px;
                    position: relative;

                    .icon {
                        width: 42px;
                        height: 42px;
                        background-repeat: no-repeat;
                        background-position: center center;
                        background-size: cover;

                        &.type1 {
                            background-image: url(../images/1.png);
                        }

                        &.type2 {
                            background-image: url(../images/2.png);
                        }

                        &.type3 {
                            background-image: url(../images/3.png);
                        }
                    }

                    .txt {
                        margin-left: 15px;
                        font-size: 16px;
                        line-height: 22px;
                        color: #333;
                    }

                    .active-box {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        padding: 5px 13px;
                        border-radius: 12px 0px 12px 0px;
                        font-size: 12px;
                        color: #72491F;
                        line-height: 17px;
                    }
                }
            }
        }

        .look-video {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            flex-shrink: 0;
            margin: 0 auto;
            width: 226px;
            height: 52px;
            background: url(../images/4.png) no-repeat center center/cover;
        }
    }

    .homework-list-empty {
        flex: 1;
        background-color: #F5F7FA;
        display: flex;
        flex-direction: column;
        align-items: center;

        .empty-img {
            margin-top: 100px;
            width: 83px;
            height: 83px;
            background: url(../images/empty.png) no-repeat center center/cover;
        }

        .empty-dec {
            margin-top: 15px;
            font-size: 15px;
            color: #666;
        }
    }

}
