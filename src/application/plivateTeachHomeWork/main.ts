/*
 * ------------------------------------------------------------------
 * 废弃，项目迁移到manual-score(填写成绩)
 * ------------------------------------------------------------------
 */

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { getStudentCourseList, kemuParamsMap, kq2hList } from ':store/chores';
import { getAuthToken, openVipWebView, openWeb, setStatusBarTheme } from ':common/core';
import { URLCommon, setPageName } from ':common/env';
import { trackEvent, trackPageLoad } from ':common/stat';
import { login } from ':common/features/login';
import { makeToast } from ':common/features/dom';

interface State {
    // 请求是否完成
    hasReq: boolean
    homeWorkList: any[]
    tabIndex: number
}
export default class extends Application<State> {
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            hasReq: false,
            homeWorkList: [],
            tabIndex: 0
        };
    }
    async didMount() {

        setPageName('私教班课后作业页');

        trackPageLoad();

        setStatusBarTheme('dark');

        const authToken = await getAuthToken();

        if (!authToken) {
            login();
        } else {

            getStudentCourseList().then(data => {
                this.setState({
                    hasReq: true,
                    homeWorkList: data.studentCourseDTOS
                });
            });

        }

    }
    onTabChange(e) {
        const { tabIndex } = this.state;
        const index = +e.refTarget.getAttribute('data-idx');

        if (tabIndex !== index) {
            this.setState({
                tabIndex: index
            });
        }

    }
    onPlayVideo(e) {
        const videoUrl = e.refTarget.getAttribute('data-url');
        openWeb({
            url: 'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-66/video.html',
            params: {
                video: videoUrl,
                title: '视频回放'
            }
        });
    }
    goWrong() {
        openWeb({
            url: 'http://jiakao.nav.mucang.cn/myError'
        });
    }
    goExamReal(e) {
        const kemu = +e.refTarget.getAttribute('data-kemu');

        trackEvent({
            fragmentName1: '课程版块',
            actionType: '点击',
            actionName: '去模考'
        });
        if (kemu === 40) {
            openWeb({
                url: 'http://jiakao.nav.mucang.cn/regain_licence?kemuStyle=1,4'
            });

        } else {
            openWeb({
                url: `http://jiakao.nav.mucang.cn/doExam?type=kaochangkaoshi&kemuStyle=${kemuParamsMap[kemu].kemu}`
            });
        }
    }
    goSpecialExercise(e) {
        const tagId = e.refTarget.getAttribute('data-tagid');
        const questionIds = JSON.parse(e.refTarget.getAttribute('data-questions') || '[]');
        const kemu = e.refTarget.getAttribute('data-kemu');

        trackEvent({
            fragmentName1: '课程版块',
            actionType: '点击',
            actionName: '去练习'
        });

        if (!questionIds?.length) {
            makeToast('该专项没有题目，请联系导师');
            return;
        }

        openWeb({
            url: `http://jiakao.nav.mucang.cn/commonPractice?ids=${questionIds.join(',')}&showPracticeResult=1&practiceMode=私教课课后作业&answerCardTag=${tagId}&kemuStyle=${kemuParamsMap[kemu].kemu}`
        });

    }
    onGoActive(e) {
        const type = +e.refTarget.getAttribute('data-type');
        // 模拟考试
        if (type === 1) {
            this.goExamReal(e);
        }
        // 专项练习
        if (type === 2) {
            this.goSpecialExercise(e);
        }
        // 扫清错题本
        if (type === 3) {
            this.goWrong();
        }
    }
    goLookLesson(e) {
        e.stopPropagation();
        e.preventDefault();

        const relatedLessonId = e.refTarget.getAttribute('data-relatedlessonid');
        const title = e.refTarget.getAttribute('data-title');

        openVipWebView({
            url: `https://laofuzi.kakamobi.com/jkbd-vip/index/plivateTeachSpecial.html?relatedLessonId=${relatedLessonId}&title=${encodeURIComponent(title)}`
        });
    }
}
