.panel-qiaoji-practice {
    min-height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: #FFF3C1;
    padding-bottom: 50px;

    .header-bg {
        height: 245px;
        background: url(../images/<EMAIL>) no-repeat center center/cover;
        position: relative;

        &.kemu4 {
            background: url(../images/<EMAIL>) no-repeat center center/cover;
        }

        .play-video {
            position: absolute;
            width: 150px;
            height: 30px;
            bottom: 10px;
            left: 80px;
        }
    }

    .auth-box {
        width: 345px;
        height: 136px;
        margin: 7px auto 0;
        position: relative;
        background-color: #fff;
        border-radius: 8px;
        padding-top: 13px;

        .auth-title {
            text-align: center;
            height: 20px;
            margin: 0 auto;
            font-weight: bold;
        }

        .auth-list {
            position: absolute;
            width: 100%;
            top: 55px;
            display: flex;
            justify-content: space-around;

            .auth-item {
                text-align: center;

                img {
                    width: 40px;
                    height: 40px;
                }

                .dec-box {
                    margin-top: 7px;
                    font-size: 12px;
                }
            }
        }
    }

    .show-box {
        .show-list {
            .show-item {
                width: 345px;
                margin: 20px auto 0;
                padding: 15px;
                background: linear-gradient(180deg, #FEEB13 0%, #FFF37B 41%, #FFF37F 100%);
                box-shadow: inset 0px 1px 0px 0px #FFFFFF;
                border-radius: 12px;
                overflow: hidden;

                .title {
                    font-weight: bold;
                    font-size: 18px;
                    margin-bottom: 10px;
                    display: flex;
                    align-items: center;

                    .topic-num {
                        width: 42px;
                        height: 20px;
                        background: #FE6710;
                        border-radius: 10px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        color: white;
                        font-size: 13px;
                        margin-left: 10px;
                    }
                }

                .dec {
                    font-size: 12px;
                    line-height: 20px;
                    color: #333;
                }

                .swiper-box {
                    padding-right: 30px;

                    .swiper-container {
                        margin-top: 10px;

                        .swiper-item {
                            position: relative;
                            width: 275px;
                            height: 174px;
                            background: #FFFFFF;
                            border-radius: 8px;
                            padding: 10px;
                            overflow-y: scroll;

                            .question {
                                font-size: 13px;
                                line-height: 18px;

                                .topic-sign {
                                    display: inline-block;
                                    width: 23px;
                                    height: 13px;
                                    margin-right: 2px;
                                    background: url(../images/<EMAIL>) no-repeat center center/cover;
                                }

                                .light {
                                    color: #FF4A40;
                                }
                            }

                            .answer-list {
                                margin-top: 8px;

                                .answer-item {
                                    display: flex;
                                    align-items: center;
                                    font-size: 12px;

                                    .select-option {
                                        margin-right: 7px;
                                        width: 27px;
                                        height: 27px;
                                        background: url(../images/<EMAIL>) no-repeat center center/cover;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                    }

                                    &.right {
                                        color: #00A0F4;

                                        .select-option {
                                            background: url(../images/<EMAIL>) no-repeat center center/cover;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .open-mask {
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 0;
                    right: 10px;
                    background-color: rgba(255, 255, 255, 0.74);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 8px;

                    .buy-btn-box {
                        width: 156px;
                        height: 36px;
                        display: flex;
                        align-items: center;
                        font-size: 14px;
                        color: white;
                        background: #FE6710;
                        border-radius: 18px;
                        padding-left: 2px;

                        &::before {
                            content: '';
                            display: inline-block;
                            width: 28px;
                            height: 28px;
                            background: url(../images/<EMAIL>) no-repeat center center/cover;
                            margin-right: 2px;
                        }
                    }
                }
            }
        }
    }


}

.mask-video {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    overflow: auto;
    z-index: 1001;
    padding: 50px 0px 80px;
    background-color: rgba(0, 0, 0, 0.5);

    .video-box {
        width: 300px;
        margin: 0 auto;
        position: relative;
    }

    .close-video {
        position: absolute;
        right: 0px;
        top: 0;
        transform: translate(28px, -28px);
        width: 30px;
        height: 30px;
        background: url(../images/close.png) no-repeat center center/cover;
    }
}
