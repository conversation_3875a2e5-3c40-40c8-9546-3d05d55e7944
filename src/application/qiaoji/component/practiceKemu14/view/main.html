<import name="style" content="./main" />

<import name="myVideo" content=":component/myVideo/main" />

<div class="panel-qiaoji-practice">
   <div class="header-bg {{URLCommon.kemu === 4?'kemu4':'kemu1'}}">
       <div class="play-video" sp-on:click="goPlayVideo"></div>
   </div>
   <div class="auth-box">
       <div class="auth-title">- 尊享{{self.authList.length}}大权益 -</div>
       <div class="auth-list">
            <sp:each for="self.authList">
                <div class="auth-item" sp-on:click="goAuth"  data-uniqkey="{{$value.uniqkey}}">
                    <img src="{{$value.icon}}" alt="">
                    <div class="dec-box">
                        <div class="dec">{{$value.dec}}</div>
                    </div>
                </div>
            </sp:each>
       </div>
   </div>
   <div class="show-box">
       <div class="show-list">
           <sp:each for="state.practiceData">
               <div class="show-item">
                   <div class="title">{{$value.title}} <span class="topic-num">{{$value.mount}}题</span></div>
                   <div class="dec">{{#$value.dec}}</div>
                   <div class="swiper-box">
                        <div class="swiper-container" skip-attribute="class|style" ref="swiper">
                            <div class="swiper-wrapper" skip-attribute="class|style">
                                <sp:each for="$value.topicList" value="$item"> 
                                    <div class="swiper-slide">
                                        <div class="swiper-item">
                                            <div class="question"><span class="topic-sign"></span>{{#$item.question}}</div>
                                            <div class="answer-list">
                                                <sp:each for="$item.answerList" value="$sitem">
                                                    <div class="answer-item {{$sitem.isCorrect?'right':''}}">
                                                    <span class="select-option">{{$sitem.isCorrect?'':$sitem.select}}</span>{{$sitem.selectValue}}
                                                    </div>
                                                </sp:each>
                                            </div>
                                        </div>
                                        <sp:if value="$index == 3">
                                            <div class="open-mask">
                                                <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                                                    解锁全部速记口诀
                                                </div>
                                            </div>
                                        </sp:if>
                                    </div>
                                    
                                </sp:each>
                            </div>
                        </div>
                    </div>
                </div>
            </sp:each>
       </div>
   </div>
  
</div>
<div class="mask-video {{state.showVideo?'':'hide'}}">
    <div class="video-box">
        <com:myVideo src="{{self.videoIntroduce}}" showVideo="{{true}}"/>
        <div class="close-video" sp-on:click="closeVideo"></div>
    </div>
</div>
