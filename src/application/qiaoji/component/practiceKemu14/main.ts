/*
 * main
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 16/3/24
 */
import { CarType, URLCommon } from ':common/env';
import { promiseList } from ':common/features/promise';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import MyVideo from ':component/myVideo/main';
import practiceData from './data';
import Swiper from 'swiper';
import 'swiper/swiper-bundle.css';
import { ELDER_QIAOJI } from ':common/navigate';
import { openVipWebView, openWeb } from ':common/core';

interface State {
    showVideo: boolean,
    practiceData: any
}
interface Props {
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    Swiper: any
    declare children: {
        myVideo: MyVideo
    }
    get videoIntroduce() {
        return ELDER_QIAOJI;
    }
    get authList() {
        if (URLCommon.isScore12) {
            return [
                {
                    icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/15/10/b1bda01414134288a208342d51b9e825.png',
                    uniqkey: promiseList.jj500t,
                    dec: URLCommon.tiku === CarType.MOTO ? '精简200题' : '精简600题'
                },
                {
                    icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/15/10/54d37cf5d31d41b78f70fcc9e37fdd53.png',
                    uniqkey: promiseList.kqmj,
                    dec: '考前秘卷'
                },
                {
                    icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/15/10/1001e0c9aa9842f0bf97b858715215b6.png',
                    uniqkey: promiseList.sjkj,
                    dec: '速记口诀'
                }
            ];
        }

        return [
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/15/10/b1bda01414134288a208342d51b9e825.png',
                uniqkey: promiseList.jj500t,
                dec: URLCommon.tiku === CarType.MOTO ? '精简200题' : '精简600题'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/15/10/54d37cf5d31d41b78f70fcc9e37fdd53.png',
                uniqkey: promiseList.kqmj,
                dec: '考前秘卷'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/15/10/1001e0c9aa9842f0bf97b858715215b6.png',
                uniqkey: promiseList.sjkj,
                dec: '速记口诀'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/15/10/5252df1eb15c46b5a703884caf8a43ed.png',
                uniqkey: promiseList.bgbc,
                dec: '不过补偿'
            }
        ];
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            practiceData,
            showVideo: false
        };
        this.props = {
        };

    }

    willReceiveProps() {
        return true;
    }
    didMount() {
        const $dom = this.getDOMNode().swiper as HTMLElement;
        this.Swiper = new Swiper($dom, {
            loop: false
        });
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    goPlayVideo() {
        this.setState({
            showVideo: true
        });
        this.children.myVideo.play();
    }
    closeVideo() {
        this.children.myVideo.pause();
        this.setState({
            showVideo: false
        });
    }
}
