<import name="style" content="./main" />
<import name="SendVipEnter" content=":component/sendVipEnter/main" />
<import name="myVideo" content=":component/myVideo/main" />

<div class="panel-qiaoji">
    <div class="ipad-box">
        <div class="phone-box">
            <img
                class="bg-img"
                src="{{URLCommon.kemu == 1?'http://exam-room.mc-cdn.cn/exam-room/2022/06/14/19/5323ad7a8ecd44b2892b9edd41f4a849.png':'http://exam-room.mc-cdn.cn/exam-room/2022/06/14/19/7e707e916ba04a039c9071d2b3fb1791.png'}}"
            />
            <sp:if value="URLCommon.isNormal">
                <div class="promision">
                    <div class="title">
                        —— 尊享{{self.authList.length}}大权益 ——
                    </div>
                    <div class="icon-list">
                        <sp:each for="self.authList">
                            <div
                                class="icon"
                                sp-on:click="goAuth"
                                data-uniqkey="{{$value.uniqkey}}"
                            >
                                <img src="{{$value.icon}}" />
                                <div class="dec-box">
                                    <span>{{$value.dec}}</span>
                                </div>
                            </div>
                        </sp:each>
                    </div>
                </div>
            </sp:if>
        </div>
    </div>
      <com:SendVipEnter name="right"
          entranceCode="{{URLCommon.kemu === 1?'ke1_qiaoji_right':'ke4_qiaoji_right'}}" position="right" />
</div>
