/*
 * main
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 16/3/24
 */
import { CarType, URLCommon } from ':common/env';
import { promiseList } from ':common/features/promise';
import { getActivityTime } from ':store/chores';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
}
interface Props {
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
  
    get authList() {
        if (URLCommon.isScore12) {
            return [
                {
                    icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/06/27/18/aa8bde86dad54f6a84cee86854d2b729.png',
                    uniqkey: promiseList.jj500t,
                    dec: URLCommon.tiku === CarType.MOTO ? '精简200题' : '精简600题'
                },
                {
                    icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/06/27/18/c182331f54464b14864a3536d799aeb2.png',
                    uniqkey: promiseList.zskcmn,
                    dec: '真实考场模拟'
                },
                {
                    icon: URLCommon.tiku === CarType.MOTO ? 'http://exam-room.mc-cdn.cn/exam-room/2022/06/27/18/bfd240491df04993a99a9d431f80a304.png' : 'http://exam-room.mc-cdn.cn/exam-room/2022/06/27/18/e117dbe1ce294dfd979350eec7d924a3.png',
                    uniqkey: promiseList.kq3tj,
                    dec: URLCommon.tiku === CarType.MOTO ? '考前两套卷' : '考前三套卷'
                }
            ];
        }

        const list = [
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/06/27/18/aa8bde86dad54f6a84cee86854d2b729.png',
                uniqkey: promiseList.jj500t,
                dec: (() => {
                    let text = '精简题库';
                    switch (URLCommon.tiku) {
                        case CarType.CAR:
                            text = '精简500题';
                            break;
                        case CarType.MOTO:
                            text = '精简题库';
                            break;
                        case CarType.TRUCK:
                            text = '精简600题';
                            break;
                        case CarType.BUS:
                            text = '精简600题';
                            break;
                        default:
                            break;
                    }
                    return text;
                })()
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/06/27/18/c182331f54464b14864a3536d799aeb2.png',
                uniqkey: promiseList.zskcmn,
                dec: '真实考场模拟'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/06/28/10/d924d48bafd24466a6b16ef5863101a6.png',
                uniqkey: promiseList.kqmj,
                dec: '考前秘卷'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/06/28/10/1ff4997d50a6471cb0636261eb604110.png',
                uniqkey: promiseList.bgbc,
                dec: '不过补偿'
            }
        ];

        if (URLCommon.isZigezheng) {
            list.pop();
        }

        return list;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
        };
        this.props = {
        };

    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
    didMount() {
        this.getActivityInfo();
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    async getActivityInfo() {
        const retData = await getActivityTime();
        this.setState({
            activityInfo: retData
        });
    }
}
