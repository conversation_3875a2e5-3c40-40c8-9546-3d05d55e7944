/*
 * ------------------------------------------------------------------
 * 智能练题购买弹窗
 * ------------------------------------------------------------------
 */

import { setPageName, URLCommon, URLParams } from ':common/env';
import jump, { navigateTo, reload } from ':common/features/jump';
import { kq2hList } from ':store/chores';
import BuyDialog, { buyDialogCloseType } from ':component/buyDialog/main';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { trackGoPay, trackPageLoad } from ':common/stat';
import { openVipWebView, openWeb, setStatusBarTheme } from ':common/core';
import { PRACTICE_ALL } from ':common/navigate';

interface State {
    showBuyDialog: boolean,
    fragmentName1: string,
    hasAllPermission: boolean,
    listData: any[]
}

export default class extends Application<State> {
    declare children: {
        BuyDialog: BuyDialog
    }
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            showBuyDialog: false,
            fragmentName1: '',
            hasAllPermission: false,
            listData: []
        };

    }
    didMount() {
        setStatusBarTheme('dark');
        setPageName('考前2小时页');

        trackPageLoad();

        this.getKq2hList();
    }
    getKq2hList() {
        kq2hList().then(data => {
            this.setState({
                hasAllPermission: data.hasAllPermission,
                listData: data.itemList
            });
        });
    }
    closePayModal = (closeType) => {
        this.setState({
            showBuyDialog: false
        });
        if (closeType === buyDialogCloseType.BOUGHT) {
            setTimeout(() => {
                reload();
            }, 2000);
        }

    }
    goQuestion(e) {
        const { listData } = this.state;
        const hasPermission = e.refTarget.getAttribute('data-haspermission');
        const id = e.refTarget.getAttribute('data-id');
        const index = e.refTarget.getAttribute('data-index');
        const item = listData[index];

        if (hasPermission) {
            if (item.gotoSkillPractice) {
                // openWeb({
                //     url: `http://jiakao.nav.mucang.cn/topic-dispersed-training-practice?tagId=${item.skillTagId}&kemu=kemu${URLCommon.kemu}`
                // });

                openWeb({
                    url: `http://jiakao.nav.mucang.cn/commonPractice?ids=${item.questionIdList.join(',')}&showPracticeResult=1&practiceMode=考前2小时&answerCardTag=${item.skillTagId}&kemu=kemu${URLCommon.kemu}`
                });
            } else {
                openVipWebView({
                    url: PRACTICE_ALL + '?id=' + id + '&index=' + index
                });
            }

        } else {
            this.openPayModal(e);
        }
    }
    openPayModal(e) {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        this.setState({
            showBuyDialog: true,
            fragmentName1
        }, () => {
            this.children.BuyDialog.setPageInfo();
        });

    }
}
