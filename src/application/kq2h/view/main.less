.page-kq2h {
    padding-bottom: calc(~"10px + constant(safe-area-inset-bottom)"
        ) !important;
    /* 兼容 iOS < 11.2 */
    padding-bottom: calc(~"10px + env(safe-area-inset-bottom)");

    .hd {
        height: 140px;
        background: url(../images/bg.png) no-repeat;
        background-size: 100%;
        flex-shrink: 0;
    }

    .con-ke1 {
        background-color: #fff;
        flex: 1;
        overflow-y: auto;
        ul {
            display: flex;
            align-items: center;
            //justify-content: space-around;
            flex-wrap: wrap;
        }

        li {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 30px;
            width: 33.33%;
        }

        .img {
            width: 35px;
            height: 35px;
        }

        .p1 {
            width: 35px;
            height: 35px;
        }

        .p2 {
            color: #333333;
            font-size: 14px;
            line-height: 20px;
            padding-top: 5px;
        }

    }

    .open-vip-btn {
        width: 345px;
        padding: 12px 0;
        margin: 0 auto;

        p {
            height: 44px;
            line-height: 44px;
            background-color: #28231D;
            color: #EFD893;
            font-size: 17px;
            text-align: center;
            border-radius: 44px;
        }
    }

    .showLookAllBuy-mask {
        position: fixed;
        z-index: 100;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
    }



    .showLookAllBuy-box {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100;
        max-height: 100%;
        overflow-y: auto;
    }

}
