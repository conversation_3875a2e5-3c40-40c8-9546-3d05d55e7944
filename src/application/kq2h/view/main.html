<import name="style" content="./main" />
<import
    name="BuyDialog"
    content=":component/buyDialog/main"
/>
<import name="header" content=":component/header/main" />
<div class="page-container page-kq2h">
     <com:header
            title="考前2小时"
            theme="white"
            endTheme="white"
     >
        <div sp:slot="right"></div>
    </com:header>
   <div class="hd"></div>
    <div class="con-ke1">
        <ul>
            <sp:each for="state.listData" value="item" index="i">
                <li class="li-item" data-id="{{item.id}}" data-index="{{i}}" data-haspermission="{{item.hasPermission?'true':''}}" sp-on:click="goQuestion" data-fragment="技巧图标">
                    <img class="img" src="{{item.icon}}">
                    <p class="p2">{{item.name}}</p>
                </li>
            </sp:each>
        </ul>
    </div>

    <sp:if value="!state.hasAllPermission">
        <div class="open-vip-btn" sp-on:click="openPayModal" data-fragment="底部吸底按钮">
            <p ref="pay">解锁全部技巧</p>
        </div>
    </sp:if>


    <sp:if value="state.showBuyDialog">
        <div class="showLookAllBuy-mask">
            <div class="showLookAllBuy-box">
                <com:BuyDialog fragmentName1="{{state.fragmentName1}}" type="component" title1="考前两小时" subTitle1="小车答题技巧" close="{{self.closePayModal}}}"/>
            </div>
        </div>
    </sp:if>
</div>  
           
