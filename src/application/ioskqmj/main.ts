/*
 * ------------------------------------------------------------------
 * ios考前密卷
 * ------------------------------------------------------------------
 */

import { setPageName, URLParams } from ':common/env';

import { Application } from '@simplex/simple-core';
import View from './view/main.html';

export default class extends Application {
    $constructor() {
        const fromPage = URLParams.fromPage || '考前秘卷弹窗页';
        const fragmentName1 = URLParams.fragmentName1 || '未知片段';

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        setPageName(fromPage);

        this.state = {
            fragmentName1,
            title1: '考前秘卷',
            subTitle1: '高频考点，考前巩固高效冲刺'
        };

    }
}
