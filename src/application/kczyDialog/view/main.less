html,
body {
    height: 100vh;
    overflow: hidden;
}
.container {
    position: relative;
    background: #fff;
    border-radius: 8px 8px 0px 0px;
    padding: 15px 15px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-bottom: calc(
        ~"1px + constant(safe-area-inset-bottom)/2"
    ) !important;
    /* 兼容 iOS < 11.2 */
    padding-bottom: calc(~"1px + env(safe-area-inset-bottom)/2");
    .big-title {
        font-size: 18px;
        font-family: PingFang SC, PingFang SC-Medium;
        font-weight: 700;
        text-align: center;
        color: #333333;
        line-height: 25px;
        margin-bottom: 10px;
    }
    .close {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 25px;
        height: 25px;
        background: url(../images/ic_close.png) no-repeat center;
        background-size: cover;
    }
    .scroll-box {
        flex: 1;
        overflow-y: auto;
        .item {
            margin-bottom: 15px;
            .rule-title {
                font-size: 15px;
                font-family: PingFang SC, PingFang SC-Medium;
                font-weight: 700;
                color: #333333;
                line-height: 21px;
                margin-bottom: 7px;
            }
            .rule-desc {
                font-size: 14px;
                font-family: PingFang SC, PingFang SC-Regular;
                color: #333333;
                line-height: 20px;
                margin-bottom: 5px;
                &:last-child {
                    margin-bottom: 0px;
                }
                .item-color {
                    color: #ff4a40;
                }
            }
        }
    }
}
