import { webClose } from ':common/core';
import { showSetting } from ':common/features/embeded';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
interface State {}
export default class extends Application<State> {
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {};
    }
    didMount() {
        showSetting({
            iosH: 422,
            androidH: 422
        });
    }
    closeDialog() {
        console.log('dianjileha');
        webClose();
    }
}
