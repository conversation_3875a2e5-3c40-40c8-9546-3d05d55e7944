<import name="style" content="./main" module="S" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import
    name="onlineDialog"
    content=":application/kqfd/component/onlineDialog/main"
/>
<div class=":zxgk-index-dialog">
    <div class=":head-bg">
        <div class=":index-top-time">
            <sp:if value="{{state.lessonSchedule.length<=0}}">
                暂无直播
                <sp:else />
                直播时间:{{self.newSchelTime}}
            </sp:if>
        </div>
    </div>

    <sp:if value="{{!props.hasPromission}}">
        <div class=":img1-box">
            <div class=":img1-t"></div>
            <div class=":img1-c"></div>
        </div>
    </sp:if>
    <div class=":img2-box">
        <div class=":img2-t"></div>
        <div class=":content">
            <div class=":step-list">
                <div class=":step-item">
                    <div class=":icon"></div>
                    <div class=":title">精品短课</div>
                    <div class=":dec">前置熟悉</div>
                    <div class=":dec1">夯实基础</div>
                </div>
                <div class=":step-item">
                    <div class=":icon"></div>
                    <div class=":title">考点直播精讲</div>
                    <div class=":dec">直播讲解</div>
                    <div class=":dec1">针对答疑</div>
                </div>
                <div class=":step-item">
                    <div class=":icon"></div>
                    <div class=":title">巩固提升</div>
                    <div class=":dec">辅导资料</div>
                    <div class=":dec1">课后巩固</div>
                </div>
            </div>

            <div class=":step1-box">
                <div class=":step1-title"></div>
                <div class=":step1-content">
                    <sp:each for="state.lessonList">
                        <div class=":step1-item" data-lessonid="{{$value.id}}" sp-on:click="onPay">
                            <div
                                class=":img"
                                style="background-image: url({{Tools.calcImg($value.image)}});"
                            ></div>
                            <div class=":dec">{{$value.title}}</div>
                        </div>
                    </sp:each>
                </div>
            </div>

            <div class=":step2-box">
                <div class=":step2-title"></div>
                <div class=":step2-title-dec">
                    为了避免重复听课，学员根据自己时间选择一天听课即可
                </div>

                <sp:each for="state.lessonSchedule">
                    <div class=":zhibo-box">
                        <div class=":time-title">{{$value.title}}</div>
                        <sp:each
                            for="$value.liveDataList"
                            value="$sonitem"
                            index="$sonIndex"
                        >
                            <div
                                class=":zhibo-item"
                                data-liveSessionId="{{$sonitem.liveSessionId}}"
                                data-fragment="直播时间表"
                                sp-on:click="onPay"
                            >
                                <div class=":title">
                                    <span class=":num"
                                        >第{{Tools.numZh($sonIndex+1)}}场</span
                                    >
                                    <span class=":right-day">
                                        <sp:if
                                            value="{{Tools.dateFormat($sonitem.beginTime,
                                                                        'MM月dd日')==Tools.dateFormat(new Date(),
                                                                        'MM月dd日')}}"
                                        >
                                            今天
                                            <sp:else />
                                            {{Tools.dateFormat($sonitem.endTime,
                                            'MM月dd日')}}
                                        </sp:if>
                                        <span class=":right-time">
                                            {{Tools.dateFormat($sonitem.beginTime,
                                            'HH:mm')}}-{{Tools.dateFormat($sonitem.endTime,
                                            'HH:mm')}}
                                        </span>
                                    </span>

                                    <sp:if value="{{$sonitem.status==1}}">
                                        <span class=":zhizho1">直播中</span>
                                    </sp:if>
                                    <sp:if value="{{$sonitem.status==2}}">
                                        <sp:if
                                            value="{{$sonitem.subscribeStatus==1}}"
                                        >
                                            <span class=":zhizho2">
                                                已预约
                                            </span>
                                            <sp:else />
                                            <span
                                                class=":zhizho2"
                                                data-liveSessionId="{{$sonitem.liveSessionId}}"
                                                data-index="{{$index}}"
                                                data-sonIndex="{{$sonIndex}}"
                                                sp-on:click="makeappointment"
                                            >
                                                预约直播
                                            </span>
                                        </sp:if>
                                    </sp:if>
                                    <sp:if value="{{$sonitem.status==3}}">
                                        <span class=":zhizho3">看回放</span>
                                    </sp:if>
                                </div>
                                <div class=":zhibo-item-content">
                                    {{$sonitem.liveContent}}
                                </div>
                            </div>
                        </sp:each>
                    </div>
                </sp:each>

                <sp:if value="state.lessonSchedule.length">
                    <div class=":look-more-zhibo" sp-on:click="onPay">
                        查看历史直播记录>
                    </div>
                </sp:if>
            </div>

            <div class=":step3-box">
                <div class=":step3-title"></div>
                <div class=":step3-content">
                    <div class=":step3-item" data-fragment="资料包" sp-on:click="onPay">
                        <div class=":l">核心知识点<br />资料包</div>
                        <div class=":r"></div>
                    </div>
                    <div class=":step3-item" data-fragment="资料包" sp-on:click="onPay">
                        <div class=":l">答题技巧<br />资料包</div>
                        <div class=":r"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class=":zhibo-common-question">
        <div class=":common-question-title"></div>
        <com:commonQuestion type="13" />
    </div>
    <com:onlineDialog></com:onlineDialog>
</div>
