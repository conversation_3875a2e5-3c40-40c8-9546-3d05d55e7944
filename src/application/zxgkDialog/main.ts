/*
 * ------------------------------------------------------------------
 * 专项攻克直播课
 * ------------------------------------------------------------------
 */

import { CarType, KemuType, PayType, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import IndexPage from ':application/zxgk/component/index/main';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { iosPay } from ':common/features/ios_pay';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackEvent, trackGoPay, trackPageLoad } from ':common/stat';
import { onWebBack } from ':common/features/persuade';
import Header from ':component/header/main';
import { checkReaded } from ':common/features/agreement';
import { getGoodsConfig } from ':store/kqfd';
import { getPermission } from ':store/chores';
import { getAuthToken, setStatusBarTheme } from ':common/core';
import { dateFormat } from ':common/utils';
import { login } from ':common/features/login';
import Loading from ':application/buyed/components/Loading/main';
import { onPageShow } from ':common/features/page_status_switch';
import { Coupon, goodsInfoWithCoupon } from ':common/features/coupon';

URLCommon.kemu = +URLParams.kemu || +URLParams.kemuStyle;

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    hasPromission: boolean,
    expireTimeString: string,
    prevScrollTop: number
}
let timer;

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog,
        header: Header,
        indexPage: IndexPage,
        loading: Loading

    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey] || {};
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }

    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            goodsInfoPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            hasPromission: false,
            expireTimeString: '',
            prevScrollTop: 0
        };

    }
    async didMount() {
        setPageName('专项攻克付费直播主页');

        const authToken = await getAuthToken();
        if (authToken) {
            await this.hasPromissionMethod();
        }
        // 页面进出时长打点
        trackPageLoad({ payStatus: this.state.hasPromission ? '1' : '2' });
        // 如果有权限，就不请求商品信息了
        if (this.state.hasPromission) {
            return;
        }

        // 获取返回的商品groupkey
        await this.getGroupKey();
        await this.getGoodInfo();
        
    }
 
    async hasPromissionMethod() {
        let permission = '';

        switch (URLCommon.tiku) {
            case CarType.CAR:
                permission = +URLCommon.kemu === 1 ? 'zxgkLiveK1' : 'zxgkLiveK4';
                break;
            default:
                break;
        }
        if (!permission) {
            return;
        }
        const checkPermission: any = await getPermission(permission);
    
        const expireTimeString = dateFormat(checkPermission.validEndTime, 'yyyy.MM.dd');

        this.setState({
            hasPromission: checkPermission.status === 1,
            expireTimeString
        });
    }
  
    async getGroupKey() {
        const groupKyConfig: any = await getGoodsConfig({
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            lessonType: 2
        });
        const goodsInfoPool: GoodsInfo[] = [];
        goodsInfoPool.push({
            groupKey: groupKyConfig.singleGoodsKey
        } as GoodsInfo);
        goodsInfoPool.push({
            groupKey: groupKyConfig.allKey
        } as GoodsInfo);

        this.state.goodsInfoPool = goodsInfoPool;
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });
        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }

                // 商品未购买才push,全科可升级也能买
                if (goodInfo.groupKey === GroupKey.ChannelKemuAll && goodInfo.upgrade) {
                    goodInfo.name = '升级' + goodInfo.name;
                    newGoodsPool.push(goodInfo);
                } else if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });
            this.setState({
                goodsInfoPool: newGoodsPool
            });
        });
    }
}
