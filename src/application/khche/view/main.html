<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="compareGood" content=":component/compareGood/main" />
<import name="compareGood1" content=":component/compareGood/main" />
<import name="compareGood2" content=":component/compareGood/main" />
<import name="compareGood3" content=":component/compareGood/main" />
<import name="buyDialogOrderSign" content=":component/buyDialogOrderSign/main" />

<import
    name="BusTruck14"
    content=":application/khche/component/BusTruck14/main"
/>
<import
    name="TruckKemuAll"
    content=":application/khche/component/TruckKemuAll/main"
/>
<import
    name="BusKemuAll"
    content=":application/khche/component/BusKemuAll/main"
/>
<import
    name="BusTruckShort"
    content=":application/khche/component/BusTruckShort/main"
/>

<import
    name="BusTruck1and4"
    content=":application/khche/component/BusTruck1and4/main"
/>
<div class="page-container page-khc page-kc page-hc">
    <div class="page-header">
        <com:header
            title="{{state.prevScrollTop > 200 ?self.nowGoodInfo.name:' '}}"
            theme="black"
            endTheme="black"
            qaKey="{{self.qaKey}}"
            scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}"
        />
    </div>
    <div class="body-panel-box">
        <div class="body-panel" sp-on:scroll="pageScroll">
            <!-- 科目1或4 -->
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.KcChannelKe1 || self.nowGoodInfo.groupKey === GroupKey.KcChannelKe4 || self.nowGoodInfo.groupKey === GroupKey.HcChannelKe1 ||self.nowGoodInfo.groupKey === GroupKey.HcChannelKe4 || self.nowGoodInfo.groupKey === GroupKey.KcChannelKe1D3 || self.nowGoodInfo.groupKey === GroupKey.KcChannelKe4D3 || self.nowGoodInfo.groupKey === GroupKey.HcChannelKe1D3 || self.nowGoodInfo.groupKey === GroupKey.HcChannelKe4D3?'show':'hide'}}"
            >
                <com:BusTruck14
                    goodsList="{{state.goodsInfoPool}}"
                    goodsInfo="{{self.nowGoodInfo}}"
                    kemu="{{URLCommon.kemu}}"
                    currentIndex="{{state.tabIndex}}"
                    headerAbTestType="{{self.headerAbtext}}"
                    labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    couponPool="{{state.couponPool}}"
                    payPrice="{{self.showPrice}}"
                    tabChange="{{self.tabChangeCall}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                >
                    <com:compareGood
                        key="compareGood"
                        name="compareGood"
                        img="https://web-resource.mc-cdn.cn/web/vip/1.png"
                    >
                        <div class="buy-box">
                            <sp:each for="state.goodsInfoPool">
                                <div
                                    class="buy-btn"
                                    sp-on:click="payBtnCall"
                                    data-fragment="VIP对比模块"
                                    data-payindex="{{$index}}"
                                >
                                    {{#self.buyBtnTxt[$value.groupKey] ||
                                    '立即购买'}}
                                </div>
                            </sp:each>
                        </div>
                    </com:compareGood>
                </com:BusTruck14>
            </div>
            <!-- 科目1和4 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.HcChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.HcChannelKe1Ke4Group).showPage)||(self.getGroupKeyInfo(GroupKey.KcChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.KcChannelKe1Ke4Group).showPage)"
            >
                <div
                    class="{{self.nowGoodInfo.groupKey === GroupKey.HcChannelKe1Ke4Group||self.nowGoodInfo.groupKey === GroupKey.KcChannelKe1Ke4Group?'show':'hide'}}"
                >
                    <com:BusTruck1and4
                        goodsInfo="{{self.nowGoodInfo}}"
                        prevScrollTop="{{state.prevScrollTop}}"
                        currentIndex="{{state.tabIndex}}"
                        goodsList="{{state.goodsInfoPool}}"
                        labelPool="{{state.labelPool}}"
                        comparePricePool="{{state.comparePricePool}}"
                        couponPool="{{state.couponPool}}"
                        payPrice="{{self.showPrice}}"
                        groupKey="{{self.nowGoodInfo.groupKey}}"
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}"
                        tabChange="{{self.tabChangeCall}}"
                    >
                        <com:compareGood1
                            key="compareGood1"
                            name="compareGood1"
                            img="https://web-resource.mc-cdn.cn/web/vip/1.png"
                        >
                            <div class="buy-box">
                                <sp:each for="state.goodsInfoPool">
                                    <div
                                        class="buy-btn"
                                        sp-on:click="payBtnCall"
                                        data-fragment="VIP对比模块"
                                        data-payindex="{{$index}}"
                                    >
                                        {{#self.buyBtnTxt[$value.groupKey] ||
                                        '立即购买'}}
                                    </div>
                                </sp:each>
                            </div>
                        </com:compareGood1>
                    </com:BusTruck1and4>
                </div>
            </sp:if>
            <!-- 短时提分 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.KcChannelKe1Short).payPrice && self.getGroupKeyInfo(GroupKey.KcChannelKe1Short).showPage) || (self.getGroupKeyInfo(GroupKey.KcChannelKe4Short).payPrice && self.getGroupKeyInfo(GroupKey.KcChannelKe4Short).showPage) || (self.getGroupKeyInfo(GroupKey.HcChannelKe4Short).payPrice && self.getGroupKeyInfo(GroupKey.HcChannelKe4Short).showPage)"
            >
                <div
                    class="{{self.nowGoodInfo.groupKey === GroupKey.KcChannelKe1Short || self.nowGoodInfo.groupKey === GroupKey.KcChannelKe4Short || self.nowGoodInfo.groupKey === GroupKey.HcChannelKe4Short?'show':'hide'}}"
                >
                    <com:BusTruckShort
                        payPrice="{{self.showPrice}}"
                        labelPool="{{state.labelPool}}"
                        groupKey="{{self.nowGoodInfo.groupKey}}"
                        isHubei="{{state.isHubei}}"
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}"
                    />
                </div>
            </sp:if>

            <!-- 货车全科 -->
            <sp:if
                value="self.getGroupKeyInfo(GroupKey.HcChannelKemuAll).payPrice && self.getGroupKeyInfo(GroupKey.HcChannelKemuAll).showPage"
            >
                <div
                    class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.HcChannelKemuAll?'show':'hide'}}"
                >
                    <com:TruckKemuAll
                        goodsInfo="{{self.getGroupKeyInfo(GroupKey.HcChannelKemuAll)}}"
                        goodsList="{{state.goodsInfoPool}}"
                        tabChange="{{self.tabChangeCall}}"
                        currentIndex="{{state.tabIndex}}"
                        comparePricePool="{{state.comparePricePool}}"
                        labelPool="{{state.labelPool}}"
                        payPrice="{{self.showPrice}}"
                        payBtnCall="{{self.payBtnCall}}"
                        goAuth="{{self.goAuth}}"
                    >
                        <com:compareGood2
                            key="compareGood2"
                            name="compareGood2"
                            img="https://web-resource.mc-cdn.cn/web/vip/1.png"
                        >
                            <div class="buy-box">
                                <sp:each for="state.goodsInfoPool">
                                    <div
                                        class="buy-btn"
                                        sp-on:click="payBtnCall"
                                        data-fragment="VIP对比模块"
                                        data-payindex="{{$index}}"
                                    >
                                        {{#self.buyBtnTxt[$value.groupKey] ||
                                        '立即购买'}}
                                    </div>
                                </sp:each>
                            </div>
                        </com:compareGood2>
                    </com:TruckKemuAll>
                </div>
            </sp:if>

            <!-- 客车全科 -->
            <sp:if
                value="self.getGroupKeyInfo(GroupKey.KcChannelKemuAll).payPrice && self.getGroupKeyInfo(GroupKey.KcChannelKemuAll).showPage"
            >
                <div
                    class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.KcChannelKemuAll?'show':'hide'}}"
                >
                    <com:BusKemuAll
                        goodsInfo="{{self.getGroupKeyInfo(GroupKey.KcChannelKemuAll)}}"
                        goodsList="{{state.goodsInfoPool}}"
                        tabChange="{{self.tabChangeCall}}"
                        currentIndex="{{state.tabIndex}}"
                        comparePricePool="{{state.comparePricePool}}"
                        labelPool="{{state.labelPool}}"
                        payPrice="{{self.showPrice}}"
                        payBtnCall="{{self.payBtnCall}}"
                        goAuth="{{self.goAuth}}"
                    >
                        <com:compareGood3
                            key="compareGood3"
                            name="compareGood3"
                            img="https://web-resource.mc-cdn.cn/web/vip/1.png"
                        >
                            <div class="buy-box">
                                <sp:each for="state.goodsInfoPool">
                                    <div
                                        class="buy-btn"
                                        sp-on:click="payBtnCall"
                                        data-fragment="VIP对比模块"
                                        data-payindex="{{$index}}"
                                    >
                                        {{#self.buyBtnTxt[$value.groupKey] ||
                                        '立即购买'}}
                                    </div>
                                </sp:each>
                            </div>
                        </com:compareGood3>
                    </com:BusKemuAll>
                </div>
            </sp:if>
        </div>
    </div>
    <div class="{{state.showFooter ? '': 'hide'}}">
        <!-- 当打开选择商品的时候需要隐藏选择切换商品，否则样式不对 -->
        <div
            class="footer {{ !state.openSelectGoods && state.goodsInfoPool.length > 1?'':'hide'}}"
        >
            <com:bottomTabs
                tabIndex="{{state.tabIndex}}"
                labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}"
                goodsList="{{state.goodsInfoPool}}"
                tabChange="{{self.tabChangeCall}}"
            />
        </div>
        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
            </div>
        </com:buyButton>
    </div>
    <!-- 所有商品信息加载完成才能加载这个组件，内部有根据商品判断 -->
    <sp:if value="state.showSignModal">
        <com:buyDialogOrderSign goodsInfoPool="{{state.goodsInfoPool}}" labelPool="{{state.labelPool}}"
            comparePricePool="{{state.comparePricePool}}" couponPool="{{state.couponPool}}"
            close="{{self.closeSignModal}}" />
    </sp:if>
    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
</div>
