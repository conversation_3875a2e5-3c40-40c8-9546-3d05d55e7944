<import name="style" content="./main" />

<import name="PriceTag" content=":component/priceTag/main" />
<import
    name="pageHeader"
    content=":application/car/component/page-header/main"
/>
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="Count" content=":component/count/main" />
<import name="giftText" content=":component/giftText/main" />
<import name="cards" content=":application/car/component/cards/main" />
<import name="buchangTips" content=":component/buchangTips/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />

<div class="panel-truckkemuall">
    <com:pageHeader
        type="truck-kemuall"
        goAuth="{{props.goAuth}}"
        uiConfig="{{self.headUiConfig}}"
        goodsInfo="{{props.goodsInfo}}"
        upgrade="{{props.goodsInfo.upgrade}}"
        iconList="{{self.iconList}}"
        buyPrice="{{props.comparePricePool[props.goodsInfo.groupKey].allPrice}}"
        give="{{state.give}}"
    />
    <com:buchangTips />
    <div class="ipad-box">
        <div class="phone-box">
            <!-- 有切换才有这个 -->
            <sp:if value="{{props.tabChange}}">
                <div class="card-box">
                    <com:cards
                        comparePricePool="{{props.comparePricePool}}"
                        goodsList="{{props.goodsList}}"
                        tabChange="{{props.tabChange}}"
                        tabIndex="{{props.currentIndex}}"
                    />
                </div>
            </sp:if>
            <sp:if
                value="props.goodsInfo.inActivity && !props.goodsInfo.upgrade"
            >
                <div
                    class="activity-buy-btn"
                    sp-on:click="pay"
                    data-fragment="主图"
                >
                    <div class="div1">
                        <p class="p1">
                            <span class="sp1"></span>
                            <span class="sp2"
                                >比分开买节省{{props.comparePricePool[props.goodsInfo.groupKey].diffPrice}}元</span
                            >
                        </p>
                        <p class="p2">
                            <span class="sp3">
                                日常价
                                <i class="i1">&nbsp; ¥ &nbsp;</i>
                                <b class="b1"
                                    >{{props.goodsInfo.inActivity.preDiscountPrice}}</b
                                >
                            </span>
                            <span class="sp4">
                                限时折后
                                <i class="i2">&nbsp; ¥ &nbsp;</i>
                                <b class="b2">{{props.payPrice || '--'}}</b>
                            </span>
                        </p>
                    </div>
                    <div class="div2">
                        <div class="p3">限时折扣</div>
                        <div class="p4">
                            <com:Count
                                name="Count"
                                startTime="{{props.goodsInfo.inActivity.discountStartTime}}"
                                endTime="{{props.goodsInfo.inActivity.discountEndTime}}"
                            ></com:Count>
                        </div>
                    </div>
                </div>
                <sp:else />
                <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                    <div class="buy-btn">
                        确认协议并支付 ¥{{props.payPrice || props.payPrice ===
                        0?props.payPrice : '--'}}
                    </div>
                    <!-- <com:giftText /> -->
                    <com:PriceTag
                        class="noScale"
                        goodsInfo="{{props.goodsInfo}}"
                        comparePriceMap="{{props.comparePricePool}}"
                        labelMap="{{props.labelPool}}"
                    />
                </div>
            </sp:if>

            <div class="protocol-box">
                <com:middleProtocol groupKey="{{props.groupKey}}" couponPool="{{props.couponPool}}" />
            </div>

            <div class="kemuall-step kemuall-1">
                <img
                    src="https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/img-title-zbkbxbd.png"
                />
            </div>
            <div class="kemuall-step kemuall-2">
                <img
                    src="https://web-resource.mc-cdn.cn/web/vip/ke1gxx.png"
                />
            </div>
            <div class="kemuall-step kemuall-3">
                <img
                    src="https://web-resource.mc-cdn.cn/minprogram/jiqiao/khke2-panel-2.png"
                    alt=""
                />
            </div>
            <div class="kemuall-step kemuall-4">
                <img
                    src="https://web-resource.mc-cdn.cn/minprogram/jiqiao/khke2-panel-3.png"
                    alt=""
                />
            </div>
            <div class="kemuall-step kemuall-5">
                <img
                    src="https://web-resource.mc-cdn.cn/web/vip/ke4gxx.png"
                    alt=""
                />
            </div>
            <div class="kemuall-step kemuall-6">
                <img
                    sp-on:click="goAuth"
                    data-uniqkey="bgbc"
                    src="https://web-resource.mc-cdn.cn/web/vip/buchang1.png"
                    alt=""
                />
            </div>
            <sp:slot />
            <com:commonQuestion type="5"></com:commonQuestion>
        </div>
    </div>
</div>
