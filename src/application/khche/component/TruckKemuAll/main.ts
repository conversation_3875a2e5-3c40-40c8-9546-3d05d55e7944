import { ABTestType } from ':common/env';
import { promiseIconList, promiseList } from ':common/features/promise';
import { TRUCK_QK_VIDEO } from ':common/navigate';
import { getActivityTime } from ':store/chores';
import { GoodsInfo, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { mergeObjects } from ':common/utils';

interface State {
    give: any,
    activityInfo: any
}

interface Props {
    goodsList: GoodsInfo[]
    currentIndex: number
    goodsInfo: GoodsInfo
    bought: boolean,
    abTestType: ABTestType
    goAuth?(e: Event)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    get iconList() {
        // pageheader的商品列表
        let iconList;
        // 不能升级并且没有活动的权益（其实就是比价）
        if (!this.props.goodsInfo.upgrade && !this.props.goodsInfo.inActivity) {
            iconList = [
                {
                    icon: promiseIconList.k1vip,
                    uniqkey: promiseList.k1vip,
                    name: '科一vip'
                },
                {
                    icon: promiseIconList.k2vip,
                    uniqkey: promiseList.k2vip,
                    name: '科二vip'
                },
                {
                    icon: promiseIconList.k3vip,
                    uniqkey: promiseList.k3vip,
                    name: '科三vip'
                },
                {
                    icon: promiseIconList.k4vip,
                    uniqkey: promiseList.k4vip,
                    name: '科四vip'
                }
            ];
        } else {
            iconList = [
                {
                    icon: promiseIconList.bxbd,
                    uniqkey: promiseList.bxbd,
                    dec: '必学榜单',
                    tip: 'https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/jk-tag-zhiboke.png',
                    tipWidth: 37
                },
                {
                    icon: promiseIconList.k1vip,
                    uniqkey: promiseList.k1vip,
                    dec: '科一VIP'
                },
                {
                    icon: promiseIconList.k2vip,
                    uniqkey: promiseList.k2vip,
                    dec: '科二VIP',
                    tip: 'https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/jk-tag-vipfangzhen.png',
                    tipWidth: 30
                },
                {
                    icon: promiseIconList.k3vip,
                    uniqkey: promiseList.k3vip,
                    dec: '科三VIP'
                },
                {
                    icon: promiseIconList.k4vip,
                    uniqkey: promiseList.k4vip,
                    dec: '科四VIP'
                }
            ];
        }

        return iconList;

    }
    get authIconList() {
        return [
            {
                groupKey: GroupKey.HcChannelKe1,
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/05/13/14/d0fdba467ff848d6940176090ab92f40.png',
                dec: '科一vip'
            },
            {
                groupKey: GroupKey.HcChannelKe2,
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/05/24/16/79deb78032644705a82646c5e67083d1.png',
                dec: '科二vip'
            },
            {
                groupKey: GroupKey.HcChannelKe3,
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/05/13/14/c1889d0eb6454b9bb55e69996f763b21.png',
                dec: '科三vip'
            },
            {
                groupKey: GroupKey.HcChannelKe4,
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/05/13/14/7b40ad7143d44739af717560962849e7.png',
                dec: '科四vip'
            },
            {
                uniqkey: promiseList.qyzbk,
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/05/13/14/958c0ee18e034d518f1126956616991d.png',
                dec: '千元直播课'
            }
        ];
    }
 
    get headUiConfig() {
        const { goodsList, currentIndex, goodsInfo } = this.props;
        const info = goodsInfo || goodsList[currentIndex];
        const config: any = {
            img: 'https://jiakao-web.mc-cdn.cn/jiakao-web/2023/04/06/10/f7bb1ae5ac454953894fa0c6b95c81c6.png',
            video: TRUCK_QK_VIDEO
        };

        if (info.giftPromotion?.promotionStatus) {
            mergeObjects(config, {

            });
        }

        if (info.headConfig) {
            mergeObjects(config, {
                img: info.headConfig.img,
                bgc: info.headConfig.bgc,
                video: info.headConfig.video,
                transparent: !!info.headConfig.img
            });
        }

        return config;
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            // 赠品
            give: {
                uniqkey: promiseList.qyzbk,
                icon: promiseIconList.qyzbk,
                name: '千元直播课'
            },
            activityInfo: {}
        };

    }

    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }

    willReceiveProps() {
        return true;
    }

    didMount() {
        this.getActivityInfo();
    }

    async getActivityInfo() {
        const retData = await getActivityTime();
        this.setState({
            activityInfo: retData
        });
    }
}
