<import name="style" content="./main" module="S" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="PriceTag" content=":component/priceTag/main" />
<import name="payDialog" content=":component/payDialog/main" />

<div class=":truck-select-goods">
    <div class=":title">购买</div>
    <div class=":close" sp-on:click="close"></div>
    <div class=":select-box">
        <sp:each for="state.goodsInfoPool">
            <div
                sp-on:click="tabChangeCall"
                class=":select-item {{state.tabIndex === $index?S.selected:''}}"
                data-index="{{$index}}"
            >
                <div class=":l">
                    <div class=":good-name">{{$value.name}}</div>
                    <div class=":good-v">有效期<span class=":v">{{$value.validDays}}</span>天</div>
                </div>
                <div class=":r">
                    <span class=":unit">￥</span>
                    <span>{{$value.payPrice}}</span>
                </div>
                  <com:PriceTag
                    class="noScale"
                    goodsInfo="{{$value}}"
                    comparePriceMap="{{state.comparePricePool}}"
                    labelMap="{{state.labelPool}}"
                />
            </div>
        </sp:each>
    </div>
    <com:buyButton>
        <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
            {{self.nowCouponInfo.couponCode?'已优惠' +
            self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
        </div>
    </com:buyButton>
    <com:payDialog />
</div>
