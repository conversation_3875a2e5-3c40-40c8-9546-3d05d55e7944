.truck-select-goods {
    position: relative;
    background: #FFFFFF;
    box-shadow: inset 0px 1px 0px 0px #FFFFFF;
    border-radius: 6px 6px 0px 0px;
    overflow: hidden;

    .title {
        padding: 17px 15px;
        font-size: 16px;
        font-weight: 500;
    }

    .close {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 20px;
        height: 20px;
        background: url(../images/close.png) no-repeat center center/cover;
    }

    .select-box {
        padding: 0 15px;

        .select-item {
            &:nth-of-type(2) {
                margin-top: 15px;

                .l {
                    .good-v {
                        .v {
                            margin: 0 4px;
                            color: #E9523F;
                        }

                        &::after {
                            display: inline-block;
                            content: '';
                            margin-left: 4px;
                            width: 98px;
                            height: 19px;
                            background: url(../images/strong.png) no-repeat center center/cover;
                        }
                    }
                }
            }

            height: 76px;
            border-radius: 4px;
            border: 1px solid #DDDDDD;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 25px 0 15px;
            position: relative;

            &.selected {
                background: rgba(255, 214, 189, 0.2);
                border: 1px solid #FFBB8A;
                position: relative;

                &::after {
                    content: '';
                    width: 30px;
                    height: 30px;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    background: url(../images/<EMAIL>) no-repeat center center/cover;
                }

                .r {
                    color: #E1813C;
                }
            }

            .l {
                color: #333;

                .good-name {
                    font-size: 18px;
                    line-height: 25px;
                    font-weight: bold;
                }

                .good-v {
                    display: flex;
                    align-items: center;
                    font-size: 13px;
                    line-height: 18px;
                }
            }

            .r {
                font-size: 26px;

                .unit {
                    font-size: 18px;
                }
            }
        }
    }
}
