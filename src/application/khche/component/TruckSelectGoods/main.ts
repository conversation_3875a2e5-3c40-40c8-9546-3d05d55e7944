/*
 * main
 *
 * name: xia<PERSON><PERSON>a
 * date: 16/3/24
 */
import { CarType, PayType, URLCommon } from ':common/env';
import { typeCode } from ':common/features/bottom';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { formatPrice } from ':common/utils';
import { iosBuySuccess } from ':common/features/ios_pay';
import { getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { webClose } from ':common/core';

interface State {
    tabIndex: number
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
}
enum typeMap {
    page = 'page',
    component = 'component'
}
interface Props {
    type: typeMap,
}

export default class extends Component<State, Props> {
    declare children: {
        buyButton: BuyButton
        payDialog: PayDialog
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { couponPool } = this.state;
        const nowPayPrice = this.nowGoodInfo.payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });
        const goodsInfoPool: GoodsInfo[] = [];
        switch (URLCommon.tiku) {
            case CarType.TRUCK:
                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKemuAll
                } as GoodsInfo);

                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKe1Time365
                } as GoodsInfo);

                break;
            default:
                break;
        }

        this.state = {
            tabIndex: 0,
            goodsInfoPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {}
        };
    }
    didMount() {
        this.getGoodInfo();
    }
    tabChangeCall = (e) => {
        const tabIndex = +e.refTarget.getAttribute('data-index');
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        this.setState({
            tabIndex
        }, () => {
            this.setPageInfo();
        });

    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const fragmentName1 = '底部吸底按钮';
        const fragmentName2 = '选择商品类型弹窗';
        const nowGoodInfo: GoodsInfo = this.nowGoodInfo;
        const bottomType: typeCode = typeCode.type2;

        this.children.buyButton.setPay({
            isInDialog: true,
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });

        switch (bottomType) {
            case typeCode.type2:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '',
                    price: this.showPrice,
                    fragmentName1,
                    fragmentName2,
                    actionName: '确认支付'
                });
                break;
            default:
                break;
        }
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {

            goodsListInfo.forEach((goodInfo) => {
                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            this.setState({
                goodsInfoPool: newGoodsPool
            });

            // this.setPageInfo();
            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getLabel();
                await this.getComparePrice();

                // this.setPageInfo();
            }, 60);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice
                    };
                }
            });

            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
        }).catch(async () => {
            this.children.payDialog.show({
                groupKey: this.nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    close() {
        const { type } = this.props;
        if (type === typeMap.component) {
            this.emit('close');
        } else {
            webClose();
        }
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    willReceiveProps() {
        return true;
    }
}
