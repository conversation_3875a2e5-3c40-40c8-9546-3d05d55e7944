<import name="style" content="./main" />
<import name="pageHeader" content=":application/car/component/page-header/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="giftText" content=":component/giftText/main" />
<import name="wenda" content=":component/wenda/main" />
<import name="cards" content=":application/car/component/cards/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />

<div class="panel-bus-truck-kemu14">
    <com:pageHeader iconList="{{state.iconList}}" goAuth="{{props.goAuth}}" uiConfig="{{self.headUiConfig}}"
    />
    <sp:if value="props.kemu == 1 && props.goodsList.length">
        <div class="card-box {{props.headerAbTestType === ABTestType.B?'card-box-b':''}}">
            <com:cards comparePricePool="{{props.comparePricePool}}" goodsList="{{props.goodsList}}"
                tabChange="{{props.tabChange}}" tabIndex="{{props.currentIndex}}" />
        </div>
    </sp:if>
    <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
        <div class="buy-btn" key="buy-btn">
            确认协议并支付 ¥{{props.payPrice}}

            <sp:if value="props.goodsInfo.inActivity">
                <span class="preDiscountPrice">日常价{{props.goodsInfo.inActivity.preDiscountPrice}}元</span>
            </sp:if>
        </div>
        <!-- <com:giftText /> -->
        <sp:if value="props.goodsInfo.inActivity">
            <div class="time-tip">
                <span class="sp">立减{{props.goodsInfo.inActivity.discountedPrice}}元</span>
                <com:Count name="Count" startTime="{{props.goodsInfo.inActivity.discountStartTime}}"
                    endTime="{{props.goodsInfo.inActivity.discountEndTime}}" />
            </div>
            <sp:elseif value="props.labelPool[props.goodsInfo.groupKey].label" />
            <span class="tip">{{props.labelPool[props.goodsInfo.groupKey].label}}<i></i></span>
        </sp:if>
    </div>
    <div class="protocol-box">
        <com:middleProtocol groupKey="{{props.groupKey}}" couponPool="{{props.couponPool}}" />
    </div>

    <div class="sec-wenda-w {{props.isHubei || Platform.isXueTang ? 'hide': ''}}">
        <com:wenda name="wenda" groupKey="{{props.groupKey}}" type="1" />
    </div>
    <div class="sec-1">
        <img
            src="{{URLCommon.kemu== 1 ? 'https://web-resource.mc-cdn.cn/web/vip/t2.png': 'https://web-resource.mc-cdn.cn/web/vip/t2-1.png'}}" />
        <div class="click-9" data-type="1" data-uniqkey="goods-quick500"></div>
    </div>
    <div class="sec-2">
        <img src="https://jiakao-web.mc-cdn.cn/jiakao-web/2022/12/12/14/2ca6ba70278d478886b2a43bbce635fe.png" />
        <div class="click-10" data-type="1" data-uniqkey="goods-sujikoujue"></div>
    </div>
    <div class="sec-5" data-type="1" data-uniqkey="goods-baozhang">
        <img src="https://web-resource.mc-cdn.cn/web/vip/t1.png" />
    </div>
    <div class="sec-6" sp-on:click="goAuth" data-uniqkey="bgbc">
        <img src="https://web-resource.mc-cdn.cn/web/vip/ban2.png" />

    </div>
    <sp:slot />
    <com:commonQuestion type="1"></com:commonQuestion>
</div>
