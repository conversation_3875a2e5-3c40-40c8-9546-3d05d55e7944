.panel-bus-truck-kemu14 {
    background-color: #221d1d;

    img {
        width: 100%;
    }
    .card-box {
        margin-bottom: 24px;
        &.card-box-b {
            margin-top: 10px;
        }
    }
    .buy-btn-box {
        position: relative;
        width: 343px;
        height: 49px;
        margin: 0px auto 15px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/btn1.png)
            no-repeat center center/100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .buy-btn {
            font-size: 19px;
            color: white;
        }

        .preDiscountPrice {
            position: relative;
            font-size: 12px;
            transform: scale(0.8);
            color: white;
            margin-left: 4px;
            margin-top: 4px;

            &::after {
                content: "";
                position: absolute;
                top: 50%;
                left: 0;
                width: 100%;
                height: 1px;
                background-color: white;
            }
        }

        .time-tip {
            position: absolute;
            top: 15px;
            right: 18px;
            transform: translateY(-50%);
            height: 22px;
            font-size: 12px;
            display: flex;
            align-items: center;
            padding: 0 6px;
            background: linear-gradient(90deg, #ffd878 0%, #ffc400 100%);
            border-radius: 33px 33px 33px 2px;
            color: #6f2117;
            overflow: hidden;
            z-index: 1;

            .sp {
                transform: scaleX(0.9) scaleY(0.9);
                transform-origin: bottom;
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                -webkit-line-clamp: 1;
            }

            .count-content {
                margin-left: 1px;
                transform: scaleX(0.9) scaleY(0.9);
                transform-origin: bottom;
            }
        }

        .tip {
            position: absolute;
            z-index: 100;
            top: 0;
            right: 0;
            transform: translateY(-50%);
            font-size: 12px;
            line-height: 14px;
            color: #6f2117;
            background: linear-gradient(90deg, #ffd878 0%, #ffc400 100%);
            padding: 5px 10px;
            border-radius: 30px;
        }
    }

    .protocol-box {
        margin-bottom: 30px;
        font-size: 13px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
    }

    .sec-wenda-w {
        padding: 0 15px;
        margin-bottom: 20px;
    }

    .sec-1 {
        position: relative;
        padding: 0 30px;
    }

    .sec-2 {
        position: relative;
        margin-top: 30px;
        padding: 0 15px;
    }

    .sec-3 {
        position: relative;
        margin-top: 40px;
        padding: 0 40px;
    }

    .sec-4 {
        position: relative;
        margin-top: 20px;
        padding: 0 15px;
    }

    .sec-5 {
        position: relative;
        margin-top: 40px;
        padding: 0 66px;
    }

    .sec-6 {
        position: relative;
        margin-top: 20px;
        padding: 0 15px;
        .summer-tag {
            width: 48px;
            height: 13px;
            position: absolute;
            top: 25px;
            left: 160px;
        }
    }
}
