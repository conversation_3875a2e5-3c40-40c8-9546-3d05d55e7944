/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { promiseIconList, promiseList } from ':common/features/promise';
import { getActivityTime } from ':store/chores';
import { CarType, KemuType, URLParams } from ':common/env';
import { GoodsInfo } from ':store/goods';
import { mergeObjects } from ':common/utils';

interface State {
    iconList: any[]
    activityInfo: any
}

interface Props {
    goodsList: GoodsInfo[]
    currentIndex: number
    goodsInfo?: GoodsInfo
    goAuth?(e: Event)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    get headUiConfig() {
        const { goodsList, currentIndex, goodsInfo } = this.props;
        const info = goodsInfo || goodsList[currentIndex];
        const config: any = {
            img: 'https://web-resource.mc-cdn.cn/web/vip/hdbus1.png'
        };

        if (info.giftPromotion?.promotionStatus) {
            mergeObjects(config, {

            });
        }

        if (info.headConfig) {
            mergeObjects(config, {
                img: info.headConfig.img,
                bgc: info.headConfig.bgc,
                video: info.headConfig.video,
                transparent: !!info.headConfig.img
            });
        }

        return config;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            iconList: [
                {
                    icon: promiseIconList.jj500t,
                    uniqkey: promiseList.jj500t,
                    dec: '精简600题',
                    dec1: '省时省力'
                },
                {
                    icon: promiseIconList.zskcmn,
                    uniqkey: promiseList.zskcmn,
                    dec: '真实考场模拟',
                    dec1: '高仿真'
                },
                {
                    icon: promiseIconList.kqmj,
                    uniqkey: promiseList.kqmj,
                    dec: '考前秘卷',
                    dec1: '高效冲刺'
                },
                {
                    icon: promiseIconList.bgbc,
                    uniqkey: promiseList.bgbc,
                    dec: '不过补偿',
                    dec1: '查看补偿说明'
                }
            ],
            activityInfo: {}
        };

    }

    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
    didMount() {
        this.getActivityInfo();
    }

    async getActivityInfo() {
        const retData = await getActivityTime();
        this.setState({
            activityInfo: retData
        });
    }
}
