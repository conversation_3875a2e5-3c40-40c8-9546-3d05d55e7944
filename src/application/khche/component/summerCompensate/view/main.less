.summer-box{
    .rule {
        font-size: 13px;
        color: rgba(255, 255, 255, 0.8);
        width: 135px;
        height: 88px;
        background: url(../images/1.png) no-repeat;
        background-size: 100% 100%;
        position:absolute;
        bottom:200px;
        left:2px;
        z-index: 999;
    }

    .rule-panel {
        width: 320px;
        padding:25px 20px 27px;
        border-radius: 16px;
        background: white;
        .close{
            position: absolute;
            top: 12px;
            right: 12px;
            width:23px;
            height: 23px;
            background: url(../images/close.png) no-repeat;
            background-size:cover;
            z-index: 2;
        }
        .rule-wraper {
            background: white;
            border-radius: 8px;
            overflow: auto;
            -webkit-overflow-scrolling: touch;


            > h4{
                font-size: 18px;
                font-weight: 600;
                color: #333333;
                line-height: 22px;
            }

            > h5{
                font-size: 14px;
                font-weight: 600;
                line-height: 22px;
                color: #333333;
            }

            > p{
                font-size: 14px;
                color: #333333;
                line-height: 22px;
            }
        }
    }
   
}