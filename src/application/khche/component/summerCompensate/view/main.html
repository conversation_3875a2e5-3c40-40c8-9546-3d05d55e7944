<import name="style" content="./main" module="S" />
<import name="dialog" content=":component/dialog/main" />

<div class=":summer-box">
    <sp:if
        value="state.activityInfo.activityIng && (URLCommon.tiku === CarType.TRUCK ||  URLCommon.tiku === CarType.BUS) && URLCommon.isNormal">
        <div class=":rule" ref="rule" skip="true" draggable="true" sp-on:click="onRule" id="drag">
        </div>
    </sp:if>

    <com:dialog>
        <div class=":rule-panel">
            <div class=":close" sp-on:click="onClose"></div>
            <div class=":rule-wraper">
                <h4 style="text-align:center;">活动规则</h4>
                <h5 style="margin-top: 20px;">活动时间： </h5>
                <p>
                    {{Tools.dateFormat(state.activityInfo.startTime, 'yyyy年MM月dd日HH:mm')}}-{{Tools.dateFormat(state.activityInfo.endTime, 'yyyy年MM月dd日HH:mm')}}
                </p>

                <h5 style="margin-top: 20px;">活动规则：</h5>
                <p> 1. 在活动期间购买客货车科一、科四VIP、全科VIP商品的学员，其考不过补偿金额翻倍（原单科补偿为50元，翻倍后为100元）；</p>
                <p> 2. 如科一科四均未考过且均达到补偿标准时，其科一科四均可获得100元补偿；</p>
                <p> 3. 在活动前购买的VIP，如资料填写时间、补偿申请时间或补偿发放时间在活动期间，也可以获得翻倍后的补偿；</p>
                <p> 4. 在活动期间购买的VIP，活动结束后再提交补偿申请，翻倍补偿仍然有效；</p>
                <p> 5. 在活动前购买的VIP且活动前已经领取过补偿的无法追加补偿；</p>
                <p> 6. 在活动结束后购买的VIP，申请补偿时补偿金额将恢复至单科50元。</p>

            </div>
        </div>
    </com:dialog>
</div>
