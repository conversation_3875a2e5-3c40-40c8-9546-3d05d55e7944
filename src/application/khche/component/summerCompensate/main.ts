/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */
import { Dialog } from ':component/dialog/main';
import { ActivityInfo, getActivityTime } from ':store/chores';
import View from './view/main.html';

interface State {
    activityInfo: ActivityInfo;
}
export default class SummerDialog extends Dialog<void, void, State> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }
    public show() {
        return super.show();
    }
    didMount() {
        this.getActivityInfo();
    }

    onRule() {
        this.show();
    }

    onClose() {
        this.hide();
    }

    async getActivityInfo() {
        const retData = await getActivityTime();
        this.setState({
            activityInfo: retData
        }, () => {
            if (retData.activityIng) {
                this.drag();
            }
        });
    }
    drag() {
        const target = this.getDOMNode().rule as HTMLElement;
        let startX = 0;
        let startY = 0;
        target.addEventListener('touchstart', function (e) {
            startX = e.targetTouches[0].pageX - this.offsetLeft;
            startY = e.targetTouches[0].pageY - this.offsetTop;
        });
        target.addEventListener('touchmove', function (e) {
            let leftX = e.targetTouches[0].pageX - startX;
            let topY = e.targetTouches[0].pageY - startY;
            const thisW = (e.targetTouches[0].target as HTMLElement).clientWidth;
            const parentW = (e.targetTouches[0].target as HTMLElement).offsetParent.clientWidth;
            const thisH = (e.targetTouches[0].target as HTMLElement).clientHeight;
            const parentH = (e.targetTouches[0].target as HTMLElement).offsetParent.clientHeight;

            if (leftX <= 0) {
                leftX = 0;
            }

            if (leftX >= parentW - thisW) {
                leftX = parentW - thisW;
            }

            if (topY <= 90) {
                topY = 90;
            }

            if (topY >= parentH - thisH) {
                topY = parentH - thisH;
            }
            this.style.left = leftX + 'px';
            this.style.top = topY + 'px';
        });
    }

}
