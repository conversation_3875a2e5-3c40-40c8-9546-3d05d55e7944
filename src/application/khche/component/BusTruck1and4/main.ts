/*
 * main
 *
 * name: xiao<PERSON>a
 * date: 16/3/24
 */

import { getCache, saveCache } from ':common/core';
import { promiseIconList, promiseList } from ':common/features/promise';
import { dateFormat, mergeObjects } from ':common/utils';
import { GoodsInfo, GroupKey } from ':store/goods';
import { globalGoodsInfo } from ':store/newGoods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import pageHeader from ':application/car/component/page-header/main';

interface State {
    iconList: any[]
}
interface Props {
    goodsList: GoodsInfo[]
    currentIndex: number
    goAuth?(any)
    payBtnCall?(e: Event)
    goodsInfo: GoodsInfo
}
export default class extends Component<State, Props> {
    declare children: {
        pageHeader: pageHeader
    }
    get headUiConfig() {
        const { goodsList, currentIndex, goodsInfo } = this.props;
        const info = goodsInfo || goodsList[currentIndex];
        const config: any = {
            img: 'http://exam-room.mc-cdn.cn/exam-room/2023/12/29/11/e3fdc7b0d289464bb0795ee782caf87c.png'
        };

        if (info.giftPromotion?.promotionStatus) {
            mergeObjects(config, {

            });
        }

        if (info.headConfig) {
            mergeObjects(config, {
                img: info.headConfig.img,
                bgc: info.headConfig.bgc,
                video: info.headConfig.video,
                transparent: !!info.headConfig.img
            });
        }

        return config;
    }
    getFormateTime(num) {
        return dateFormat(num, 'MM月dd日 hh:mm', 24);
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            iconList: [
                {
                    uniqkey: promiseList.k1vip,
                    icon: promiseIconList.k1vip,
                    dec: '科一VIP'
                },
                {
                    uniqkey: promiseList.bgbc,
                    icon: promiseIconList.k1bgbc,
                    dec: '科一考不过<br/>补偿'
                },
                {
                    uniqkey: promiseList.k4vip,
                    icon: promiseIconList.zskcmn,
                    dec: '科四VIP'
                },
                {
                    uniqkey: promiseList.bgbc,
                    icon: promiseIconList.khcheKebgbc,
                    dec: '科四考不过<br/>补偿'
                }
            ],
            goodsActivityInfo: {}
        };

    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
