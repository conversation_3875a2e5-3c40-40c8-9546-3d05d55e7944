<import name="style" content="./main" />
<import
    name="pageHeader"
    content=":application/car/component/page-header/main"
/>
<import name="cards" content=":application/car/component/cards/main" />

<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="wenda" content=":component/wenda/main" />
<import name="giftText" content=":component/giftText/main" />
<import name="PriceTag" content=":component/priceTag/main" />
<import
    name="middleProtocol"
    content=":application/car/component/middleProtocol/main"
/>

<div class="panel-carkemu1and4">
      <com:pageHeader goAuth="{{props.goAuth}}"
           uiConfig="{{self.headUiConfig}}"
          iconList="{{state.iconList}}" />

    <div class="ipad-box">
        <div class="phone-box">
            <!-- 有切换才有这个 -->
            <sp:if value="{{props.tabChange}}">
                <div
                    class="card-box"
                    style="background: linear-gradient({{state.activeBgc}} 50%, #f2f2f2 51%, #f2f2f2 100%);"
                >
                    <com:cards
                        comparePricePool="{{props.comparePricePool}}"
                        goodsList="{{props.goodsList}}"
                        tabChange="{{props.tabChange}}"
                        tabIndex="{{props.currentIndex}}"
                    />
                </div>
            </sp:if>

            <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                <div class="buy-btn">
                    确认协议并支付 ¥{{props.payPrice || props.payPrice ===
                    0?props.payPrice : '--'}}
                </div>
                <!-- <com:giftText/> -->
                <com:PriceTag
                    class="noScale"
                    goodsInfo="{{props.goodsInfo}}"
                    comparePriceMap="{{props.comparePricePool}}"
                    labelMap="{{props.labelPool}}"
                />
            </div>
            <div class="protocol-box">
                <com:middleProtocol groupKey="{{props.groupKey}}" couponPool="{{props.couponPool}}" />
            </div>

            <div class="step-box">
                <div class="step step1">
                    <img
                        src="http://exam-room.mc-cdn.cn/exam-room/2022/07/28/10/8d5d42b4250d4fa490c6e1160743c94e.png"
                        alt=""
                    />
                </div>
                <div class="step step2">
                    <img
                        src="https://jiakao-web.mc-cdn.cn/jiakao-web/2022/12/12/14/2ca6ba70278d478886b2a43bbce635fe.png"
                        alt=""
                    />
                </div>

                <div class="step step5">
                    <img
                        src="http://exam-room.mc-cdn.cn/exam-room/2022/07/28/10/aa49e9edf2374e98aba776c1ef87ac4e.png"
                        alt=""
                    />
                </div>

                <div class="step step6">
                    <img
                        src="http://exam-room.mc-cdn.cn/exam-room/2022/07/28/10/db61f095c80c4d6e850cae051de7267e.png"
                        alt=""
                    />
                </div>

                <div class="step step3">
                    <img
                        src="http://exam-room.mc-cdn.cn/exam-room/2022/07/28/10/9e99342d251c4d7cb96b4f4e36422f03.png"
                    />
                </div>
                <div
                    class="step step4"
                    sp-on:click="goAuth"
                    data-uniqkey="bgbc"
                >
                    <img
                        src="http://exam-room.mc-cdn.cn/exam-room/2022/07/28/10/98a6a1d0e07d4e4fbc8e464526739505.png"
                    />
                </div>
            </div>
            <sp:slot />
            <com:commonQuestion type="1" kemuTxt="科一和科四" >
            </com:commonQuestion>
        </div>
    </div>
</div>