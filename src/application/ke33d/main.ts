/*
 * ------------------------------------------------------------------
 * 科目三3D购买弹窗
 * ------------------------------------------------------------------
 */

import { Application } from '@simplex/simple-core';
import { ComparePriceInfo, comparePrice, GoodsInfo, GroupKey, getGroupSessionInfo, getSessionExtra, GoodsExtra } from '../../store/goods';
import View from './view/main.html';
import { CarType, PayType, setPageName, URLCommon, URLParams } from ':common/env';
import { iosBuySuccess } from ':common/features/ios_pay';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackGoPay } from ':common/stat';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { showSetting } from ':common/features/embeded';
import { BUYED_URL } from ':common/navigate';
import { webClose } from ':common/core';
import { batchGetBestCoupon, Coupons, couponWithHint, goodsInfoWithCoupon } from ':common/features/coupon';
import PayTypeCom from ':component/payType/main';
import jump from ':common/features/jump';

setPageName(URLParams.fromPage || '项目视频详情页');
const fragmentName1 = URLParams.fragmentName1 || '未知片段';
const groupKey = URLCommon.tiku === CarType.MOTO ? GroupKey.MotoChannelKe3 : GroupKey.ChannelKe3;
const bundleGroupKey = GroupKey.ChannelKe34;

interface State {
    tabIndex: number;
    goodsList: GoodsInfo[];
    coupons: Coupons;
    diffPrice: ComparePriceInfo;
    ke3Label: GoodsExtra;
}

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeCom;
    };

    $constructor(o) {
        this.$super({
            target: o.target || document.body,
            name: module.id,
            view: View
        });
        this.state = {
            tabIndex: -1,
            goodsList: [],
            coupons: {},
            diffPrice: null,
            ke3Label: null
        };
    }

    get currentGoods() {
        return goodsInfoWithCoupon(this.state.goodsList[this.state.tabIndex], this.currentCoupon);
    }

    get currentCoupon() {
        return couponWithHint(this.state.coupons[this.state.goodsList[this.state.tabIndex].groupKey]);
    }

    async didMount() {
        this.windowResize();
        trackGoPay({
            groupKey,
            fragmentName1,
            payPathType: 0
        });
        showSetting({
            iosH: 300,
            androidH: 460
        });
        this.event.on('close', 'click', () => {
            webClose();
        });

        ensureSiriusBound({ groupKey, type: PayBoundType.GoLogin });

        await this.fetchGoodsInfo();
        this.fetchCouponInfo();
        this.fetchLabelInfo();
    }

    async fetchGoodsInfo() {
        const groupKeys = [groupKey, bundleGroupKey];

        if (URLCommon.tiku === CarType.MOTO) {
            groupKeys.splice(1, 1);
        }

        const [goodsInfo, bundleGoodsInfo] = await getGroupSessionInfo({
            groupKeys
        });

        if (goodsInfo.bought) {
            jump.replace(BUYED_URL);
            return;
        }
        const goodsList = [goodsInfo];
        if (bundleGoodsInfo && !bundleGoodsInfo.bought) {
            goodsList.push(bundleGoodsInfo);

            const diffPrice = await comparePrice({ groupKey: bundleGroupKey, upgradeStrategyCode: bundleGoodsInfo.upgradeStrategyCode });
            this.setState({
                diffPrice
            });
        }
        this.setState({ goodsList });
        // 默认选中第一个Tab
        this.switchTab(0);
    }

    async fetchCouponInfo() {
        // 获取优惠券信息
        const coupons = await batchGetBestCoupon(this.state.goodsList);
        this.setState({ coupons });
        this.setPayment();
    }

    async fetchLabelInfo() {
        // 获取label信息
        const ke3Label = await getSessionExtra({ groupKey });
        this.setState({ ke3Label });
    }

    switchTab(tabIndex: number) {
        if (tabIndex !== this.state.tabIndex) {
            this.setState({ tabIndex });
            this.setPayment();
        }
    }

    /** 设置支付参数 */
    public setPayment() {
        this.children.buyButton.setPay({
            androidPay: this.pay.bind(this),
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => iosBuySuccess({ groupKey: this.currentGoods.groupKey }),
            isInDialog: true
        });

        if (this.currentGoods.inActivity) {
            this.children.buyButton.setButtonConfig({
                groupKey: this.currentGoods.groupKey,
                type: 6,
                title: '确认协议并支付',
                subtitle: '有效期' + this.currentGoods.validDays + '天',
                price: this.currentGoods.payPrice,
                validDays: this.currentGoods.validDays,
                originalPrice: `日常价￥${this.currentGoods.inActivity.preDiscountPrice}`,
                discount: `已立减${this.currentGoods.inActivity.discountedPrice}元`,
                fragmentName1,
                fragmentName2: '支付弹窗'
            });
        } else {
            this.children.buyButton.setButtonConfig({
                groupKey: this.currentGoods.groupKey,
                type: 1,
                title: '确认协议并支付',
                price: this.currentGoods.payPrice,
                validDays: this.currentGoods.validDays,
                fragmentName1,
                fragmentName2: '支付弹窗'
            });
        }
    }

    /** 发起支付 */
    public async pay(stat: PayStatProps) {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.currentGoods.groupKey,
            sessionIds: this.currentGoods.sessionIds,
            activityType: this.currentGoods.activityType,
            couponCode: this.currentCoupon.code,
            ...stat
        }, false).then(() => newBuySuccess({ groupKey: this.currentGoods.groupKey }, 2));
    }

    onTabClick(e) {
        const idx = +e.refTarget.getAttribute('data-idx');
        this.switchTab(idx);
    }
    windowResize() {
        const reCalc = function () {
            const dimensionWidth = Math.min(document.documentElement.clientWidth, 480);

            const rate = dimensionWidth / Package.build.style.baseWidth;

            const baseFontSize = 100 * rate;
            window.baseFontSize = baseFontSize;

            document.documentElement.style.fontSize = baseFontSize + 'px';
        };
        setTimeout(() => {
            reCalc();
        }, 500);
    }
}
