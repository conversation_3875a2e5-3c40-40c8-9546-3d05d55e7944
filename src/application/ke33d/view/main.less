html,body {
    height: 100vh;
    overflow: hidden;
}

.container {
    padding: 15px 15px 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    max-width: 375px;
    margin: 0 auto;

    .scroll-box {
        flex: 1;
        overflow-y: auto;
    }
}

.title {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC, sans-serif;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
    flex-shrink: 0;
}

.close {
    position: absolute !important;
    top: 8px;
    right: 10px;
    width: 20px;
    height: 20px;
    background: url("../images/close.png");
    background-size: cover;
}

.tabs {
    margin-top: 25px;
    display: flex;
    justify-content: space-between;
}

.tab {
    width: 0;
    flex: 1;
    border-radius: 8px;
    text-align: center;
    position: relative;

    .top {
        line-height: 51px;
        font-size: 18px;
        font-family: PingFangSC-Semibold, PingFang SC, sans-serif;
        font-weight: 600;
        white-space: nowrap;
        border-radius: 8px 8px 0 0;

        &.smaller {
            font-size: 15px;
        }
    }

    .bottom {
        line-height: 25px;
        font-size: 12px;
        font-family: PingFangSC-Medium, PingFang SC, sans-serif;
        font-weight: 500;
        border-radius: 0 0 8px 8px;
    }

    .label {
        display: flex;
        white-space: nowrap;
        position: absolute;
        right: 6px;
        top: -12px;
        line-height: 16px;
        padding: 3px 8px 1px;
        background: linear-gradient(
            90deg,
            #ff7810 0%,
            #fe3c29 55%,
            #fe6164 100%
        );
        border-radius: 33px 33px 33px 2px;
        font-size: 10px;
        font-family: PingFangSC-Medium, PingFang SC, sans-serif;
        font-weight: 500;
        color: #ffffff;
    }

    & + & {
        margin-left: 11px;
    }

    &--normal {
        box-shadow: 0 0 0 1px #f4c2a2;

        .top {
            color: #171717;
            background: #ffffff;
        }

        .bottom {
            color: #aa4120;
            background: #fef0e7;
        }
    }

    &--active {
        .top {
            color: #ffffff;
            background: linear-gradient(113deg, #353b4e 0%, #1d222b 100%);
        }

        .bottom {
            color: #aa4120;
            background: linear-gradient(118deg, #f9dbc0 0%, #efaf8b 100%);
        }
    }
}

.detail {
    margin-top: 15px;
    height: 0;
    flex: 1;
}

.subtitle {
    margin-bottom: 10px;
    font-size: 15px;
    font-family: PingFangSC-Medium, PingFang SC, sans-serif;
    font-weight: 500;
    color: #692204;
    line-height: 21px;
    text-align: center;
}

.showcase {
    display: flex;
    align-items: center;
    padding: 6px;
    padding-left: 0;
    background: linear-gradient(
        156deg,
        #ffe6ca 0%,
        #ffddb9 19%,
        #fbdfbd 44%,
        #facc91 100%
    );
    border-radius: 5px;
    border: 1px solid #ffffff;
    text-align: center;
    white-space: pre-wrap;

    .left {
        width: 70px;
        font-size: 12px;
        font-family: PingFangSC-Semibold, PingFang SC, sans-serif;
        font-weight: 600;
        color: #692204;
        line-height: 17px;
        display: flex;
        padding-top: 4px;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .price {
            font-size: 16px;
            color: #f73b31;
            line-height: 22px;

            span {
                font-size: 11px;
                line-height: 16px;
            }
        }
    }

    .card {
        height: 86px;
        padding: 0 6px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: linear-gradient(163deg, #fffbf6 0%, #fff7ee 100%);
        border-radius: 5px;
        border: 1px solid #ffcc8a;
        font-size: 13px;
        font-family: PingFangSC-Medium, PingFang SC, sans-serif;
        font-weight: 500;
        color: #a03c1c;
        width: 0;
        flex: 1;

        .top {
            min-height: 32px;
            line-height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .price {
            font-size: 16px;
            line-height: 22px;

            span {
                font-size: 11px;
                line-height: 16px;
            }
        }

        .desc {
            font-size: 12px;
            line-height: 22px;
        }
    }

    .circle {
        margin: 0 -5px;
        position: relative;
        width: 15px;
        height: 15px;
        background: url("../images/circle.png");
        background-size: cover;
    }
}

.sec1 {
    margin-top: 15px;
    height: 75px;
    background: url(../images/20.png) no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    padding: 4px 15px 0 15px;
    
    .desc1 {
        font-size: 18px;
        font-weight: bold;
        color: #681309;
        line-height: 18px;
    }

    .desc2 {
        font-size: 14px;
        font-weight: 400;
        color: #9f5217;
        line-height: 20px;
        padding-top: 10px;
    }

    .price {
        flex: 1;
        text-align: right;
        padding-right: 10px;

        i {
            font-size: 18px;
            font-weight: 400;
            color: #681309;
            line-height: 25px;
            padding-right: 4px;
        }

        b {
            font-size: 30px;
            font-weight: bold;
            color: #681309;
            line-height: 36px;
        }
    }
}

.sec2 {
    h3 {
        font-size: 16px;
        font-weight: bold;
        line-height: 21px;
        text-align: center;
        color: #692204;
    }

    .steps {
        display: flex;
        justify-content: space-around;
        padding-top: 15px;
    }

    .step {
        width: 78px;
        height: 86px;
        border-radius: 5px;
        position: relative;
        background: url(../images/13.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
    }

    i {
        width: 33px;
        height: 15px;
        margin-top: 1px;
        margin-left: 1px;
    }

    .icon1 {
        background: url(../images/15.png) no-repeat;
        background-size: 100% 100%;
    }

    .icon2 {
        background: url(../images/16.png) no-repeat;
        background-size: 100% 100%;
    }

    .icon3 {
        background: url(../images/17.png) no-repeat;
        background-size: 100% 100%;
    }

    .icon4 {
        background: url(../images/21.png) no-repeat;
        background-size: 100% 100%;
    }

    span {
        font-size: 13px;
        color: #8c3418;
        font-weight: bold;
        line-height: 16px;
        padding: 0px 10px;
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 0 6px;
    }

    label {
        font-size: 12px;
        color: #a03c1c;
        line-height: 17px;
        text-align: center;
        padding: 3px 0;
    }
}

.pay-type {
    margin-bottom: -10px;
    margin-left: 10px;
    flex-shrink: 0;
}

.buy-btn {
    margin: 0 -10px;
    flex-shrink: 0;
}

body {
    max-width: none;
    background: linear-gradient(
        180deg,
        #ffeddc 0%,
        #fffaf0 38%,
        #ffffff 73%,
        #ffffff 100%
    );
    box-shadow: 0px 1px 0px 0px #ffffff;
    border-radius: 6px 6px 0px 0px;
}