<import name="style" content="./main" module="S" />

<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="Count" content=":component/count/main" />

<div class=":container">
    <div class=":title">购买</div>
    <div class="hotArea :close" ref="close"></div>
    <div class=":scroll-box">
        <!-- 显示单个Tab -->
        <sp:if value="state.goodsList.length===1">
            <div class=":sec1">
                <div>
                    <p class=":desc1">{{state.goodsList[0].name}}</p>
                    <p class=":desc2">
                        {{URLCommon.tiku === CarType.MOTO
                        ?'快速学习科目三考试规则':'驾校练完回家练'}}
                    </p>
                </div>
                <div class=":price">
                    <i>¥</i><b>{{state.goodsList[0].payPrice || '--'}}</b>
                </div>
            </div>
        </sp:if>

        <!-- 显示多个Tab -->
        <sp:if value="state.goodsList.length!==1">
            <div class=":tabs">
                <sp:each for="state.goodsList">
                    <div
                        class=":tab {{$index===state.tabIndex?S.tabActive:S.tabNormal}}"
                        sp-on:click="onTabClick"
                        data-idx="{{$index}}"
                    >
                        <div class=":top {{$value.name.length>=7&&S.smaller}}">
                            {{$value.name}}&nbsp;{{$value.payPrice}}元
                        </div>
                        <div class=":bottom">
                            {{$index===0?'驾校练完回家练':'比分开买立省'+state.diffPrice.savePrice+'元'}}
                        </div>
                        <sp:if value="$value.inActivity">
                            <div class=":label">
                                <span
                                    >限时立减{{$value.inActivity.discountedPrice}}元&nbsp;</span
                                >
                                <com:Count
                                    startTime="{{$value.inActivity.discountStartTime}}"
                                    endTime="{{$value.inActivity.discountEndTime}}"
                                />
                            </div>
                        </sp:if>
                    </div>
                </sp:each>
            </div>
        </sp:if>

        <div class=":detail">
            <sp:if value="state.tabIndex===0">
                <div class=":sec2">
                    <h3>
                        - {{URLCommon.tiku === CarType.MOTO ?
                        '两大服务助你高效学习' : '限时附赠 4 大权益'}} -
                    </h3>
                    <div class=":steps">
                        <sp:each for="state.ke3Label.highlights">
                            <div class=":step">
                                <i class="{{S['icon' + ($index + 1)]}}"></i>
                                <span>{{$value.highlight}}</span>
                                <label
                                    >{{#($value.description ||
                                    '&nbsp;')}}</label
                                >
                            </div>
                        </sp:each>
                    </div>
                </div>
            </sp:if>
            <sp:if value="state.tabIndex===1">
                <div class=":subtitle">
                    {{state.goodsList[1].name}}，一起买更优惠
                </div>
                <div class=":showcase">
                    <div class=":left">
                        <span>比分开买<br />立省</span>
                        <span class=":price"
                            ><span>￥</span>{{state.diffPrice.savePrice}}</span
                        >
                    </div>
                    <sp:each for="state.diffPrice.groupItems">
                        <div class=":card">
                            <span class=":top">{{$value.name}}</span>
                            <sp:if value="$value.price">
                                <span class=":price"
                                    ><span>￥</span>{{$value.price}}</span
                                >
                                <sp:else />
                                <span class=":desc"
                                    >{{$value.description}}</span
                                >
                            </sp:if>
                        </div>
                        <div
                            class=":circle"
                            sp:if="{{$index!==state.diffPrice.groupItems.length-1}}"
                        />
                    </sp:each>
                </div>
            </sp:if>
        </div>
    </div>

    <div class=":buy-btn">
        <com:buyButton>
            <div sp:slot="couponEntry">{{self.currentCoupon.hint}}</div>
        </com:buyButton>
    </div>
</div>
