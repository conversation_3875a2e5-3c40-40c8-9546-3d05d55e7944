<import name="style" content="./main" />
<import name="LightBuyDialog" content="../component/LightBuyDialog/main" />
<import name="header" content=":component/header/main" />
<div class="page-container page-dgmn-page" sp-on:scroll="onPageScroll">
    <div class="page-header">
        <com:header title="{{state.prevScrollTop > 200?'灯光模拟':' '}}" theme="white" endTheme="white"
            scrollTop="{{state.prevScrollTop}}">
            <div sp:slot="right"></div>
        </com:header>
    </div>

    <div class="page-content">
        <div class="reason-box">
            <div class="title"></div>
            <div class="reason1">
                <div class="reason-title">原因一：<span class="light">车型多 </span>&nbsp; {{state.brandNumber}}种考试车基本覆盖全国
                </div>
                <div class="carType-list">
                    <div class="swiper-container" skip-attribute="class|style" ref="swiper">
                        <div class="swiper-wrapper" skip-attribute="class|style">
                            <sp:each for="state.brandList" value="$item">
                                <div class="swiper-slide">
                                    <sp:each for="$item">
                                        <div class="swiper-item brand-item">
                                            <div class="img" style="background-image: url({{$value.brandIcon}});">
                                            </div>
                                            <div class="dec">{{$value.name}}</div>
                                        </div>
                                    </sp:each>
                                </div>
                            </sp:each>
                        </div>
                    </div>
                    <div class="point-box">
                        <sp:each for="state.brandList">
                            <span class="point {{state.currentIndex === $index?'active':''}}"></span>
                        </sp:each>
                    </div>
                </div>
            </div>
            <div class="reason2">
                <div class="reason-title">
                    原因二：<span class="light">模型真 </span>&nbsp; 按真车等比还原内饰和操作
                </div>
                <div class="modal-list">
                    <div class="modal-item {{state.activeModalIndex === 0?'active':''}}" data-index="0"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">捷达</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 1?'active':''}}" data-index="1"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">老爱丽舍</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 2?'active':''}}" data-index="2"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">起亚k2</div>
                    </div>
                    <div class="open-buy">
                        <div class="buy-box-title">
                            解锁灯光模拟
                        </div>
                        <div class="buy-box-dec">
                            查看全部考试车内饰
                        </div>
                        <sp:if value="{{state.hasPromission}}">
                            <div class="buy-btn" data-fragment="原因二" sp-on:click="goUse">
                                <p >去使用</p>
                            </div>
                            <sp:else />
                            <div class="buy-btn" sp-on:click="openPayModal" data-fragment="原因二">
                                <p >去解锁</p>
                            </div>
                        </sp:if>
                    </div>
                    <!-- <div class="modal-item {{state.activeModalIndex === 3?'active':''}}" data-index="3"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">荣威350</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 4?'active':''}}" data-index="4"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">伊兰特</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 5?'active':''}}" data-index="5"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">铃木尚悦</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 6?'active':''}}" data-index="6"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">比亚迪F3</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 7?'active':''}}" data-index="7"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">吉利帝豪</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 8?'active':''}}" data-index="8"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">奇瑞E3</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 9?'active':''}}" data-index="9"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">标志301</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 10?'active':''}}" data-index="10"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">传祺GA3</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 11?'active':''}}" data-index="11"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">海马福美来</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 12?'active':''}}" data-index="12"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">日产新阳光</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 13?'active':''}}" data-index="13"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">东风风神s30</div>
                    </div>
                    <div class="modal-item {{state.activeModalIndex === 14?'active':''}}" data-index="14"
                        sp-on:click="changeActiveModalIndex">
                        <div class="img"></div>
                        <div class="name">卡罗拉花冠</div>
                    </div> -->
                </div>
            </div>
            <div class="reason3">
                <div class="reason-title">原因三：<span class="light">题库全&nbsp; </span> 当地教练实时更新灯光考题</div>
                <div class="loc"><span class="icon"></span>{{state.cityName}}本地题</div>
                <div class="questions">
                    <div class="question-item">
                        <div class="question-title">下面将进行模拟夜间行驶场景灯光使用的考试，请按语音指令在5秒内做出相应的灯光操作，请开启前照灯。</div>
                        <div class="question-answer">正确步骤：<span class="yellow">近光灯</span></div>
                    </div>
                    <div class="question-item">
                        <div class="question-title">夜间在有路灯的道路上行驶</div>
                        <div class="question-answer">正确步骤：<span class="yellow">近光灯</span></div>
                    </div>
                    <div class="question-item">
                        <div class="question-title">夜间通过没有交通信号灯控制的路口</div>
                        <div class="question-answer">正确步骤：<span class="yellow">近光灯，远近光灯交替</span></div>
                    </div>
                </div>
                <div class="look-more" sp-on:click="lookMore" data-fragment="原因三">查看更多 ></div>
            </div>
        </div>
    </div>
    <sp:if value="{{state.hasPromission}}">
        <div class="open-vip-btn-box" >
            <div class="open-vip-btn" data-fragment="底部吸底按钮" sp-on:click="goUse">去使用灯光模拟</div>
        </div>
        <sp:else />
        <div class="open-vip-btn-box">
            <div sp-on:click="openPayModal" data-fragment="底部吸底按钮" class="open-vip-btn">立即解锁灯光模拟</div>
        </div>
    </sp:if>



    <sp:if value="state.showBuyDialog">
        <div class="showLookAllBuy-mask">
            <div class="showLookAllBuy-box">
                <com:LightBuyDialog fragmentName1="{{state.buyModalFragmentName1}}" close="{{self.closePayModal}}}" />
            </div>
        </div>
    </sp:if>
</div>
