.page-dgmn-page {
    display: flex;
    flex-direction: column;
    overflow: auto;

    .page-header {
        width: 100%;
        height: 300px;
        flex-shrink: 0;
        background: url(../images/page-header.png) no-repeat left top;
        background-size: 100% 100%;
    }

    .page-content {
        flex: 1;
        padding-bottom: 90px;

        background: linear-gradient(180deg, #5bd7ff 0%, #adf5ff 100%);

        .reason-box {
            width: 355px;
            padding: 15px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0px 2px 5px 0px #ffffff inset;
            margin: -25px auto 0;

            .title {
                width: 279px;
                height: 35px;
                background: url(../images/<EMAIL>) no-repeat center center/cover;
                margin: 0 auto;
            }

            .reason-title {
                height: 28px;
                background: linear-gradient(270deg, #fd9f28, #fd7427);
                border-radius: 14px;
                padding-left: 10px;
                display: flex;
                align-items: center;
                font-size: 14px;
                color: white;

                .light {
                    color: #F7FF1A;
                }
            }

            .reason1 {
                margin-top: 10px;

                .carType-list {
                    margin-top: 12px;
                    width: 325px;
                    height: 223px;
                    background: linear-gradient(180deg, #fff6e6, #ffffff);
                    border-radius: 6px;
                    overflow: hidden;

                    .swiper-container {
                        height: 195px;

                        .swiper-wrapper {
                            height: 100%;

                            .swiper-slide {
                                height: 100%;
                            }
                        }
                    }


                    .brand-item {
                        float: left;
                        width: 25%;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        margin-top: 16px;

                        .img {
                            width: 28px;
                            height: 28px;
                            background-position: center center;
                            background-repeat: no-repeat;
                            background-size: cover;
                        }

                        .dec {
                            margin-top: 3px;
                            font-size: 12px;
                            color: #292E50;
                            line-height: 17px;
                        }
                    }

                    .point-box {
                        text-align: center;

                        .point {
                            display: inline-block;
                            width: 5px;
                            height: 4px;
                            opacity: 0.6;
                            background: #fe8409;
                            border-radius: 2px;
                            margin-left: 5px;

                            &.active {
                                width: 10px;
                                opacity: 1;
                            }
                        }
                    }
                }
            }

            .reason2 {
                margin-top: 15px;

                .modal-list {
                    overflow: auto;
                    display: flex;
                    margin-top: 15px;

                    .modal-item {
                        flex-shrink: 0;
                        width: 126px;
                        height: 114px;
                        background: linear-gradient(273deg, #ffe6c5 0%, #ffcc9d 97%);
                        border-radius: 8px;
                        overflow: hidden;

                        &.active {
                            border: 2px solid #ff890f;

                        }

                        .img {
                            height: 86px;
                        }

                        .name {
                            height: 28px;
                            color: #AA4120;
                            font-size: 14px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }

                        &:nth-of-type(n+2) {
                            margin-left: 10px;
                        }

                        &:nth-of-type(1) {
                            .img {
                                background: url(../images/jieda.gif) no-repeat center center/cover;
                            }
                        }

                        &:nth-of-type(2) {
                            .img {
                                background: url(../images/old-ailishe.gif) no-repeat center center/cover;
                            }
                        }

                        &:nth-of-type(3) {
                            .img {
                                background: url(../images/qiyak2.gif) no-repeat center center/cover;
                            }
                        }

                        // &:nth-of-type(4) {
                        //     .img {
                        //         background: url(../images/rongwei350.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(5) {
                        //     .img {
                        //         background: url(../images/yilante.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(6) {
                        //     .img {
                        //         background: url(../images/linmushangyue.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(7) {
                        //     .img {
                        //         background: url(../images/biyadiF3.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(8) {
                        //     .img {
                        //         background: url(../images/jilidihao.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(9) {
                        //     .img {
                        //         background: url(../images/qirui.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(10) {
                        //     .img {
                        //         background: url(../images/biaozhi301.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(11) {
                        //     .img {
                        //         background: url(../images/chuanqiGA3.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(12) {
                        //     .img {
                        //         background: url(../images/haimafumeilai.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(13) {
                        //     .img {
                        //         background: url(../images/richanxinyangguang.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(14) {
                        //     .img {
                        //         background: url(../images/dongfengfengshens30.gif) no-repeat center center/cover;
                        //     }
                        // }

                        // &:nth-of-type(15) {
                        //     .img {
                        //         background: url(../images/kaluolahuaguan.gif) no-repeat center center/cover;
                        //     }
                        // }
                    }

                    .open-buy {
                        width: 126px;
                        height: 114px;
                        background: #ffedd5;
                        border-radius: 8px;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        flex-shrink: 0;
                        margin-left: 10px;
                        color: #AA4120;

                        .buy-box-title {
                            font-size: 14px;
                            font-weight: bold;
                        }

                        .buy-box-dec {
                            font-size: 12px;
                            font-weight: 200;
                            margin-top: 8px;
                        }

                        .buy-btn {
                            width: 66px;
                            height: 24px;
                            background: #bc6546;
                            border-radius: 12px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            font-size: 12px;
                            color: white;
                            margin-top: 10px;
                        }
                    }
                }
            }

            .reason3 {
                margin-top: 20px;

                .loc {
                    font-size: 13px;
                    margin-top: 10px;
                    line-height: 18px;
                    display: flex;
                    align-items: center;

                    .icon {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        background: url(../images/<EMAIL>) no-repeat center center/cover;
                        margin-right: 2px;
                    }
                }

                .questions {
                    .question-item {
                        margin-top: 10px;
                        background: linear-gradient(88deg, #fff3de 2%, #fffaef 99%, #fffaef 99%);
                        border-radius: 6px;
                        padding: 12px 10px;
                        line-height: 18px;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        font-size: 13px;

                        .question-title {
                            color: #333;
                        }

                        .question-answer {
                            margin-top: 5px;

                            .yellow {
                                color: #FF6D40;
                            }
                        }
                    }
                }

                .look-more {
                    font-size: 12px;
                    line-height: 17px;
                    color: #666;
                    text-align: center;
                    margin-top: 15px;
                }
            }
        }

    }

    .showLookAllBuy-mask {
        position: absolute;
        z-index: 100;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);

        .showLookAllBuy-box {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 100;
            max-height: 100%;
            overflow-y: auto;
        }
    }


    .open-vip-btn-box {
        position: absolute;
        z-index: 10;
        bottom: 0;
        left: 0;
        width: 100%;
        background-color: #adf5ff;

        .open-vip-btn {
            width: 335px;
            height: 44px;
            background: linear-gradient(270deg, #fe8604, #ff5900);
            border-radius: 22px;
            margin: 20px auto;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 16px;
        }
    }


}
