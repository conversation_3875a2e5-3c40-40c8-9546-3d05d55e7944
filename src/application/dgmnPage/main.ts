/*
 * ------------------------------------------------------------------
 * 智能练题购买弹窗
 * ------------------------------------------------------------------
 */

import { setPageName, URLCommon, URLParams } from ':common/env';
import jump, { navigateTo, reload } from ':common/features/jump';
import { getBrandList, kq2hList } from ':store/chores';
import BuyDialog, { buyDialogCloseType } from ':component/buyDialog/main';
import { Application } from '@simplex/simple-core';
import LightBuyDialog from './component/LightBuyDialog/main';
import View from './view/main.html';
import { trackEvent, trackGoPay, trackPageLoad } from ':common/stat';
import { getSystemInfo, openVipWebView, openWeb, setStatusBarTheme } from ':common/core';
import { PRACTICE_ALL } from ':common/navigate';
import Swiper from 'swiper';
import 'swiper/swiper-bundle.css';
import { getCityName } from ':common/utils';
import { getGroupSessionInfo, GroupKey } from ':store/goods';
import { ensureSiriusBound, PayBoundType } from ':common/features/pay';

interface State {
    brandList: any[],
    currentIndex: number,
    activeModalIndex: number,
    prevScrollTop: number
    showBuyDialog: boolean
    cityCode: string
    cityName: string
    buyModalFragmentName1: string
    hasPromission: boolean
    brandNumber: number
}

let timer;

export default class extends Application<State> {
    declare children: {
        LightBuyDialog: LightBuyDialog
    }
    Swiper: any;
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            brandList: [],
            currentIndex: 0,
            activeModalIndex: 0,
            prevScrollTop: 0,
            showBuyDialog: false,
            cityCode: '',
            cityName: '',
            buyModalFragmentName1: '',
            brandNumber: 0,
            hasPromission: false
        };

    }
    didMount() {
        setStatusBarTheme('dark');
        setPageName('灯光模拟落地页');

        trackPageLoad();

        this.getSystemInfo();

        this.getBrandList().then(() => {
            setTimeout(() => {
                const $dom = this.getDOMNode().swiper as HTMLElement;

                this.Swiper = new Swiper($dom, {
                    loop: false,
                    on: {
                        slideChangeTransitionEnd: (swiper) => {
                            this.setState({
                                currentIndex: swiper.activeIndex
                            });
                        }
                    }
                });
            }, 100);

        });

        this.isPromission();

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: GroupKey.ChannelKe3
        });
    }
    async getSystemInfo() {
        const systemInfo = await getSystemInfo();
        const cityCode = systemInfo._userCity || systemInfo._cityCode;
        const cityName = await getCityName(+cityCode) || '';

        this.setState({
            cityCode,
            cityName
        });

    }
    getBrandList() {
        return getBrandList().then(data => {
            this.setState({
                brandList: data.arr,
                brandNumber: data.num
            });
        });
    }
    isPromission() {
        getGroupSessionInfo({ groupKeys: [GroupKey.ChannelKe3] }).then(goodsListInfo => {
            if (goodsListInfo[0].bought) {
                this.setState({
                    hasPromission: true
                });
            }

        });
    }
    closePayModal = (closeType) => {
        this.setState({
            showBuyDialog: false
        });
        if (closeType === buyDialogCloseType.BOUGHT) {
            setTimeout(() => {
                reload();
            }, 2000);
        }

    }

    openPayModal(e) {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');

        trackGoPay({
            groupKey: GroupKey.ChannelKe3,
            payPathType: 0,
            fragmentName1
        });
   
        this.setState({
            showBuyDialog: true,
            buyModalFragmentName1: fragmentName1
        }, () => {
            this.children.LightBuyDialog.setPageInfo();
        });

    }
    changeActiveModalIndex(e) {
        const { activeModalIndex } = this.state;
        const index = +e.refTarget.getAttribute('data-index');

        if (index !== activeModalIndex) {
            this.setState({
                activeModalIndex: index
            });
        }
    }
    onPageScroll(e) {
        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            this.setState({
                prevScrollTop
            });
        }, 100);
    }
    goUse(e) {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        trackEvent({
            fragmentName1,
            actionType: '点击',
            actionName: '去使用'
        });

        openWeb({
            url: 'http://jiakao.nav.mucang.cn/lightSimulation'
        });
    }
    lookMore(e) {
        if (this.state.hasPromission) {
            const fragmentName1 = e.refTarget.getAttribute('data-fragment');
            trackEvent({
                fragmentName1,
                actionType: '点击',
                actionName: '查看更多'
            });
            
            openWeb({
                url: 'http://jiakao.nav.mucang.cn/lightSimulation'
            });
        } else {
            this.openPayModal(e);
        }
    }
}
