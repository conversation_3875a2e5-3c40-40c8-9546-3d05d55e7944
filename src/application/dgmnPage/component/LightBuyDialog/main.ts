/*
 * ------------------------------------------------------------------
 * 科三路线
 * ------------------------------------------------------------------
 */
import { MCProtocol } from '@simplex/simple-base';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PayTypeComponent from ':component/payType/main';
import { getAuthToken, getSystemInfo, webClose } from ':common/core';
import { LangType, PayType, Platform, URLCommon, URLParams, Version } from ':common/env';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackGoPay } from ':common/stat';
import { PROTOCOL1_URL, PROTOCOL2_URL } from ':common/navigate';
import { comparePrice, getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { typeCode } from ':common/features/bottom';
import { iosDialogBuySuccess } from ':common/features/ios_pay';
import { buyDialogCloseType } from ':component/buyDialog/main';
import { formatPrice } from ':common/utils';

enum typeMap {
    page = 'page',
    component = 'component'
}

interface State {
    tabIndex: number,
    placeInfo: {
        cityCode: string
        cityName: string
    }
    goodsInfoPool: GoodsInfo[],
    couponPool: any,
    comparePricePool: any,
}
interface Props {
    type: typeMap,
    show: boolean,
    fragmentName1: string
    close(typeMap?)
}

const fragmentName2 = '支付弹窗';

export default class extends Component<State, Props> {

    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog
        payType: PayTypeComponent
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
    * 如果有优惠券的价格为0的就显示0
    * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
   */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            placeInfo: null,
            goodsInfoPool: [{
                groupKey: GroupKey.ChannelKe3
            } as GoodsInfo,
            {
                groupKey: (() => {
                    let key = GroupKey.ChannelKe3Group;
                    if (Platform.isWeiyu) {
                        key = GroupKey.ChannelKe3RouteWeiyu;
                        if (URLParams._lang && URLParams._lang === LangType.ZH) {
                            key = GroupKey.ChannelKe3Route;
                        }
                    }

                    return key;
                })()
            } as GoodsInfo,
            {
                groupKey: GroupKey.ChannelKe34
            } as GoodsInfo
            ],
            comparePricePool: {},
            couponPool: {}
        };

    }
    async didMount() {

        this.getGoodInfo();

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: this.nowGoodInfo.groupKey
        });
    }
    tabChange = (e) => {
        const index = +e.refTarget.getAttribute('data-type');

        this.setState({
            tabIndex: index
        });

        this.setPageInfo();
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const groupKeys: GroupKey[] = [];
        const newGoodsPool = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {

            if (goodsListInfo[0].bought) {
                this.close();
                return;
            }

            goodsListInfo.forEach((goodInfo, index) => {

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            this.setState({
                goodsInfoPool: newGoodsPool
            });

            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getComparePrice();

                this.setPageInfo();
            }, 60);

        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice,
                        groupItems: item.groupItems
                    };
                }
            });

            this.setState({ comparePricePool });
        });
    }
    setPageInfo(stat?: { fragmentName1?: string, fragmentName2?: string }) {
        this.setBuyBottom(stat);
    }
    setBuyBottom(stat) {
        const fragmentName1 = this.props.fragmentName1 || '底部吸底按钮';
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const bottomType: typeCode = typeCode.type4;

        this.children.buyButton.setPay({
            isInDialog: true,
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                this.close(buyDialogCloseType.BOUGHT);
            }
        });

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1,
                    fragmentName2,
                    ...stat
                });
                break;
            default:
                break;
        }
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    pay = async (stat: PayStatProps) => {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo?.couponCode,
            ...stat
        }, false).then(() => {
            this.close(buyDialogCloseType.BOUGHT);
        });
    }
    close(closeType?) {
        const { type, close } = this.props;

        this.children.buyButton.hideButton();

        close && close(closeType);

    }
    willReceiveProps() {
        return true;
    }
}
