.route-pay-dialog {
    background: #ffffff;
    display: flex;
    flex-direction: column;
    max-height: 100%;

    .hedaer-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #F2F2F2;
        flex-shrink: 0;

        .content {
            padding: 11px 6px 8px 6px;
            flex: 1;
            overflow: hidden;
        }

        .title {
            padding-left: 9px;
            border-radius: 30px;
            background-color: #fffaef;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 30px;

            .msg {
                width: 16px;
                height: 16px;
                background: url(../images/msg.png) no-repeat left center;
                background-size: 16px 16px;
            }

            .txt {
                color: #333333;
                font-size: 12px;
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                padding-left: 8px;
                display: block;
            }

            .btn {
                background-color: rgba(243, 232, 188, .5);
                color: #634A12;
                font-size: 14px;
                line-height: 20px;
                padding: 5px 10px 4px 12px;
                border-radius: 30px;
                height: 30px;
                box-sizing: border-box;
            }
        }

        .title1 {
            font-size: 16px;
            padding: 5px 10px;
            color: #333333;
        }

        .close {
            width: 20px;
            height: 20px;
            padding: 5px 15px 5px 5px;
            background: url(../images/close.png) no-repeat center;
            background-size: 20px 20px;
            margin-right: 15px;
        }
    }

    .part-c {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow-y: auto;

        .pay-con {
            padding: 0 15px;
        }

        .good-item {
            margin-top: 15px;
            height: 76px;
            padding: 10px 15px;
            box-sizing: border-box;
            border: 1px solid #DDDDDD;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .good1 {
                .p1 {
                    color: #333333;
                    font-size: 18px;
                    font-weight: 600;
                }

                .dec {
                    color: #999;
                    font-size: 12px;
                    margin-top: 6px;
                }

                // .p2 {
                //   padding-top: 4px;
                //   color: #333333;
                //   font-size: 12px;

                //   b {
                //     color: #F25146;
                //   }
                // }
            }

            .good2 {
                padding-top: 5px;
                padding-left: 10px;

                .p1 {
                    flex: 1;
                    color: #333333;
                    font-weight: bold;
                    text-align: right;

                    i {
                        font-size: 18px;
                        padding-right: 4px;
                    }

                    label {
                        font-size: 26px;
                    }
                }

                .p2 {
                    color: #A0A0A0;
                    font-size: 12px;
                    text-decoration: line-through;
                }
            }

            &.active {
                border: 1px solid #F25247;
                background: url(../images/selected.png) no-repeat top right;
                background-color: #FFF4F4;
                background-size: 30px 31px;

                .price {
                    color: #F25247 !important;
                }
            }
        }

        .good-item1 {
            margin-top: 15px;
            padding: 10px 15px;
            border: 1px solid #DDDDDD;
            border-radius: 4px;

            .good1 {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .p1 {
                    font-size: 18px;
                    line-height: 1;
                    font-weight: bold;
                    color: #333333;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .compare_price {
                        display: inline-block;
                        margin-left: 5px;
                        width: 104px;
                        height: 19px;
                        background: url(../images/compare_price.png) no-repeat center center/100% 100%;

                        &.upgrade {
                            background: url(../images/6.png) no-repeat center center/100% 100%;
                        }
                    }
                }

                .p2 {
                    i {
                        color: #333333;
                        font-size: 18px;
                        font-weight: bold;
                        padding-right: 4px
                    }

                    color: #333333;
                    font-size: 26px;
                    font-weight: bold;
                }
            }

            .good2 {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 17px;

                .auth-item {
                    text-align: center;

                    .img {
                        display: inline-block;
                        width: 36px;
                        height: 36px;
                        position: relative;


                    }

                    .auth-info {
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;


                        .auth-dec {
                            font-size: 11px;
                            color: #6E6E6E;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }

                    &:nth-of-type(1) {
                        .img {
                            background: url(../images/<EMAIL>) no-repeat center center/cover;
                        }
                    }

                    &:nth-of-type(2) {
                        .img {
                            background: url(../images/<EMAIL>) no-repeat center center/cover;

                        }
                    }

                    &:nth-of-type(3) {
                        .img {
                            background: url(../images/<EMAIL>) no-repeat center center/cover;

                            .tip {
                                position: absolute;
                                top: 0;
                                right: 0;
                                transform: translate(70%, -50%);
                                width: 48px;
                                height: 15px;
                                background: url(../images/<EMAIL>) no-repeat center center/100% 100%;
                            }
                        }
                    }

                    &:nth-of-type(4) {
                        .img {
                            background: url(../images/<EMAIL>) no-repeat center center/cover;
                        }
                    }

                }

                &.interact {
                    .auth-item {
                        &:nth-of-type(2) {
                            .img {
                                background: url(../images/<EMAIL>) no-repeat center center/cover;

                                // .tip {
                                //     position: absolute;
                                //     top: 0;
                                //     right: 0;
                                //     transform: translate(70%, -50%);
                                //     width: 48px;
                                //     height: 15px;
                                //     background: url(../images/<EMAIL>) no-repeat center center/100% 100%;
                                // }
                            }
                        }

                        &:nth-of-type(4) {
                            .img {
                                background: url(../images/<EMAIL>) no-repeat center center/cover;
                            }
                        }
                    }
                }
            }

            &.active {
                border: 1px solid #F25247;
                background: url(../images/selected.png) no-repeat top right;
                background-color: #FFF4F4;
                background-size: 30px 31px;

                .price {
                    color: #F25247 !important;
                }
            }
        }

        .good-item2 {
            margin-top: 15px;
            padding: 13px 15px;
            border: 1px solid #DDDDDD;
            border-radius: 4px;

            .goods {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .p1 {
                    font-size: 18px;
                    display: flex;
                    align-items: center;
                    font-weight: bold;
                    color: #333333;
                    white-space: nowrap;

                    .compare_price {
                        display: inline-block;
                        margin-left: 5px;
                        width: 104px;
                        height: 19px;
                        background: url(../images/compare_price1.png) no-repeat center center/100% 100%;
                    }
                }

                .p2 {
                    color: #333333;
                    font-size: 26px;
                    font-weight: bold;
                }
            }

            .equity {
                display: flex;
                justify-content: space-between;
                margin-top: 10px;

                .item {
                    width: 98px;
                    height: 44px;
                    background-color: #FCF1EE;
                    background-size: 18px 24px;
                    background-repeat: no-repeat;
                    background-position: bottom right;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &:nth-of-type(1) {
                        background-image: url(../images/3.png);
                    }

                    &:nth-of-type(2) {
                        background-image: url(../images/4.png);
                    }

                    &:nth-of-type(3) {
                        background-image: url(../images/5.png);
                    }

                    .info {
                        margin-left: 11px;

                        .name {
                            font-size: 13px;
                            font-weight: 500;
                            color: #464646;
                            line-height: 18px;
                        }

                        .prize {
                            font-size: 10px;
                            font-weight: 400;
                            color: #6E6E6E;
                        }

                    }

                    .dec {
                        font-size: 12px;
                        color: #464646;
                        padding: 0 15px;
                        line-height: 17px;
                        text-align: center;
                    }
                }
            }

            &.active {
                border: 1px solid #F25247;
                background: url(../images/selected.png) no-repeat top right;
                background-color: #FFF4F4;
                background-size: 30px 31px;

                .price {
                    color: #F25247 !important;
                }
            }
        }

    }

    .dialog-footer {
        flex-shrink: 0;
    }

    .part3 {
        padding-left: 25px;
        display: flex;
        align-items: center;
    }

    .rc-w {
        padding-left: 15px;
        display: flex;
        justify-content: space-between;

        .rc {
            line-height: 14px;
            color: #F25247;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-right: 15px;
        }

    }

    .part4 {
        padding: 15px 15px 20px 15px;
        box-sizing: border-box;
        flex-shrink: 0;

        .pay-btn {
            height: 44px;
            background: linear-gradient(90deg, #FF8149 0%, #FF2803 100%);
            border-radius: 44px;
            color: #FFFFFF;
            line-height: 44px;
            text-align: center;
            font-size: 17px;
            position: relative;
        }
    }
}
