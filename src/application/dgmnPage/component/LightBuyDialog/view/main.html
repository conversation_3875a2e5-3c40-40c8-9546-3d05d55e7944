<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="payType" content=":component/payType/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />

<div class="route-pay-dialog">
    <div class="hedaer-wrap">
        <div class="content">
            <p class="title1">立即解锁</p>
        </div>
        <label class="close" sp-on:click="close"></label>
    </div>
    <div class="part-c">
        <div class="pay-con">
            <div class="good-item {{state.tabIndex == 0 ? 'active': ''}}" sp-on:click="tabChange" data-type="0">
                <div class="good1">
                    <p class="p1">{{state.goodsInfoPool[0].name}}</p>
                    <div class="dec">含灯光模拟和科三3D模拟</div>
                </div>
                <div class="good2">
                    <p class="p1">
                        <i class="price">¥</i><label class="price">{{state.goodsInfoPool[0].payPrice ||
                                '--'}}</label>
                    </p>
                </div>
            </div>
            <sp:if value="state.goodsInfoPool[1]">
                <div class="good-item1 {{state.tabIndex == 1 ? 'active': ''}}" sp-on:click="tabChange" data-type="1">
                    <div class="good1">
                        <p class="p1">
                            {{state.goodsInfoPool[1].name}}
                            <span class="compare_price {{state.goodsInfoPool[1].upgrade?'upgrade':''}}"></span>
                        </p>
                        <p class="p2">
                            <i class="price">¥</i><label class="price">{{state.goodsInfoPool[1].payPrice ||
                                    '--'}}</label>
                        </p>
                    </div>
                    <div class="good2">
                        <div class="auth-item">
                            <div class="img"></div>
                            <div class="auth-info">
                                <div class="auth-dec">考场实拍</div>
                            </div>
                        </div>
                        <div class="auth-item">
                            <div class="img"></div>
                            <div class="auth-info">
                                <div class="auth-dec">3D练车</div>
                            </div>
                        </div>
                        <div class="auth-item">
                            <div class="img">
                            </div>
                            <div class="auth-info">
                                <div class="auth-dec">灯光模拟</div>
                            </div>
                        </div>
                        <div class="auth-item">
                            <div class="img"></div>
                            <div class="auth-info">
                                <div class="auth-dec">必考项目</div>
                            </div>
                        </div>
                    </div>
                </div>
            </sp:if>

            <sp:if value="state.goodsInfoPool[2]">
                <div class="good-item2 {{state.tabIndex == 2 ? 'active': ''}}" sp-on:click="tabChange" data-type="2">
                    <div class="goods">
                        <p class="p1">
                            {{state.goodsInfoPool[2].name}}
                            <span class="compare_price"></span>
                        </p>
                        <p class="p2">
                            <i class="price">¥</i><label class="price">{{state.goodsInfoPool[2].payPrice ||
                                    '--'}}</label>
                        </p>
                    </div>
                    <div class="equity">
                        <sp:each for="state.comparePricePool[state.goodsInfoPool[2].groupKey].groupItems" value="item"
                            index="i">
                            <sp:if value="item.price && i<3">
                                <div class="item">
                                    <div class="info">
                                        <div class="name">
                                            {{item.name}}
                                        </div>

                                        <div class="prize">
                                            单卖￥{{item.price}}
                                        </div>
                                    </div>
                                </div>
                                <sp:elseif value="i<3" />
                                <div class="item">
                                    <div class="dec">
                                        {{item.name}}{{item.description}}
                                    </div>
                                </div>
                            </sp:if>
                        </sp:each>
                    </div>
                </div>
            </sp:if>
        </div>
    </div>

    <com:buyButton>
        <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
            {{self.nowCouponInfo && self.nowCouponInfo.couponCode?'已优惠' +
            self.nowCouponInfo.priceCent + '元':'领取优惠券'}}
        </div>
    </com:buyButton>
    <com:payDialog />
</div>
