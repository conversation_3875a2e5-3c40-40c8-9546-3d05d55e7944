/*
 * main
 *
 * name: xia<PERSON><PERSON>a
 * date: 16/3/24
 */

import { URLCommon, URLParams } from ':common/env';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
interface State {
    passRate: number;
}
interface Props { }
export default class extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            passRate: 0
        };
        this.props = {};
    }
    didMount() {
        this.getKe14PassRate();
    }
    handleShowData(passRate) {
        if (passRate <= 0) {
            return 0;
        }
        let newpassRate = String(passRate);
        if (newpassRate.indexOf('.') !== -1) {
            newpassRate = newpassRate.substring(
                0,
                newpassRate.indexOf('.') + 2
            );
        }
        return Number(newpassRate) >= 100 ? 100 : Number(newpassRate);
    }
    // 获取科一科四通过率
    getKe14PassRate() {
        MCProtocol.Vip.getPassRate({
            car: URLCommon.tiku,
            kemu: +URLCommon.kemu,
            kemuStyle: +URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            callback: (ret) => {
                const passRate = this.handleShowData(((ret.data * 1000) / 10) || 0);
                this.setState({ passRate: passRate });
            }
        });
    }
    willReceiveProps() {
        return true;
    }
}
