.pass-rate-contanier {
    min-height: 226px;
    background: #FFFFFF;
    box-shadow: inset 1px 1px 0px 0px rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 15px 15px 40px 15px;
    margin: 15px;
    margin-top: -110px;
    width: calc(100% - 30px);

    .title {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #191D2A;
        line-height: 25px;
    }

    .desc-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15px;

        .left {
            font-size: 46px;
            font-family: DINAlternate-Bold, DINAlternate;
            font-weight: bold;
            color: #FF7237;
            padding-right: 25px;
            text-align: center;

            &.success {
                color: #04A5FF
            }

            .fuhao {
                font-size: 30px;
            }
        }

        .right {
            flex: 1;
          

            .right-title {
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #354152;
                line-height: 18px;
                margin-bottom: 8px;
            }

            .right-desc {
                width: 219px;
                padding: 5px 8px 6px 7px;
                background: rgba(4, 165, 255, 0.09);
                border-radius: 4px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #04A5FF;
                line-height: 17px;
                width: 100%;
                &.right-desc-error {
                    background:rgba(255, 145, 55, 0.09);
                    color: #FF7237;
                }
            }
        }
    }

    .pass-rate-progress {
       width: 100%;
        height: 6px;
        background: linear-gradient(117deg, #FE80CB 0%, #60C4FE 56%, #0489FF 100%);
        border-radius: 4px;
        margin-top: 30px;
        position: relative;

    }

    .rate {
        position: absolute;
        top: 15px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #191D2A;
    }

    .lower {
        left: 0px;
    }

    .low {
        left: 30%;
    }

    .higher {
        left: 50%;
    }

    .up {
        left: 90%;
        width: 28px;
        margin-left: -14px;
        color: #04A5FF;
    }

    .up-to {
        left: 95%;
        width: 28px;
        top:-14px;

    }

    .up-style {
        height: 18px;
        width:18px;
        background: #04A5FF;
        box-shadow: 0px 1px 3px 0px rgba(32, 155, 255, 0.22);
        border: 4px solid #FFFFFF;
        position: absolute;
        left: 90%;
        top: -9px;
        margin-left: -9px;
        border-radius: 50%;
       
    }

    .up-arrive {
        position: absolute;
        top:-16px;
        width:1px;
        height:22px;
        background: #04A5FF;
         &::after {
            display: inline-block;
            content: '';
             width: 6px;
             height: 6px;
             border: 3px solid #04A5FF;
            background: #FFFFFF;
            position: absolute;
            border-radius: 50%;
            box-shadow: 0px 1px 3px 0px rgba(32, 155, 255, 0.22);
            left: -5.5px;
            top:-12px;
        }
    }
       
       

}