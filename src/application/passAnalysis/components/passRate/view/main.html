<import name="style" content="./main" module="S" />
<div class=":pass-rate-contanier">

    <div class=":title">
        <sp:if value='{{URLCommon.isScore12}}'>
            当前满分学习考试预测通过率
            <sp:else />
            当前{{Texts.currentKemuTxt}}预测通过率
        </sp:if>
    </div>
    <div class=":desc-container">
        <div class=":left {{state.passRate>=90?S.success:''}}">{{state.passRate}}<span class=":fuhao">%</span></div>
        <div class=":right">
            <sp:if value='{{state.passRate<90}}'>
                <p class=":right-title">您的预测通过率未达到合格标准，还有很大的提升空间。</p>
                <sp:if value='{{!props.hasDay||props.hasDay=="今天"}}'>
                    <p class=":right-desc {{S.rightDescError}}">您需要将通过率提升至合格，才能参加正式考试</p>
                    <sp:else />
                    <p class=":right-desc {{S.rightDescError}}">您需要在仅剩的{{props.hasDay}}天内，将通过率提升至合格，才能参加正式考试</p>
                </sp:if>
                <sp:else />
                <p class=":right-title">您的预测通过率达到合格标准，考前巩固更有底气。</p>
                <sp:if value='{{props.hasDay=="今天"}}'>
                    <p class=":right-desc">放轻松，不紧张，预祝您今天考试成功</p>
                    <sp:else />
                    <p class=":right-desc">考前多巩固，扫清错题，考试更有信心</p>
                </sp:if>

            </sp:if>


        </div>
    </div>
    <div class=":pass-rate-progress">
        <div class=":rate {{S.lower}}">非常低</div>
        <div class=":rate {{S.low}}">很低</div>
        <div class=":rate {{S.higher}}">不高</div>
        <div class=":rate {{S.up}}">合格</div>
        <div class=":rate {{S.upTo}}">很高</div>
        <div class=":up-style"></div>
        <div class=":up-arrive" style="left: {{state.passRate}}%">
        </div>
    </div>
</div>