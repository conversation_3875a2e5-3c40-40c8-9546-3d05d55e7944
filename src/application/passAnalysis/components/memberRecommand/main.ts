/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */

import { URLCommon } from ':common/env';
import { Component } from '@simplex/simple-core';
import { openWeb } from ':common/core';
import View from './view/main.html';
import { getMemberRecomand } from ':store/passAnalysis';
interface State {
    recommendVip: any[];
}
interface Props {

}
export default class extends Component<State, Props> {
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            recommendVip: []
        };
        this.props = {

        };

    }
    async didMount() {
        const memberRecomandData = await getMemberRecomand({
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu
        });
        const reg = /!png$/;
        memberRecomandData.itemList && memberRecomandData.itemList.forEach((res) => {
            res.coverImg = res.coverImg?.replace(reg, '');
        });
        this.setState({ recommendVip: memberRecomandData.itemList || [] });
    }
    gotoRecommend(e) {
        const gotoUrl = e.refTarget.getAttribute('data-href');
        // window.location.href = gotoUrl;
        openWeb({
            url: gotoUrl
        });
    }
    willReceiveProps() {
        return true;
    }
}
