/*
 * main
 *通过率分析
 * name: xiaojia
 * date: 16/3/24
 */
import { Component } from '@simplex/simple-core';
import { openWeb, openVipWebView } from ':common/core';
import { CarType, URLCommon, URLParams } from ':common/env';
import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
import { dateFormat, getHasDay, handleIosZigezhen } from ':common/utils';
import PassRate from ':application/passAnalysis/components/passRate/main';
import StudyStep from ':application/passAnalysis/components/studyStep/main';
import { AGED_500, BUYED_URL, EXAM_JUAN, JING_JIANG } from ':common/navigate';
import { scrollTop } from ':common/features/dom';

const knowledgePointCount = 4;
interface State {
    tabIndex: number;
    tabArray: any[];
    normalReport: any;
    vipReport: any;
    novipReport: any;
    examReport: any;
    knowledgePointReport: any;
    errorReport: any;
    milepostIndex: number;
}

interface Props {
    examTime: number;
    isVip: boolean;
    isFixedTab: boolean;
    parentSelf: any,
    goAuth?(any);
    selectExamTime?();
    tabChange?(any);
    payBtnCall?(e: Event);
}

export default class extends Component<State, Props> {
    declare children: {
        passRate: PassRate;
        studyStep: StudyStep;
    };
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            tabArray: [
                { index: 0, name: '练习情况', id: '#practice' },
                { index: 1, name: '考试情况', id: '#exam' },
                { index: 2, name: '知识点掌握情况分析', id: '#knowledge' }
            ],
            normalReport: {},
            vipReport: {},
            novipReport: {},
            examReport: {},
            knowledgePointReport: {},
            errorReport: {},
            milepostIndex: 1
        };
    }
    // 最近5次的
    get recentTimes() {
        return 5;
    }
    get getNowTime() {
        return dateFormat(new Date(), 'yyyy-MM-dd');
    }
    get getHandleTime() {
        const remainDay = this.hasDay;
        let time: string;
        const one = 1 * 24 * 60 * 60 * 1000;
        const three = 3 * 24 * 60 * 60 * 1000;
        if (remainDay === '今天') {
            time = '今天';
        } else {
            time = dateFormat(this.props.examTime, 'M.d');
        }
        const time1 = dateFormat(this.props.examTime - one, 'M.d');
        const time2 = dateFormat(this.props.examTime - one, 'M.d');
        const time3 = dateFormat(this.props.examTime - three, 'M.d');
        return {
            time,
            time1,
            time2,
            time3
        };
    }
    // 剩余天数
    get hasDay() {
        const examTime = dateFormat(this.props.examTime, 'yyyy-MM-dd');
        const now = dateFormat(new Date().getTime(), 'yyyy-MM-dd');
        if (!examTime) {
            return '';
        }
        const remainTime =
            this.props.examTime - new Date(new Date().toDateString()).getTime();
        if (remainTime <= 0) {
            return '';
        }

        if (examTime === now) {
            return '今天';
        }
        return getHasDay(remainTime);
    }
    get getExamTime() {
        const remainDay = this.hasDay;
        if (!remainDay) {
            return '?月?日';
        }
        if (remainDay === '今天') {
            return '今天';
        }
        const examTime = dateFormat(this.props.examTime, 'M.d');
        return examTime;
    }
    async didMount() {
        this.getisNoVip();
        this.getmcProtocol();
    }
    public visibilityStateMethod() {
        this.getisNoVip();
        this.getmcProtocol();
        this.children.passRate.getKe14PassRate();
        this.children.studyStep &&
            this.children.studyStep.getKe14LearningData(URLCommon.kemu);
    }
    tabChange(e) {
        const index = e.refTarget.getAttribute('data-index');
        const scrollTop0 = document.getElementById('practice')?.offsetTop;
        const scrollTop1 = document.getElementById('exam')?.offsetTop;
        const scrollTop2 = document.getElementById('knowledge')?.offsetTop;
        const distanceArray = [scrollTop0, scrollTop1, scrollTop2];
        let pageHeader = 0;
        if (URLParams.passShowType === 'dialog') {
            pageHeader =
                (this.parent.getDOMNode().dialogHeader as HTMLElement)?.offsetHeight;
        } else {
            pageHeader =
                (this.props.parentSelf.children.header.getDOMNode().header as HTMLElement)?.offsetHeight;
        }
        const tabChangeContainer = (this.getDOMNode().tabChangeContainer as HTMLElement)?.offsetHeight;
        let allHeight = pageHeader + tabChangeContainer + 30;
        if (!this.props.isFixedTab) {
            allHeight = pageHeader + tabChangeContainer + tabChangeContainer;
        }
        scrollTop(this.parent.getDOMNode().bodyPanelContainer as HTMLElement, distanceArray[index] - allHeight);
    }
    milepostChange(e) {
        const index = e.refTarget.getAttribute('data-index');
        this.setState({ milepostIndex: index });
    }
    selectExamTime() {
        this.props.selectExamTime && this.props.selectExamTime();
    }
    gotoVipBuyed() {
        if (this.props.isVip) {
            openVipWebView({
                // eslint-disable-next-line max-len
                url:
                    BUYED_URL +
                    '?inletSource=' +
                    URLParams.inletSource +
                    '&_appName=' +
                    URLParams._appName +
                    '&_version=' +
                    URLParams._version +
                    '&authToken=' +
                    URLParams.authToken +
                    '&bizCode=' +
                    URLParams.bizCode +
                    '&bizVersion=' +
                    URLParams.bizVersion +
                    '&carStyle=' +
                    URLParams.carStyle +
                    '&carType=' +
                    URLParams.carStyle +
                    '&from=' +
                    URLParams.from +
                    '&fromPathCode=' +
                    URLParams.fromPathCode +
                    '&id=' +
                    URLParams.id +
                    '&kemu=' +
                    URLParams.kemu +
                    '&kemuStyle=1&patternCode=' +
                    URLParams.patternCode +
                    '&sceneCode=' +
                    URLParams.sceneCode
            });
        }
    }
    handleShowData(doneCount, totalCount) {
        if (totalCount <= 0) {
            return 0;
        }
        // eslint-disable-next-line
        let count = (doneCount / totalCount) * 100 + '';
        if (count.indexOf('.') !== -1) {
            count = count.substring(0, count.indexOf('.') + 2);
        }
        const showCount = Number(count) >= 100 ? 100 : Number(count);
        return showCount;
    }
    getisNoVip() {
        let totalCount;
        if (URLCommon.isScore12) {
            switch (URLCommon.tiku) {
                case CarType.CAR:
                case CarType.TRUCK:
                case CarType.BUS:
                    totalCount = 600;
                    break;
                case CarType.MOTO:
                    totalCount = 200;
                    break;
                default:
                    break;
            }
        } else {
            switch (URLCommon.tiku as string) {
                case CarType.CAR:
                    totalCount = 500;
                    break;
                case CarType.TRUCK:
                case CarType.BUS:
                    totalCount = 600;
                    break;
                case CarType.MOTO:
                    totalCount = URLCommon.kemu === 1 ? 120 : 100;
                    break;
                case CarType.GUACHE:
                    totalCount = 500;
                    break;
                case CarType.KEYUN:
                case CarType.JIAOLIAN:
                case CarType.CHACHE:
                    totalCount = 500;
                    break;
                default:
                    totalCount = 200;
                    break;
            }
        }

        const newvipReport = {
            doneCount: 0,
            totalCount: totalCount,
            isComplate: false,
            progress: 0
        };
        this.setState({ novipReport: newvipReport });
    }
    getmcProtocol() {
        MCProtocol.data.practice.normalReport({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            callback: (res) => {
                const newnormalReport = res.data || {};
                console.log('错题分步的数据normalReport', JSON.stringify(newnormalReport));
                newnormalReport.progress = this.handleShowData(
                    newnormalReport.doneCount || 0,
                    newnormalReport.totalCount || 0
                );
                if (newnormalReport.totalCount > 0) {
                    newnormalReport.isComplate =
                        newnormalReport.doneCount >= newnormalReport.totalCount;
                }
                this.setState({ normalReport: newnormalReport });
            }
        });
        MCProtocol.data.practice.vipReport({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            callback: (res) => {
                const newvipReport = res.data || {};
                console.log('practice.vipReport初始值', JSON.stringify(newvipReport));
                newvipReport.progress = this.handleShowData(
                    newvipReport.doneCount || 0,
                    newvipReport.totalCount || 0
                );
                if (newvipReport.totalCount > 0) {
                    newvipReport.isComplate =
                        newvipReport.doneCount >= newvipReport.totalCount;
                }
                this.setState({ vipReport: newvipReport });
            }
        });
        MCProtocol.data.exam.report({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            recentTimes: this.recentTimes,
            callback: (res) => {
                const examData = res.data || {};
                let score = 0;
                examData.records &&
                    examData.records.forEach((res) => {
                        score += +res.score;
                    });

                if (examData.records.length === 1) {
                    examData.recentscore = 0;
                } else {
                    examData.recentscore = (
                        score / examData.records.length || 0
                    ).toFixed(0);
                }
                if (examData.recentscore >= 100) {
                    examData.recentscore = 100;
                } else if (examData.recentscore <= 0) {
                    examData.recentscore = 0;
                }
                this.setState({ examReport: examData });
            }
        });
        MCProtocol.data.practice.knowledgePointReport({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            recentTimes: this.recentTimes,
            callback: (res) => {
                console.log('zhishidian', JSON.stringify(res.data));
                this.setState({
                    knowledgePointReport: res.data || {}
                });
            }
        });
        MCProtocol.data.practice.errorReport({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            knowledgePointCount: knowledgePointCount,
            callback: (res) => {
                const errorReport = res.data || {};
                console.log('错题分步的数据errorReport', JSON.stringify(errorReport));
                this.setState({ errorReport });
            }
        });
    }
    vipCountStudent() {
        let url;
        if (URLCommon.tiku === CarType.CAR) {
            // location.href = URLCommon.isElder ? AGED_500 : JING_JIANG;
            url = URLCommon.isElder ? AGED_500 : JING_JIANG;
        } else {
            // location.href = JING_JIANG;
            url = JING_JIANG;
        }
        if (handleIosZigezhen(url)) {
            return;
        }
        openWeb({
            url: url
        });
    }
    gotoexam() {
        // location.href = EXAM_JUAN;
        if (handleIosZigezhen(EXAM_JUAN)) {
            return;
        }
        openWeb({
            url: EXAM_JUAN
        });
    }
    gotoErrDetail(e) {
        const id = e.refTarget.getAttribute('data-id');
        // window.location.href =
        //     'http://jiakao.nav.mucang.cn/knowledge-detail?knowledgeId=' +
        //     id +
        //     '&showRelatedQuestions=true';
        const url = 'http://jiakao.nav.mucang.cn/knowledge-detail?knowledgeId=' +
            id +
            '&showRelatedQuestions=true';
        if (handleIosZigezhen(url)) {
            return;
        }
        openWeb({
            url: url
        });
    }
    // 为熟练跳转，未做跳转
    unskilledMethod(e) {
        // window.location.href = 'http://jiakao.nav.mucang.cn/knowledge-list';
        const type = e.refTarget.getAttribute('data-type');
        const url = 'http://jiakao.nav.mucang.cn/knowledge-list?tab=' + type;
        if (handleIosZigezhen(url)) {
            return;
        }
        openWeb({
            url: url
        });
    }
    // 错题跳转 错误率跳转
    errorCountMethod() {
        // window.location.href = 'http://jiakao.nav.mucang.cn/myError';
        const url = 'http://jiakao.nav.mucang.cn/myError';
        if (handleIosZigezhen(url)) {
            return;
        }
        openWeb({ url: url });
    }
    willReceiveProps() {
        return true;
    }
}
