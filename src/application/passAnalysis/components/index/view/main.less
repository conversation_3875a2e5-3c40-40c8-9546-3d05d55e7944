.panel-home-index {
    padding-bottom: 40px;
    background: #e5f3ff;
    &.zhibojian-page-index {
        padding-bottom: 20px;
    }

    .panel-page-header-container {
        position: relative;
    }

    .panel-page-header {
        background-repeat: no-repeat;
        height: 300px;
        background-size: cover;
        background-image: url(../images/top_bj.png);
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 90px 15px 0px 15px;
        &.zhi<PERSON><PERSON><PERSON> {
            padding-top: 30px;
            height: 260px;
        }
        &.show-dialog-style {
            padding-top: 60px;
            height: 270px;
            // height: 159px;
        }

        .panel-page-header-left {
            .logo {
                width: 113px;
                height: auto;
                // height: 21px;
                // background-repeat: no-repeat;
                // background-size: cover;
                // background-image: url(../images/logo.png);
                margin-bottom: 10px;
            }

            .title {
                font-size: 15px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #191d2a;
                margin-bottom: 7px;
                line-height: 21px;

                .vip-bages {
                    height: 18px;
                    margin-left: 2px;
                    vertical-align: middle;
                }
            }

            .desc {
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #191d2a;
            }
        }

        .panel-page-header-right {
            .content {
                width: 118px;
                height: 83px;
                text-align: center;
                background: linear-gradient(198deg, #6dc9ff 0%, #24b8ff 100%);
                border-radius: 6px;
                position: relative;

                .time {
                    font-size: 27px;
                    font-family: PingFangSC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #ffffff;
                    padding-top: 25px;
                    &.tips-font {
                        font-size: 25px;
                    }
                }

                .time-desc {
                    font-size: 11px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: rgba(255, 255, 255, 0.8);
                    margin-top: 8px;
                }

                .tag {
                    display: inline-block;
                    width: 63px;
                    height: 20px;
                    text-align: center;
                    line-height: 20px;
                    background: linear-gradient(
                        121deg,
                        #ffc348 0%,
                        #fea816 100%
                    );
                    border-radius: 0px 6px 0px 6px;
                    position: absolute;
                    top: 0px;
                    right: 0px;
                    font-size: 11px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #ffffff;
                }
            }
        }
    }

    .content-body {
        margin: 0px 15px;
    }

    .milepost-contanier {
        height: auto;
        background: linear-gradient(180deg, #fff6e2 0%, #ffffff 30%);
        border-radius: 8px;
        border: 1px solid #ffffff;
        padding: 15px;
        &.margin-bottom {
            margin-bottom: 24px;
        }

        .title {
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 600;
            color: #191d2a;
            line-height: 25px;
            margin-bottom: 10px;
            top: 30px;
        }

        .desc {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #9b5b00;
            line-height: 17px;
            margin-bottom: 8px;
        }

        .milepost-rate {
            height: 6px;
            width: 100%;
            background: url(../images/jdt.png) no-repeat center center;
            background-size: 100%;
            border-radius: 4px;
            text-align: center;
            position: relative;
            margin-top: 19px;

            .rate-container {
                position: absolute;
                width: 100%;
            }

            .rate {
                display: inline-block;
                width: 20%;

                .rate-span {
                    display: inline-block;
                    width: 22px;
                    height: 22px;
                    background-repeat: no-repeat;
                    background-size: cover;
                    background-image: url(../images/ic_wks.png);
                    position: relative;
                    top: -15px;

                    &.rateSpan1 {
                        background-image: url(../images/ic_ywc.png);
                        left: -4px;
                    }

                    &.rateSpan5 {
                        width: 30px;
                        height: 30px;
                        top: -11px;
                        background-image: url(../images/ic_v.png);
                    }
                }

                &.on {
                    .rate-span {
                        background-image: url(../images/ic_xz.png);
                        width: 28px;
                        height: 28px;
                        top: -13px;
                    }
                }
                &.vipComplete {
                    .rate-span {
                        background-image: url(../images/ic_wc.png);
                        width: 25px;
                        height: 25px;
                    }
                }
                &.vip-complete-on-click {
                    .rate-span {
                        background-image: url(../images/ic_wc.png);
                        width: 28px;
                        height: 28px;
                        top: -13px;
                    }
                }
            }
        }

        .text-tips {
            margin-top: 16px;

            .rate-text {
                display: inline-block;
                width: 20%;
                font-size: 11px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #191d2a;
                text-align: center;
                line-height: 16px;
                font-size: 11px;
                vertical-align: top;

                &.rateTex1 {
                    color: #e29f87;
                }
                &.select-value {
                    color: #c43e0e;
                }
            }

            &.text-tips-time {
                margin-top: 15px;

                .rate-text {
                    font-size: 13px;
                }
            }
        }

        .milepost-tips {
            height: auto;
            background: rgba(253, 204, 55, 0.15);
            border-radius: 4px;
            padding: 7px 9px;
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #9b5b00;
            line-height: 18px;
            margin-top: 10px;
            position: relative;

            .ic-sjx {
                position: absolute;
                top: -10px;
                width: 14px;
                height: 10px;
                background: url(../images/ic_sjx.png) no-repeat center center;
                background-size: 100%;
            }

            span {
                color: #ff763c;
                font-size: 13px;
            }
        }
    }

    .tab-change-container {
        display: flex;
        justify-content: flex-start;
        width: 100%;
        min-height: 49px;
        align-items: center;
        padding-top: 10px;
        margin-bottom: 10px;
        padding-left: 10px;
        padding-right: 10px;
        .title {
            flex: 1;
            font-size: 15px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #191d2a;
            text-align: center;
            position: relative;
            &.title2 {
                flex: 0 0 180px;
            }
            &.title3 {
                flex: 0 0 140px;
            }

            &.on {
                font-size: 18px;
                font-weight: 600;
                position: relative;

                &::after {
                    position: absolute;
                    left: 50%;
                    margin-left: -10px;
                    content: '';
                    display: inline-block;
                    width: 20px;
                    height: 3px;
                    background: #191d2a;
                    border-radius: 2px;
                    margin-top: 25px;
                }
            }
        }

        &.tabFixed {
            position: absolute;
            top: 0px;
            margin-top: 0px;
            left: 0px;
            right: 0px;
            z-index: 22;
            height: 49px;
            line-height: 49px;
            background: #ffffff;
            width: 100%;
            &.showtab-fixed {
                z-index: 2;
                background: #e6f3ff;
            }

            .on {
                &::after {
                    margin-top: 42px;
                }
            }
        }
    }

    .practice-container {
        .practice-title {
            background: linear-gradient(308deg, #def0ff 0%, #b5dbff 100%);
            border-radius: 6px;
            padding: 10px;
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #0c235f;
            line-height: 20px;
            margin-bottom: 15px;
        }

        .practice-vip {
            padding: 15px;
            height: 252px;
            background: #ffffff;
            border-radius: 8px;
            position: relative;

            .practice-vip-title {
                width: 107px;
                height: 24px;
                line-height: 24px;
                text-align: center;
                background: linear-gradient(270deg, #ffde74 0%, #ffefb7 100%);
                border-radius: 4px;
                font-size: 13px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #8f4600;

                .vip-bg {
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    background: url(../images/ic_vipp.png) no-repeat center;
                    background-size: 100%;
                    margin-right: 4px;
                    vertical-align: middle;
                }
            }

            .complete {
                position: absolute;
                right: 0px;
                top: 0px;
                width: 67px;
                height: 52px;
                background: url(../images/no.png) no-repeat center center;
                background-size: 100%;

                &.success {
                    background: url(../images/yiwancheng.png) no-repeat center
                        center;
                    background-size: 100%;
                }
            }

            .complete-progress {
                width: 100%;
                height: 6px;
                background: #f3f1e9;
                border-radius: 3px;
                margin-top: 45px;
                position: relative;

                .complete-progress-on {
                    position: absolute;
                    top: 0px;
                    left: 0px;
                    border-radius: 3px;
                    height: 6px;

                    // background: linear-gradient(
                    //     117deg,
                    //     #ffe7a8 0%,
                    //     #ff6827 100%
                    // );
                    background: #ff7130;
                }

                .complete-progress-on-yuan {
                    position: absolute;
                    top: -5px;
                    width: 16px;
                    height: 16px;
                    background: #ff7130;
                    border: 3px solid #ffffff;
                    border-radius: 50%;
                }

                .complete-progress-tips {
                    position: absolute;
                    margin-left: -16px;
                    top: -30px;
                    width: 38px;
                    height: 22px;
                    line-height: 19px;
                    background: url(../images/progress.png) no-repeat center;
                    background-size: 100%;
                    font-size: 11px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #ffffff;
                    text-align: center;
                }
            }

            .practice-total {
                width: 100%;
                margin-top: 14px;
                display: flex;
                justify-content: space-between;

                .total {
                    flex: 0 0 54%;
                    text-align: center;

                    &.total {
                        flex: 0 0 23%;
                    }

                    &.total2 {
                        flex: 0 0 23%;
                    }

                    .total-tag {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #999999;
                        line-height: 17px;
                    }

                    .total-total {
                        font-size: 15px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #191d2a;
                        line-height: 21px;
                    }
                }
            }

            .practice-vip-count {
                height: 70px;
                background: linear-gradient(270deg, #fef6d9 0%, #ffe48c 100%);
                border-radius: 8px;
                position: relative;
                margin-top: 19px;
                display: flex;
                align-items: center;
                padding-right: 15px;

                .left {
                    width: 74px;
                    height: 60px;
                    background: url(../images/ic-dp.png) no-repeat center center;
                    background-size: 100%;
                }

                .center {
                    flex: 1;

                    .title {
                        font-size: 17px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 500;
                        color: #eb6401;
                        line-height: 24px;
                        margin-bottom: 3px;
                    }

                    .desc {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #5d594b;
                    }
                }

                .right {
                    width: 86px;
                    text-align: center;
                    line-height: 30px;
                    height: 30px;
                    background: linear-gradient(
                        90deg,
                        #ffbb14 0%,
                        #ff7f3e 100%
                    );
                    border-radius: 15px;
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #ffffff;

                    .right-shanjiao {
                        display: inline-block;
                        width: 15px;
                        height: 9px;
                        background: url(../images/right-shanjiao.png) no-repeat
                            center center;
                        background-size: 100%;
                    }
                }

                .shanjiao {
                    position: absolute;
                    top: -9px;
                    left: 20px;
                    width: 15px;
                    height: 9px;
                    background: url(../images/sanjiao.png) no-repeat center
                        center;
                    background-size: 100%;
                }
            }

            &.practice-common {
                margin-bottom: 10px;
                height: 158px;

                .practice-vip-title {
                    width: 72px;
                    background: linear-gradient(
                        308deg,
                        #b5dbff 0%,
                        #def0ff 100%
                    );
                    color: #3a5498;
                }

                .complete-progress-on {
                    // background: linear-gradient(
                    //     117deg,
                    //     #60c4fe,
                    //     #0489ff
                    // );
                    background: #0f90ff;
                }

                .complete-progress-on-yuan {
                    background: #0f90ff;
                }

                .complete-progress-tips {
                    background: url(../images/jk_ic_dtfh_jindu.png) no-repeat
                        center;
                    background-size: 100%;
                }
            }
        }

        .practice-vip-vs {
            width: 100%;
            text-align: center;
            position: relative;

            .vs {
                display: inline-block;
                width: 40px;
                height: 40px;
                background: url(../images/VS.png) no-repeat center;
                background-size: 100%;
                position: absolute;
                top: -22px;
                left: 0px;
                right: 0px;
                margin: auto;
                z-index: 1;
            }
        }
    }

    .exam-container {
        padding: 15px;
        height: auto;
        background: #ffffff;
        border-radius: 8px;
        position: relative;
        margin-top: 15px;

        .exam-title {
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 600;
            color: #191d2a;
        }

        .complete {
            position: absolute;
            right: 0px;
            top: 0px;
            width: 67px;
            height: 52px;
            background: url(../images/no.png) no-repeat center center;
            background-size: 100%;
            &.success {
                background: url(../images/yiwancheng.png) no-repeat center
                    center;
                background-size: 100%;
            }
        }

        .exam-content {
            min-height: 208px;
            background: linear-gradient(
                180deg,
                #e8f7ff 0%,
                #fdffff 57%,
                #ffffff 100%
            );
            border-radius: 6px;
            margin-top: 18px;

            .exam-content-title {
                padding: 15px;
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #1a1c25;
                line-height: 18px;

                .title-tips {
                    color: #04a5ff;
                }
            }

            .exam-total {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 10px 15px;

                .exam-count {
                    flex: 1;
                    text-align: center;

                    &.exam-count1 {
                        flex: 0 0 23%;
                    }

                    &.exam-count3 {
                        flex: 0 0 28%;
                    }

                    .exam-count-tag {
                        font-size: 22px;
                        font-family: DINAlternate-Bold, DINAlternate;
                        font-weight: bold;
                        color: #191d2a;
                        line-height: 26px;
                        margin-bottom: 5px;
                    }

                    .exam-count-total {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #54545e;
                    }
                }
            }

            .exam-vip-count {
                height: 70px;
                background: linear-gradient(270deg, #fef6d9 0%, #ffe48c 100%);
                border-radius: 8px;
                position: relative;
                margin-top: 19px;
                display: flex;
                align-items: center;
                padding-right: 8px;

                .left {
                    width: 74px;
                    height: 60px;
                    background: url(../images/ic-zskcmn.png) no-repeat center
                        center;
                    background-size: 100%;
                }

                .center {
                    flex: 1;

                    .title {
                        font-size: 17px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 500;
                        color: #eb6401;
                        line-height: 24px;
                        margin-bottom: 3px;
                    }

                    .desc {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #5d594b;
                    }
                }

                .right {
                    width: 86px;
                    text-align: center;
                    line-height: 30px;
                    height: 30px;
                    background: linear-gradient(
                        90deg,
                        #ffbb14 0%,
                        #ff7f3e 100%
                    );
                    border-radius: 15px;
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #ffffff;

                    .right-shanjiao {
                        display: inline-block;
                        width: 15px;
                        height: 9px;
                        background: url(../images/right-shanjiao.png) no-repeat
                            center center;
                        background-size: 100%;
                    }
                }

                .shanjiao {
                    position: absolute;
                    top: -9px;
                    left: 20px;
                    width: 15px;
                    height: 9px;
                    background: url(../images/sanjiao.png) no-repeat center
                        center;
                    background-size: 100%;
                }
            }

            .exam-vip-bottom {
                margin-top: 10px;
                font-size: 11px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #54545e;
                line-height: 16px;
            }
        }
    }

    .knowledge-container {
        padding: 15px 15px 0px 15px;
        height: auto;
        background: #ffffff;
        border-radius: 8px;
        margin-top: 15px;
        &.knowledge-other {
            padding-bottom: 15px;
        }

        .knowledge-title {
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 600;
            color: #191d2a;
            line-height: 25px;
        }

        .knowledge-model {
            height: 137px;
            background: #ffffff;
            border: 1px solid #f0eff4;
            margin-top: 15px;
            border-radius: 4px;

            .top {
                background: linear-gradient(90deg, #f3f6ff 0%, #fffeff 100%);
                padding: 7px 15px;

                .title {
                    font-size: 15px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #191d2a;
                    line-height: 21px;
                    margin-bottom: 2px;
                }

                .desc {
                    font-size: 11px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #54545e;
                    line-height: 16px;
                }
            }

            .content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 15px;

                .content-total {
                    flex: 1;
                    text-align: center;

                    .total-tag {
                        font-size: 22px;
                        font-family: DINAlternate-Bold, DINAlternate;
                        font-weight: bold;
                        color: #191d2a;
                        line-height: 26px;
                        margin-bottom: 5px;
                    }

                    .total-total {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #63656e;
                        line-height: 17px;

                        .icon {
                            display: inline-block;
                            width: 10px;
                            height: 10px;
                            background: url(../images/ic_qsy_jt_l.png) no-repeat
                                center;
                            background-size: 100%;
                            vertical-align: middle;
                        }
                    }
                }
            }
        }

        .knowledge-qita {
            font-size: 15px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 600;
            color: #191d2a;
            line-height: 21px;
            margin-top: 15px;
        }

        .knowledge-fenbu {
            margin-top: 15px;

            .fenbu {
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-bottom: 1px solid #f0eff4;

                padding-bottom: 12px;
                padding-top: 12px;

                &:last-child {
                    border-bottom: none;
                }

                .fenbu-left {
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #191d2a;
                    flex: 1;
                    display: flex;
                    align-items: center;
                }

                .fenbu-right {
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #999999;
                    padding-left: 20px;

                    .fenbu-right-icon {
                        display: inline-block;
                        width: 15px;
                        height: 15px;
                        background: url(../images/fenbu.png) no-repeat center;
                        background-size: 100%;
                        vertical-align: middle;
                    }
                }
            }
        }
    }

    .suggess-container {
        margin-top: 20px;

        .title {
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #191d2a;
            line-height: 25px;
            margin-bottom: 15px;
        }

        .suggess-tips {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #191d2a;
            line-height: 20px;
            margin-top: 20px;
            margin-bottom: 15px;
        }

        .fei-vip-tips {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #04a5ff;
            line-height: 20px;
            margin-bottom: 5px;
        }

        .fei-vip-tips-desc {
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 600;
            color: #191d2a;
            line-height: 25px;
            margin-bottom: 15px;
            .fei-vip-tips-desc-span {
                margin-left: 5px;
                margin-right: 5px;
                color: #04a5ff;
            }
        }

        .suggess-content-fei-vip {
            height: 73px;
            background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding-left: 15px;
            margin-bottom: 10px;

            .title-fei-vip {
                font-size: 16px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 600;
                color: #333333;
                line-height: 22px;
                margin-bottom: 5px;
            }

            .desc-fei-vip {
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #63656e;
                line-height: 18px;
            }
        }

        .suggess-kecheng {
            display: flex;
            align-items: center;
            justify-content: center;

            .kecheng {
                flex: 1;
                height: 134px;
                background: #0d98fd;
                border-radius: 4px;
                margin-right: 10px;

                &:last-child {
                    margin-right: 0px;
                }
            }
        }
        .zhibojian-ke1 {
            width: 345px;
            height: 626px;
            background: url(../images/zhibojian-bg.png) no-repeat center;
            background-size: 100%;
            margin: 0 auto;
            padding-top: 11px;
            padding-bottom: 10px;
            overflow: hidden;
            .step01 {
                width: 341px;
                height: 157px;
                background: url(../images/zhibojian_01.png) no-repeat center;
                background-size: 100%;
                margin-bottom: 5px;
                margin-left: 3px;
            }
            .step02 {
                width: 341px;
                height: 138px;
                background: url(../images/zhibojian_02.png) no-repeat center;
                background-size: 100%;
                margin-bottom: 5px;
                margin-left: 3px;
            }
            .step03 {
                width: 341px;
                height: 138px;
                background: url(../images/zhibojian_03.png) no-repeat center;
                background-size: 100%;
                margin-bottom: 5px;
                margin-left: 3px;
            }
            .step04 {
                width: 341px;
                height: 157px;
                background: url(../images/zhibojian_04.png) no-repeat center;
                background-size: 100%;
                margin-bottom: 5px;
                margin-left: 3px;
            }
        }
    }
}
