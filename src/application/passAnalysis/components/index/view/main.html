<import name="style" content="./main" module="S" />
<import name="passRate" content=":application/passAnalysis/components/passRate/main" />
<import name="memberRecommand" content=":application/passAnalysis/components/memberRecommand/main" />
<import name="studyStep" content=":application/passAnalysis/components/studyStep/main" />
<div class=":panel-home-index {{URLParams.inletSource=='zhibojian'?S.zhibojianPageIndex:''}}" id="body-panel-pass">
    <div class=":panel-page-header-container">
        <div
            class=":panel-page-header {{URLParams.inletSource=='zhibojian'?S.zhibijian:''}} {{URLParams.passShowType=='dialog'?S.showDialogStyle:''}}">
            <div class=":panel-page-header-left">
                <!-- <p class=":logo"></p> -->
                <img class=":logo"
                    src="https://sirius.mc-cdn.cn/mc-sirius/2022/07/06/19/067673f01ad14b9483bbad52c0ff8e82.png" />
                <p class=":title">
                    {{props.isVip?'VIP':'普通'}}学员：{{props.userData.nickname||'***'}}
                    <sp:if value='{{props.isVip}}'>
                        <img class=":vip-bages" src="{{props.userVipIcon}}" alt="">
                    </sp:if>
                </p>
                <p class=":desc">{{self.getNowTime}}</p>
            </div>
            <sp:if value="{{!Platform.isHarmony}}">
                <div class=":panel-page-header-right">
                    <div class=":content" sp-on:click="selectExamTime">
                        <p class=":time {{self.getExamTime=='?月?日'?S.tipsFont:''}}">{{self.getExamTime}}</p>
                        <p class=":time-desc ">{{self.getExamTime=="?月?日"?'请选择':''}}正式考试时间</p>
                        <sp:if value='{{self.hasDay}}'>
                            <span class=":tag"> {{self.hasDay=='今天'?'今天':'仅剩'+self.hasDay+'天'}}</span>
                        </sp:if>
    
                    </div>
                </div>
            </sp:if>
        </div>
        <com:passRate name="passRate" hasDay="{{self.hasDay}}"></com:passRate>
    </div>
    <div class=":content-body">
        <div class=":milepost-contanier {{props.isFixedTab&&S.marginBottom}}">
            <div class=":title">备考里程碑建议</div>
            <sp:if value='{{!self.hasDay}}'>
                <div class=":desc">设置了考试时间，我们将为您建议每个重要里程碑的完成时间点，便于您合理安排备考。</div>
            </sp:if>
            <sp:if value='{{self.hasDay}}'>
                <div class=":text-tips {{S.textTipsTime}}">
                    <span class=":rate-text"></span>
                    <span
                        class=":rate-text {{state.milepostIndex==1?S.selectValue:''}}">{{self.getHandleTime.time3}}</span>
                    <span
                        class=":rate-text {{state.milepostIndex==2?S.selectValue:''}}">{{self.getHandleTime.time2}}</span>
                    <span
                        class=":rate-text {{state.milepostIndex==3?S.selectValue:''}}">{{self.getHandleTime.time1}}</span>
                    <span class=":rate-text">{{self.getHandleTime.time}}</span>
                </div>
            </sp:if>
            <div class=":milepost-rate">
                <div class=":rate-container">
                    <div class=":rate" data-index="0">
                        <span class=":rate-span {{S.rateSpan1}}"></span>
                    </div>
                    <div class=":rate   {{state.milepostIndex==1?S.on:''}} {{props.isVip&&state.vipReport.isComplate&&state.milepostIndex!=1?S.vipComplete:''}} {{props.isVip&&state.vipReport.isComplate&&state.milepostIndex==1?S.vipCompleteOnClick:''}} {{!props.isVip&&state.normalReport.isComplate&&state.milepostIndex!=1?S.vipComplete:''}}  {{!props.isVip&&state.normalReport.isComplate&&state.milepostIndex==1?S.vipCompleteOnClick:''}}"
                        data-index="1" sp-on:click="milepostChange">
                        <span class=":rate-span"></span>
                    </div>
                    <div class=":rate  {{state.milepostIndex==2?S.on:''}} {{state.examReport.successCount>=self.recentTimes&&state.milepostIndex!=2?S.vipComplete:''}} {{state.examReport.successCount>=self.recentTimes&&state.milepostIndex==2?S.vipCompleteOnClick:''}}"
                        data-index="2" sp-on:click="milepostChange">
                        <span class=":rate-span"></span>
                    </div>
                    <div class=":rate {{state.milepostIndex==3?S.on:''}} {{state.errorReport.errorCount<=0&&state.milepostIndex!=3?S.vipComplete:''}} {{state.errorReport.errorCount<=0&&state.milepostIndex==3?S.vipCompleteOnClick:''}}"
                        data-index="3" sp-on:click="milepostChange">
                        <span class=":rate-span"></span>
                    </div>
                    <div class=":rate" data-index="4">
                        <span class=":rate-span {{S.rateSpan5}}"></span>
                    </div>
                </div>
            </div>
            <div class=":text-tips">
                <span class=":rate-text {{S.rateTex1}}">开始练习</span>
                <span class=":rate-text {{state.milepostIndex==1?S.selectValue:''}}">完成练习</span>
                <span
                    class=":rate-text {{state.milepostIndex==2?S.selectValue:''}}">近{{self.recentTimes}}次模考试平均分合格</span>
                <span class=":rate-text {{state.milepostIndex==3?S.selectValue:''}}">巩固错题</span>
                <span class=":rate-text">正式考试</span>
            </div>
            <sp:if value='{{state.milepostIndex==1}}'>
                <sp:if value='{{props.isVip}}'>
                    <div class=":milepost-tips">
                        <sp:if value='{{state.vipReport.progress>=100}}'>
                            您题库练习完成度达到<span>{{state.vipReport.progress}}%</span>，{{self.hasDay?(self.hasDay==='今天'?'今天需要巩固错题':'剩余'+self.hasDay+'天需要巩固错题'):'正式考试前需要巩固错题'}}。
                            <sp:else />
                            您的题库练习完成度只有<span>{{state.vipReport.progress}}%</span>，{{self.hasDay?(self.hasDay==='今天'?'今天内要完成':'剩余'+self.hasDay+'天内要完成'):'正式考试前要完成'}}<span>{{(state.vipReport.totalCount||0)-(state.vipReport.doneCount||0)}}题</span>的练习。
                        </sp:if>

                        <div class=":ic-sjx" style="left:{{state.milepostIndex*20+8}}%"></div>
                    </div>
                    <sp:else />
                    <div class=":milepost-tips">
                        <sp:if value='{{state.normalReport.progress>=100}}'>
                            您题库练习完成度达到<span>{{state.normalReport.progress}}%</span>，{{self.hasDay?self.hasDay=='今天'?'今天需要巩固错题':'剩余'+self.hasDay+'天需要巩固错题':'正式考试前需要巩固错题'}}。
                            <sp:else />
                            您的题库练习完成度只有<span>{{state.normalReport.progress}}%</span>，{{self.hasDay?self.hasDay=='今天'?'今天内要完成':'剩余'+self.hasDay+'天内要完成':'正式考试前要完成'}}<span>{{(state.normalReport.totalCount||0)-(state.normalReport.doneCount||0)}}题</span>的练习。
                        </sp:if>

                        <div class=":ic-sjx" style="left:{{state.milepostIndex*20+8}}%"></div>
                    </div>
                </sp:if>
            </sp:if>
            <sp:if value='{{state.milepostIndex==3}}'>
                <sp:if value='{{props.isVip}}'>
                    <div class=":milepost-tips">
                        您的做题数<span>{{state.vipReport.doneCount}}</span>，错题数为<span>{{state.errorReport.errorCount}}</span>，考前一定要扫清错题哦。
                        <div class=":ic-sjx" style="left:{{state.milepostIndex*20+8}}%"></div>
                    </div>
                    <sp:else />
                    <div class=":milepost-tips">
                        您的做题数<span>{{state.normalReport.doneCount}}</span>，错题数为<span>{{state.errorReport.errorCount}}</span>，考前一定要扫清错题哦。
                        <div class=":ic-sjx" style="left:{{state.milepostIndex*20+8}}%"></div>
                    </div>

                </sp:if>

            </sp:if>
            <sp:if value='{{state.milepostIndex==2}}'>

                <div class=":milepost-tips">
                    <sp:if value='{{state.examReport.records.length<self.recentTimes}}'>
                        您模拟考试了<span>{{state.examReport.examCount}}</span>次，合格了<span>{{state.examReport.successCount}}</span>次，模拟考试平均分为<span>{{state.examReport.recentscore}}</span>。
                        <sp:else />
                        您模拟考试了<span>{{state.examReport.examCount}}</span>次，合格了<span>{{state.examReport.successCount}}</span>次，近<span>{{self.recentTimes}}</span>次模拟考试平均分为<span>{{state.examReport.recentscore}}</span>。
                    </sp:if>
                    <div class=":ic-sjx" style="left:{{state.milepostIndex*20+8}}%">
                    </div>
                </div>
            </sp:if>

        </div>
        <!-- style="top:{{props.pageHeader-2}}px" -->
        <div ref="tabChangeContainer" class=":tab-change-container {{props.isFixedTab&&S.tabFixed}} {{URLParams.passShowType=='dialog'?S.showtabFixed:''}}"
            style="top:{{URLParams.passShowType=='dialog'?Tools.clacRem(40):props.pageHeader-2}}px">
            <sp:each for='{{state.tabArray}}'>
                <div data-index="{{$index}}"
                    class=":title {{$index==props.tabIndex?S.on:''}}  {{$index==2&props.tabIndex==2?S.title2:''}}  {{$index==2&props.tabIndex!=2?S.title3:''}}"
                    sp-on:click="tabChange">
                    {{$value.name}}
                </div>

            </sp:each>
        </div>
        <div id="practice" class=":practice-container">
            <div class=":practice-title">正式考试为随机抽题组卷，请一定要完成题库练习。您需要在正式考试前，完成未做题。</div>
            <sp:if value='{{!props.isVip}}'>
                <div class=":practice-vip {{S.practiceCommon}}">

                    <p class=":practice-vip-title"></span>普通学员</p>
                    <div class=":complete {{state.normalReport.isComplate?S.success:''}}">
                    </div>
                    <div class=":complete-progress">
                        <div class=":complete-progress-on" style="width:{{state.normalReport.progress}}%"></div>
                        <div class=":complete-progress-on-yuan" style="left:{{state.normalReport.progress}}%"></div>
                        <div style="left:{{state.normalReport.progress}}%" class=":complete-progress-tips">
                            {{state.normalReport.progress}}%</div>
                    </div>
                    <div class=":practice-total">
                        <div class=":total total">
                            <p class=":total-tag">已做题</p>
                            <p class=":total-total">{{state.normalReport.doneCount}}题</p>
                        </div>
                        <div class=":total">
                            <p class=":total-tag">未做题</p>
                            <p class=":total-total">
                                {{state.normalReport.totalCount>0?(state.normalReport.totalCount-state.normalReport.doneCount):''}}题
                            </p>
                        </div>
                        <div class=":total {{S.total2}}">
                            <p class=":total-tag">目标</p>
                            <p class=":total-total">{{state.normalReport.totalCount}}题</p>
                        </div>
                    </div>

                </div>
                <div class=":practice-vip-vs">
                    <span class=":vs"></span>
                </div>
            </sp:if>
            <sp:if value='{{props.isVip}}'>
                <div class=":practice-vip">
                    <p class=":practice-vip-title"><span class=":vip-bg"></span>VIP学员专享</p>
                    <div class=":complete {{state.vipReport.isComplate?S.success:''}}"></div>
                    <div class=":complete-progress">
                        <div class=":complete-progress-on" style="width:{{state.vipReport.progress}}%"></div>
                        <div class=":complete-progress-on-yuan" style="left:{{state.vipReport.progress}}%"></div>
                        <div style="left:{{state.vipReport.progress}}%" class=":complete-progress-tips">
                            {{state.vipReport.progress}}%</div>
                    </div>
                    <div class=":practice-total">
                        <div class=":total total">
                            <p class=":total-tag">已做题</p>
                            <p class=":total-total">{{state.vipReport.doneCount}}题</p>
                        </div>

                        <div class=":total">
                            <p class=":total-tag">未做题</p>
                            <p class=":total-total">
                                {{state.vipReport.totalCount>0?(state.vipReport.totalCount-state.vipReport.doneCount):''}}题
                            </p>
                        </div>
                        <div class=":total {{S.total2}}">
                            <p class=":total-tag">目标</p>
                            <p class=":total-total">{{state.vipReport.totalCount}}题</p>
                        </div>
                    </div>
                    <div class=":practice-vip-count" sp-on:click="vipCountStudent">
                        <div class=":left"></div>
                        <div class=":center">
                            <p class=":title">精简{{state.vipReport.totalCount}}题</p>
                            <p class=":desc">破解高频考点 省时省力</p>
                        </div>
                        <div class=":right">
                            去练习<span class=":right-shanjiao"></span>
                        </div>
                        <div class=":shanjiao"></div>
                    </div>
                </div>
                <sp:else />
                <div class=":practice-vip">
                    <p class=":practice-vip-title"><span class=":vip-bg"></span>VIP学员专享</p>
                    <div class=":complete {{state.novipReport.isComplate?S.success:''}}"></div>
                    <div class=":complete-progress">
                        <div class=":complete-progress-on" style="width:{{state.novipReport.progress}}%"></div>
                        <div class=":complete-progress-on-yuan" style="left:{{state.novipReport.progress}}%"></div>
                        <div style="left:{{state.novipReport.progress}}%" class=":complete-progress-tips">
                            {{state.novipReport.progress}}%</div>
                    </div>
                    <div class=":practice-total">
                        <div class=":total total">
                            <p class=":total-tag">已做题</p>
                            <p class=":total-total">{{state.novipReport.doneCount}}题</p>
                        </div>

                        <div class=":total">
                            <p class=":total-tag">未做题</p>
                            <p class=":total-total">{{state.novipReport.totalCount-state.novipReport.doneCount}}题</p>
                        </div>
                        <div class=":total {{S.total2}}">
                            <p class=":total-tag">目标</p>
                            <p class=":total-total">{{state.novipReport.totalCount}}题</p>
                        </div>
                    </div>
                    <div class=":practice-vip-count" sp-on:click="vipCountStudent">
                        <div class=":left"></div>
                        <div class=":center">
                            <p class=":title">精简{{state.novipReport.totalCount}}题</p>
                            <p class=":desc">破解高频考点 省时省力</p>
                        </div>
                        <div class=":right">
                            去练习<span class=":right-shanjiao"></span>
                        </div>
                        <div class=":shanjiao"></div>
                    </div>
                </div>
            </sp:if>

        </div>
        <div id="exam" class=":exam-container">
            <div class=":exam-title">考试情况</div>
            <div class=":complete {{state.examReport.successCount>=self.recentTimes&&S.success}}"></div>
            <div class=":exam-content">
                <div class=":exam-content-title">
                    模拟考试是按照<span class=":title-tips">正式考试规则</span>组卷，可以检验练习成果，也是能否去参加正式考试的重要考核标准之一。
                </div>
                <div class=":exam-total">
                    <div class=":exam-count {{S.examCount1}}">
                        <p class=":exam-count-tag">{{state.examReport.examCount}}</p>
                        <p class=":exam-count-total">考试次数</p>
                    </div>
                    <div class=":exam-count">
                        <p class=":exam-count-tag">{{state.examReport.successCount}}</p>
                        <p class=":exam-count-total">合格次数</p>
                    </div>
                    <div class=":exam-count {{S.examCount3}}">
                        <p class=":exam-count-tag">{{{{state.examReport.recentscore}}}}</p>
                        <sp:if value='{{state.examReport.records.length>=5}}'>
                            <p class=":exam-count-total">近{{state.examReport.records.length}}次平均分</p>
                            <sp:else />
                            <p class=":exam-count-total">平均分</p>
                        </sp:if>

                    </div>
                </div>
                <div class=":exam-vip-count" sp-on:click="gotoexam">
                    <div class=":left"></div>
                    <div class=":center">
                        <p class=":title">真实考场模拟</p>
                        <p class=":desc">提前熟悉正式考试操作页面 </p>
                    </div>
                    <div class=":right">
                        去练习<span class=":right-shanjiao"></span>
                    </div>
                    <div class=":shanjiao"></div>
                </div>
                <sp:if value='{{props.isVip&&!URLCommon.isScore12&&!URLCommon.isZigezheng}}'>
                    <div class=":exam-vip-bottom">
                        【VIP学员专属福利】在正式考试前模拟考试只要完成两次90分，如未通过正式考试，可申请补偿50元
                    </div>
                </sp:if>

            </div>
        </div>
        <div id="knowledge" class=":knowledge-container {{!state.errorReport.errorCount?S.knowledgeOther:''}}">
            <div class=":knowledge-title">知识点掌握情况分析</div>
            <div class=":knowledge-model">
                <div class=":top">
                    <p class=":title">核心知识点</p>
                    <p class=":desc">知识点要熟练掌握，才能举一反三</p>
                </div>
                <div class=":content">
                    <div class=":content-total">
                        <p class=":total-tag">{{state.knowledgePointReport.totalCount}}</p>
                        <p class=":total-total">目标</p>
                    </div>
                    <div class=":content-total" data-type="unmastered" sp-on:click="unskilledMethod">
                        <p class=":total-tag">{{state.knowledgePointReport.unskilledCount}}</p>
                        <p class=":total-total">未熟练掌握<span class=":icon"></span></p>
                    </div>
                    <div class=":content-total" data-type="undo" sp-on:click="unskilledMethod">
                        <p class=":total-tag">{{state.knowledgePointReport.undoneCount}}</p>
                        <p class=":total-total">未练习<span class=":icon"></span></p>
                    </div>
                </div>
            </div>

            <div class=":knowledge-model">
                <div class=":top">
                    <p class=":title">错题分布</p>
                    <p class=":desc">反复巩固，在考试前扫清错题，考试才更有底气</p>
                </div>
                <div class=":content">
                    <div class=":content-total">
                        <p class=":total-tag">{{state.normalReport.doneCount}}</p>
                        <p class=":total-total">已做题</p>
                    </div>
                    <div class=":content-total" sp-on:click="errorCountMethod">
                        <p class=":total-tag">{{state.errorReport.errorCount}}</p>
                        <p class=":total-total">错题数<span class=":icon"></span></p>
                    </div>
                    <div class=":content-total">
                        <p class=":total-tag">
                            {{state.normalReport.doneCount>0?(state.errorReport.errorCount/state.normalReport.doneCount)*100>=100?100:((state.errorReport.errorCount/state.normalReport.doneCount)*100).toFixed(0):0}}%
                        </p>
                        <p class=":total-total">错误率</p>
                    </div>
                </div>
            </div>
            <sp:if value='{{state.errorReport.errorCount>0 && state.errorReport.knowledgePoint.length}}'>
                <div class=":knowledge-qita">错误率最高的知识点分布</div>
                <div class=":knowledge-fenbu">
                    <sp:each for='{{state.errorReport.knowledgePoint}}'>
                        <sp:if value='{{$value.errorCount>0}}'>
                            <div class=":fenbu" data-id="{{$value.id}}" sp-on:click="gotoErrDetail">
                                <div class=":fenbu-left">{{$value.name}}</div>
                                <div class=":fenbu-right">
                                    {{$value.errorCount}}
                                    <span class=":fenbu-right-icon"></span>
                                </div>
                            </div>
                        </sp:if>
                    </sp:each>
                </div>
            </sp:if>

        </div>
        <div class=":suggess-container">
            <sp:if value='{{URLParams.inletSource=="zhibojian"&&URLCommon.kemu==1}}'>
                <div class=":fei-vip-tips">{{self.hasDay?'根据您的约考时间，建议您采用以下高效学习方案':'建议您采用以下高效学习方案'}}</div>
                <div class=":fei-vip-tips-desc">
                    全科VIP高效学，考不过补偿140元
                </div>
                <div class=":zhibojian-ke1">
                    <div class=":step01" sp-on:click="gotoVipBuyed"></div>
                    <div class=":step02" sp-on:click="gotoVipBuyed"></div>
                    <div class=":step03" sp-on:click="gotoVipBuyed"></div>
                    <div class=":step04" sp-on:click="gotoVipBuyed"></div>
                </div>
                <sp:if value='{{props.isVip}}'>
                    <com:memberRecommand></com:memberRecommand>
                </sp:if>
                <sp:else />
                <sp:if value='{{props.isVip}}'>
                    <div class=":title">{{self.hasDay?'根据您的约考时间，建议您':''}}</div>
                    <com:studyStep name="studyStep"></com:studyStep>
                    <com:memberRecommand></com:memberRecommand>
                </sp:if>
                <sp:if value='{{!props.isVip}}'>
                    <sp:if value='{{self.hasDay}}'>
                        <div class=":fei-vip-tips">根据您的约考时间，建议您采用以下高效学习方案</div>
                    </sp:if>

                    <div class=":fei-vip-tips-desc">
                        <sp:if value='{{URLCommon.isScore12}}'>
                            短平快<span class=":fei-vip-tips-desc-span">3步</span>学习法，助力拿回驾照

                            <sp:else />
                            3步高效学{{URLCommon.isZigezheng?'':Texts.currentKemuTxt+'，考不过补偿50元'}}
                        </sp:if>

                    </div>
                    <div class=":suggess-content-fei-vip">
                        <sp:if value='{{URLCommon.isScore12}}'>
                            <div class=":title-fei-vip">
                                练习精简{{state.novipReport.totalCount}}题
                            </div>
                            <sp:else />
                            <div class=":title-fei-vip">
                                练习VIP精简{{state.novipReport.totalCount}}题
                            </div>
                        </sp:if>

                        <div class=":desc-fei-vip">
                            重点考点全覆盖，省时省力
                        </div>

                    </div>
                    <div class=":suggess-content-fei-vip">
                        <div class=":title-fei-vip">
                            真实考场模拟
                        </div>
                        <div class=":desc-fei-vip">
                            高仿真还原考场电脑考试规则和操作
                        </div>

                    </div>
                    <div class=":suggess-content-fei-vip">
                        <div class=":title-fei-vip">
                            <sp:if value='{{URLCommon.isScore12}}'>
                                <sp:if value='{{URLCommon.tiku==CarType.MOTO}}'>
                                    考前两套卷做对
                                    <sp:else />
                                    考前三套卷做对
                                </sp:if>
                                <sp:else />
                                考前秘卷做对
                            </sp:if>

                        </div>
                        <div class=":desc-fei-vip">
                            巩固高频考点更高效
                        </div>

                    </div>
                </sp:if>
            </sp:if>


        </div>

    </div>
</div>