.content {
    position: relative;
    width: 640/2px;
    height: 732/2px;
    background: url(../images/banner.png) no-repeat, linear-gradient(180deg, #a5e3ff, #fcfeff 33%, #ffffff);
    background-size: 100% auto, cover;
    border-radius: 48/2px;
    overflow: hidden;
}

.close {
    width: 44/2px;
    height: 44/2px;
    position: absolute;
    top: 30/2px;
    right: 30/2px;
    background-image: url(../images/close.png);
    background-size: cover;
}

.title {
    margin: 452/2px 0 48/2px;
    font-size: 38/2px;
    font-weight: bold;
    text-align: center;
    color: #333333;
    line-height: 52/2px;
}

.btn {
    margin: 0 auto;
    width: 540/2px;
    height: 88/2px;
    line-height: 88/2px;
    text-align: center;
    background: #04a5ff;
    border-radius: 440/2px;
    font-size: 32/2px;
    color: #ffffff;
}