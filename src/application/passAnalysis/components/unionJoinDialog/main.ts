/*
 * ------------------------------------------------------------------
 * 同期同学会引导弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { Dialog } from ':component/dialog/main';

export default class PayDialog extends Dialog<unknown, boolean> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }

    public show(cb?: AnyFunc): Promise<boolean | void> {
        return this.children.dialog.show(cb);
    }

    goUnionJoin() {
        this.hide(true);
    }

    close() {
        this.hide(false);
    }
}
