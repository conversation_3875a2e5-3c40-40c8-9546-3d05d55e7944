/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */

import { CarType, Platform, URLCommon, URLParams } from ':common/env';
import { Component } from '@simplex/simple-core';
import { openWeb } from ':common/core';
import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
import { AGED_500, EXAM_JUAN, JING_JIANG, MI_JIANG, SCORE12 } from ':common/navigate';
import { handleIosZigezhen } from ':common/utils';
interface State {
    steps: any[];
    minScore: number
}
interface Props { }
export default class extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            steps: [],
            minScore: 95
        };
        this.props = {};
    }
    didMount() {
        this.getKe14LearningData(URLCommon.kemu);
    }
    async getKe14LearningData(kemu) {
        let examPassCount = 0;
        const score12 = {
            name:
                URLCommon.tiku === CarType.MOTO
                    ? '考前两套卷做对'
                    : '考前三套卷做对',
            rightCount: '--',
            totalCount: '--',
            sceneCode: '102',
            press: 0,
            type: 'score12'
        };
        const mijuan = {
            name: '考前秘卷做对',
            rightCount: '--',
            totalCount: '--',
            press: 0,
            type: 'mijuan'
        };
        const steps = [
            {
                name: 'VIP精简500题做对',
                rightCount: '--',
                totalCount: '--',
                press: 0,
                type: 'jingjian'
            },
            {
                name: '真实考场模拟',
                rightCount: '--',
                totalCount: '--',
                examCount: 0,
                isSecond: true,
                press: '',
                type: 'exam'
            },
            URLCommon.isScore12 ? score12 : mijuan
        ];
        // 第一步

        const jinglainData: any = await this.getPracticeRecord(
            'jingjian500',
            kemu
        );
        steps[0].name = 'VIP精简' + jinglainData.totalCount + '题做对';
        steps[0].rightCount = jinglainData.rightCount;
        steps[0].totalCount = jinglainData.totalCount;

        if (
            jinglainData.rightCount !== '--' &&
            jinglainData.totalCount !== '--'
        ) {
            steps[0].press = Number(
                (
                    (jinglainData.rightCount / jinglainData.totalCount) * 100 ||
                    0
                ).toFixed(2)
            );
            steps[0].press = steps[0].press <= 0 ? 0 : steps[0].press;
        }

        // 第二步
        const realRoomData: any = await this.getExamRecord('realRoom', kemu);

        realRoomData.forEach((item) => {
            if (parseInt(item.score) >= this.state.minScore) {
                examPassCount++;
            }
        });
        steps[1].examCount = examPassCount;
        steps[1].press = examPassCount >= 10 ? 10 : 0;
        // 第三步
        // 扣满12分
        if (URLCommon.isScore12) {
            const progressData: any = await this.getPracticeProgress(kemu);
            if (+progressData.rightCount >= +progressData.questionCount) {
                progressData.rightCount = progressData.questionCount;
            }
            if (+progressData.rightCount === -1) {
                progressData.rightCount = '--';
            }
            if (+progressData.questionCount === -1) {
                progressData.questionCount = '--';
            }

            steps[2].rightCount = progressData.rightCount;
            steps[2].totalCount = progressData.questionCount;
            if (progressData.rightCount !== '--' && progressData.questionCount !== '--') {
                steps[2].press = Number(
                    (
                        (progressData.rightCount / progressData.questionCount) * 100 || 0
                    ).toFixed(2)
                );
                steps[2].press = steps[2].press <= 0 ? 0 : steps[2].press;
            }
        } else {
            // 普通场景
            const mijuanData: any = await this.getPracticeRecord('mijuan', kemu);
            steps[2].rightCount = mijuanData.rightCount;
            steps[2].totalCount = mijuanData.totalCount;
            if (mijuanData.rightCount !== '--' && mijuanData.totalCount !== '--') {
                steps[2].press = Number(
                    (
                        (mijuanData.rightCount / mijuanData.totalCount) * 100 || 0
                    ).toFixed(2)
                );
                steps[2].press = steps[2].press <= 0 ? 0 : steps[2].press;
            }
        }
        this.setState({ steps });
    }
    // 获取科一科四练习记录
    getPracticeRecord(type, kemu) {
        return new Promise((resolve) => {
            // eslint-disable-next-line no-unreachable
            MCProtocol.Vip.getPracticeRecord({
                car: URLCommon.tiku,
                kemu: parseInt(kemu),
                kemuStyle: parseInt(kemu),
                carStyle: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                type: type,
                callback: async (ret) => {
                    let data;
                    console.log('科一科四练', JSON.stringify(ret.data));
                    try {
                        let newdata;
                        if (typeof ret.data === 'string') {
                            newdata = JSON.parse(ret.data);
                        } else {
                            newdata = ret.data;
                        }
                        if (newdata.totalCount <= 0) {
                            data = {
                                rightCount: '--',
                                errorCount: '--',
                                totalCount: '--'
                            };
                        } else if (newdata.rightCount === -1) {
                            data = {
                                rightCount: '--',
                                errorCount: newdata.errorCount,
                                totalCount: newdata.totalCount
                            };
                        } else {
                            data = {
                                rightCount: newdata.rightCount,
                                errorCount: newdata.errorCount,
                                totalCount: newdata.totalCount
                            };
                        }
                    } catch (e) {
                        data = {
                            rightCount: '--',
                            errorCount: '--',
                            totalCount: '--'
                        };
                    }

                    resolve(data);
                }
            });
        });
    }
    getSceneCode() {
        let score12Type: number;
        // 判断以前版本传了score12为1代表扣满12分
        if (+URLParams.score12 === 1) {
            score12Type = 102;
        } else {
            score12Type = 101;
        }
        return +URLParams.sceneCode || score12Type;
    }
    // 获取科一科四考试记录
    getExamRecord(type, kemu) {
        return new Promise((resolve) => {
            // 资格证不知道最低分数，所以要先查最低分数，再查合格次数,资格证传的科目是8
            if (
                URLCommon.isZigezheng ||
                (URLCommon.tiku === CarType.MOTO && this.getSceneCode() === 102)
            ) {
                MCProtocol.Vip.getExamRule({
                    car: URLCommon.tiku,
                    kemu: URLCommon.tiku === CarType.MOTO ? 1 : 8,
                    kemuStyle: URLCommon.tiku === CarType.MOTO ? 1 : 8,
                    carStyle: URLCommon.tiku,
                    patternCode: URLParams.patternCode,
                    sceneCode: this.getSceneCode(),
                    callback: (ret: any) => {
                        let data: any;
                        try {
                            if (typeof ret.data === 'string') {
                                data = JSON.parse(ret.data);
                            } else {
                                data = ret.data;
                            }
                        } catch (e) {
                            data = {};
                        }
                        this.setState({ minScore: +data.passScore });
                        console.log('最定分', data.passScore);
                        console.log('MCProtocol.Vip.getExamRecord参数', {
                            car: URLCommon.tiku,
                            kemu: kemu,
                            type: type,
                            minScore: data.passScore
                        });
                        MCProtocol.Vip.getExamRecord({
                            car: URLCommon.tiku,
                            kemu: kemu,
                            kemuStyle: kemu,
                            carStyle: URLCommon.tiku,
                            sceneCode: URLParams.sceneCode,
                            patternCode: URLParams.patternCode,
                            type: type,
                            minScore: data.passScore,
                            callback: function (ret) {
                                console.log('MCProtocol.Vip.getExamRecord', ret);
                                let data;
                                try {
                                    if (typeof ret.data === 'string') {
                                        data = JSON.parse(ret.data);
                                    } else {
                                        data = ret.data;
                                    }
                                } catch (e) {
                                    data = [];
                                }
                                resolve(data);
                            }
                        });
                    }
                });
            } else {
                console.log('MCProtocol.Vip.getExamRecord参数', {
                    car: URLCommon.tiku,
                    kemu: kemu,
                    type: type,
                    // 兼容，ios是大于等于最小分数，andriod是大于
                    minScore: Platform.isIOS ? this.state.minScore : 94
                });
                MCProtocol.Vip.getExamRecord({
                    car: URLCommon.tiku,
                    kemu: kemu,
                    kemuStyle: kemu,
                    carStyle: URLCommon.tiku,
                    sceneCode: URLParams.sceneCode,
                    patternCode: URLParams.patternCode,
                    type: type,
                    // 兼容，ios是大于等于最小分数，andriod是大于
                    minScore: Platform.isIOS ? this.state.minScore : 94,
                    callback: function (ret) {
                        console.log('MCProtocol.Vip.getExamRecord', ret);
                        let data;
                        try {
                            if (typeof ret.data === 'string') {
                                data = JSON.parse(ret.data);
                            } else {
                                data = ret.data;
                            }
                        } catch (e) {
                            data = [];
                        }
                        resolve(data);
                    }
                });
            }
        });

    }
    // 扣满12分第三步骤，三套券协议
    getPracticeProgress(kemu) {
        return new Promise((resolve) => {
            MCProtocol.Vip.getPracticeProgress({
                car: URLCommon.tiku,
                kemu: kemu,
                kemuStyle: kemu,
                carStyle: URLCommon.tiku,
                patternCode: URLParams.patternCode,
                sceneCode: URLParams.sceneCode,
                callback: (data) => {
                    resolve(data);
                }
            });

        });
    }
    gotoStudy(e) {
        const type = e.refTarget.getAttribute('data-type');

        let action: string;
        if (type === 'jingjian') {
            if (URLCommon.tiku === CarType.CAR && URLCommon.isElder) {
                action = AGED_500;
            } else {
                action = JING_JIANG;
            }
        } else if (type === 'exam') {
            action = EXAM_JUAN;
        } else if (type === 'mijuan') {
            if (URLCommon.isScore12) {
                action = SCORE12;
            } else {
                action = MI_JIANG;
            }
        } else if (type === 'score12') {
            action = SCORE12;
        }
        if (handleIosZigezhen(action)) {
            return;
        }
        // location.href = dataAction;
        openWeb({ url: action });
    }
    willReceiveProps() {
        return true;
    }
}
