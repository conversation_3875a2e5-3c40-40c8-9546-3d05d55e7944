<import name="style" content="./main" module="S" />
<div class=":study-step-contanier">
    <sp:each for='{{state.steps}}'>
        <sp:if value='{{$value.isSecond}}'>
            <div data-type="{{$value.type}}" sp-on:click="gotoStudy" class=":suggess-content">
                <div class=":left">
                    <p class=":title">{{$value.name}}</p>
                    <p class=":desc">10次以上<span>{{state.minScore}}</span>分</p>
                </div>
                <div class=":right  {{$value.examCount<10?S.press:''}}">
                    {{$value.examCount}}次
                </div>
            </div>
            <sp:else />
            <div data-type="{{$value.type}}" sp-on:click="gotoStudy" class=":suggess-content">
                <div class=":left">
                    <p class=":title">{{$value.name}}</p>
                    <p class=":desc">{{$value.rightCount}}/<span>{{$value.totalCount}}</span></p>
                </div>
                <div class=":right {{$value.press<90?S.press:''}}">
                    {{$value.press>=100?100:$value.press}}%
                </div>
            </div>
        </sp:if>
    </sp:each>
</div>