import { getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupComparePrice, GroupKey } from ':store/goods';
import View from './view/main.html';
import { CarType, getPageName, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import BuyButton from ':component/buyButton/main';
import throttle from 'lodash/throttle';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { PayStatProps } from ':component/buyButton/main';
import { Application } from '@simplex/simple-core';
import PayDialog from ':component/payDialog/main';
import { getBestCoupon } from ':common/features/coupon';
import { checkReaded } from ':common/features/agreement';
import { trackEvent, trackGoPay, trackPageLoad } from ':common/stat';
import { getAuthToken, openVipWebView, webClose, openWeb } from ':common/core';
import { PAY_GUIDE_URL } from ':common/navigate';
import { MCProtocol } from '@simplex/simple-base';
import { onPageShow } from ':common/features/page_status_switch';
import HomeIndex from ':application/passAnalysis/components/index/main';
import UnionJoinDialog from ':application/passAnalysis/components/unionJoinDialog/main';
import Header from ':component/header/main';
import { getIsVipInfo } from ':store/passAnalysis';
import { dateFormat } from ':common/utils';
import jump from ':common/features/jump';
import html2canvas from 'html2canvas';
import { zigezhengGroupKeyObj } from ':common/features/zigezheng';
import { onWebBack } from ':common/features/persuade';
import { getUnionJoined } from ':store/chores';
import { hesitateUserPersuade } from ':common/features/hesitate';

if (URLParams.kemuStyle) {
    openWeb({
        url: 'http://jiakao.nav.mucang.cn/switchKemu?kemu=' + URLParams.kemuStyle
    });
}
interface State {
    tabIndex: number;
    goodsInfoPool: GoodsInfo[];
    couponPool: object;
    labelPool: object;
    comparePricePool: object;
    isFixedTab: boolean;
    scrollTabIndex: number;
    imgSrc: any;
    isVip: boolean;
    userVipIcon: string;
    examTime: number;
    userData: any;
    isExpirTime: boolean;
    pageHeader: number;
    theme: string;
}
// eslint-disable-next-line
const oneTime = 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 59 * 1000;
let timer;
export default class extends Application<State> {

    declare children: {
        unionJoinDialog: UnionJoinDialog;
        payDialog: PayDialog;
        buyButton: BuyButton;
        homeIndex: HomeIndex;
        header: Header
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey] || {};
    }
    /**
    * 如果有优惠券的价格为0的就显示0
    * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
   */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;
        // 这期暂时不做优惠券
        // if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
        //     const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
        //     return +showPrice > 0 ? String(showPrice) : '0';
        // }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {
            tabIndex: 0,
            goodsInfoPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            isFixedTab: false,
            imgSrc: '',
            scrollTabIndex: 0,
            isVip: false,
            userVipIcon: '',
            examTime: 0,
            userData: {
                nickname: '***'
            },
            pageHeader: 0,
            isExpirTime: false,
            theme: '#ffffff'
        };
    }
    async didMount() {
        // 设置主题色
        MCProtocol.Core.Web.setStatusBarTheme({ theme: 'dark' });
        onPageShow(() => {
            console.log('onPageShowvisibility返回执行了哈');
            this.children.homeIndex.visibilityStateMethod();
            this.getExamTime();
        });
        setPageName('预测通过率页');
        this.setUserData();
        this.getExamTime();
        const authToken = await getAuthToken();
        let isVip = false;
        if (authToken) {
            const vipInfo = await getIsVipInfo();
            isVip = vipInfo.isVip;

            this.setState({ isVip, userVipIcon: vipInfo.userVipIcon });
        }
        // 页面进出时长打点
        trackPageLoad({
            payStatus: isVip ? '1' : '2'
        });
        if (URLParams.inletSource === 'zhibojian') {
            return;
        }
        const isPortrait =
            window.orientation === 90 ||
            window.orientation === -90 ||
            location.pathname.indexOf('dgmnq.html') !== -1;
        // 480是 common/features/embeded.ts 中写死的
        const maxWidth = 480;
        const dimensionWidth = isPortrait
            ? Math.min(window.screen.width, window.screen.height, maxWidth)
            : Math.min(document.documentElement.clientWidth, maxWidth);

        if (!isVip) {
            if (URLParams.passShowType === 'dialog') {
                this.setState({ theme: '#E6F3FF' });
                // TODO: 低端机判断不了横竖屏

                if (Platform.isAndroid) {
                    MCProtocol.Vip.show({
                        h5whRate: dimensionWidth / (0.8 * window.screen.height),
                        h5ContentMaxWidth: 480
                    });
                } else {
                    MCProtocol.Vip.show({
                        h5whRate: dimensionWidth / (0.65 * window.screen.height),
                        h5ContentMaxWidth: 480
                    });
                }
            }
            this.renderVipPage();
        } else if (URLParams.passShowType === 'dialog') {
            // 是vip,ios没有购买按钮，高度设置一样的
            this.setState({ theme: '#E6F3FF' });
            MCProtocol.Vip.show({
                h5whRate: dimensionWidth / (0.8 * window.screen.height),
                h5ContentMaxWidth: 480
            });
        }

        if (URLParams.passShowType !== 'dialog' && URLCommon.tiku === CarType.CAR && !URLCommon.isZigezheng && URLCommon.isNormal) {
            this.handleUnionJoinDialog(isVip);
        }
        if (!this.goBackPage) {
            this.goBackPage = this.tryClose;
        }
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });
    }
    async renderVipPage() {
        const kemu = +URLCommon.kemu;

        const goodsInfoPool: GoodsInfo[] = [];
        if (URLCommon.isScore12) {
            switch (URLCommon.tiku) {
                case CarType.CAR:
                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKou12
                    } as GoodsInfo);
                    break;
                case CarType.TRUCK:
                    goodsInfoPool.push({
                        groupKey: GroupKey.HcChannelKou12
                    } as GoodsInfo);
                    break;
                case CarType.BUS:
                    goodsInfoPool.push({
                        groupKey: GroupKey.KcChannelKou12
                    } as GoodsInfo);
                    break;
                case CarType.MOTO:
                    goodsInfoPool.push({
                        groupKey: GroupKey.MotoChannelKou12
                    } as GoodsInfo);
                    break;
                default: break;
            }
        } else if (URLCommon.isElder) {
            goodsInfoPool.push({
                groupKey: kemu === 1 ? GroupKey.ElderChannelKe1
                    : GroupKey.ElderChannelKe4
            } as GoodsInfo);
            if (kemu === 1) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKemuAll
                } as GoodsInfo);
            }
        } else {
            switch (URLCommon.tiku) {
                case CarType.CAR:
                    goodsInfoPool.push({
                        groupKey: kemu === 1
                            ? GroupKey.ChannelKe1
                            : GroupKey.ChannelKe4
                    } as GoodsInfo);
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1Ke4Group
                        } as GoodsInfo);
                    }
                    break;
                case CarType.TRUCK:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.HcChannelKe1 : GroupKey.HcChannelKe4
                    } as GoodsInfo);
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe1Ke4Group
                        } as GoodsInfo);
                    }
                    break;
                case CarType.BUS:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.KcChannelKe1 : GroupKey.KcChannelKe4
                    } as GoodsInfo);
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.KcChannelKe1Ke4Group
                        } as GoodsInfo);
                    }
                    break;
                case CarType.MOTO:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.MotoChannelKe1 : GroupKey.MotoChannelKe4
                    } as GoodsInfo);
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.MotoChannelKe1Ke4Group
                        } as GoodsInfo);
                    }
                    break;
                case CarType.GUACHE:
                    goodsInfoPool.push({
                        groupKey: GroupKey.GcChannelKe4
                    } as GoodsInfo);
                    break;
                default:
                    if (URLCommon.isZigezheng) {
                        goodsInfoPool.push({
                            groupKey: zigezhengGroupKeyObj[URLCommon.tiku]
                        } as GoodsInfo);
                    }
                    break;
            }
        }
        this.state.goodsInfoPool = goodsInfoPool;
        await this.getGoodInfo();
        setTimeout(async () => {
            await this.getCoupon();
            await this.getLabel();
            await this.getComparePrice();
        }, 60);
        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: async () => {
                iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });
        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: goodsInfoPool[this.state.tabIndex].groupKey
        });
    }

    async tryClose() {
        if (await hesitateUserPersuade()) {
            return;
        } else {
            webClose();
        }
    }
    backCall = () => {
        this.goBackPage();
    }
    goBackPage = null;
    /** 同期同学会引导弹窗 */
    handleUnionJoinDialog(isVip: boolean) {
        const checkUnionJoinDialogGoBack = async () => {
            const logined = await getAuthToken();
            if (!logined) {
                return true;
            }
            const { value: joined } = await getUnionJoined({
                carType: URLCommon.tiku,
                kemu: URLCommon.kemu
            }).catch(() => {
                return {
                    value: true
                };
            });
            if (joined) {
                return true;
            }
            trackEvent({
                // payStatus: isVip ? '1' : '2',
                payStatus: '0',
                fragmentName1: '引导同学会弹窗',
                actionType: '出现'
            });
            const confirm = await this.children.unionJoinDialog.show();
            if (!confirm) {
                return true;
            }
            trackEvent({
                fragmentName1: '引导同学会弹窗',
                actionType: '点击',
                actionName: '去了解'
            });
            openVipWebView({
                url: 'https://laofuzi.kakamobi.com/exam-together/index.html'
            });
            return false;
        };

        if (Platform.isAndroid) {
            // let flag = false;
            // this.goBackPage = async () => {
            //     if (!flag) {
            //         flag = true;
            //         if (checkUnionJoinDialogGoBack()) {
            //             this.tryClose();
            //         }
            //     } else {
            //         this.tryClose();
            //     }
            // };
        } else if (Platform.isIOS) {
            const scroller = this.getDOMNode().bodyPanelContainer as HTMLElement;
            // 头部高
            const headerHeight = document.getElementById('page-header').offsetHeight;
            // 1rem高
            const floatTabsHeight = 1 * window.baseFontSize;
            scroller.addEventListener('scroll', function onScroll() {
                const examOffset = document.getElementById('exam').offsetTop - headerHeight - floatTabsHeight;
                const scrollTop = scroller.scrollTop;
                if (scrollTop >= examOffset) {
                    checkUnionJoinDialogGoBack();
                    scroller.removeEventListener('scroll', onScroll);
                }
            });
        }
    }
    setUserData() {
        MCProtocol.Core.User.get((data) => {
            this.setState({
                userData: data.data
            });
        });
    }
    selectExamTime = throttle(() => {
        MCProtocol['jiakao-global'].web.showExamDateAlert({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            from: URLParams.from,
            fromPageCode: URLParams.from,
            pageName: getPageName(),
            autoPassAnalysis: false,
            callback: (data) => {
                console.log('选择时间弹窗的回调', JSON.stringify(data));
                if (
                    (Platform.isAndroid && data.date) ||
                    (Platform.isIOS && data.data.date)
                ) {
                    // window.location.reload();
                    jump.reload();
                }
            }
        });
    }, 1000, {
        trailing: false
    })
    getExamTime() {
        MCProtocol['jiakao-global'].web.getExamDate({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            callback: (res) => {
                let examTime = 0;
                if (res.data.date) {
                    examTime = res.data.date;
                }
                let isExpirTime = false;
                if (examTime) {
                    const examTimeString = dateFormat(
                        examTime,
                        'yyyy/MM/dd  00:00:00'
                    );
                    examTime = new Date(examTimeString).getTime() + oneTime;

                    isExpirTime =
                        examTime -
                        new Date(new Date().toDateString()).getTime() <=
                        0;
                } else {
                    isExpirTime = true;
                }

                this.setState({ examTime: examTime, isExpirTime });
            }
        });
    }
    pageScroll(e) {

        timer && clearTimeout(timer);
        timer = setTimeout(() => {
            const prevScrollTop = Math.ceil(e.refTarget?.scrollTop) + 1;
            const scrollTop0 = document.getElementById('practice')?.offsetTop;
            const scrollTop1 = document.getElementById('exam')?.offsetTop;
            const scrollTop2 = document.getElementById('knowledge')?.offsetTop;
            let pageHeader = 0;
            if (URLParams.passShowType === 'dialog') {
                pageHeader =
                    (this.getDOMNode().dialogHeader as HTMLElement)?.offsetHeight;
            } else {
                pageHeader =
                    (this.children.header.getDOMNode().header as HTMLElement)?.offsetHeight;
            }
            const tabChangeContainer = (this.children.homeIndex.getDOMNode().tabChangeContainer as HTMLElement)?.offsetHeight;
            let allHeight = pageHeader + tabChangeContainer + 30;
            if (!this.state.isFixedTab) {
                allHeight =
                    pageHeader + tabChangeContainer + tabChangeContainer;
            }
            if (scrollTop0 - allHeight <= prevScrollTop) {
                this.setState({
                    isFixedTab: true,
                    scrollTabIndex: 0,
                    pageHeader
                });
            } else {
                this.setState({
                    isFixedTab: false,
                    scrollTabIndex: 0,
                    pageHeader
                });
            }

            if (scrollTop1 - allHeight <= prevScrollTop) {
                this.setState({ scrollTabIndex: 1 });
            }
            if (scrollTop2 - allHeight <= prevScrollTop) {
                this.setState({ scrollTabIndex: 2 });
            }
            if (URLParams.passShowType === 'dialog') {
                this.setDialogheaderBG(prevScrollTop, this.state.theme);
            } else {
                this.children.header.setScrollBg(prevScrollTop, this.state.theme);
            }
        }, 30);
    }
    private maxLimit = 200;
    setDialogheaderBG(scrollTop: number, finalBgColor: string) {

        const headerDom = this.getDOMNode().dialogHeader as HTMLElement;
        const r = parseInt(finalBgColor.slice(1, 3), 16);
        const g = parseInt(finalBgColor.slice(3, 5), 16);
        const b = parseInt(finalBgColor.slice(5, 7), 16);
        const rgb = [r, g, b].join(',');
        const alpha = scrollTop / this.maxLimit;
        headerDom.style.backgroundColor = `rgba(${rgb},${alpha}`;
    }
    dialogclose() {
        webClose();
    }
    getJpeg(dom) {
        return new Promise((resolve) => {
            html2canvas(dom, {
                width: dom?.scrollWidth,
                height: dom?.scrollHeight,
                windowWidth: dom?.scrollWidth,
                windowHeight: dom?.scrollHeight,
                x: 0,
                allowTaint: false,
                useCORS: true,
                y: window.pageYOffset
            }).then((canvas) => {
                const jpeg = canvas.toDataURL('image/jpeg', 1.0);
                // resolve(new Blob([jpeg]));
                resolve(jpeg);
            });
        });
    }
    shareMethod() {
        this.setState({ isFixedTab: false }, () => {
            setTimeout(async () => {
                const sharePageHome =
                    document.getElementById('body-panel-pass');
                const imgSrc = await this.getJpeg(sharePageHome);
                MCProtocol['jiakao-global'].web.previewSharedContent({
                    image: imgSrc,
                    title: '通过率分析',
                    kemu: URLCommon.kemu
                });
            }, 50);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    // 比价必须在商品详情之后
    async getComparePrice() {
        const { goodsInfoPool } = this.state;

        GroupComparePrice({ groupKeys: goodsInfoPool.map(item => item.groupKey).join(',') }).then(comparePricePool => {
            this.setState({ comparePricePool });
        });

    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        let { tabIndex } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach((item) => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async (goodsListInfo) => {

            goodsListInfo.forEach((goodInfo, index) => {
                // 商品未购买才push,或是可升级
                if (!goodInfo.bought || goodInfo.upgrade) {
                    goodInfo.name = goodInfo.upgrade ? '升级' + goodInfo.name : goodInfo.name;
                    newGoodsPool.push(goodInfo);
                }
            });

            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }

            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool
            });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: '',
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });

        });
    }
    buttonBuy = (e) => {
        checkReaded(() => {
            const tabIndex = e.refTarget.getAttribute('data-tabIndex');
            this.setState({ tabIndex }, () => {
                this.payBtnCall(e);
            });

        });
    }
    payBtnCall = (e) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });
        }
    }
    gotoPayGuide() {
        openVipWebView({
            url: PAY_GUIDE_URL
        });
    }
}