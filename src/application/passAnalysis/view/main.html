<import name="style" content="./main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="PriceTag" content=":component/priceTag/main" />
<import name="header" content=":component/header/main" />
<import name="HomeIndex" content="../components/index/main" />
<import name="unionJoinDialog" content="../components/unionJoinDialog/main" />
<div class="page-passAnalysis-home">
    <div class="page-header {{URLParams.inletSource=='zhibojian'?'hide':'show'}} {{URLParams.passShowType=='dialog'?'dialog-header-style':''}}"
        id="page-header">
        <sp:if value='{{URLParams.passShowType=="dialog"}}'>
            <div class="header-dialog-style" ref="dialogHeader" skip-attribute="class,style">
                <div class="showtype-dialog-title">考试分析</div>
                <div class="showtype-dialog-close" sp-on:click="dialogclose">
                    <span class="icon"></span>
                </div>
            </div>
            <sp:else />
            <com:header title="考试分析" theme="white" endTheme="white" scrollTop="{{state.prevScrollTop}}"
                back="{{self.backCall}}">
                <sp:if value="{{!Platform.isHarmony}}">
                    <div sp:slot="right" sp-on:click="shareMethod" class="header-share">
                        分享
                    </div>
                    <sp:else />
                    <div sp:slot="right">
                    </div>
                </sp:if>
            </com:header>
        </sp:if>
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll" ref="bodyPanelContainer">

        <com:HomeIndex name="homeIndex" selectExamTime="{{self.selectExamTime}}" userData="{{state.userData}}"
            examTime="{{state.examTime}}" pageHeader="{{state.pageHeader}}" isVip="{{state.isVip}}"
            userVipIcon="{{state.userVipIcon}}" tabIndex="{{state.scrollTabIndex}}" isFixedTab="{{state.isFixedTab}}"
            parentSelf="{{self}}">
        </com:HomeIndex>
    </div>
    <sp:if value='{{URLParams.inletSource!=="zhibojian"}}'>
        <div class="footer">
            <sp:if value='{{state.isVip&&state.isExpirTime && !Platform.isHarmony}}'>
                <div class="select-exam-button">
                    <div class="button" sp-on:click="selectExamTime">
                        <p class="title">请选择考试时间</p>
                        <p class="desc">为您分析备考情况</p>
                    </div>
                </div>
            </sp:if>
            <sp:if value='{{!state.isVip}}'>
                <div class="button-container">
                    <div class="buy-button">
                        <sp:each for='{{state.goodsInfoPool}}'>
                            <div sp-on:click="buttonBuy" data-tabIndex="{{$index}}"
                                data-fragment="{{$index==0?'底部吸底左侧按钮':'底部吸底右侧按钮'}}"
                                class="{{state.goodsInfoPool.length>=2&&$index===0?'goods-button':'goods-button01'}}   {{state.goodsInfoPool.length===1?'goods-button02':''}}">
                                <div class="goods-button-title">{{$value.name}} {{$value.payPrice}}元</div>
                                <div class="goods-button-desc">{{$value.validDays}}天有效期</div>
                                <sp:if
                                    value='{{(state.goodsInfoPool.length>=2&&$index===1)||(state.goodsInfoPool.length==1)}}'>
                                    <com:PriceTag class="passrate-label" goodsInfo="{{$value}}"
                                        comparePriceMap="{{state.comparePricePool}}" labelMap="{{state.labelPool}}" />
                                </sp:if>

                            </div>
                        </sp:each>

                    </div>
                    <div class="xieyi">
                        <com:readProtocol theme="kqfd-dialog" protocolText2="《会员协议》" protocolText1="购买即表示您同意" />
                        <sp:if value='{{Platform.isIOS}}'>
                            <div class="pay-guide" sp-on:click="gotoPayGuide">支付教程 ></div>
                        </sp:if>
                        <!-- 暂不做优惠券 -->
                        <!-- <div class="coupon-pick">
                                    {{self.nowCouponInfo.couponCode?'已优惠' +
                                    self.nowCouponInfo.priceCent + '元':''}}
                                </div> -->
                    </div>

                </div>
            </sp:if>

        </div>
    </sp:if>
    <com:payDialog />
    <com:buyButton />
    <com:unionJoinDialog />
</div>


<!-- <com:buyButton>
        <div sp:slot="couponEntry" class="go_coupon">
            {{self.nowCouponInfo.couponCode?'已优惠' +
            self.nowCouponInfo.priceCent + '元':''}}
        </div>
    </com:buyButton> -->
