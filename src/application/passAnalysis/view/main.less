body {
    background: transparent !important;
}
.page-passAnalysis-home {
    background: #e5f3ff;
    height: 100%;
    display: flex;
    flex-direction: column;

    .page-header {
        position: absolute;
        z-index: 1000;
        top: 0;
        left: 0;
        width: 100%;

        &.show {
            display: block;
        }
        &.hide {
            display: none;
        }
        &.dialog-header-style {
            z-index: 2;
        }

        .header-share {
            font-size: 15px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #000000;
        }
        .header-dialog-style {
            height: 55px;
            line-height: 55px;
            padding: 0px;
            max-width: 375px;
            .showtype-dialog-close {
                position: absolute;
                z-index: 0;
                width: 60px;
                height: 40px;
                top: 0px;
                right: 0px;
                padding-right: 15px;
                text-align: right;

                .icon {
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    background: url(../images/close.png) no-repeat center;
                    background-size: 100%;
                }
            }
            .showtype-dialog-title {
                flex: 1;
                font-size: 18px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 700;
                text-align: center;
                color: #333333;
            }
        }
    }

    .body-panel {
        flex: 1;
        background: #e5f3ff;
        overflow-y: scroll;
    }

    .footer {
        margin-top: -20px;
        position: relative;
        z-index: 10;
        &.hide {
            display: none;
        }

        .select-exam-button {
            width: 100%;
            height: 69px;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #ffffff;

            .button {
                width: 345px;
                height: 44px;
                background: #04a5ff;
                border-radius: 22px;
                text-align: center;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .title {
                    font-size: 16px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #ffffff;
                    line-height: 22px;
                }

                .desc {
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #ffffff;
                }
            }
        }
        .button-container {
            padding-top: 10px;
            width: 100%;
            height: 113px;
            background: linear-gradient(180deg, #ffffff, #ffffff);
            border-radius: 10px 10px 0px 0px;
            box-shadow: 0px -3px 7px 0px rgba(208, 192, 192, 0.12);
            .panel-label {
            }
            .buy-button {
                display: flex;
                align-items: center;
                justify-content: center;
                padding-bottom: 10px;

                .goods-button {
                    width: 160px;
                    height: 50px;
                    background: linear-gradient(
                        113deg,
                        #353b4e 10%,
                        #1d222b 91%
                    );
                    border-radius: 28px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                }
                .goods-button01 {
                    width: 185px;
                    height: 50px;
                    background: linear-gradient(315deg, #ff4a40, #ff7d76);
                    border-radius: 28px;
                    margin-left: 10px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                }
                .goods-button02 {
                    width: 355px;
                    height: 50px;
                    background: linear-gradient(315deg, #ff4a40, #ff7d76);
                    border-radius: 28px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                    margin-left: 0px;
                }
                .button-xuan-fu {
                    position: absolute;
                    top: -18px;
                    right: 0px;
                    width: 100px;
                    height: 22px;
                    background: linear-gradient(180deg, #ffee6d, #ffc806);
                    border-radius: 14px 14px 14px 0px;
                    font-size: 11px;
                    font-family: PingFangSC, PingFangSC-Medium;
                    font-weight: 500;
                    text-align: center;
                    color: #333330;
                    line-height: 22px;
                    text-align: center;
                }
                .goods-button-title {
                    font-size: 14px;
                    font-family: PingFangSC, PingFangSC-Semibold;
                    font-weight: 600;
                    color: #ffffff;
                    line-height: 20px;
                }
                .goods-button-desc {
                    font-size: 10px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    color: rgba(255, 255, 255, 0.8);
                    line-height: 14px;
                }
                .passrate-label {
                    top: -10px !important;
                    .tip {
                        display: inline-block;
                        height: 18px;
                        background: linear-gradient(
                            110deg,
                            #ffd74f 3%,
                            #ffc634 86%
                        );
                        border-radius: 0px 10px 0px 8px;
                        padding: 0px 8px;
                        font-size: 11px;
                        font-family: PingFangSC, PingFangSC-Regular;
                        font-weight: 400;
                        color: #8c2801;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transform: scale(1);
                    }
                    .time-tip {
                        display: inline-block;
                        height: 18px;
                        transform: scale(1);
                        background: linear-gradient(
                            110deg,
                            #ffd74f 3%,
                            #ffc634 86%
                        );
                        border-radius: 0px 10px 0px 8px;
                        padding: 0px 8px;
                        font-size: 11px;
                        color: #8c2801;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
            }
            .xieyi {
                padding-left: 10px;
                display: flex;
                justify-content: space-between;
                .pay-guide {
                    font-size: 12px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    color: #1dacf9;
                    line-height: 17px;
                    margin-right: 17px;
                }
                .coupon-pick {
                    height: 30px;
                    font-size: 12px;
                    line-height: 30px;
                    background: #ffffff;
                    box-sizing: border-box;
                    margin-top: 2px;
                    color: #741b01;
                    text-align: right;
                }
            }
        }
    }
}
