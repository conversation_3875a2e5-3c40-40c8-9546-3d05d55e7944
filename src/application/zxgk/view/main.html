<import name="style" content="./main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="header" content=":component/header/main" />
<import name="readProtocol" content=":component/readProtocol/main" />

<import name="indexPage" content=":application/zxgk/component/index/main" />
<import name="loading" content=":application/buyed/components/loading/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />

<div class="page-container page-zxgk">
    <div class="page-header">
        <com:header title="专项攻克训练" theme="white"
            endTheme="white" scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}" />
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <com:indexPage expireTimeString="{{state.expireTimeString}}" hasPromission="{{state.hasPromission}}"
            goodsInfoPool="{{state.goodsInfoPool}}" pageShow="{{self.pageShow}}"></com:indexPage>
    </div>
    <div class="footer">
        <sp:if value="Platform.isAndroid && !state.hasPromission">
            <com:payType theme="horizontal" />
        </sp:if>
        <div class="buy-button">
            <sp:if value='{{state.hasPromission}}'>
                <div class="goods-button02" sp-on:click="gotoZhibojian"  data-fragment="底部吸底按钮">
                    <div class="goods-button-title">进入当前直播</div>
                    <div class="goods-button-desc">{{state.expireTimeString}}到期</div>
                </div>
                <sp:else />
                <sp:each for='{{state.goodsInfoPool}}'>
                    <div sp-on:click="buttonBuy" data-tabIndex="{{$index}}"
                        data-fragment="{{$index==0?'底部吸底左侧按钮':'底部吸底右侧按钮'}}"
                        class="{{state.goodsInfoPool.length>=2&&$index===0?'goods-button':'goods-button01'}}   {{state.goodsInfoPool.length===1?'goods-button02':''}}">
                        <sp:if value='{{$index===0}}'>
                            <div class="goods-button-title">{{$value.payPrice}}元立即开通</div>
                            <sp:else />
                            <div class="goods-button-title">
                                {{$value.payPrice}}元{{!$value.upgrade?'开通':''}}{{$value.name}}
                            </div>
                        </sp:if>

                        <div class="goods-button-desc">{{$value.validDays}}天有效期</div>
                        <sp:if value='{{state.goodsInfoPool.length>=2&&$index===1}}'>
                            <div class="button-xuan-fu">考不过补偿140元</div>
                        </sp:if>

                    </div>
                </sp:each>
            </sp:if>
            
        </div>
        <sp:if value='{{!state.hasPromission}}'>
            <com:readProtocol  theme="kqfd-dialog"/>
        </sp:if>

    </div>
    <com:payDialog />

    <com:buyButton />
    <com:loading />
    <com:expiredDialog />
</div>