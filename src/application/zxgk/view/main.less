.page-zxgk {
    background: linear-gradient(90deg, #ebf2f4 10%, #b5e9fc 99%);
    height: 100%;

    .page-header {
        position: absolute;
        z-index: 1000;
        top: 0;
        left: 0;
        width: 100%;
    }

    .body-panel {
        flex: 1;
        overflow-y: scroll;
    }

    .footer {
        position: relative;
        z-index: 10;
        border-radius: 16px 16px 0 0;
        padding: 18px 15px 10px 15px;
        background: linear-gradient(180deg, #F0FDFD 10%, #b5e9fc 99%);
        padding-bottom: calc(~"10px + constant(safe-area-inset-bottom)/2"
            ) !important;
        /* 兼容 iOS < 11.2 */
        padding-bottom: calc(~"10px + env(safe-area-inset-bottom)/2");

        .buy-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding-bottom: 10px;

            .goods-button {
                width: 140px;
                height: 59px;
                background: url(../images/button-left.png) no-repeat center;
                background-size: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
            }

            .goods-button01 {
                width: 190px;
                height: 59px;
                background: url(../images/button-right.png) no-repeat center;
                background-size: 100%;
                margin-left: 15px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
            }

            .goods-button02 {
                width: 335px;
                height: 52px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
                background: linear-gradient(90deg, #fe7b8b 10%, #f1341e 53%, #fbc0b8 93%);
                border-radius: 26px;
                box-shadow: 1px 1px 3px 0px rgba(254, 144, 161, 0.78);
            }

            .button-xuan-fu {
                position: absolute;
                top: 0px;
                right: 5px;
                transform: translateY(-100%);
                width: 116px;
                height: 39px;
                text-align: center;
                padding-top: 9px;
                font-size: 12px;
                color: #6E2F1C;
                background: url(../images/<EMAIL>) no-repeat center center/cover;
            }

            .goods-button-title {
                font-size: 15px;
                font-family: PingFangSC, PingFangSC-Semibold;
                font-weight: 600;
                color: #ffffff;
                line-height: 20px;
            }

            .goods-button-desc {
                font-size: 11px;
                font-family: PingFangSC, PingFangSC-Regular;
                font-weight: 400;
                color: #ffffff;
                line-height: 14px;
            }
        }
    }
}
