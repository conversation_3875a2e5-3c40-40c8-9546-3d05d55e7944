/*
 * main
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 16/3/24
 */
import { URLCommon, URLParams } from ':common/env';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import KqfdBuyDialog from ':application/kqfd/component/kqfdBuyDialog/main';
import { trackEvent, trackGoPay } from ':common/stat';
import { GoodsInfo } from ':store/goods';
import { getLessonList, getLiveLessonSchedule, getLiveWithPriority, subscribe } from ':store/kqfd';
import { dateFormat } from ':common/utils';
import OnlineDialog from ':application/kqfd/component/onlineDialog/main';
import { getAuthToken, openVipWebView, openWeb } from ':common/core';
import { KQFD_HISTORY, REAL_ROOM, SECRET, VIP_LIVE, ZXGK_HISTORY } from ':common/navigate';
import { makeToast } from ':common/features/dom';
import { onPageShow } from ':common/features/page_status_switch';

interface State {
    fragmentName1: string,
    lessonList: any[]
    lessonSchedule: any[]
}
interface Props {
    goodsInfoPool: GoodsInfo[],
    hasPromission: boolean,
    pageShow()
}

export default class extends Component<State, Props> {
    declare children: {
        kqfdBuyDialog: KqfdBuyDialog,
        onlineDialog: OnlineDialog
    }
    // 获取最新直播时间
    get newSchelTime() {
        const { lessonSchedule } = this.state;
        // 去最新的直播中，预约，回放
        const newZhiboData = [];
        lessonSchedule && lessonSchedule.forEach((res) => {
            newZhiboData.push(...res.liveDataList);
        });
        // status:1直播中，2预约，3回放
        const sortByData: any = newZhiboData && newZhiboData.sort((a, b) => {
            return +a.status - +b.status;
        });

        // eslint-disable-next-line max-len
        const timeString = dateFormat(sortByData[0]?.beginTime, 'MM.dd') + ' ' + dateFormat(sortByData[0]?.beginTime, 'HH:mm');
        return timeString;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            fragmentName1: '',
            lessonList: [],
            lessonSchedule: []
        };

    }
    willReceiveProps() {
        return true;
    }
    async didMount() {
        this.getLessonList();
        this.getZhiboData();
    }

    getLessonList() {
        getLessonList({
            page: 1,
            limit: 15,
            tagKey: 'ZXGK'
        }).then((data: any) => {

            this.setState({
                lessonList: data.itemList
            });
        });
    }
    async getZhiboData() {
        const lessonSchedule: any = await getLiveLessonSchedule({
            limit: 2,
            lessonType: 2
        });

        this.setState({ lessonSchedule });

        if (lessonSchedule.length <= 0) {
            this.children.onlineDialog.show();
        }
    }
    dialogBuy = (e) => {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        // 点击支付按钮打点
        trackGoPay({
            groupKey: this.props.goodsInfoPool[0].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        this.setState({ fragmentName1 }, () => {
            this.children.kqfdBuyDialog.show();
        });
    }
    parentZhibojian() {
        getLiveWithPriority({
            lessonType: 2
        }).then(data => {
            openWeb({
                url: 'http://jiakao.nav.mucang.cn/topLesson/live?id=' + data.value + '&from=' + URLParams.from + '&backHome=0&fromItemCode=' + URLParams.fromItemCode
            });
        });
    }
    gotoZhibojian = (e) => {
        const liveSessionId = e.refTarget.getAttribute('data-liveSessionId');
        if (this.props.hasPromission) {
            openWeb({
                url: 'http://jiakao.nav.mucang.cn/topLesson/live?id=' + liveSessionId + '&from=' + URLParams.from + '&backHome=0&fromItemCode=' + URLParams.fromItemCode
            });
        } else {
            this.dialogBuy(e);
        }
    }
    goLesson(e) {
        const lessonId = e.refTarget.getAttribute('data-lessonid');
        if (this.props.hasPromission) {
            openWeb({
                url: 'http://jiakao.nav.mucang.cn/topLesson/detail?id=' + lessonId + '&from=' + URLParams.from + '&backHome=0&fromItemCode=' + URLParams.fromItemCode
            });
        } else {
            this.dialogBuy(e);
        }
    }
    gotoZiliao = (e) => {
        if (this.props.hasPromission) {
            trackEvent({
                fragmentName1: '',
                actionName: '资料包',
                actionType: '点击',
                payStatus: 1
            });
            openWeb({
                url: SECRET + window.location.search.replace('noTopInset', 'removenoTopInset')
            });
        } else {
            this.dialogBuy(e);
        }

    }
    async makeappointment(e) {
        const { lessonSchedule } = this.state;
        e.stopPropagation();
        const lessonId = e.refTarget.getAttribute('data-liveSessionId');
        const index = e.refTarget.getAttribute('data-index');
        const sonIndex = e.refTarget.getAttribute('data-sonIndex');
        await subscribe({ lessonId });
        lessonSchedule[index].liveDataList[sonIndex].subscribeStatus = 1;

        this.setState({
            lessonSchedule
        });

    }
    async viewHistory() {
        openVipWebView({
            url: ZXGK_HISTORY + window.location.search
        });

        await new Promise<void>(resolve => {
            onPageShow(() => {
               
                if (document.visibilityState === 'visible') {
                    resolve();
                }
            });
        });
    
        if (this.props.pageShow) {
            this.props.pageShow();
        }

    }
}
