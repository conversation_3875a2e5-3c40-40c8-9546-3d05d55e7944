.zxgk-index {
    .head-bg {
        height: 453px;
        background: url(../images/<EMAIL>) no-repeat center center/cover;
        position: relative;

        .index-top-time {

            font-size: 17px;
            font-weight: 500;
            color: #000;
            position: absolute;
            top: 82px;
            right: 11px;
            width: 194px;
            height: 34px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .index-buy-button {
        width: 318px;
        height: 50px;
        background-color: #FE5870;
        border-radius: 25px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 2px auto 0px;
        z-index: 1;
        position: relative;

        .buy-title {
            font-size: 17px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            color: #ffffff;
            line-height: 24px;
            margin-top: -5px;
        }

        .buy-desc {
            font-size: 10px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.8);
            line-height: 14px;
        }
    }

    .img1-box {
        .img1-t {
            width: 127px;
            height: 60px;
            background: url(../images/1.png) no-repeat center center/cover;
            margin: 4px auto 0;

        }

        .img1-c {
            width: 345px;
            height: 130px;
            background: url(../images/2.png) no-repeat center center/cover;
            margin: 0 auto;
        }
    }

    .img2-box {

        .img2-t {
            margin: 9px auto 0;
            width: 285px;
            height: 75px;
            background: url(../images/3.png) no-repeat center center/cover;
        }

        .content {
            padding: 15px;
            width: 345px;
            background: #ffffff;
            border-radius: 14px;
            margin: 0 auto;

            .step-list {
                display: flex;
                justify-content: space-between;

                .step-item {
                    width: 99px;
                    height: 134px;
                    text-align: center;
                    background: #f8f8f8;
                    border-radius: 6px;
                    padding: 12px 0;

                    .icon {
                        width: 33px;
                        height: 33px;
                        margin: 0 auto;
                    }

                    &:nth-last-of-type(1) {
                        .icon {
                            background: url(../images/6.png) no-repeat center center/cover;
                        }
                    }

                    &:nth-last-of-type(2) {
                        .icon {
                            background: url(../images/5.png) no-repeat center center/cover;
                        }
                    }

                    &:nth-last-of-type(3) {
                        .icon {
                            background: url(../images/4.png) no-repeat center center/cover;
                        }
                    }

                    .title {
                        margin-top: 5px;
                        color: #211f38;
                        font-size: 15px;
                        line-height: 22px;
                        font-weight: bold;
                    }

                    .dec {
                        margin-top: 7px;
                        font-size: 14px;
                        color: #434343;
                        line-height: 20px;
                    }

                    .dec1 {
                        font-size: 14px;
                        color: #434343;
                        line-height: 20px;
                    }
                }
            }

            .step1-box {
                .step1-title {
                    width: 271px;
                    height: 29px;
                    margin-top: 18px;
                    background: url(../images/7.png) no-repeat center center/cover;
                }

                .step1-content {
                    margin-top: 19px;
                    overflow-x: auto;
                    display: flex;
                    margin-right: -15px;

                    .step1-item {
                        margin-right: 10px;
                        width: 96px;
                        flex-shrink: 0;

                        .img {
                            width: 96px;
                            height: 120px;
                            background-repeat: no-repeat;
                            background-position: center;
                            background-size: cover;
                            border-radius: 4px;
                            overflow: hidden;
                        }

                        .dec {
                            margin-top: 8px;
                            font-size: 15px;
                            line-height: 21px;
                            overflow: hidden;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 2;
                            text-overflow: ellipsis;
                        }
                    }
                }
            }

            .step2-box {
                .step2-title {
                    width: 308px;
                    height: 29px;
                    margin-top: 18px;
                    background: url(../images/8.png) no-repeat center center/cover;
                }

                .step2-title-dec {
                    margin-top: 9px;
                    font-size: 12px;
                    color: #666;
                    line-height: 18px;
                }

                .zhibo-box {
                    margin-top: 20px;


                    .time-title {
                        text-align: center;
                        color: #111B30;
                        font-size: 17px;
                        font-weight: bold;

                    }

                    .zhibo-item {
                        border-radius: 10px;
                        overflow: hidden;
                        margin-top: 10px;

                        .title {
                            color: white;
                            font-size: 15px;
                            background: linear-gradient(90deg, #34a7ff 2%, #85d4ff);
                            height: 33px;
                            display: flex;
                            align-items: center;

                            .num {
                                margin-left: 15px;
                            }

                            .right-day {
                                margin-left: 10px;
                            }

                            .right-time {
                                margin-left: 6px;
                            }

                            .zhizho1 {
                                display: inline-block;
                                width: 55px;
                                height: 22px;
                                line-height: 22px;
                                background: #fe674f;
                                border-radius: 4px;
                                font-size: 13px;
                                font-family: PingFangSC, PingFangSC-Semibold;
                                font-weight: 600;
                                text-align: center;
                                color: #ffffff;
                                margin-right: 5px;
                                margin-left: auto;
                            }

                            .zhizho2 {
                                width: 65px;
                                height: 22px;
                                line-height: 22px;
                                background: #1696FF;
                                border-radius: 4px;
                                font-size: 13px;
                                font-family: PingFangSC, PingFangSC-Semibold;
                                font-weight: 600;
                                text-align: center;
                                color: white;
                                margin-right: 5px;
                                margin-left: auto;
                            }

                            .zhizho3 {
                                width: 55px;
                                height: 22px;
                                background: #FEEF88;
                                border-radius: 4px;
                                font-size: 13px;
                                font-family: PingFangSC, PingFangSC-Semibold;
                                font-weight: 600;
                                text-align: center;
                                color: #211F38;
                                line-height: 22px;
                                margin-right: 5px;
                                margin-left: auto;
                            }
                        }

                        .zhibo-item-content {
                            padding: 15px;
                            background-color: #EFF8FF;
                            font-size: 14px;
                            color: #111B30;
                            line-height: 20px;
                        }
                    }
                }

                .look-more-zhibo {
                    margin-top: 15px;
                    text-align: center;
                    color: #04a5ff;
                    font-size: 14px;
                }
            }

            .step3-box {
                .step3-title {
                    width: 308px;
                    height: 30px;
                    margin-top: 25px;
                    background: url(../images/9.png) no-repeat center center/cover;
                }

                .step3-content {
                    margin-top: 15px;
                    display: flex;
                    justify-content: space-between;

                    .step3-item {
                        width: 152px;
                        height: 73px;
                        background: #f8f8f8;
                        border-radius: 11px;
                        padding: 15px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        .l {
                            color: #111B30;
                            font-size: 14px;
                            font-weight: bold;
                            line-height: 20px;
                        }

                        .r {
                            width: 40px;
                            height: 40px;
                        }

                        &:nth-of-type(1) {
                            .r {
                                background: url(../images/10.png) no-repeat center center/cover;
                            }
                        }

                        &:nth-of-type(2) {
                            .r {
                                background: url(../images/11.png) no-repeat center center/cover;
                            }
                        }
                    }
                }
            }
        }
    }

    .zhibo-common-question {
        margin-top: 15px;

        .common-question-title {
            width: 130px;
            height: 60px;
            margin: 0 auto;
            background: url(../images/12.png) no-repeat center center/cover;
        }
    }
}
