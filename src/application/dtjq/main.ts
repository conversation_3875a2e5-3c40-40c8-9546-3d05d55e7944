/*
 * ------------------------------------------------------------------
 * 答题技巧
 * ------------------------------------------------------------------
 */

import { ABTestKey, ABTestType, setPageName, URLParams } from ':common/env';
import { getAbtest } from ':store/chores';

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
export default class extends Application {
    $constructor() {
        const fromPage = URLParams.fromPage || '答题技巧弹窗页';
        const fragmentName1 = URLParams.fragmentName1 || '未知片段';

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        setPageName(fromPage);

        this.state = {
            fragmentName1,
            title1: '答题技巧',
            subTitle1: '顺口溜巧记题，生动简单易懂'
        };

    }
    async didMount() {
        const { strategy } = await getAbtest();
        
        strategy[ABTestKey.key29] = strategy[ABTestKey.key29] || ABTestType.A;

        this.setState({
            strategy
        });

    }
}
