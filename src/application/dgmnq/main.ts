/*
 * ------------------------------------------------------------------
 * 灯光模拟科三VIP购买居中弹窗
 * 
 * 注意：iOS端的弹窗是原生实现的
 * ------------------------------------------------------------------
 */

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { CarType, PayType, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayTypeCom from ':component/payType/main';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackGoPay } from ':common/stat';
import { setEmbeddedHeight } from ':common/features/embeded';
import { openVipWebView, webClose } from ':common/core';
import { iosDialogBuySuccess } from ':common/features/ios_pay';
import { Coupon, getBestCoupon, goodsInfoWithCoupon } from ':common/features/coupon';
import { BUYED_URL } from ':common/navigate';
import { onPageShow } from ':common/features/page_status_switch';

setPageName(URLParams.fromPage || URLParams.pageName || '灯光模拟练习开始页');
const fragmentName1 = URLParams.fragmentName1 || '未知片段';

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
}

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeCom;
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
    * 如果有优惠券的价格为0的就显示0
    * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
   */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;

            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    $constructor() {

        this.$super({
            name: module.id,
            target: document.body,
            view: View
        });

        const goodsInfoPool = [];
        if (Platform.isWeiyu) {
            if (URLCommon.tiku === CarType.CAR) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe3
                } as GoodsInfo);

            } else if (URLCommon.tiku === CarType.TRUCK) {
                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKe3
                } as GoodsInfo);

            }
        } else if (URLCommon.tiku === CarType.CAR) {
            goodsInfoPool.push({
                groupKey: GroupKey.ChannelKe3
            } as GoodsInfo);
            goodsInfoPool.push({
                groupKey: GroupKey.ChannelKe34
            } as GoodsInfo);

        } else if (URLCommon.tiku === CarType.TRUCK) {
            goodsInfoPool.push({
                groupKey: GroupKey.HcChannelKe3
            } as GoodsInfo);

            goodsInfoPool.push({
                groupKey: GroupKey.HcChannelKe34
            } as GoodsInfo);

        } 
        
        this.state = {
            tabIndex: 0,
            goodsInfoPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {}
        };
    }

    async didMount() {

        // app代理方法
        this.appEventProxy();

        await this.getGoodInfo();

        trackGoPay({
            groupKey: this.nowGoodInfo.groupKey,
            fragmentName1,
            payPathType: 0
        });

        ensureSiriusBound({ groupKey: this.nowGoodInfo.groupKey, type: PayBoundType.GoLogin });

    }

    tabChangeCall = (tabIndex) => {

        if (tabIndex === this.state.tabIndex) {
            return;
        }

        this.setState({
            tabIndex
        }, () => {
            this.setPageInfo();
        });

    }
    appEventProxy() {
        if (Platform.isAndroid) {
            setEmbeddedHeight(385 / 350);
        } else {
            setEmbeddedHeight(385 / 320);
        }
    }

    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {

            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    openVipWebView({
                        url: BUYED_URL + '?iosH5Head=show'
                    });
                    (new Promise<void>(resolve => {
                        onPageShow(resolve);
                    })).then(() => {
                        webClose();
                    });
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            this.setState({
                goodsInfoPool: newGoodsPool
            });

            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getLabel();
                await this.getComparePrice();

                console.log(this.state);

                this.setPageInfo();
            }, 60);

        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice,
                        groupItems: item.groupItems
                    };
                }
            });

            this.setState({ comparePricePool });
        });
    }
    private setPageInfo() {
        this.children.buyButton.setPay({
            androidPay: this.pay.bind(this),
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey }),
            isInDialog: true
        });
        this.children.buyButton.setButtonConfig({
            groupKey: this.nowGoodInfo.groupKey,
            type: 3,
            title: `¥ ${this.showPrice} 确认协议并支付`,
            fragmentName1,
            fragmentName2: '支付弹窗'
        });
    }
    /** 发起支付 */
    async pay(stat: PayStatProps) {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey }, 2);
        });
    }

    onCloseClick() {
        webClose();
    }
}
