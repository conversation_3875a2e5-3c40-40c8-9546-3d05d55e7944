html,
body {
    max-width: inherit !important;
    height: 100vh;
    overflow: hidden;
}

.dgmnq-page {
    position: relative;
    padding: 20px 20px 0;
    height: 100%;
    background: #ffffff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;

    .close {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 17px;
        height: 17px;
        background: url("../images/close.png");
        background-size: cover;
    }

    .bottom-tabs {
        padding: 25px 0 0;

        .hd-tabs {
            .hd-tab {
                width: 165px;
                height: 42px;
                border-radius: 8px;
                background-color: #fff;

                &.active {
                    background: linear-gradient(113deg,
                            #353b4e 0%,
                            #1d222b 100%);
                }
            }
        }
    }

    .alone-box {
        .alone {
            margin-top: 10px;
            height: 45px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            background: linear-gradient(113deg, #353b4e 0%, #1d222b 100%);
            border-radius: 6px;
            font-size: 14px;
        }
    }

    .s-title{
        font-size: 14px;
        text-align: center;
        margin: 10px 0;
    }

    .tab-content {
        position: relative;
        height: 100px;
        border-radius: 6px;
        box-sizing: border-box;
    }

    .tab-content1 {
        .add-buy {
            display: flex;
            justify-content: space-around;

            .step {
                width: 78px;
                height: 88px;
                background: url(../images/13.png) no-repeat center center/cover;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;

                .icon-multi {
                    width: 51px;
                    height: 19px;
                    background: url(http://exam-room.mc-cdn.cn/exam-room/2022/07/12/11/baa34cadd2524f2088f3bad93e562116.png) no-repeat center center/cover;
                    position: absolute;
                    right: -20px;
                    top: -10px;
                }

                .c {
                    width: 60px;
                    font-size: 13px;
                    color: #8c3418;
                    text-align: center;
                    font-weight: bold;
                    margin-bottom: 12px;
                    line-height: 18px;
                }

                .b {
                    font-size: 12px;
                    position: absolute;
                    bottom: 0;
                    height: 22px;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #a03c1c;
                }

                &::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 32px;
                    height: 14px;
                }

                // &:nth-of-type(1)::before {
                //     background: url(../images/<EMAIL>) no-repeat center center/cover;
                // }

                // &:nth-of-type(2)::before {
                //     background: url(../images/<EMAIL>) no-repeat center center/cover;
                // }

                // &:nth-of-type(3)::before {
                //     background: url(../images/<EMAIL>) no-repeat center center/cover;
                // }

                // &:nth-of-type(4) {
                //     width: 78px;
                //     height: 88px;
                //     background: url(../images/14.png) no-repeat center center/cover;
                //     display: flex;
                //     align-items: center;
                //     justify-content: center;

                //     .c {
                //         width: 70px;
                //         font-size: 14px;
                //         color: #aa4120;
                //         text-align: center;
                //         font-weight: bold;
                //         margin-bottom: 0;
                //     }
                // }
            }
        }
    }

    .tab-content2 {
        padding: 7px;
        padding-left: 64px;
        position: relative;
        background: url(../images/19.png) no-repeat center center/cover;

        .diff-box {
            position: absolute;
            left: 0;
            height: 100%;
            width: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 7px;
            box-sizing: border-box;

            .diff {
                line-height: 17px;
                color: #aa4120;
                font-size: 12px;
                text-align: center;

                .unit {
                    color: #f73b31;
                }

                .price {
                    font-size: 16px;
                    color: #f73b31;
                }
            }
        }

        .compare-box {
            display: flex;
            justify-content: space-between;

            .item {
                width: 65px;
                height: 86px;
                background: #fffbf6;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                border-radius: 5px;
                border: 1px solid #fddec8;
                color: #a03c1c;
                position: relative;
                flex: 1;

                .name {
                    font-weight: bold;
                    font-size: 13px;
                    text-align: center;
                }

                .price-box {
                    margin-top: 8px;
                    font-weight: bold;

                    .unit {
                        font-size: 12px;
                    }

                    .price {
                        font-size: 16px;
                    }
                }

                &:not(:last-child)::after {
                    content: "";
                    width: 17px;
                    height: 17px;
                    background: url(../images/<EMAIL>) no-repeat center center/cover;
                    position: absolute;
                    z-index: 1;
                    top: 50%;
                    right: 0;
                    transform: translate(10px, -50%);
                }
            }
        }
    }

    .pay-type {
        margin-bottom: -15px;
        margin-top: -10px;
    }
    .buy-btn{
        .buy-footer {
            padding: 10px 0 0;
        }
    }

}
