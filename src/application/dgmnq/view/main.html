<import name="style" content="./main" />
<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />

<div class="dgmnq-page container">
    <div class="hotArea close" sp-on:click="onCloseClick"></div>

    <sp:if value="state.goodsInfoPool.length > 1">
        <com:bottomTabs
            tabIndex="{{state.tabIndex}}"
            labelPool="{{state.labelPool}}"
            comparePricePool="{{state.comparePricePool}}"
            goodsList="{{state.goodsInfoPool}}"
            tabChange="{{self.tabChangeCall}}"
        />
        <sp:else />
        <div class="alone-box">
            <sp:if value="URLParams._lang == 'ug'">
                <div class="alone" dir="rtl">¥ {{state.goodsInfoPool[0].payPrice}} {{state.goodsInfoPool[0].name}} </div>
                <sp:else/>
                <div class="alone">{{state.goodsInfoPool[0].name}} {{state.goodsInfoPool[0].payPrice}}元</div>
            </sp:if>
        </div>
    </sp:if>
    <div class="s-title">
        <!-- 正常版的逻辑是如果sTitleMap中能找到就使用，不能找到就看是否有比价（只有2个全科在里面找不到，不能先判断比价，因为不是所有比价的都有直播课） -->
        <sp:if value="state.comparePricePool[self.nowGoodInfo.groupKey]">
            -  一起买更实惠  -
            <sp:else/>
            -  限时专项{{state.labelPool[self.nowGoodInfo.groupKey].highlights.length}}大权益  -
        </sp:if>
    </div>
    <sp:if value="state.comparePricePool[self.nowGoodInfo.groupKey]">
        <div class="tab-content tab-content2">
            <div class="diff-box">
                <div class="diff">
                    <span>比分开买立省</span>
                    <span class="unit">￥</span>
                    <span class="price"
                        >{{state.comparePricePool[self.nowGoodInfo.groupKey].diffPrice}}</span
                    >
                </div>
            </div>
            <div class="compare-box">
                <sp:each
                    for="state.comparePricePool[self.nowGoodInfo.groupKey].groupItems"
                >
                    <div class="item">
                        <div class="name">{{$value.name}}</div>
                        <sp:if value="$value.price">
                            <div class="price-box">
                                <span class="unit">￥</span>
                                <span class="price">{{$value.price}}</span>
                            </div>
                            <sp:else />
                            <div class="price-box">
                                <div class="unit">{{$value.description}}</div>
                            </div>
                        </sp:if>
                    </div>
                </sp:each>
            </div>
        </div>
        <sp:else />
        <div class="tab-content tab-content1">
            <div class="add-buy">
                <sp:each
                    for="state.labelPool[self.nowGoodInfo.groupKey].highlights"
                >
                    <div class="step">
                        <div class="c">{{$value.highlight}}</div>
                        <div class="b">{{$value.description}}</div>
                    </div>
                </sp:each>
            </div>
        </div>
    </sp:if>

    <div class="buy-btn">
        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元':''}}
            </div>
        </com:buyButton>
    </div>
</div>
