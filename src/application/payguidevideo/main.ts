/*
 * ------------------------------------------------------------------
 * ios支付引导页查看视频
 * ------------------------------------------------------------------
 */

import { URLParams } from ':common/env';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
import Texts from ':common/features/texts';
interface State {
    payType: string,
    poster: {
        weixin: string,
        alipay: string
    },
    playUrl: {
        weixin: string,
        alipay: string
    },
    cover: {
        weixin: string,
        alipay: string
    },
    title: {
        weixin: string,
        alipay: string
    }
}
export default class extends Application<State> {
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {
            payType: URLParams.payType || 'weixin',
            poster: {
                weixin: 'http://exam-room.mc-cdn.cn/exam-room/2022/03/15/14/e757ed3142b948bd980641fbc02a6de0.png',
                alipay: 'http://exam-room.mc-cdn.cn/exam-room/2022/03/15/14/d8b18c7ef2af4d5d85d1359ae9c8e59f.png'
            },
            playUrl: {
                weixin: `${Texts.TVHOSTMAP.maiche}/2021/12/09/d2d569ce5eda4e9e83226022e1eb9ce2.middle.mp4`,
                alipay: `${Texts.TVHOSTMAP.maiche}/2021/12/09/34f710b01c5e44ec9d9b8304dfa5f353.middle.mp4`
            },
            cover: {
                weixin: '',
                alipay: ''
            },
            title: {
                weixin: '微信支付教程视频',
                alipay: '支付宝支付教程视频'
            }
        };

    }
    didMount() {
        document.title = this.state.title[this.state.payType];
        MCProtocol.Core.Web.setting({
            title: this.state.title[this.state.payType],
            titleBar: true,
            toolbar: true,
            menu: false,
            button: false
        });
    }
}
