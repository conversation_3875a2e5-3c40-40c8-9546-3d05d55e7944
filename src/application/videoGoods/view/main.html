<import name="style" content="./main" module="S" />
<import name="buyButton" content=":component/buyButton/main" />

<div class=":container">
    <div class=":video" skip="true" ref="videoContainer">
        <video ref="video" id="video" webkit-playsinline x5-playsinline loop type="video/mp4" playsinline="true"
            autoplay sp-on:click="onVideoClick" />
    </div>
    <div class=":close" sp-on:click="onCloseClick"><i /></div>
    <div class=":controls {{state.showControls?'':S.hidden}}">
        <div class=":play" sp-on:click="onVideoClick" />
        <div class=":time" skip="true">
            <div class=":current" ref="currentTime"></div>
            <div class=":divider">/</div>
            <div class=":total" ref="duration"></div>
        </div>
        <div class=":slider" ref="videoSlider" skip="true">
            <div class=":track">
                <div class=":current" ref="videoCurrent">
                    <div class=":handle" ref="handle" />
                </div>
            </div>
        </div>
    </div>
    <div class=":goods-list {{state.showControls?S.hidden:''}}">
        <div
            class=":anchors {{!self.currentVideoInfo.anchorItemList||!self.currentVideoInfo.anchorItemList.length?S.hidden:''}}">
            <sp:each for="{{self.currentVideoInfo.anchorItemList}}">
                <div class=":anchor {{state.selectedAnchor===$index?S['active']:''}}" sp-on:click="onAnchorClick"
                    data-idx="{{$index}}">{{$value.name}}</div>
            </sp:each>
        </div>
        <div class=":scroller" ref="scroller">
            <div class=":scroller-gap" />
            <sp:each for="{{state.goodsList}}">
                <div class=":goods {{state.selectedGoods===$index?S.active:''}}" sp-on:click="onGoodsClick"
                    data-idx="{{$index}}">
                    <img class=":goods-pic" src="{{$value.imageUrl}}" />
                    <div class=":goods-info">
                        <div class=":goods-title">{{$value.name}}</div>
                        <div class=":goods-desc">{{$value.introduce}}</div>
                        <div class=":goods-price">
                            <sp:if value="{{state.goodsInfoList[$index]}}">
                                <span>￥</span>{{state.goodsInfoList[$index].payPrice}}
                            </sp:if>
                        </div>
                        <sp:if value="{{state.goodsInfoList[$index].upgrade}}">
                            <div class=":goods-btn">去升级</div>
                            <sp:elseif value="{{state.goodsInfoList[$index].bought}}" />
                            <div class=":goods-btn">去使用</div>
                            <sp:else />
                            <div class=":goods-btn">立即购买</div>
                        </sp:if>
                    </div>
                </div>
            </sp:each>
            <div class=":scroller-gap" />
        </div>
    </div>
    <div class=":dialog {{state.showDialog?'':S.hide}}">
        <div class=":dialog-content">
            <div class=":dialog-close" sp-on:click="onDialogCloseClick" />
            <div class=":dialog-title">购买</div>
            <div class=":dialog-goods">
                <div class=":dialog-goods-name">{{self.currentVideoInfo.name}}</div>
                <div class=":dialog-goods-price">
                    <span>￥</span>{{self.currentGoods.payPrice}}
                </div>
            </div>
            <com:buyButton>
                <div sp:slot="couponEntry">
                    <div class="coupon-position-bottom" sp-on:click="goCoupon">
                        {{self.currentCoupon.hint}}
                    </div>
                </div>
            </com:buyButton>
        </div>
    </div>
</div>