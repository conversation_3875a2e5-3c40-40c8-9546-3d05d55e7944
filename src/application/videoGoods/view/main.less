.container {
    height: 100vh;
    background: linear-gradient(90deg, #251d5a, #5f1d22);
    position: relative;
    user-select: none;
}

.hide {
    display: none !important;
}

.hidden {
    visibility: hidden;
}

.close {
    position: fixed;
    right: 10px;
    top: 48px;
    width: 25px;
    height: 25px;
    background: rgba(0, 0, 0, 0.15);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    // 扩大点击区域
    &:after {
        content: '';
        position: absolute;
        top: -30%;
        bottom: -30%;
        left: -30%;
        right: -30%;
    }

    i {
        width: 11px;
        height: 11px;
        background-image: url(../images/close.png);
        background-size: cover;
    }
}

.video {
    width: 100%;
    height: 100%;
    position: relative;

    video {
        width: 100%;
        height: 100%;
        visibility: hidden;
    }

    img {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: block;
        visibility: hidden;
    }
}

.controls {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    .play {
        width: 64px;
        height: 64px;
        background-image: url(../images/play.png);
        background-size: cover;
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
    }

    .time {
        margin-bottom: 34px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        font-weight: bold;
        color: rgba(255, 255, 255, 0.70);
        line-height: 24px;
        text-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.40);

        .current {
            color: #ffffff;
        }

        .divider {
            margin: 0 10px;
        }
    }

    .slider {
        margin-bottom: 50px;
        width: 370px;
        height: 60px;
        display: flex;

        .track {
            width: 320px;
            height: 2px;
            margin: auto;
            background: rgba(255, 255, 255, 0.40);
            border-radius: 3px;
            position: relative;
            transition: height 0.15s;

            // 扩大点击响应
            &:after {
                content: '';
                position: absolute;
                left: 0;
                right: 0;
                height: 20px;
                top: 50%;
                transform: translateY(-50%);
            }
        }

        .current {
            left: 0;
            top: 0;
            height: 100%;
            position: absolute;
            background: #ffffff;
            border-radius: 3px;
        }

        .handle {
            position: absolute;
            z-index: 1;
            width: 6px;
            height: 6px;
            right: 0;
            top: 50%;
            transform: translateX(50%) translateY(-50%);
            background: #ffffff;
            border-radius: 50%;
            transition: height 0.15s, width 0.15s;

            &:after {
                position: absolute;
                content: '';
                width: 24px;
                height: 24px;
                top: 50%;
                left: 50%;
                transform: translateX(-50%) translateY(-50%);
            }
        }

        &.touched {
            .track {
                height: 6px;
            }

            .handle {
                width: 12px;
                height: 12px;
            }
        }

        &.clicked {
            .handle {
                width: 8px;
                height: 8px;
            }
        }
    }
}

.goods-list {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 70px;
    // 增大点击范围，防止误点到视频
    padding-top: 20px;
}

.anchors {
    margin-left: 8px;
    margin-bottom: 10px;
    padding: 2px;
    background: linear-gradient(90deg, rgba(0, 75, 117, 0.80), rgba(23, 25, 29, 0.60));
    border-radius: 12px;
    display: inline-block;
    font-size: 0;

    .anchor {
        padding: 0 6px;
        display: inline-block;
        font-size: 11px;
        line-height: 20px;
        color: #ffffff;
        position: relative;

        // 扩大点击区域
        &:after {
            content: '';
            position: absolute;
            top: -30%;
            bottom: -30%;
            left: 0;
            right: 0;
        }

        &.active {
            font-weight: bold;
            background: #04a5ff;
            border-radius: 11px;
        }
    }
}

.scroller {
    display: flex;
    white-space: nowrap;
    overflow-x: auto;

    &::-webkit-scrollbar {
        width: 0;
        height: 0;
        display: none;
    }

    .scroller-gap {
        width: 10px;
        flex-shrink: 0;
    }
}

.goods {
    flex-shrink: 0;
    height: 94px;
    display: flex;
    background: rgba(23, 25, 29, 0.80);
    border: 0.3px solid rgba(244, 247, 247, 0.30);
    border-radius: 14px;

    .goods-pic {
        width: 86px;
        height: 86px;
        margin: auto 4px;
        border-radius: 10px;
        object-fit: cover;
    }

    .goods-info {
        position: relative;
        margin-left: 4px;
        width: 192px;
        display: none;
    }

    .goods-title {
        margin-top: 8px;
        font-size: 15px;
        font-weight: bold;
        color: #ffffff;
        line-height: 21px;
        height: 21px;
        white-space: nowrap;
        max-width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .goods-desc {
        margin-top: 1px;
        font-size: 11px;
        color: #f9dbc0;
        line-height: 16px;
        height: 16px;
        white-space: nowrap;
        max-width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .goods-price {
        margin-top: 16px;
        line-height: 25px;
        font-size: 18px;
        font-weight: bold;
        color: #f9dbc0;

        span {
            font-weight: normal;
            font-size: 12px;
            line-height: 17px;
        }
    }

    .goods-btn {
        position: absolute;
        right: 6px;
        bottom: 6px;
        width: 94px;
        height: 32px;
        background: linear-gradient(135deg, #f9dbc0, #efaf8b);
        color: #3e260b;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        font-weight: bold;
    }

    &.active .goods-info {
        display: block;
    }
}

.goods+.goods {
    margin-left: 4px;
}

.dialog {
    background: rgba(0, 0, 0, 0.65);
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    .dialog-content {
        position: relative;
        background: linear-gradient(180deg, #ffeddc 1%, #fffaf0 38%, #ffffff 77%, #ffffff);
        border-radius: 6px 6px 0px 0px;
        box-shadow: 0px 1px 0px 0px #ffffff inset;
    }

    .dialog-close {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 20px;
        height: 20px;
        background-image: url(../images/close2.png);
        background-size: cover;
    }

    .dialog-title {
        padding-top: 16px;
        padding-left: 15px;
        font-size: 14px;
        color: #9f5217;
        line-height: 20px;
    }

    .dialog-goods {
        margin: 14px auto 0;
        width: 361px;
        height: 91px;
        display: flex;
        background-image: url(../images/goods-check.png), url(../images/goods.png);
        background-size: 30px 30px, cover;
        background-position: bottom 9px right 7px, top left;
        background-repeat: no-repeat;
    }

    .dialog-goods-name {
        margin-left: 23px;
        margin-top: 30px;
        font-size: 20px;
        font-weight: bold;
        color: #681309;
        line-height: 28px;
        flex: 1;
    }

    .dialog-goods-price {
        margin-right: 28px;
        margin-top: 21px;
        font-weight: bold;
        color: #681309;
        font-size: 30px;
        line-height: 42px;

        span {
            font-size: 18px;
            line-height: 25px;
            font-weight: normal;
        }
    }
}