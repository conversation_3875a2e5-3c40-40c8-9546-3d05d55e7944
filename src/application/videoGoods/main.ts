/*
 * ------------------------------------------------------------------
 * VIP商品介绍视频模板
 * ------------------------------------------------------------------
 */

import { openVipWebView, setStatusBarTheme, webClose } from ':common/core';
import View from './view/main.html';
import S from './view/main.less';
import { Application } from '@simplex/simple-core';
import { formatPrice, showClock } from ':common/utils';
import { VideoGoods, getGoodsIntroVideo } from ':store/chores';
import { Platform, URLParams, setPageName } from ':common/env';
import { GoodsInfo, getGroupSessionInfo } from ':store/goods';
import BuyButton from ':component/buyButton/main';
import { PayBoundType, startSiriusPay } from ':common/features/pay';
import { iosBuySuccess } from ':common/features/ios_pay';
import jump from ':common/features/jump';
import { BUYED_URL } from ':common/navigate';
import { Coupon, couponWithHint, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { scrollIntoView } from ':common/features/dom';
import { onWebBack } from ':common/features/persuade';
import { trackEvent, trackGoPay } from ':common/stat';
import { onPageShow } from ':common/features/page_status_switch';

setPageName('VIP商品视频介绍页');

interface State {
    goodsList: VideoGoods[];
    goodsInfoList: GoodsInfo[];
    couponList: Coupon[];
    selectedGoods: number;
    selectedAnchor: number;
    showControls: boolean;
    showDialog: boolean;
}

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
    };

    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            goodsList: [],
            goodsInfoList: [],
            couponList: [],
            selectedGoods: -1,
            selectedAnchor: -1,
            showControls: false,
            showDialog: false
        };

    }

    didMount() {
        setStatusBarTheme('light');
        if (Platform.isIOS) {
            // ios禁用优化返回手势,跟进度条拖动冲突了
            onWebBack(() => {
                return;
            });
        }
        onPageShow(() => {
            // 切到后台会暂停，回到前台继续播放
            if (!this.state.showControls && this.getVideoElement().paused) {
                this.getVideoElement().play();
            }
        });
        this.fetchData();
        this.listenVideo();
        this.listenProgressTouch();
        this.setPayDialog();
    }

    private get currentVideoInfo() {
        return this.state.goodsList[this.state.selectedGoods];
    }

    private get currentGoods() {
        return goodsInfoWithCoupon(this.state.goodsInfoList[this.state.selectedGoods], this.currentCoupon);
    }

    private get currentCoupon() {
        return couponWithHint(this.state.couponList[this.state.selectedGoods]);
    }

    private async fetchData() {
        const { itemList: videoGoods } = await getGoodsIntroVideo({ entrance: URLParams.entrance });
        this.setState({ goodsList: videoGoods });

        // 提前加载海报图
        videoGoods.forEach(g => {
            const img = document.createElement('img');
            img.src = g.videoDTO.thumbnail.url;
            const isPortrait = g.videoDTO.thumbnail.width < g.videoDTO.thumbnail.height;
            if (isPortrait) {
                img.style.objectFit = 'cover';
            } else {
                img.style.objectFit = 'contain';
            }
            this.getVideoContainer().appendChild(img);
        });

        const selectedIndex = videoGoods.findIndex(g => g.channelCode === URLParams.groupKey);
        this.selectVideoGoods(Math.max(0, selectedIndex));

        const goodsInfoList = await getGroupSessionInfo({ groupKeys: videoGoods.map(item => item.channelCode) });
        this.setState({
            goodsInfoList
        });
        this.setPayPrice();

        const couponList = await Promise.all(goodsInfoList.map(getBestCoupon));
        this.setState({ couponList });
        this.setPayPrice();
    }

    /** 选中第几个商品 */
    private async selectVideoGoods(index: number) {
        this.setState({ selectedGoods: index, selectedAnchor: -1 }, () => {
            trackEvent({
                fragmentName1: '商品卡片',
                actionType: '出现',
                actionName: '',
                payStatus: '0',
                videoId: this.currentVideoInfo.videoId
            });
            const scroller = this.getDOMNode().scroller as HTMLElement;
            scrollIntoView(scroller.children[index + 1] as HTMLElement, { center: true });
        });
        this.setCurrentAnchor(0);

        this.changeVideo(this.currentVideoInfo);
    }

    /** 视频播放容器 */
    private getVideoContainer() {
        return this.getDOMNode().videoContainer as HTMLElement;
    }

    /** 视频video元素 */
    private getVideoElement() {
        return this.getVideoContainer().firstElementChild as HTMLVideoElement;
    }

    /** 展示视频海报 */
    private showVideoPoster() {
        const posters = this.getVideoContainer().querySelectorAll('img');
        posters.forEach((poster, i) => {
            if (i === this.state.selectedGoods) {
                poster.style.visibility = 'visible';
            } else {
                poster.style.visibility = 'hidden';
            }
        });
    }

    /** 隐藏视频海报 */
    private hideVideoPoster() {
        const posters = this.getVideoContainer().querySelectorAll('img');
        posters.forEach((poster) => {
            poster.style.visibility = 'hidden';
        });
    }

    /** 设置视频当前时长 */
    private setCurrentTimeDisplay(currentTime: number) {
        const currentTimeEle = this.getDOMNode().currentTime as HTMLElement;
        currentTimeEle.textContent = showClock(currentTime);
    }

    /** 设置视频总时长 */
    private setDurationDisplay(duration: number) {
        const durationEle = this.getDOMNode().duration as HTMLElement;
        durationEle.textContent = showClock(duration);
    }

    /** 设置当前锚点 */
    private setCurrentAnchor(currentTime: number) {
        const anchorItemList = this.currentVideoInfo.anchorItemList;
        if (!anchorItemList) {
            return;
        }

        /** iOS下设置currentTime后，实际currentTime可能会小一些, 这里干脆都加下 */
        const tolerance = 0.5;
        let anchorIndex = anchorItemList.slice().reverse().findIndex((item) => item.second <= currentTime + tolerance);
        if (anchorIndex < 0) {
            // 没找到则设置为最后一个(实际是第一个)
            anchorIndex = anchorItemList.length;
        }

        const selectedAnchor = anchorItemList.length - anchorIndex - 1;
        if (selectedAnchor !== this.state.selectedAnchor) {
            this.setState({ selectedAnchor });
        }
    }

    /** 是否正在拖动进度条 */
    dragging = false;

    /** 监听视频事件 */
    private listenVideo() {
        const videoEle = this.getVideoElement();

        /** iOS下连续两个webview播放视频，则第二个webview的视频播一会后会莫名的暂停 */
        const fixIOSPause = () => {
            if (!Platform.isIOS) {
                return;
            }
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                if (!videoEle.paused) {
                    videoEle.play();
                }
            }, 200);
        };

        /** 进度条当前时长bar */
        const videoCurrentEle = this.getDOMNode().videoCurrent as HTMLElement;

        videoEle.addEventListener('loadedmetadata', () => {
            this.setDurationDisplay(videoEle.duration);

            // 这时候有可能用户已经切到后台了
            if (document.hidden) {
                console.log('document.hidden，先暂停视频');
                videoEle.pause();
            }
            this.setCurrentTimeDisplay(0);
        });
        videoEle.addEventListener('loadeddata', () => {
            // 隐藏海报
            videoEle.style.visibility = 'visible';
            this.hideVideoPoster();
        });
        videoEle.addEventListener('timeupdate', () => {
            fixIOSPause();
            this.setCurrentAnchor(videoEle.currentTime);
            if (!this.dragging) {
                this.setCurrentTimeDisplay(videoEle.currentTime);
                // 这里用百分比长度
                videoCurrentEle.style.width = `${videoEle.currentTime / videoEle.duration * 100}%`;
            }
        });
        videoEle.addEventListener('error', err => {
            console.log('视频出错', err);
        });
        videoEle.addEventListener('pause', (e) => {
            console.log('视频暂停', e.target);
        });
    }

    /** 监听进度条触摸事件 */
    private listenProgressTouch() {
        /** 进度条触摸区域 */
        const sliderEle = this.getDOMNode().videoSlider as HTMLElement;
        const { left: leftBound, right: rightBound, top: topBound, bottom: bottomBound } = sliderEle.getBoundingClientRect();
        /** 进度条拖拽圆点 */
        const handleEle = this.getDOMNode().handle as HTMLElement;
        /** 进度条当前时长bar */
        const videoCurrentEle = this.getDOMNode().videoCurrent as HTMLElement;
        /** 进度条总时长bar */
        const videoTrackEle = videoCurrentEle.parentElement as HTMLElement;
        const { left: minX, width: containerWidth } = videoTrackEle.getBoundingClientRect();

        /** 根据进度条长度计算时长 */
        const calcCurrentTime = (progress: number) => {
            // 保留两位小数，不能超过视频长度
            return Math.min(Math.round(progress / containerWidth * this.getVideoElement().duration * 100) / 100, this.getVideoElement().duration);
        };

        /** 根据手指触摸点设置进度 */
        const setProgress = (clientX: number) => {
            const progress = Math.min(Math.max(clientX - minX, 0), containerWidth);
            videoCurrentEle.style.width = progress + 'px';
            const currentTime = calcCurrentTime(progress);
            this.setCurrentTimeDisplay(currentTime);
            return currentTime;
        };

        /** 触摸结束后修改播放进度 */
        const setCurrentTimeAfterTouch = () => {
            const currentTime = calcCurrentTime(videoCurrentEle.offsetWidth);
            this.getVideoElement().currentTime = currentTime;

            this.dragging = false;
            sliderEle.classList.toggle(S.touched);
        };

        /** 直接点击后修改播放进度 */
        const setCurrentTimeAfterClick = (clientX: number) => {
            const currentTime = setProgress(clientX);
            this.getVideoElement().currentTime = currentTime;

            sliderEle.classList.toggle(S.clicked);
            setTimeout(() => {
                sliderEle.classList.toggle(S.clicked);
            }, 150);
        };

        handleEle.addEventListener('touchstart', () => {
            this.dragging = true;
            sliderEle.classList.toggle(S.touched);
        });
        handleEle.addEventListener('touchmove', (e) => {
            if (!this.dragging) {
                return;
            }

            // 如果手指移出边界了
            const { clientX, clientY } = e.touches[0];
            if (clientX < leftBound || clientX > rightBound || clientY < topBound || clientY > bottomBound) {
                setCurrentTimeAfterTouch();
                return;
            }

            setProgress(clientX);
        });
        handleEle.addEventListener('touchend', (e) => {
            // 避免再次触发点击事件
            e.preventDefault();
            if (this.dragging) {
                setCurrentTimeAfterTouch();
            }
        });

        videoTrackEle.addEventListener('click', e => {
            // 如果手指移出边界了
            const { clientX, clientY } = e;
            if (clientX < leftBound || clientX > rightBound || clientY < topBound || clientY > bottomBound) {
                return;
            }
            setCurrentTimeAfterClick(clientX);
        });
    }

    private changeVideoCurrent(time: number) {
        this.getVideoElement().currentTime = time;
        this.setCurrentTimeDisplay(time);
    }

    private timer = null;
    private changeVideo(goods: VideoGoods) {
        const video = this.getVideoElement();
        const isPortrait = goods.videoDTO.thumbnail.height > goods.videoDTO.thumbnail.width;

        // ios视频原生自带的海报加载太慢了，前端模拟一下；
        // ios的视频切换src也会出现视频尺寸的闪动，所以在ios下切视频需重新创建新的video;
        // 安卓如果用前端模拟的话，首次加载会闪一下，所以先用原生的(微信内置浏览器有问题)
        // 低版本安卓机也会有海报的问题，所以干脆全部特殊处理
        const customPoster = true;
        if (customPoster) {
            this.showVideoPoster();
            clearTimeout(this.timer);
            video.remove();
            const newVideo = video.cloneNode() as HTMLVideoElement;
            newVideo.src = goods.videoDTO.highUrl;
            // 判断视频横竖方向，竖屏需按高度撑满
            if (isPortrait) {
                newVideo.style.objectFit = 'cover';
            } else {
                newVideo.style.objectFit = 'contain';
            }
            newVideo.style.visibility = 'hidden';
            newVideo.onclick = this.onVideoClick.bind(this);
            this.getVideoContainer().insertBefore(newVideo, this.getVideoContainer().firstElementChild);
            this.listenVideo();
        } else {
            video.poster = goods.videoDTO.thumbnail.url;
            video.src = goods.videoDTO.highUrl;
            // 判断视频横竖方向，竖屏需按高度撑满
            if (isPortrait) {
                video.style.objectFit = 'cover';
            } else {
                video.style.objectFit = 'contain';
            }
            video.style.visibility = 'visible';
        }
    }

    private setPayDialog() {
        this.children.buyButton.setPay({
            isInDialog: true,
            androidPay: () => {
                startSiriusPay({
                    type: PayBoundType.GoLogin,
                    groupKey: this.currentGoods.groupKey,
                    sessionIds: this.currentGoods.sessionIds,
                    activityType: this.currentGoods.activityType,
                    couponCode: this.currentCoupon.code,
                    fragmentName1: '商品卡片',
                    fragmentName2: ''
                });
            },
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosBuySuccess({ groupKey: this.currentGoods.groupKey });
            }
        });
    }

    private setPayPrice() {
        if (!this.state.showDialog) {
            return;
        }
        this.children.buyButton.setButtonConfig({
            type: 1,
            groupKey: this.currentGoods.groupKey,
            title: '确认协议并支付',
            price: this.currentGoods.payPrice,
            validDays: this.currentGoods.validDays,
            fragmentName1: '商品卡片',
            fragmentName2: '',
            payPathType: 0,
            extraInfo: {
                videoId: this.currentVideoInfo.videoId
            },
            videoId: this.currentVideoInfo.videoId,
            actionName: '确认支付'
        });
    }

    private openPayDialog() {
        console.log('openPayDialog，暂停视频');
        this.getVideoElement().pause();
        this.setState({
            showDialog: true
        });
        this.setPayPrice();
    }

    private closePayDialog() {
        this.getVideoElement().play();
        this.setState({
            showDialog: false
        });
        this.children.buyButton.hideButton();
    }

    onCloseClick() {
        webClose();
    }

    onGoodsClick(e) {
        const idx = +e.refTarget.getAttribute('data-idx');
        if (this.state.selectedGoods === idx) {
            if (this.currentGoods.upgrade || !this.currentGoods.bought) {
                this.onBuyNowClick();
            } else {
                this.onBoughtClick();
            }
            return;
        }
        this.selectVideoGoods(idx);
    }

    onAnchorClick(e) {
        const idx = +e.refTarget.getAttribute('data-idx');
        this.setState({
            selectedAnchor: idx
        });
        const videoDuration = this.currentVideoInfo.videoDTO.duration;
        const anchorSecond = this.currentVideoInfo.anchorItemList[idx].second;
        if (anchorSecond < 0) {
            console.error('视频锚点有误, 小于0');
        }
        if (anchorSecond > videoDuration) {
            console.error('视频锚点有误, 大于视频总时长');
        }
        this.changeVideoCurrent(anchorSecond);
    }

    onVideoClick() {
        const showControls = !this.state.showControls;
        this.setState({
            showControls
        });
        if (showControls) {
            console.log('onVideoClick, 暂停视频');
            this.getVideoElement().pause();
        } else {
            this.getVideoElement().play();
        }
    }

    onBuyNowClick() {
        trackGoPay({
            groupKey: this.currentGoods.groupKey,
            fragmentName1: '商品卡片',
            payPathType: 0,
            videoId: this.currentVideoInfo.videoId
        });
        this.openPayDialog();
    }

    onBoughtClick() {
        // TODO: 是否打开新webview以优化体验
        jump.replace(BUYED_URL);
    }

    onDialogCloseClick() {
        this.closePayDialog();
    }

    async goCoupon() {
        const couponInfo = await selectUserCoupon(this.currentGoods, this.currentCoupon?.code);

        if (couponInfo) {
            const { couponList } = this.state;
            couponList[this.state.selectedGoods] = {
                ...couponList[this.state.selectedGoods],
                price: formatPrice(couponInfo.priceCent)
            };
            this.state.couponList = couponList;
            this.setPayPrice();
        }
    }
}
