<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="moveGoods" content=":component/moveGoods/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="ElderCouponDialog" content=":component/elderCoupon/main" />
<import name="CarShort" content=":application/car/component/CarShort/main" />
<import
    name="Kemu14Panel"
    content=":application/session500/component/normal_kemu14/main"
/>
<import
    name="Score12Panel"
    content=":application/session500/component/score12/main"
/>
<import
    name="ElderPanel"
    content=":application/session500/component/elder/main"
/>
<import
    name="ZigezhengPanel"
    content=":application/session500/component/zigezheng/main"
/>
<import
    name="MotoPanel"
    content=":application/session500/component/moto/main"
/>

<import
    name="CarKemuAll"
    content=":application/car/component/CarKemuAll/main"
/>
<import
    name="CarKemu1and4"
    content=":application/car/component/CarKemu1and4/main"
/>
<import
    name="TruckKemuAll"
    content=":application/khche/component/TruckKemuAll/main"
/>
<import
    name="BusKemuAll"
    content=":application/khche/component/BusKemuAll/main"
/>
<import name="MotoKemuAll" content=":application/moto/component/MotoKemuAll/main" />
<import
    name="MotoKemu1and4"
    content=":application/moto/component/MotoKemu1and4/main"
/>
<import
    name="Score12Short"
    content=":application/score12buy/component/score12Short/main"
/>

<import
    name="BusTruck1and4"
    content=":application/khche/component/BusTruck1and4/main"
/>
<import name="standbyGoods" content=":application/car/component/standbyGoods/main" />
<import name="sendKe2Dialog" content=":component/sendKe2Dialog/main" />
<!-- <import name="sendKe2Right" content=":component/sendKe2Right/main" /> -->

<div class="page-container page-session500">
    <div class="page-header">
        <com:header
            title="{{state.prevScrollTop > 100 ? self.pageTitle : ' '}}"
            theme="black"
            endTheme="black"
            qaKey="{{self.qaKey}}"
            back="{{self.backCall}}"
            scrollTop="{{state.prevScrollTop}}"
        />
    </div>

    <div class="body-panel-box">
        <div class="body-panel" sp-on:scroll="pageScroll">
            <div class="panel-box {{state.tabIndex == 0?'':'hide'}}">
                <sp:if value="URLCommon.isScore12">
                    <com:Score12Panel
                        payPrice="{{self.showPrice}}"
                        payBtnCall="{{self.payBtnCall}}"
                        goAuth="{{self.goAuth}}"
                    />
                    <sp:elseif value="URLCommon.isElder" />
                    <com:ElderPanel
                        payBtnCall="{{self.payBtnCall}}"
                        goAuth="{{self.goAuth}}"
                    />
                    <sp:elseif value="URLCommon.isZigezheng" />
                    <com:ZigezhengPanel
                        payBtnCall="{{self.payBtnCall}}"
                        payPrice="{{self.showPrice}}"
                        goAuth="{{self.goAuth}}"
                    />
                    <sp:elseif value="URLCommon.tiku === CarType.MOTO" />
                    <com:MotoPanel
                        payBtnCall="{{self.payBtnCall}}"
                        goAuth="{{self.goAuth}}"
                    />
                    <sp:else />
                    <com:Kemu14Panel
                        goodsInfo="{{state.goodsInfoPool[0]}}"
                        standbyPool="{{state.standbyPool}}"
                        payPrice="{{self.showPrice}}"
                        payBtnCall="{{self.payBtnCall}}"
                        goAuth="{{self.goAuth}}"
                        changeGoods="{{self.changeGoods}}"
                    />
                </sp:if>
            </div>
    
            <!-- 小车短时提分 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.ChannelKe4Short).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKe4Short).showPage) || (self.getGroupKeyInfo(GroupKey.GcChannelKe4Short).payPrice && self.getGroupKeyInfo(GroupKey.GcChannelKe4Short).showPage)"
            >
                <div
                    class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.ChannelKe4Short || self.nowGoodInfo.groupKey === GroupKey.GcChannelKe4Short?'':'hide'}}"
                >
                    <com:CarShort
                        payPrice="{{self.showPrice}}"
                        labelPool="{{state.labelPool}}"
                        groupKey="{{self.nowGoodInfo.groupKey}}"
                        isHubei="{{state.isHubei}}"
                        payBtnCall="{{self.payBtnCall}}"
                        goAuth="{{self.goAuth}}"
                    />
                </div>
            </sp:if>
    
            <!-- 小车全科 -->
            <sp:if value="self.getGroupKeyInfo(GroupKey.ChannelKemuAll).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKemuAll).showPage">
                <div
                    class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.ChannelKemuAll?'':'hide'}}"
                >
                    <com:CarKemuAll
                        tiku="{{state.tiku}}"
                        showActiveBtn="{{true}}"
                        goodsInfo="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll)}}"
                        currentIndex="{{state.tabIndex}}"
                        goodsList="{{state.goodsInfoPool}}"
                        comparePricePool="{{state.comparePricePool}}"
                        groupKey="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll).groupKey}}"
                        payPrice="{{self.showPrice}}"
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}"
                    />
                </div>
            </sp:if>
    
            <!-- 扣满12分短时提分 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.ChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKou12Short).showPage) || (self.getGroupKeyInfo(GroupKey.HcChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.HcChannelKou12Short).showPage) || (self.getGroupKeyInfo(GroupKey.KcChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.KcChannelKou12Short).showPage) || (self.getGroupKeyInfo(GroupKey.MotoChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.MotoChannelKou12Short).showPage)"
            >
                <div
                    class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.ChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.HcChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.KcChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.MotoChannelKou12Short?'':'hide'}}"
                >
                    <com:Score12Short
                        goodsInfo="{{self.nowGoodInfo}}"
                        labelPool="{{state.labelPool}}"
                        isHubei="{{state.isHubei}}"
                        comparePricePool="{{state.comparePricePool}}"
                        payPrice="{{self.showPrice}}"
                        groupKey="{{self.nowGoodInfo.groupKey}}"
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}"
                    />
                </div>
            </sp:if>
    
            <!-- 小车科一科四组合包 -->
            <sp:if
                value="self.getGroupKeyInfo(GroupKey.ChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKe1Ke4Group).showPage"
            >
                <div
                    class="{{self.nowGoodInfo.groupKey === GroupKey.ChannelKe1Ke4Group?'':'hide'}}"
                >
                    <com:CarKemu1and4
                        currentIndex="{{state.tabIndex}}"
                        goodsList="{{state.goodsInfoPool}}"
                        labelPool="{{state.labelPool}}"
                        comparePricePool="{{state.comparePricePool}}"
                        payPrice="{{self.showPrice}}"
                        groupKey="{{self.nowGoodInfo.groupKey}}"
                        kemu="{{state.kemu}}"
                        tiku="{{state.tiku}}"
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}"
                    />
                </div>
            </sp:if>
            <!-- 客货车科一科四组合包 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.HcChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.HcChannelKe1Ke4Group).showPage)||(self.getGroupKeyInfo(GroupKey.KcChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.KcChannelKe1Ke4Group).showPage)"
            >
                <div
                    class="{{self.nowGoodInfo.groupKey === GroupKey.HcChannelKe1Ke4Group||self.nowGoodInfo.groupKey === GroupKey.KcChannelKe1Ke4Group?'':'hide'}}"
                >
                    <com:BusTruck1and4
                        goodsInfo="{{self.nowGoodInfo}}"
                        labelPool="{{state.labelPool}}"
                        comparePricePool="{{state.comparePricePool}}"
                        payPrice="{{self.showPrice}}"
                        groupKey="{{self.nowGoodInfo.groupKey}}"
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}"
                    />
                </div>
            </sp:if>
            <!-- 货车全科 -->
            <sp:if value="self.getGroupKeyInfo(GroupKey.HcChannelKemuAll).payPrice">
                <div class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.HcChannelKemuAll?'':'hide'}}">
                    <com:TruckKemuAll goodsInfo="{{self.getGroupKeyInfo(GroupKey.HcChannelKemuAll)}}"
                        comparePricePool="{{state.comparePricePool}}" labelPool="{{state.labelPool}}" tiku="{{state.tiku}}"
                        payPrice="{{self.showPrice}}" payBtnCall="{{self.payBtnCall}}" goAuth="{{self.goAuth}}" />
                </div>
            </sp:if>
    
            <!-- 客车全科 -->
            <sp:if value="self.getGroupKeyInfo(GroupKey.KcChannelKemuAll).payPrice">
                <div class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.KcChannelKemuAll?'':'hide'}}">
                    <com:BusKemuAll goodsInfo="{{self.getGroupKeyInfo(GroupKey.KcChannelKemuAll)}}"
                        comparePricePool="{{state.comparePricePool}}" labelPool="{{state.labelPool}}"
                        payPrice="{{self.showPrice}}" payBtnCall="{{self.payBtnCall}}" goAuth="{{self.goAuth}}" />
                </div>
            </sp:if>
    
            <!-- moto的科一科四组合包 -->
            <sp:if
                value="self.getGroupKeyInfo(GroupKey.MotoChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.MotoChannelKe1Ke4Group).showPage"
            >
                <div
                    class="{{self.nowGoodInfo.groupKey === GroupKey.MotoChannelKe1Ke4Group?'':'hide'}}"
                >
                    <com:MotoKemu1and4
                        goodsInfo="{{self.nowGoodInfo}}"
                        payPrice="{{self.showPrice}}"
                        labelPool="{{state.labelPool}}"
                        groupKey="{{self.nowGoodInfo.groupKey}}"
                        isHubei="{{state.isHubei}}"
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}"
                    />
                </div>
            </sp:if>

            <!-- moto全科 -->
            <sp:if value="self.getGroupKeyInfo(GroupKey.MotoChannelKemuAll).payPrice">
                <div class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.MotoChannelKemuAll?'':'hide'}}">
                    <com:MotoKemuAll 
                        goodsInfo="{{self.nowGoodInfo}}"
                        labelPool="{{state.labelPool}}" 
                        comparePricePool="{{state.comparePricePool}}"
                        payPrice="{{self.showPrice}}" 
                        groupKey="{{self.nowGoodInfo.groupKey}}" 
                        goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}">
                    </com:MotoKemuAll>
                </div>
            </sp:if>
        </div>
    </div>

    <div class="footer-box">
          <com:standbyGoods changeGoods="{{self.changeGoods}}"
              standbyPool="{{state.tabIndex === 0?state.standbyPool:[]}}" className="type1" />
        <div class="footer {{state.goodsInfoPool.length > 1?'':'hide'}}">
            <com:bottomTabs
                tabIndex="{{state.tabIndex}}"
                labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}"
                goodsList="{{state.goodsInfoPool}}"
                standbyPool="{{state.tabIndex === 0?state.standbyPool:[]}}"
                tabChange="{{self.tabChangeCall}}"
                changeGoods="{{self.changeGoods}}"
            />
        </div>

        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon coupon-position-bottom" sp-on:click="goCoupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
            </div>
        </com:buyButton>
    </div>

    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
    <com:ElderCouponDialog />
    <com:sendKe2Dialog goodsInfo="{{self.nowGoodInfo}}" />
    <!-- <com:sendKe2Right goodsInfo="{{self.nowGoodInfo}}" /> -->
    <!-- <com:moveGoods info="{{(self.moveGoodsVideo[self.nowGoodInfo.groupKey] || {})}}"
        groupKey="{{self.nowGoodInfo.groupKey}}" /> -->
</div>
