/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */

import { promiseIconList, promiseList } from ':common/features/promise';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
}
interface Props {
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    get iconList() {
        return [
            {
                icon: promiseIconList.jjtk,
                uniqkey: promiseList.jjtk,
                dec: '精简题库'
            },
            {
                icon: promiseIconList.zskcmn,
                uniqkey: promiseList.zskcmn,
                dec: '真实考场模拟'
            },
            {
                icon: promiseIconList.kqmj,
                uniqkey: promiseList.kqmj,
                dec: '考前秘卷'
            }
        ];
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
        };
        this.props = {
        };

    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
