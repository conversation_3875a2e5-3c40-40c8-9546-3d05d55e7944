<import name="style" content="./main" />

<import name="commonQuestion" content=":component/commonQuestion/main" />

<div class="panel-session500-zigezheng">
    <div class="sec1"></div>
    <div class="sec2-w">
        <p class="p">题目太多，学的慢？精简</p>
        <p class="line"></p>
        <p class="p">题库更高效</p>
        <div class="sec2"></div>

        <div class="sec3_w">
            <div class="title">尊享{{self.iconList.length}}大权益</div>
            <div class="line"></div>
            <div class="ic_list_box">
                <ul class="ic_list">
                    <sp:each for="self.iconList">
                        <li sp-on:click="goAuth" data-uniqkey="{{$value.uniqkey}}">
                            <span
                                class="icon"
                                style="background-image: url({{$value.icon}});"
                            ></span>
                            <span class="desc">{{$value.dec}}</span>
                        </li>
                    </sp:each>
                </ul>
            </div>
            <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                <div class="buy-btn">
                    ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
                    '--'}} 立即开通
                </div>
                <sp:if value="props.labelPool[props.groupKey].label">
                    <i class="label"
                        >{{props.labelPool[props.groupKey].label}}</i
                    >
                </sp:if>
            </div>
        </div>
    </div>

    <div class="sec3"></div>
    <div class="sec4"></div>
    <div class="sec7">
        <com:commonQuestion type="6"></com:commonQuestion>
    </div>
</div>
