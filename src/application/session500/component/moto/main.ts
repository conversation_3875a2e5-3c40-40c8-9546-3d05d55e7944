/*
 * main
 *
 * name: xia<PERSON><PERSON>a
 * date: 16/3/24
 */
import { promiseIconList, promiseList } from ':common/features/promise';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
    iconList: any[]
}
interface Props {
    payBtnCall?(e: Event)
    goAuth?(any)
}

export default class extends Component<State, Props> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            iconList: [
                {
                    icon: promiseIconList.jjtk,
                    uniqkey: promiseList.jjtk,
                    name: '精简题库',
                    dec: '省时省力'
                },
                {
                    icon: promiseIconList.zskcmn,
                    uniqkey: promiseList.zskcmn,
                    name: '真实考场模拟',
                    dec: '高仿真'
                },
                {
                    icon: promiseIconList.kqmj,
                    uniqkey: promiseList.kqmj,
                    name: '考前秘卷',
                    dec: '高效冲刺'
                },
                {
                    icon: promiseIconList.bgbc,
                    uniqkey: promiseList.bgbc,
                    name: '考不过补偿',
                    dec: '查看补偿说明'
                }
            ]
        };
        this.props = {
        };

    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
