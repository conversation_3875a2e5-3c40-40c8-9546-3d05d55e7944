.panel-session500-normal {
    background: #F4F7F7;
    position: relative;
    .sec1 {
        height: 418px;
        background: url(../images/1.png) no-repeat;
        background-size: 100% auto;
        position: relative;
        background-color: #fff;

        &.car.giftPromotion{
             background: url(http://exam-room.mc-cdn.cn/exam-room/2024/12/05/16/989d414cde5e46acb3493d066bc33c3c.png) no-repeat center center/cover;
        }

        &.truck,
        &.bus {
            background: url(http://exam-room.mc-cdn.cn/exam-room/2022/07/12/17/cca0f51573934087914fde81afaad214.png) no-repeat center center/cover;
        }

        .hd-price {
            width: 187px;
            height: 52px;
            position: absolute;
            top: 194px;
            right: 31px;
            box-sizing: border-box;
            padding: 6px 6px 8px 9px;
            background: url(../images/5.png) no-repeat;
            background-size: cover;
            display: flex;
            display: -webkit-flex;
            align-items: center;
            -webkit-align-items: center;
            justify-content: center;
            -webkit-justify-content: center;
            font-size: 18px;
            color: #442000;
            text-align: center;
            font-weight: bold;

            b {
                display: block;
                line-height: 24px;
            }

            span {
                display: block;
                padding-right: 4px;
                line-height: 24px;
            }

            label {
                height: 11px;
                width: 11px;
                margin: 0;
                background: url(../images/6.png) no-repeat;
                background-size: 100% 100%;
            }
        }

    }

    .buy-btn-box {
        position: relative;
        width: 343px;
        height: 49px;
        margin: 5px auto 15px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/btn1.png) no-repeat center center/100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .buy-btn {
            font-size: 19px;
            color: white;
        }

        .label {
            position: absolute;
            right: 0px;
            top: -7px;
            background: linear-gradient(360deg, #F9C39F 0%, #FEDEC7 100%);
            border-radius: 0 10px 0 8px;
            font-size: 12px;
            font-weight: 500;
            color: #622604;
            transform: scale3d(0.9, 0.9, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2px 9px;
            font-weight: 500;
        }

    }

    .protocol-box {
        box-sizing: border-box;
        margin-bottom: 20px;
        font-size: 13px;
        display: flex;
        justify-content: center;
    }


    .sec2 {
        height: 213px;
        width: 361px;
        padding: 10px 15px 20px;
        background-size: 100% 100%;
        margin-top: -130px;
        margin-left: 7px;
        position: relative;

        &.kemu1 {
            background-image: url(https://web-resource.mc-cdn.cn/web/vip/500ti/1.png);
        }

        &.kemu4 {
            background-image: url(http://exam-room.mc-cdn.cn/exam-room/2022/02/10/15/84bb0485dd2047499a53e81802cfbb5f.png);
        }

        &.truck.kemu1 {
            background-image: url(https://web-resource.mc-cdn.cn/web/vip/500ti/6.png);
        }

        &.truck.kemu4 {
            background-image: url(https://web-resource.mc-cdn.cn/web/vip/500ti/6.png);
        }

        &.bus.kemu1 {
            background-image: url(https://web-resource.mc-cdn.cn/web/vip/500ti/6.png);
        }

        &.bus.kemu4 {
            background-image: url(https://web-resource.mc-cdn.cn/web/vip/500ti/6.png);
        }
    }

    .bgbc-dec {
        margin: 5px auto 30px;
        width: 345px;
        height: 182px;
        background: url(http://exam-room.mc-cdn.cn/exam-room/2023/11/06/14/d3220825a32b481d90e68c9f6406c394.png) no-repeat center center/cover;

    }

    .auth-list {
        background-color: #fff;
        padding-bottom: 15px;

        .h3-1 {
            font-size: 20px;
            font-weight: bold;
            color: #333333;
            padding: 15px 15px 0 15px;
        }

        .sec3 {
            .icon-list {
                display: flex;
                flex-wrap: wrap;
                align-items: center;

                .icon-item {
                    width: 25%;
                    margin-top: 15px;
                    text-align: center;

                    img {
                        display: block;
                        margin: 0 auto;
                        width: 47px;
                    }

                    .dec-box {
                        margin-top: 5px;
                        font-size: 14px;
                    }
                }
            }
        }
    }

    .h3-2 {
        font-size: 20px;
        font-weight: bold;
        color: #333333;
        padding: 25px 15px;
    }

    .sec4 {
        width: 345px;
        height: 105px;
        background: url(../images/3.png) no-repeat;
        background-size: 100% 100%;
        position: relative;
        margin: 0 auto;
        // padding-bottom: 20px;
        background-color: #fff;

        .text {
            color: #D59E57;
            font-size: 14px;
            position: absolute;
            left: 20px;
            bottom: 38px;
            letter-spacing: 2px;
        }

        .intro {
            position: absolute;
            left: 15px;
            bottom: 8px;
            font-size: 10px;
            color: #96490E;
            line-height: 14px;
            display: flex;
            background-color: #FFE3B1;
            border-radius: 20px;
            transform: scale3d(0.9, 0.9, 1);
            transform-origin: left;

            span {
                border: 1px solid #ffffff;
                padding: 4px 8px 4px 8px;
                border-radius: 20px;
            }
        }

        .summer-icon {
            width: 58px;
            height: 16px;
            background: url(https://jiakao-web.mc-cdn.cn/jiakao-web/2022/06/20/18/18c704f1bd124a07b23706bbed0f0114.png) no-repeat;
            background-size: 100% 100%;
            position: absolute;
            top: 26px;
            right: 136px;
        }
    }

    .divider {
        height: 15px;
        background: #F4F7F7;
    }

    .common-question {
        .sec-w {
            padding-top: 0;
        }
    }
}
