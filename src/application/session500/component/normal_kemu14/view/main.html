<import name="style" content="./main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="myVideo" content=":component/myVideo/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />
<import name="SendVipEnter" content=":component/sendVipEnter/main" />
<import name="standbyGoods" content=":application/car/component/standbyGoods/main" />
<import name="sendKe2Txt" content=":component/sendKe2Txt/main" />

<div class="panel-session500-normal">
    <div class="sec1 {{URLCommon.tiku}} {{props.goodsInfo.giftPromotion && props.goodsInfo.giftPromotion.promotionStatus?'giftPromotion':''}}">
    </div>
    <div class="sec2 {{URLCommon.tiku}} {{URLCommon.kemu == 1?'kemu1':'kemu4'}}">
        <sp:if value="URLCommon.tiku === CarType.CAR && state.showVideo">
            <com:myVideo src="{{self.videoIntroduce}}"></com:myVideo>
        </sp:if>
    </div>

    <sp:if value="props.standbyPool.length">
        <com:standbyGoods changeGoods="{{props.changeGoods}}" standbyPool="{{props.standbyPool}}" />
    </sp:if>

    <com:sendKe2Txt goodsInfo="{{props.goodsInfo}}" />
    <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
        <div class="buy-btn">
            确认协议并支付 ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
                    '--'}}
        </div>
    </div>
    <div class="protocol-box">
        <com:middleProtocol groupKey="{{props.groupKey}}" couponPool="{{props.couponPool}}" />
    </div>

    <com:SendVipEnter name="right" entranceCode="{{URLCommon.kemu === 1?'ke1_session500_right':'ke4_session500_right'}}"
        position="right" />

    <div class="bgbc-dec">

    </div>
    <div class="auth-list">
        <h3 class="h3-1">附赠{{state.authList.length}}大权益</h3>
        <div class="sec3">
            <div class="icon-list">
                <sp:each for="state.authList">
                    <div class="icon-item" sp-on:click="goAuth" data-uniqkey="{{$value.uniqkey}}">
                        <img src="{{$value.icon}}" />
                        <div class="dec-box">
                            <span>{{$value.dec}}</span>
                        </div>
                    </div>
                </sp:each>
            </div>
        </div>

        <h3 class="h3-2">考不过补偿</h3>
        <div class="sec4" sp-on:click="goAuth" data-uniqkey="bgbc">
            <p class="text">
                <span>{{Texts.currentCarStyleTxt}}不过，补偿50元</span>
            </p>
            <p class="intro"><span>查看详细补偿条件和说明 ></span></p>
        </div>
    </div>
    <div class="divider"></div>
    <com:commonQuestion type="1" />
</div>
