/*
 * main
 *
 * name: xia<PERSON>jia
 * date: 16/3/24
 */
import { CAR_SESSION500_VIDEO } from ':common/navigate';
import { ActivityInfo, getActivityTime } from ':store/chores';
import { GoodsInfo } from ':store/goods';
import { getSalesRightsList } from ':store/sales_rights';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { KemuType, URLCommon } from ':common/env';

interface State {
    activityInfo?: ActivityInfo
    authList: any[],
    showVideo: boolean,
}
interface Props {
    goodsInfo: GoodsInfo
    payBtnCall?(e: Event)
    goAuth?(any)
}

export default class extends Component<State, Props> {
    get videoIntroduce() {
        return CAR_SESSION500_VIDEO;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            authList: [],
            showVideo: false
        };

    }
    getSessionList() {
        const { goodsInfo } = this.props;
        // 由于外面的abtest导致这个组件会在有groupKey之前加载，所以可能取不到值，只能延时加载
        if (!goodsInfo) {
            setTimeout(() => {
                this.getSessionList();
            }, 200);
        } else {
            getSalesRightsList({
                groupKey: goodsInfo.groupKey
            }).then(data => {
                data = data.splice(0, 8);
                this.setState({
                    authList: data.map(item => ({
                        uniqkey: item.code,
                        icon: item.icon,
                        dec: item.name
                    }))
                });
            });
        }
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
    didMount() {
        setTimeout(() => {
            this.setState({
                showVideo: true
            });
        }, 500);
        this.getActivityInfo();
        this.getSessionList();
    }

    async getActivityInfo() {
        const retData = await getActivityTime();
        this.setState({
            activityInfo: retData
        });
    }
}
