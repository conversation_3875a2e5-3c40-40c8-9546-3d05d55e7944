<import name="style" content="./main" module="S" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import
    name="kqfdBuyDialog"
    content=":application/kqfd/component/kqfdBuyDialog/main"
/>
<import
    name="onlineDialog"
    content=":application/kqfd/component/onlineDialog/main"
/>
<div class=":zdst-index">
    <div class=":head-bg">
        <div class=":index-top-time">
            <sp:if value="{{state.lessonSchedule.length<=0}}">
                暂无直播
                <sp:else />
                直播时间:{{self.newSchelTime}}
            </sp:if>
        </div>
    </div>
    <sp:if value="{{!props.hasPromission}}">
        <div
            class=":index-buy-button"
            data-fragment="主图"
            sp-on:click="dialogBuy"
        >
            <span class=":buy-title"
                >{{props.goodsInfoPool[0].payPrice}}元立即开通</span
            >
            <span class=":buy-desc"
                >{{props.goodsInfoPool[0].validDays}}天内可观看所有重点刷题直播</span
            >
        </div>
    </sp:if>

    <div class=":img1-box"></div>
    <div class=":img2-box">
        <div class=":img2-t"></div>
        <div class=":content">
            <div class=":step-list">
                <div class=":step-item">
                    <div class=":icon"></div>
                    <div class=":title">重点题直播精讲</div>
                    <div class=":dec">直播讲解</div>
                    <div class=":dec1">针对答疑</div>
                </div>
                <div class=":step-item">
                    <div class=":icon"></div>
                    <div class=":title">短课复习</div>
                    <div class=":dec">随时可看</div>
                    <div class=":dec1">夯实基础</div>
                </div>
                <div class=":step-item">
                    <div class=":icon"></div>
                    <div class=":title">巩固提升</div>
                    <div class=":dec">辅导资料</div>
                    <div class=":dec1">课后巩固</div>
                </div>
            </div>

            <div class=":step2-box">
                <div class=":step2-title"></div>
                <div class=":step2-title-dec">
                    为了避免重复听课，学员根据自己时间选择一天听课即可
                </div>
                <div class=":zhibo-box">
                    <sp:each for="state.lessonSchedule">
                        <div class=":zhibo-item-box">
                            <div class=":time-title">{{$value.title}}</div>
                            <sp:each
                                for="$value.liveDataList"
                                value="$sonitem"
                                index="$sonIndex"
                            >
                                <div
                                    class=":zhibo-item"
                                    data-liveSessionId="{{$sonitem.liveSessionId}}"
                                    data-fragment="直播时间表"
                                    sp-on:click="gotoZhibojian"
                                >
                                    <div class=":title">
                                        <span class=":num"
                                            >第{{Tools.numZh($sonIndex+1)}}场</span
                                        >
                                        <span class=":right-day">
                                            <sp:if
                                                value="{{Tools.dateFormat($sonitem.beginTime,
                                                                            'MM月dd日')==Tools.dateFormat(new Date(),
                                                                            'MM月dd日')}}"
                                            >
                                                今天
                                                <sp:else />
                                                {{Tools.dateFormat($sonitem.endTime,
                                                'MM月dd日')}}
                                            </sp:if>
                                            <span class=":right-time">
                                                {{Tools.dateFormat($sonitem.beginTime,
                                                'HH:mm')}}-{{Tools.dateFormat($sonitem.endTime,
                                                'HH:mm')}}
                                            </span>
                                        </span>
    
                                        <sp:if value="{{$sonitem.status==1}}">
                                            <span class=":zhizho1">直播中</span>
                                        </sp:if>
                                        <sp:if value="{{$sonitem.status==2}}">
                                            <sp:if
                                                value="{{$sonitem.subscribeStatus==1}}"
                                            >
                                                <span class=":zhizho2">
                                                    已预约
                                                </span>
                                                <sp:else />
                                                <span
                                                    class=":zhizho2"
                                                    data-liveSessionId="{{$sonitem.liveSessionId}}"
                                                    data-index="{{$index}}"
                                                    data-sonIndex="{{$sonIndex}}"
                                                    sp-on:click="makeappointment"
                                                >
                                                    预约直播
                                                </span>
                                            </sp:if>
                                        </sp:if>
                                        <sp:if value="{{$sonitem.status==3}}">
                                            <span class=":zhizho3">看回放</span>
                                        </sp:if>
                                    </div>
                                    <div class=":zhibo-item-content">
                                        {{$sonitem.liveContent}}
                                    </div>
                                </div>
                            </sp:each>
                        </div>
                    </sp:each>
                      <sp:if value="state.lessonSchedule.length">
                    <div class=":look-more-zhibo" sp-on:click="viewHistory">
                        查看历史直播记录>
                    </div>
                </sp:if>
                </div>

              
            </div>

            <div class=":step1-box">
                <div class=":step1-title"></div>
                <div class=":step1-content-box">
                    <div class=":step1-content">
                        <sp:each for="state.lessonList">
                            <div
                                class=":step1-item"
                                data-lessonid="{{$value.id}}"
                                sp-on:click="goLesson"
                            >
                                <div
                                    class=":img"
                                    style="background-image: url({{Tools.calcImg($value.image)}});"
                                ></div>
                                <div class=":dec">{{$value.title}}</div>
                            </div>
                        </sp:each>
                    </div>
                </div>
            </div>

            <div class=":step3-box">
                <div class=":step3-title"></div>
                <div class=":step3-content">
                    <div
                        class=":step3-item"
                        sp-on:click="gotoZiliao"
                        data-fragment="资料包"
                    >
                        <div class=":l">核心知识点<br />资料包</div>
                        <div class=":r"></div>
                    </div>
                    <div
                        class=":step3-item"
                        sp-on:click="gotoZiliao"
                        data-fragment="资料包"
                    >
                        <div class=":l">答题技巧<br />资料包</div>
                        <div class=":r"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class=":zhibo-common-question">
        <div class=":common-question-title"></div>
        <com:commonQuestion type="14" />
    </div>
</div>

<com:kqfdBuyDialog
    goodsInfoPool="{{props.goodsInfoPool}}"
    fragmentName1="{{state.fragmentName1}}"
></com:kqfdBuyDialog>
<com:onlineDialog></com:onlineDialog>
