<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="videoList" content="../component/videoList/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import
    name="userCommonQuestion"
    content="../component/userCommonQuestion/main"
/>

<div class="page-container page-ke2route">
    <div class="body-panel" sp-on:scroll="pageScroll">
        <div class="banner image-box">
            <com:header
                title="{{state.cityName + '科二考场'}}"
                theme="black"
                endTheme="black"
                scrollTop="{{state.prevScrollTop}}"
                back="{{self.backCall}}"
            />
            <div class="area">{{state.cityName}}</div>
            <div class="active-list">
                <div class="active-item image-box">
                    <div class="info">
                        <div class="title">122驾考网点信息同步</div>
                        <div class="dec">提前感受考场环境</div>
                    </div>
                </div>
                <div class="active-item image-box">
                    <div class="info">
                        <div class="title">考场流程视频讲解</div>
                        <div class="dec">扣分点难点快速掌握</div>
                    </div>
                </div>
                <div class="active-item image-box">
                    <div class="info">
                        <div class="title">考场3D仿真模拟考</div>
                        <div class="dec">专项攻克弱点</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="exam-top">
            <div class="icon image-box"></div>
            <div class="txt">{{state.cityName}}科二考场</div>
            <div class="line-num">共{{state.videoList.length}}个考场</div>
        </div>
        <sp:if value="state.areaList && state.areaList.length">
            <div class="area-scroll {{Simple.CONST.isIOS?'ios':''}}">
                <div class="area-list">
                    <span
                        sp-on:click="changeTab"
                        class="area-item {{!state.tabId?'active':''}}"
                        >全部</span
                    >
                    <sp:each for="state.areaList">
                        <span
                            sp-on:click="changeTab"
                            data-id="{{$value.areaCode}}"
                            class="area-item {{state.tabId == $value.areaCode?'active':''}}"
                            >{{$value.areaName}}</span
                        >
                    </sp:each>
                </div>
            </div>
        </sp:if>

        <div class="exam-room">
            <com:videoList
                list="{{state.videoList}}"
                lookAll="{{self.openLookAllBuy}}"
            />
        </div>
        <div class="day-question">
            <com:userCommonQuestion kemu="2" />
        </div>
    </div>
    <div class="showLookAllBuy-mask {{state.showLookAllBuy?'':'hide'}}">
        <div class="showLookAllBuy-box" skip="true" ref="embeded"></div>
    </div>
    <com:buyButton/>
    <com:payDialog />
</div>
