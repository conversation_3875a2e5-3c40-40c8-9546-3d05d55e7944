.page-ke2route {
    background-color: #fff;

    .image-box {
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
    }

    .body-panel {
        flex: 1;
        position: relative;
        overflow-y: auto;

        .banner {
            width: 100%;
            height: 500px;
            background-image: url(../images/<EMAIL>);
            position: relative;

            .area {
                margin-top: 10px;
                margin-left: 15px;
                color: white;
                font-size: 14px;
                display: flex;
                align-items: center;

                &::before {
                    content: '';
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    background: url(../images/<EMAIL>) no-repeat center center/cover;
                    margin-right: 4px;
                }
            }

            .active-list {
                position: absolute;
                bottom: 20px;
                padding: 0 15px;

                .active-item {
                    width: 345px;
                    height: 75px;
                    margin: 10px auto;
                    padding-left: 106px;
                    box-sizing: border-box;

                    &:nth-of-type(1) {
                        background-image: url(https://web-resource.mc-cdn.cn/web/route/ban1.png);
                    }

                    &:nth-of-type(2) {
                        background-image: url(https://web-resource.mc-cdn.cn/web/route/ban2.png);
                    }

                    &:nth-of-type(3) {
                        background-image: url(https://web-resource.mc-cdn.cn/web/route/ban3.png);
                    }

                    .info {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        height: 100%;

                        .title {
                            font-size: 16px;
                            font-weight: 500;
                        }

                        .dec {
                            font-size: 14px;
                            color: #777;
                            margin-top: 8px;
                        }
                    }

                }
            }
        }
    }

    .exam-top {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0 15px;
        box-sizing: border-box;
        position: relative;
        margin-bottom: 9px;

        .icon {
            position: absolute;
            top: -20px;
            left: 0;
            width: 69px;
            height: 69px;
            background-image: url(https://web-resource.mc-cdn.cn/web/route/icon.png);
        }

        .txt {
            font-size: 20px;
            font-weight: 600;
            color: #41464F;
            margin-left: -15px;
            padding-left: 69px;
        }

        .line-num {
            color: #FD7B39;
            margin-left: auto;
            font-size: 13px;
        }
    }

    .area-scroll {
        position: sticky;
        top: 75px;
        left: 0;
        z-index: 30;
        width: 100%;
        overflow-x: auto;
        background-color: #fff;

        &.ios {
            top: calc(~'constant(safe-area-inset-top) + 45px');
            top: calc(~'env(safe-area-inset-top) + 45px');
        }

        .area-list {
            padding: 8px 5px 8px 15px;
            white-space: nowrap;
            display: inline-block;

            .area-item {
                margin-right: 10px;
                padding: 0 15px;
                height: 28px;
                background: #F2F2F2;
                border-radius: 14px;
                display: inline-block;
                font-size: 14px;
                line-height: 28px;
                color: #666666;

                &.active {
                    background: rgba(242, 82, 71, 0.14);
                    color: #F25247;
                }
            }
        }
    }


    .exam-room {
        padding-top: 10px;
    }

    .day-question {
        margin-top: 40px;
        margin-bottom: 20px;
    }

    .showLookAllBuy-mask {
        position: fixed;
        z-index: 100;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
    }



    .showLookAllBuy-box {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100;
    }
}
