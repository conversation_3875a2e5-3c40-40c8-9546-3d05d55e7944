/*
 * ------------------------------------------------------------------
 * 科二路线
 * ------------------------------------------------------------------
 */
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import Ke23D from ':application/ke23d/main';
import { getSystemInfo } from ':common/core';
import { PayType, Platform, setPageName } from ':common/env';
import { trackGoPay, trackPageLoad } from ':common/stat';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { getSceneListByArea } from ':store/chores';
import { getCityName } from ':common/utils';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { typeCode } from ':common/features/bottom';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    showLookAllBuy: boolean,
    cityCode: string,
    cityName: string,
    tabId: string
    areaList: any[]
    videoList: any[],
    prevScrollTop: number
}
interface Props {

}

let timer;

export default class extends Application<State, Props> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    /**
    * 如果有优惠券的价格为0的就显示0
    * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
   */
    get showPrice() {
        const { tabIndex, goodsInfoPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    ke23d: Ke23D;
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            goodsInfoPool: [{
                groupKey: GroupKey.ChannelKe2
            } as GoodsInfo],
            cityCode: '',
            cityName: '',
            // 查看完整视频弹窗
            showLookAllBuy: false,
            // 视频播放列表
            videoList: [],
            // 地域列表
            areaList: null,
            // tab的id
            tabId: null,
            prevScrollTop: 0
        };

    }
    async didMount() {
        setPageName('考场视频列表页');

        // 装载
        const ke23d = this.ke23d = new Ke23D({
            target: this.getDOMNode().embeded as HTMLElement
        });

        ke23d.on('close', () => {
            this.setState({ showLookAllBuy: false });
            this.setPageInfo();
        });

        ke23d.render();

        this.registeredBottomFn();

        this.getSystemInfo().then(() => {
            this.getVideoList();
        });

        await this.getGoodInfo();

        // 页面进出时长打点
        trackPageLoad();

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: this.nowGoodInfo.groupKey
        });
    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;

            this.setState({
                prevScrollTop
            });
        }, 10);
    }
    registeredBottomFn() {
        const { showLookAllBuy } = this.state;

        if (showLookAllBuy) {
            this.ke23d.setPayment();
        } else {
            // 注册底部支付方法
            this.children.buyButton.setPay({
                androidPay: this.pay,
                iosPaySuccess: () => {
                    iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
                }
            });
        }

    }
    async getSystemInfo() {
        const systemInfo = await getSystemInfo();
        const cityCode = systemInfo._userCity || systemInfo._cityCode;
        const cityName = await getCityName(+cityCode) || '';

        this.setState({
            cityCode,
            cityName
        });

    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {

            this.setState({
                goodsInfoPool: goodsListInfo
            });

            this.setPageInfo();
        });
    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const fragmentName1 = '底部吸底按钮';
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const bottomType: typeCode = typeCode.type4;

        this.registeredBottomFn();

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: this.showPrice + '元解锁本地科二考场',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1
                });
                break;
            default:
                break;
        }
    }
    changeTab(e) {
        const id = e.refTarget.getAttribute('data-id');

        // 组件被复用，组件状态被保留，所以先将videoList清空
        this.setState({
            tabId: id,
            videoList: []
        }, () => {
            this.getVideoList();
        });

    }
    getVideoList() {
        const { cityCode, tabId } = this.state;

        const params: any = {
            _userCity: cityCode
        };

        if (tabId) {
            params.areaCode = tabId;
        }

        getSceneListByArea(params).then(data => {
            if (data) {
                const videoList = data.sceneData.sceneList || [];

                this.setState({
                    areaList: this.state.areaList || data.areaList,
                    videoList
                });

            }
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
        }).catch(async () => {
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    openLookAllBuy = () => {
        this.setState({
            showLookAllBuy: true
        });

        this.registeredBottomFn();
    }
}
