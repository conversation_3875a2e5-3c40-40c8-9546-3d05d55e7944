import { openWeb } from ':common/core';
import { KemuType, URLCommon, URLParams } from ':common/env';
import jump from ':common/features/jump';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import tbVideo from ':component/tbVideo/main';

interface State {
    // 是否完成播放
    playEnd: boolean
    play: boolean;
}

interface Props {
    info: any
    lookAll(any)
}

export default class extends Component<State, Props> {
    declare children: {
        tbVideo: tbVideo
    }
    get videoList() {
        const { info } = this.props;
        const list = [];

        info.previewVideoCompose.middle.forEach(item => {
            if (item.url) {
                list.push(item.url);
            }
        });

        return list;
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            playEnd: false,
            play: false
        };

    }
    onReload(e) {
        e.stopPropagation();

        this.children.tbVideo.play();

        this.setState({
            playEnd: false
        });
    }
    onLookAll(e) {
        const { lookAll } = this.props;
        e.stopPropagation();

        lookAll && lookAll(e);
    }
    onPlayEnd = () => {
        this.setState({
            playEnd: true
        });
    }
    onGoDetail = (e) => {
        const { info } = this.props;
        const { id, cityCode, placeId } = info;

        // 科二和科三公用这个组件，但是跳转的详情不同
        if (URLCommon.kemu === KemuType.Ke3) {
            openWeb({
                url: `http://jiakao.nav.mucang.cn/show-exam-route-video?placeId=${placeId}&routeId=${id}&from=${URLParams.from}`
            });
        } else {
            jump.navigateTo('https://jiakao.nav.mucang.cn/sceneVideo/detail', {
                videoId: id,
                cityCode,
                from: URLParams.from
            });
        }
    }
    willReceiveProps() {
        return true;
    }
}
