<import name="style" content="./main" />
<import name="tbVideo" content=":component/tbVideo/main" />

<div
    class="ke2-video-item"
    sp-on:click="onGoDetail"
    data-id="{{props.info.id}}"
    data-cityCode="{{props.info.cityCode}}"
    data-placeid="{{props.info.placeId}}"
>
    <div class="video-box">
        <com:tbVideo
            videoList="{{self.videoList}}"
            poster="{{Tools.calcImg(props.info.videoImage)}}"
            showVideo="{{true}}"
            onPlayEnd="{{self.onPlayEnd}}"
            onVideoClick="{{self.onGoDetail}}"
        >
        </com:tbVideo>

        <label
            class="difficult {{(props.info.difficultyCount > 0 && !state.play) ? '': 'hide'}}"
            ><i class="icon"></i
            ><span class="count"
                >此路线有<b>{{props.info.difficultyCount}}</b>个难点</span
            ></label
        >
       
        <div class="mask {{state.playEnd?'':'hide'}}">
            <div class="pause-reason play-end">
                <div class="dec">试看视频已结束</div>
                <div class="active-box">
                    <div sp-on:click="onReload" class="active-btn reload-play">
                        再看一遍
                    </div>
                    <div
                        sp-on:click="onLookAll"
                        data-fragment="试看结束"
                        class="active-btn look-all"
                    >
                        看完整视频
                    </div>
                </div>
            </div>
        </div>
    </div>
    <sp:if value='{{props.showBot!=="ke3RouteB"}}'>
        <div class="bot">
            <label class="name">{{props.info.name}}</label>
            <div class="bot-right">
            <span class="text">更新于{{Tools.dateFormat(props.info.updateTime, 'yyyy.MM.dd')}}</span>
            <span class="text">本月已核验</span>
            </div>
        </div>
    </sp:if>
</div>
