.ke2-video-item {
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    margin-bottom: 25px;

    .hide {
        display: none !important;
    }

    .video-box {
        height: 194px;
        border-radius: 5px;
        overflow: hidden;
        position: relative;
        .video-top{
            position: absolute;
            z-index: 10;
            top:0;
            left: 0;
        }
        video {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }

        .difficult {
            position: absolute;
            right: 5px;
            top: 5px;
            z-index: 19;
            height: 23px;
            background: linear-gradient(316deg, #FF5400 0%, #FA232F 100%);
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            color: #FFFFFF;
            line-height: 24px;
            padding: 0 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;

            .count {
                height: 23px;
                padding-top: 1px;
                box-sizing: border-box;
            }

            b {
                color: #FFD921;
                font-weight: 500;
                padding: 0 5px;
            }

            .icon {
                display: block;
                height: 17px;
                width: 17px;
                background-image: url(../images/23.png);
                background-repeat: no-repeat;
                background-position: left center;
                background-size: 17px 17px;
            }
        }

        .mask {
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            bottom: 0;
            background: rgba(33, 33, 33, 0.8);
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 20;

            .pause-reason {
                .dec {
                    text-align: center;
                    font-size: 15px;
                    color: #FFFFFF;
                }

                .active-box {
                    display: flex;
                    padding: 20px 35px;
                    justify-content: space-around;
                    width: 100%;
                    box-sizing: border-box;

                    .active-btn {
                        display: flex;
                        align-items: center;
                        height: 32px;
                        color: #FFFFFF;
                        border-radius: 100px;
                        font-size: 14px;
                        padding: 0 22px;

                        &.reload-play {
                            border: 1px solid rgba(255, 255, 255, 0.3);
                        }

                        &.look-all {
                            background: rgba(255, 255, 255, 0.3);
                            ;
                            margin-left: 26px;
                        }
                    }
                }
            }
        }
    }

    .bot {
        min-height: 56px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px;
        font-size: 17px;
        font-weight: 500;
        color: #333;

        .name {
            flex: 1;
            font-size: 17px;
            font-weight: 500;
            color: #333333;
            line-height: 24px;
        }
        .bot-right{
            width: 160px;
            display: flex;
            flex-direction: column;
            text-align: right;
        }

        .text {
            font-size: 13px;
            font-weight: 400;
            color: #A2A5AA;
            line-height: 18px;
        }
    }
}
