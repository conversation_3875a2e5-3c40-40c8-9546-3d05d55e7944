<import name="style" content="./main" />

<import name="videoItem" content="../../videoItem/main" />

<div class="ke2-video-list {{props.showBot==='ke3RouteB'?'ke3-route-content-b':''}}">
    <sp:each for="props.list">
        <div class="item-box" ref="itemBox">
            <com:videoItem name="{{'videoItem' + $index}}" info="{{$value}}" netWork="{{state.netWork}}" allowNoneWifiPlay="{{state.allowNoneWifiPlay}}" allowNoWifiPlayCall="{{self.goOn}}" lookAll="{{props.lookAll}}"
            showBot="{{props.showBot}}"
            />
        </div>
    </sp:each>
</div>