import { getSystemInfo } from ':common/core';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
    netWork: string
    allowNoneWifiPlay: boolean
}

export default class extends Component<State> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            netWork: 'wifi',
            allowNoneWifiPlay: false
        };

    }
    didMount() {
        this.getSystemInfo();
    }
    async getSystemInfo() {
        const systemInfo = await getSystemInfo();

        this.setState({
            netWork: systemInfo._network
        });

    }
    goOn = (flag) => {
        this.setState({
            allowNoneWifiPlay: flag
        });
    }
    willReceiveProps() {
        return true;
    }
}
