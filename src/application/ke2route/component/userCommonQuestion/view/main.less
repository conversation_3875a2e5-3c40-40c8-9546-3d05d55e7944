.user-common-question {

    .image-box {
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center center;
    }

    .title {
        height: 28px;
        font-size: 20px;
        display: flex;
        align-items: center;
        color: #333;
        font-weight: 500;
        padding: 0 15px;
        margin-bottom: 16px;
    }

    .question-item {
        .q-title {
            height: 20px;
            display: flex;
            align-items: center;
            padding: 0 15px;

            .icon {
                width: 16px;
                height: 16px;
                background-image: url(../images/question.png);
            }

            .txt {
                margin-left: 10px;
                font-size: 15px;
            }
        }

        .answer-list {
            .swiper-slide {
                box-sizing: border-box;
                padding: 15px 15px;
            }

            .swiper-item {
                padding: 15px;
                box-sizing: border-box;
                background: #FFFFFF;
                box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                border-radius: 3px;
            }

            .user-box {
                display: flex;
                align-items: center;
                margin-bottom: 14px;

                .user-icon {
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                }

                .user-info {
                    margin-left: 10px;
                    font-size: 12px;

                    .name {
                        font-weight: 600;
                    }

                    .time-buyed {
                        margin-top: 4px;
                        color: #6E6E6E;
                    }
                }
            }

            .txt-content {
                font-size: 13px;
                line-height: 19px;
            }

            .q-footer {
                margin-top: 12px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .postion {
                    font-size: 12px;
                    color: #A0A0A0;
                }

                .more {
                    color: #6E6E6E;
                    font-size: 13px;
                    display: flex;
                    align-items: center;

                    &:before {
                        content: '';
                        margin-right: 2px;
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        background: url(../images/<EMAIL>) no-repeat center center/cover;
                    }
                }
            }
        }
    }
}