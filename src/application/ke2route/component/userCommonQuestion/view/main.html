<import name="style" content="./main" />


<div class="user-common-question">
    <div class="title">常见问题</div>

    <sp:each for="state.list">
        <div class="question-item">
            <div class="q-title">
                <div class="icon image-box"></div>
                <div class="txt">{{$value.question.content}}</div>
            </div>
            <div class="answer-list">
                <div class="swiper-container" skip-attribute="class|style" ref="swiper">
                    <div class="swiper-wrapper" skip-attribute="class|style">
                        <sp:each for="$value.answerList" value="$item">
                            <div class="swiper-slide">
                                <div class="swiper-item">
                                    <sp:if value="$item.nickName && $item.avatar">
                                        <div class="user-box">
                                            <div class="user-icon image-box" style="background-image: url({{$item.avatar}});"></div>
                                            <div class="user-info">
                                                <div class="name">{{$item.nickName}}</div>
                                                <div class="time-buyed">{{$item.buyTime}}购买</div>
                                            </div>
                                        </div>
                                    </sp:if>
                                    <div class="txt-content">{{$item.content}}</div>
                                </div>
                                <sp:if value="$value.answerList.length > 1">
                                    <div class="q-footer">
                                        <div class="postion">第{{$index + 1}}/{{$value.answerList.length}}条回答</div>
                                        <div class="more {{$value.answerList.length != ($index + 1)?'':'hide'}}">左滑查看更多</div>
                                    </div>
                                 </sp:if>
                            </div>
                        </sp:each>
                    </div>
                </div>
            </div>
        </div>
    </sp:each>
</div>
