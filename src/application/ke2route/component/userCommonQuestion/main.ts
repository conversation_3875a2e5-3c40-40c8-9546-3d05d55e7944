/*
 * ------------------------------------------------------------------
 * 小车科一科四
 * ------------------------------------------------------------------
 */

import { getUserQuestions } from ':store/chores';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import Swiper from 'swiper';
import 'swiper/swiper-bundle.css';

export default class extends Component {
    Swiper: any;
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            list: []
        };

    }
    didMount() {

        this.getList().then(() => {
            setTimeout(() => {
                const $dom = this.getDOMNode().swiper as HTMLElement;

                this.Swiper = new Swiper($dom, {
                    loop: false,
                    on: {
                        slideChangeTransitionEnd: (swiper) => {
                            this.setState({
                                currentIndex: swiper.activeIndex
                            });
                        }
                    }
                });
            }, 0);

        });
    }
    getList() {
        return getUserQuestions().then(data => {
            this.setState({
                list: data.itemList
            });
        });
    }
    willReceiveProps() {
        return true;
    }
}
