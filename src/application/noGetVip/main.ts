/*
 * ------------------------------------------------------------------
 * ios会员未到账恢复
 * ------------------------------------------------------------------
 */

import { ensureLogin, openWeb, webClose } from ':common/core';
import jump from ':common/features/jump';
import { BUYED_URL } from ':common/navigate';
import { MCProtocol } from '@simplex/simple-base';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
    /**
     * 恢复发货的结果
     *  0: 无状态 1: 恢复成功 2:恢复失败
     */
    result: 0 | 1 | 2,
    iosButtonConfig: any
}

export default class extends Application<State> {
    $constructor(params, context) {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.context = context;
        this.state = {
            result: 0,
            iosButtonConfig: {
                type: 1,
                price: 100,
                validDays: 20,
                title: 'type1'
            }
        };
    }
    didMount() {
        setTimeout(() => {
            this.setState({
                iosButtonConfig: {
                    type: 6,
                    price: 165,
                    originalPrice: 170,
                    discount: 5,
                    title: 'type5',
                    subtitle: 'type5sub'
                }
            });
        }, 5000);
    }
    goServer() {
        openWeb({
            url: 'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-downApp/jkbdgw.html?open=true'
        });
    }
    realSendVip() {
        MCProtocol.Pay.restoreApplePay((data) => {
            const result: boolean = data.data.code;

            this.setState({
                result: result ? 1 : 2
            });
        });
    }
    async reSendVip() {
        const authToken = await ensureLogin();

        if (authToken) {
            this.realSendVip();
        }
    }
    cancleStatusModal() {
        return () => {
            this.setState({
                result: 0
            });
        };
    }
    goUse() {
        // jump.replace(BUYED_URL);
        webClose();
    }
    failOk() {
        this.cancleStatusModal()();
    }
}
