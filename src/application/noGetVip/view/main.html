<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="modal" content=":component/modal/main" />
<import name="buyButton" content=":component/buyButton/main" />
<div class="no-get-vip">
    <com:header title="恢复购买">
        <div sp:slot="right"></div>
    </com:header>
    <div class="body">
        <div class="banner"></div>
        <div class="content">
            <div class="text">
                通过iOS系统的苹果IAP（苹果内购）购买的驾考宝典商品，若扣款成功且收到苹果公司成功支付的邮件后，权益无法使用，您可以尝试点击下面的【恢复购买】按钮恢复购买
            </div>
            <div class="send-vip" sp-on:click="reSendVip">恢复购买</div>

            <div class="dec-title">使用说明</div>
            <div class="dec">
                如有其它任何疑问，请单击<span
                    class="goServer"
                    sp-on:click="goServer"
                    >联系客服</span
                >
            </div>
        </div>
    </div>
   <com:buyButton config="{{state.iosButtonConfig}}"/>
    <com:modal
        show="{{!!state.result}}"
        customTitle="{{true}}"
        customFooter="{{true}}"
        cancleFn="{{self.cancleStatusModal()}}"
    >
        <sp:if value="{{state.result == 1}}">
            <div sp:slot="title"></div>
            <div sp:slot="body" class="vip-send-success-body">
                <div class="success-txt">
                    <span class="success-icon"></span>
                    恢复成功
                </div>
                <div class="success-use" sp-on:click="goUse">去使用</div>
            </div>
            <div sp:slot="footer"></div> 
        </sp:if>

        <sp:if value="{{state.result == 2}}">
            <div sp:slot="title"></div>
            <div sp:slot="body" class="vip-send-fail-body">
                <div class="title">恢复失败</div>
                <div class="dec">未能找到您的已支付订单，若您确定已完成支付，请联系Apple客服。</div>
                <div class="fail-ok" sp-on:click="failOk">好的</div>
            </div>
            <div sp:slot="footer"></div>
        </sp:if>
    </com:modal>
</div>
