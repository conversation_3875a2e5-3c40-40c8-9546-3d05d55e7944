<import name="style" content="./main" module="S" />
<import name="header" content=":component/header/main" />
<import name="tag" content=":component/tag/main" />
<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />

<import
    name="ChangeScene"
    content=":application/score12buySuccess/component/changeScene/main"
/>

<div class="page-container :page-score12-success">
    <div class=":page-header">
        <com:header
            title=" "
            theme="white"
            endTheme="white"
            scrollTop="{{state.prevScrollTop}}"
        >
            <div sp:slot="right"></div>
        </com:header>
    </div>
    <div class=":score12-success-panel" sp-on:scroll="pageScroll">
        <div class=":banner {{S[URLCommon.tiku]}}">
            <sp:if value="Version.bizVersion > 6 && !URLCommon.isScore12">
                <div class=":change-btn" sp-on:click="onChangeScene">切换扣满12分题库</div>
            </sp:if>
            <!-- <div class=":answer-box">
                <div class=":answer-jindu">答对进度：{{state.finised}}/300</div>
                <div class=":progress-box">
                    <div
                        class=":progress"
                        style="width:{{state.finised/300 * 100}}%"
                    ></div>
                </div>
            </div> -->
        </div>
        <div class=":practice-box">
            <div class=":practice-list">
                <div
                    sp-on:click="goPractice"
                    data-type="1"
                    class=":practice-item"
                >
                    <div class=":title">考前点题卷（一）</div>
                    <div class=":btn">去做题</div>
                </div>
                <div
                    sp-on:click="goPractice"
                    data-type="2"
                    class=":practice-item"
                >
                    <div class=":title">考前点题卷（二）</div>
                    <div class=":btn">去做题</div>
                </div>
                <!-- moto车只有2套卷 -->
                <sp:if value="URLCommon.tiku !== CarType.MOTO">
                    <div
                        sp-on:click="goPractice"
                        data-type="3"
                        class=":practice-item"
                    >
                        <div class=":title">考前点题卷（三）</div>
                        <div class=":btn">去做题</div>
                    </div>
                </sp:if>
            </div>
        </div>

        <div class=":score12buySuccess-component-add-buy">
            <div class=":add-buy-content">
                <div class=":vip-up-title">
                    <div class=":l">做完还是没信心？</div>
                    <div class=":r">
                        {{state.goodsInfoPool.length ===
                        0?'讲师带学，学习更高效':'限时优惠升级，学习更高效'}}
                    </div>
                </div>

                <sp:if value="state.goodsInfoPool.length === 0">
                    <div class=":no-add">
                        <div
                            class=":video-img"
                            sp-on:click="goCourseDetail"
                        ></div>
                        <div
                            sp-on:click="goCourseDetail"
                            class=":go-course-detail"
                        >
                            去看专题课程
                        </div>
                    </div>
                </sp:if>

                <!-- 只有一个的情况只可能是短时提分 -->
                <sp:if value="state.goodsInfoPool.length === 1">
                    <div class=":tab-box2">
                        <com:tag name="tag" text="讲师带学更高效" />
                        <div class=":info">
                            <div class=":title">
                                {{state.goodsInfoPool[0].name}}
                            </div>
                            <div class=":dec">
                                <span>单独售卖价</span>
                                <span class=":unit">￥</span>
                                <span class=":price"
                                    >{{state.goodsInfoPool[0].originalPrice}}</span
                                >
                                <span class=":validity"
                                    >/{{state.goodsInfoPool[0].validDays}}天</span
                                >
                            </div>
                        </div>
                        <div class=":price">
                            <span class=":unit">￥</span>
                            {{state.goodsInfoPool[0].payPrice}}
                        </div>
                    </div>
                </sp:if>
                <sp:if value="state.goodsInfoPool.length === 2">
                    <div class=":tab-box1">
                        <div
                            sp-on:click="onChangeTab"
                            class=":tab tab1 {{state.tabIndex == 0?S.active:''}}"
                            data-tab="0"
                        >
                            <div class=":title">
                                {{state.goodsInfoPool[0].name}}
                            </div>
                            <!-- <div class=":dec">
                                <span>单独售卖价</span>
                                <span class=":unit">￥</span>
                                <span class=":price"
                                    >{{state.goodsInfoPool[0].originalPrice}}</span
                                >
                                <span class=":validity"
                                    >/{{state.goodsInfoPool[0].validDays}}天</span
                                >
                            </div> -->
                            <div class=":bottom">
                                仅需<span class=":price"
                                    >{{state.goodsInfoPool[0].payPrice}}</span
                                >元即可升级
                            </div>
                        </div>
                        <div
                            sp-on:click="onChangeTab"
                            class=":tab tab2 {{state.tabIndex == 1?S.active:''}}"
                            data-tab="1"
                        >
                            <com:tag name="tag" text="讲师带学更高效" />

                            <div class=":title">
                                {{state.goodsInfoPool[1].name}}
                            </div>
                            <!-- <div class=":dec">
                                <span>单独售卖价</span>
                                <span class=":unit">￥</span>
                                <span class=":price"
                                    >{{state.goodsInfoPool[1].originalPrice}}</span
                                >
                                <span class=":validity"
                                    >/{{state.goodsInfoPool[1].validDays}}天</span
                                >
                            </div> -->
                            <div class=":bottom">
                                仅需<span class=":price"
                                    >{{state.goodsInfoPool[1].payPrice}}</span
                                >元即可升级
                            </div>
                        </div>
                    </div>
                </sp:if>

                <!-- 小车客货车扣满12分 -->
                <sp:if
                    value="self.nowGoodInfo.groupKey === GroupKey.ChannelKou12 || self.nowGoodInfo.groupKey === GroupKey.KcChannelKou12 || self.nowGoodInfo.groupKey === GroupKey.HcChannelKou12"
                >
                    <div class=":score12-auth-view"></div>
                </sp:if>
                <!-- 小车客货车短时提分 -->
                <sp:if
                    value="self.nowGoodInfo.groupKey === GroupKey.ChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.KcChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.HcChannelKou12Short"
                >
                    <div class=":score12-short-auth-view"></div>
                </sp:if>

                <!-- moto的扣满12分 -->
                <sp:if
                    value="self.nowGoodInfo.groupKey === GroupKey.MotoChannelKou12"
                >
                    <div class=":score12-auth-view {{S[URLCommon.tiku]}}"></div>
                </sp:if>
                <!-- moto的短时提分 -->
                <sp:if
                    value="self.nowGoodInfo.groupKey === GroupKey.MotoChannelKou12Short"
                >
                    <div
                        class=":score12-short-auth-view {{S[URLCommon.tiku]}}"
                    ></div>
                </sp:if>
            </div>
        </div>
    </div>
    <div class=":footer {{state.goodsInfoPool.length > 0?'':'hide'}}">
        <com:buyButton>
            <div
                sp:slot="couponEntry"
                class=":go_coupon"
                sp-on:click="goCoupon"
            >
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元 >':'领取优惠券'}}
            </div>
        </com:buyButton>
    </div>
</div>

<sp:if value="Version.bizVersion > 6 && !URLCommon.isScore12">
    <com:ChangeScene />
</sp:if>
