.page-score12-success {
    background-color: #EEF9FF;
    height: 100vh;
    display: flex;
    flex-direction: column;

    .page-header {
        position: absolute;
        z-index: 1000;
        top: 0;
        left: 0;
        width: 100%;
    }

    .score12-success-panel {
        flex: 1;
        overflow: auto;

        .banner {
            flex-shrink: 0;
            height: 244px;
            background: url(../images/<EMAIL>) no-repeat center center/cover;
            position: relative;

            &.moto {
                background: url(../images/top_ch_bj_moto.png) no-repeat center center/cover;
            }

            .change-btn {
                position: absolute;
                z-index: 1000;
                bottom: 167px;
                right: 0;
                width: 136px;
                height: 30px;
                background: linear-gradient(148deg, #FECD53 0%, #FFB11A 100%);
                display: flex;
                justify-content: center;
                align-items: center;
                color: white;
                font-size: 13px;
                font-weight: 500;
                border-radius: 15px 0 0 15px;

                &::before {
                    content: '';
                    width: 11px;
                    height: 9px;
                    background: url(../images/<EMAIL>) no-repeat center center/cover;
                }
            }

            .answer-box {
                position: absolute;
                bottom: 30px;
                left: 20px;

                .answer-jindu {
                    font-size: 14px;
                    font-weight: 500;
                }

                .progress-box {
                    margin-top: 10px;
                    width: 335px;
                    height: 7px;
                    margin: 10px auto 0;
                    background: #fff;
                    border-radius: 4px;

                    .progress {
                        height: 100%;
                        border-radius: 4px;
                        background-color: #0AEAC6;
                        transition: all .5s;
                        position: relative;

                        &:after {
                            content: '';
                            position: absolute;
                            top: 50%;
                            right: 0;
                            transform: translate(50%, -50%);
                            width: 9px;
                            height: 9px;
                            border: 3px solid #0AEAC6;
                            border-radius: 50%;
                            background-color: #fff;
                        }
                    }
                }
            }
        }

        .practice-box {
            position: relative;
            z-index: 10;
            padding: 0 15px;
            margin-top: -25px;
            margin-bottom: 15px;

            .practice-list {
                .practice-item {
                    border-radius: 8px;
                    margin-top: 10px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    background-color: #fff;
                    padding: 0 15px;
                    height: 66px;

                    .title {
                        font-size: 16px;
                    }

                    .btn {
                        width: 80px;
                        height: 30px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        color: #fff;
                        font-size: 14px;
                        background-color: #27CEFF;
                        border-radius: 20px;
                    }
                }
            }
        }

        .score12buySuccess-component-add-buy {
            background: linear-gradient(181deg, #BEF2FF 0%, #E2F9FF 43%, #F6FDFF 100%);
            box-shadow: inset 0px 1px 0px 0px #FFFFFF;
            border-radius: 10px 10px 0px 0px;

            .add-buy-content {
                box-sizing: border-box;
                padding: 10px 15px 0;
                width: 100%;

                .vip-up-title {
                    margin-top: 18px;
                    margin-bottom: 15px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .l {
                        font-size: 19px;
                        font-weight: bold;
                        color: #222539;
                    }

                    .r {
                        font-size: 13px;
                        color: #647687;
                    }
                }

                .no-add {
                    margin: 20px auto 0;

                    .video-img {
                        width: 345px;
                        height: 195px;
                        background: url(../images/<EMAIL>) no-repeat center center/cover;
                    }

                    .go-course-detail {
                        margin: 32px auto 22px;
                        width: 345px;
                        height: 48px;
                        background: #0DC8FF;
                        border-radius: 24px;
                        border: 1px solid #FFFFFF;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 16px;
                        color: white;
                        font-weight: bold;
                    }
                }

                .tab-box1 {
                    display: flex;
                    justify-content: space-between;

                    .tab {
                        box-sizing: border-box;
                        width: 167px;
                        height: 90px;
                        padding: 20px 12px 6px;
                        background: #FFFFFF;
                        border-radius: 8px;
                        border: 1px solid #68DDFF;
                        position: relative;
                        text-align: center;

                        .title {
                            font-size: 16px;
                            font-weight: 600;
                            line-height: 22px;
                        }

                        .dec {
                            font-size: 12px;
                            line-height: 16px;
                            color: #222539;
                            margin-top: 5px;
                        }

                        .bottom {
                            position: absolute;
                            width: 100%;
                            left: 0;
                            bottom: 0;
                            height: 28px;
                            background-color: #DBF1FF;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            color: #008BC5;
                            font-size: 12px;
                            font-weight: bold;
                            border-radius: 0 0 6px 6px;
                            overflow: hidden;

                            .price {
                                font-size: 16px;
                            }
                        }


                        &.active {
                            background: linear-gradient(115deg, #FFDBB9 0%, #FED9B4 47%, #FFBE89 100%);
                            border: 1px solid #FFFFFF;

                            .title {
                                color: #C24D00;
                            }

                            .bottom {
                                color: #C24D00;
                                background: linear-gradient(297deg, #FFE9C9 0%, #FFF4E1 100%);
                            }
                        }

                    }
                }

                .tab-box2 {
                    position: relative;
                    width: 347px;
                    height: 92px;
                    background: url(../images/<EMAIL>) no-repeat center center/cover;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 0 31px 0 21px;

                    .info {
                        .title {
                            font-size: 18px;
                            color: #C24D00;
                            font-weight: bold;
                        }

                        .dec {
                            font-size: 12px;
                            margin-top: 7px;
                            color: #9F5217;
                        }
                    }

                    >.price {
                        color: #C24D00;
                        font-size: 30px;

                        .unit {
                            font-size: 18px;
                        }
                    }
                }

                .score12-auth-view {
                    margin-top: 10px;
                    width: 345px;
                    height: 107px;
                    background: url(../images/<EMAIL>) no-repeat center center/cover;

                    &.moto {
                        background: url(../images/bj_moto.png) no-repeat center center/cover;
                    }
                }

                .score12-short-auth-view {
                    margin-top: 10px;
                    width: 345px;
                    height: 107px;
                    background: url(../images/<EMAIL>) no-repeat center center/cover;

                    &.moto {
                        background: url(../images/bj_dstx_moto.png) no-repeat center center/cover;
                    }
                }
            }


        }
    }


    .footer {
        flex-shrink: 0;
        background-color: #fff;

        .paytype-box {
            padding: 0 10px;
        }
    }

}
