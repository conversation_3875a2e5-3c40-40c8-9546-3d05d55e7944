/*
 * ------------------------------------------------------------------
 * 扣满12分购买成功页
 * ------------------------------------------------------------------
 */

import jump from ':common/features/jump';

import { Application } from '@simplex/simple-core';
import { MCProtocol } from '@simplex/simple-base';
import View from './view/main.html';
import { CarType, PayType, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';

import BuyButton, { PayStatProps } from ':component/buyButton/main';
import ChangeScene from ':application/score12buySuccess/component/changeScene/main';
import payType from ':component/payType/main';
import { onPageShow } from ':common/features/page_status_switch';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { typeCode } from ':common/features/bottom';
import { formatPrice } from ':common/utils';
import { newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { iosBuySuccess } from ':common/features/ios_pay';
import { onWebBack } from ':common/features/persuade';
import { goBack, openWeb } from ':common/core';
import { changeSceneScore12, OUTLIMIT } from ':common/navigate';
import { checkRestriction } from ':store/chores';

let timer;
interface State {
    finised: number
    tabIndex: 0 | 1,
    goodsInfoPool: GoodsInfo[]
    comparePricePool: any
    couponPool: any
    labelPool: any
    prevScrollTop: number
}

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton
        payType: payType
        ChangeScene: ChangeScene
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo?.groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
     */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex]?.payPrice;

        if (couponPool[this.nowGoodInfo?.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        const goodsInfoPool = [];

        switch (URLCommon.tiku) {
            case CarType.CAR:
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKou12
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKou12Short
                } as GoodsInfo);
                break;
            case CarType.TRUCK:
                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKou12
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKou12Short
                } as GoodsInfo);
                break;
            case CarType.BUS:
                goodsInfoPool.push({
                    groupKey: GroupKey.KcChannelKou12
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.KcChannelKou12Short
                } as GoodsInfo);
                break;
            case CarType.MOTO:
                goodsInfoPool.push({
                    groupKey: GroupKey.MotoChannelKou12
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.MotoChannelKou12Short
                } as GoodsInfo);
                break;
            default:
                break;
        }

        this.state = {
            finised: 0,
            tabIndex: 0,
            goodsInfoPool: goodsInfoPool,
            comparePricePool: {},
            couponPool: {},
            labelPool: {},
            prevScrollTop: 0
        };

    }
    async didMount() {
        await this.checkRestriction();
        setPageName('扣满12分三套卷页');

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });
        this.appProxy();
        this.getProgress();

        this.getGoodInfo();
    }
    appProxy() {
        onPageShow(() => {
            this.getProgress();
        });

        onWebBack(() => {
            goBack();
            return Promise.resolve();
        });
    }
    getProgress() {
        MCProtocol.Vip.getPracticeProgress({
            car: URLCommon.tiku,
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            callback: (data) => {
                this.setState({
                    finised: data.rightCount
                });
            }
        });
    }
    onChangeTab(e) {
        const tabIndex = e.refTarget.getAttribute('data-tab');

        this.setState({
            tabIndex: tabIndex
        });

        this.setPageInfo();
    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            this.setState({
                prevScrollTop
            });
        }, 10);
    }
    setPageInfo() {
        if (this.state.goodsInfoPool.length) {
            this.setBuyBottom();
        } else if (Platform.isIOS) {
            // ios的情况下，上个页面的底部按钮要关闭（当前webview跳转的）
            MCProtocol.Vip.setBottom({ type: 0 });
        }
    }

    /** 进入页面先判断是否设备超限 */
    async checkRestriction() {
        const { pass } = await checkRestriction();
        if (!pass) {
            jump.replace(OUTLIMIT);
        }
    }
    setBuyBottom() {
        const fragmentName1 = '引导升级区';
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const bottomType: typeCode = typeCode.type3;

        this.children.buyButton.setButtonConfig({
            type: bottomType,
            groupKey: nowGoodInfo.groupKey,
            title: '¥ ' + this.showPrice + ' 确认协议并支付',
            subtitle: '有效期' + nowGoodInfo.validDays + '天',
            fragmentName1
        });
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {

            goodsListInfo.forEach(goodInfo => {
                if (!goodInfo.bought || (goodInfo.bought && goodInfo.upgrade)) {
                    newGoodsPool.push(goodInfo);
                }
            });

            this.setState({
                goodsInfoPool: newGoodsPool
            });

            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getComparePrice();
                await this.getLabel();

                this.setPageInfo();
            }, 60);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        upGroupItems: item.groupItems,
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice
                    };
                }
            });

            this.setState({ comparePricePool });
        });
    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });

            this.setState({ labelPool });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo?.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
        });
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);
        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    goPractice(e) {
        const type = e.refTarget.getAttribute('data-type');

        jump.navigateTo('http://jiakao.nav.mucang.cn/fullScore', {
            type
        });
    }
    goCourseDetail() {
        openWeb({
            url: 'http://jiakao.nav.mucang.cn/topLesson/detail?id=787'
        });
    }
    onChangeScene() {
        changeSceneScore12();
    }
}
