.change-scene-dialog {
    .dialog-wrap {
        width: 320px;
        position: relative;
        .top-bg {
            height: 154px;
            background: url(../images/head-bg.png) no-repeat center center/cover;
        }

        .content {
            margin-top: -1px;
            background-color: #fff;
            padding-bottom: 32px;
            padding-top: 28px;
            border-radius: 0 0 8px 8px;
            .title {
                text-align: center;
                font-size: 18px;
                font-weight: bold;
            }

            .dec {
                margin-top: 18px;
                color: #6e6e6e;
                font-size: 14px;
                text-align: center;
            }

            .btn {
                width: 229px;
                height: 44px;
                background: linear-gradient(135deg, #67CEF8 0%, #1E74FA 100%);
                box-shadow: 0px 4px 12px -4px rgba(4, 165, 255, 0.5);
                border-radius: 22px;
                margin: 22px auto 0;

                color: white;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 16px;
            }
        }
        .close-dialog{
            width: 36px;
            height: 36px;
            background: url(../images/close.png) no-repeat center center/cover;
            position: absolute;
            bottom: -60px;
            left: 50%;
            transform: translateX(-50%);
        }
    }
   
}
