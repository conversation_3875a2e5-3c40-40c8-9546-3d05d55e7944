/*
 * ------------------------------------------------------------------
 * 分享(因为只在安卓上面展示，所以不用考虑ios底部按钮层级问题)
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
import { Dialog } from ':component/dialog/main';
import { replace } from ':common/features/jump';
import { changeSceneScore12, SCORE12_BUYED_URL } from ':common/navigate';
import { trackDialogShow, trackEvent } from ':common/stat';

const fragmentName1 = '引导切换扣满12分场景弹窗';

export default class changeScene extends Dialog {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }
    didMount() {
        trackDialogShow({
            fragmentName1
        });
        this.show();
    }
    onClose() {
        this.hide();
    }
    onChangeScene() {
        trackEvent({
            fragmentName1,
            actionType: '点击',
            actionName: '切换场景'

        });
        changeSceneScore12();
    }
}
