/*
 * main
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 16/3/24
 */
import { URLCommon, URLParams } from ':common/env';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import KqfdBuyDialog from ':application/kqfd/component/kqfdBuyDialog/main';
import { trackEvent, trackGoPay } from ':common/stat';
import { GoodsInfo } from ':store/goods';
import { getHistoryList, getLiveLessonSchedule, subscribe } from ':store/kqfd';
import { dateFormat } from ':common/utils';
import OnlineDialog from ':application/kqfd/component/onlineDialog/main';
import { getAuthToken, openVipWebView, openWeb } from ':common/core';
import { KQFD_HISTORY, REAL_ROOM, SECRET, VIP_LIVE } from ':common/navigate';
import { makeToast } from ':common/features/dom';

interface State {
    fragmentName1: string
    lessonSchedule: any[],
    hasMore: boolean,
    limit: number,
    page: number,
    loadTextTips: string,
}
interface Props {
    pageScroll(event: EventTarget)
    goodsInfoPool: GoodsInfo[],
    hasPromission: boolean
}

export default class extends Component<State, Props> {
    declare children: {
        kqfdBuyDialog: KqfdBuyDialog,
        onlineDialog: OnlineDialog
    }
    // 获取最新直播时间
    get newSchelTime() {
        const { lessonSchedule } = this.state;
        // 去最新的直播中，预约，回放
        const newZhiboData = [];
        lessonSchedule && lessonSchedule.forEach((res) => {
            newZhiboData.push(...res.liveDataList);
        });
        // status:1直播中，2预约，3回放
        const sortByData: any = newZhiboData && newZhiboData.sort((a, b) => {
            return +a.status - +b.status;
        });

        // eslint-disable-next-line max-len
        const timeString = dateFormat(sortByData[0]?.beginTime, 'MM.dd') + ' ' + dateFormat(sortByData[0]?.beginTime, 'HH:mm');
        return timeString;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            fragmentName1: '',
            lessonSchedule: [],
            hasMore: true,
            limit: 5,
            page: 0,
            loadTextTips: '加载中'
        };

    }
    willReceiveProps() {
        return true;
    }
    async didMount() {
        this.getZhiboData();
    }
    async getHistory() {
        const { limit, page, lessonSchedule } = this.state;
        const resHistory: any = await getHistoryList({
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            lessonType: 2,
            page: page,
            limit: limit
        });
        if (resHistory.length <= 0) {
            this.setState({ hasMore: false, loadTextTips: '没有更多了~' });
        }
        this.setState({
            lessonSchedule: [
                ...lessonSchedule,
                ...resHistory
            ]
        }, () => {
            if (this.state.lessonSchedule.length <= 0) {
                this.children.onlineDialog.show();
            }
        });
    }
    async getZhiboData() {
        const lessonSchedule: any = await getLiveLessonSchedule({
            limit: -1,
            lessonType: 2
        });
       
        this.setState({ lessonSchedule, page: 1 }, () => {
            this.getHistory();
        });
    }
    onPay() {
        window.top.postMessage({
            type: 'pay',
            data: {}
        }, '*');
    }
    async makeappointment(e) {
        const { lessonSchedule } = this.state;
        e.stopPropagation();
        const lessonId = e.refTarget.getAttribute('data-liveSessionId');
        const index = e.refTarget.getAttribute('data-index');
        const sonIndex = e.refTarget.getAttribute('data-sonIndex');
        await subscribe({ lessonId });
        lessonSchedule[index].liveDataList[sonIndex].subscribeStatus = 1;

        this.setState({
            lessonSchedule
        });

    }
    scroll = (e) => {
        this.props.pageScroll && this.props.pageScroll(e);
    }
    requestData = async () => {
        if (!this.state.hasMore) {
            return;
        }
        this.setState({
            page: ++this.state.page
        });

        await this.getHistory();
    }
}
