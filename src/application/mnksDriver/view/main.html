<import name="style" content="./main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payType" content=":component/payType/main" />
<div class="page-container page-mnks-driver">
    <div class="mnks-driver-container">
        <div class="title">
            恢复驾驶证真实考场模拟
        </div>
        <div class="desc">还原电脑正式考试界面</div>
        <div class="close" sp-on:click="onCloseClick"></div>
        <div class="quanyi"></div>
    </div>
    <div class="pay-type {{Platform.isIOS && 'hide'}}">
        <com:payType theme="mnks-driver" />
    </div>
    <div class="buy-btn">
        <com:buyButton noPayType="true">
            <div sp:slot="couponEntry" class="go_coupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元':''}}
            </div>
        </com:buyButton>
    </div>
</div>