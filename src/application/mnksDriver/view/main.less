.page-mnks-driver {
    position: relative;
    background: #ffffff;
    box-shadow: 0px 1px 0px 0px #e8e8e8 inset;
    border-radius: 6px 6px 0px 0px;
    padding: 15px 15px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    .mnks-driver-container {
        flex: 1;
        position: relative;
        .title {
            font-size: 18px;
            font-family: PingFangSC, PingFangSC-Semibold;
            font-weight: 700;
            text-align: left;
            color: #333333;
            line-height: 25px;
        }
        .desc {
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: left;
            color: #666666;
            line-height: 20px;
            margin-bottom: 14px;
        }
        .close {
            position: absolute;
            top: 0px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: url("../images/close.png");
            background-size: cover;
        }
        .quanyi {
            width: 345px;
            height: 120px;
            background: url(../images/quanyi-bg.png) no-repeat center center;
            background-size: 100% 100%;
        }
    }
    .pay-type {
        margin-bottom: -10px;
        margin-left: 10px;
    }

    .buy-btn {
        margin: 0 -10px;
    }
}
