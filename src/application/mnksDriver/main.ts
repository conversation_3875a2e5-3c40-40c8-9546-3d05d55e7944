/*
 * ------------------------------------------------------------------
 * 恢复驾驶证模拟考试购买弹窗
 * ------------------------------------------------------------------
 */
import { CarType, PayType, setPageName, URLCommon, URLParams } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayTypeCom from ':component/payType/main';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { iosDialogBuySuccess } from ':common/features/ios_pay';
import { webClose } from ':common/core';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackEvent, trackGoPay } from ':common/stat';
import { Coupon, getBestCoupon, goodsInfoWithCoupon } from ':common/features/coupon';
import { showSetting } from ':common/features/embeded';

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
}
export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton,
        payType: PayTypeCom;
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    get pageName() {
        return '恢复驾驶证考试前置页';
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    $constructor() {
        const goodsInfoPool: GoodsInfo[] = [];
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        switch (URLCommon.tiku) {
            case CarType.CAR:
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe1
                } as GoodsInfo);
                break;
            case CarType.TRUCK:
                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKe1
                } as GoodsInfo);
                break;
            case CarType.BUS:
                goodsInfoPool.push({
                    groupKey: GroupKey.KcChannelKe1
                } as GoodsInfo);
                break;
            case CarType.MOTO:
                goodsInfoPool.push({
                    groupKey: GroupKey.MotoChannelKe1
                } as GoodsInfo);
                break;
            default: break;
        }

        this.state = {
            tabIndex: 0,
            goodsInfoPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {}
        };

    }
    async didMount() {
        showSetting({
            iosH: 205,
            androidH: 331
        });
        const { tabIndex, goodsInfoPool } = this.state;
        this.setPageInfo();
        await this.getGoodInfo();
        // 先展示页面，再去请求无关的信息
        setTimeout(async () => {
            await this.getCoupon();
            await this.getLabel();
            await this.getComparePrice();

            this.setPageInfo();
        }, 60);
        setPageName(this.pageName);

        trackEvent({
            fragmentName1: '购买弹窗',
            actionType: '出现'
        });
        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosDialogBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
            },
            isInDialog: true
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey
        });

    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {

        const fragmentName1 = '购买弹窗';
        const { tabIndex, goodsInfoPool, labelPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];

        let tag = null;
        const label = labelPool[nowGoodInfo.groupKey]?.label;

        if (label) {
            tag = {
                text: label
            };
        }
        this.children.buyButton.setButtonConfig({
            groupKey: nowGoodInfo.groupKey,
            type: 4,
            title: `¥ ${this.showPrice}元确认协议并支付`,
            subtitle: '有效期' + nowGoodInfo.validDays + '天',
            fragmentName1,
            tag: tag
        });

    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {

                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });
            this.setState({
                goodsInfoPool: newGoodsPool
            });
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice
                    };
                }
            });
            this.setState({ comparePricePool });
        });
    }

    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey }, 2);
        });
    }

    onCloseClick = () => {
        webClose();
    }
}
