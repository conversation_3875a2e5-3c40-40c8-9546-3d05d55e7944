.vip-course-container-re {
    height: 326/2px;
    background: url("../../../images/vip-buyed/vip-course.png") no-repeat
        center/cover;
    background-size: 100% 100%;
    margin-top: 15px;
    overflow: hidden;

    &.update-style {
        height: 426/2px;
    }

    .title-a {
        height: 86/2px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 24/2px;
        padding-right: 24/2px;
        position: relative;
        top: 2/2px;

        .left {
            .left-img1 {
                display: inline-block;
                width: 46/2px;
                height: 42/2px;
                background: url("../../../images/vip-buyed/vip-course-left.png")
                    no-repeat center center;
                background-size: 46/2px 42/2px;
            }

            .left-img2 {
                display: inline-block;
                width: 232/2px;
                height: 40/2px;
                background: url("../../../images/vip-buyed/vip-couse-font.png")
                    no-repeat center center;
                margin-left: 6/2px;
                background-size: 232/2px 40/2px;
            }
        }

        .right-container-top-left {
            line-height: 16px;
            font-size: 24/2px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #783509;
            vertical-align: middle;
            display: flex;
            align-items: center;

            &:after {
                content: "";
                display: inline-block;
                width: 16/2px;
                height: 26/2px;
                margin-left: 4/2px;
                background: url("../../../images/vip-buyed/vip-course-jiantou.png")
                    no-repeat center center;
                background-size: 100% 100%;
            }
        }
    }

    .scroll-div {
        width: 100%;
        overflow-x: scroll;
        overflow-y: hidden;
        position: relative;
        white-space: nowrap;
    }

    .update-guide-huiyuan {
        width: 650/2px;
        height: 80/2px;
        display: flex;
        align-items: center;
        margin: 22/2px 24/2px;
        background: url(../../../images/vip-buyed/update-bg.png) no-repeat
            center center;
        background-size: 100% 100%;
        font-size: 13px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: bold;
        color: #692204;
        padding-left: 10px;
        padding-right: 10px;

        .img {
            display: inline-block;
            width: 78/2px;
            height: 66/2px;
            background: url(../../../images/vip-buyed/update-fudai.png)
                no-repeat center center;
            background-size: 100% 100%;
            margin-left: 22/2px;
            margin-right: 6/2px;
        }

        .price {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            .price-price {
                font-size: 23px;
                color: #ff4a40;
                vertical-align: sub;
            }
        }
        .update-price {
            display: inline-block;
        }

        .center-title {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 10px;
            .swallconfig-name {
                color: #ff4a40;
                font-style: normal;
            }
        }

        .right-icon {
            width: 20px;
            height: 19px;
            vertical-align: middle;
            background: url("../images/right-jiantou.png") no-repeat center
                center;
            background-size: 100% 100%;
        }
    }

    .title-b {
        .item-b {
            display: inline-block;
            width: 448/2px;
            height: 220/2px;
            margin-left: 20/2px;
            background: #ffffff;
            border-radius: 12/2px;
            white-space: normal;
            vertical-align: top;
            position: relative;
            background: url("../../../images/vip-buyed/vip-course-item.png")
                no-repeat center center;
            background-size: 448/2px 220/2px;
            &.item-b-other {
                background: url("../images/kqfd-bg.png") no-repeat center;
                background-size: 224px 110px;
            }

            &:last-child {
                margin-right: 20/2px;
            }

            .top {
                position: relative;
                padding: 24/2px 24/2px 0px 24/2px;

                .left-title-meme {
                    font-size: 28/2px;
                    line-height: 18px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    text-overflow: -o-ellipsis-lastline;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    -webkit-box-orient: vertical;
                    padding-right: 108/2px;
                    &.left-title-meme-other {
                        padding-right: 12px;
                    }
                    .kqfd {
                        display: inline-block;
                        width: 50px;
                        text-align: center;
                        height: 21px;
                        line-height: 21px;
                        align-items: center;
                        justify-content: center;
                        background: linear-gradient(
                            285deg,
                            #ef4b24 7%,
                            #ff6942 88%
                        );
                        border-radius: 4px;
                        font-size: 12px;
                        font-family: PingFangSC, PingFangSC-Medium;
                        font-weight: 600;
                        text-align: center;
                        color: #ffffff;
                        margin-right: 5px;
                        vertical-align: middle;
                        margin-bottom: 3px;
                        .kqfd-icon {
                            display: inline-block;
                            width: 18px;
                            height: 18px;
                            background: url(../images/qitaPlay.png) no-repeat
                                center;
                            background-size: 100%;
                            vertical-align: middle;
                        }
                    }
                }

                .right {
                    width: 64/2px;
                    height: 64/2px;
                    right: 24/2px;
                    position: absolute;
                    top: 28/2px;

                    vertical-align: middle;
                    background: url("../../../images/vip-buyed/vip-course-play.png")
                        no-repeat center center;
                    background-size: 100% 100%;
                }
            }
            .kqfd-bottom {
                position: absolute;
                bottom: 18px;
                font-size: 12px;
                font-family: PingFangSC, PingFangSC-Regular;
                font-weight: 400;
                color: #666666;
                line-height: 17px;
                padding-left: 9px;
                .symbol {
                    font-size: 16px;
                    font-family: PingFangSC, PingFangSC-Medium;
                    font-weight: 500;
                    color: #ff4a40;
                    line-height: 22px;
                    margin-left: 26px;
                }
                .price {
                    font-size: 22px;
                    font-family: PingFangSC, PingFangSC-Medium;
                    font-weight: 500;
                    color: #ff4a40;
                    line-height: 22px;
                }
                .money {
                    font-size: 16px;
                    color: #ff4a40;
                    margin-left: 26px;
                }
            }
            .bottom {
                position: absolute;
                width: 100%;
                bottom: 30/2px;
                padding-left: 24/2px;

                .left {
                    vertical-align: middle;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                    margin-right: 140/2px;
                    font-size: 24/2px;

                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #666666;
                    vertical-align: middle;
                    width: 230/2px;

                    .font {
                        vertical-align: middle;
                    }

                    .avter {
                        background-repeat: no-repeat;
                        background-size: cover;
                        background-position: center center;
                        display: inline-block;
                        width: 50/2px;
                        height: 50/2px;
                        border-radius: 50%;
                        margin-right: 10/2px;
                        vertical-align: middle;

                        &:last-child {
                            margin-right: 0px;
                        }
                    }
                }

                .right {
                    position: absolute;
                    right: 24/2px;
                    top: 0px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #ff4a40;
                    text-align: right;

                    .money {
                        font-size: 32/2px;

                        &.money01 {
                            position: relative;
                        }

                        .symbol {
                        }

                        .price {
                            font-size: 42/2px;
                        }
                    }
                }
            }
        }
    }
}
