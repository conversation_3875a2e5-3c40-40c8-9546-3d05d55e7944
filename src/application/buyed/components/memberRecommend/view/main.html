<import name="style" content="./main" module="S" />
<div>
    <sp:if value='{{!Platform.isNoProtocol&&(state.recommendVip.length>0||state.lesson.goodsKey)}}'>
        <div class="{{S.vipCourseContainerRe}} {{props.nowGoodInfo.upgrade?S.updateStyle:''}}">
            <div class="{{S.titleA}}">
                <div class="{{S.left}}">
                    <span class="{{S.leftImg1}}"></span>
                    <span class="{{S.leftImg2}}"></span>
                </div>
                <div class="{{S.rightContainerTopLeft}}" sp-on:click='gotoRecommendVip'>
                    讲师带学提升快
                </div>
            </div>
            <div class="{{S.scrollDiv}}">
                <div class="{{S.titleB}}">
                    <sp:if value='{{state.lesson.goodsKey}}'>
                        <div class="{{S.itemB}} {{S.itemBOther}}" sp-on:click="gotoKaoqian">
                            <div class="{{S.top}}">
                                <div class="{{S.leftTitleMeme}} {{S.leftTitleMemeOther}}">
                                    <span class="{{S.kqfd}}"><span
                                            class="{{S.kqfdIcon}}"></span>直播</span>{{state.lesson.title}}
                                </div>
                            </div>
                            <div class="{{S.kqfdBottom}}">
                                {{state.lesson.timeStering}}
                                <sp:if value='{{state.bought}}'>
                                    <span class="{{S.money}}">已解锁</span>
                                    <sp:else />
                                    <span class="{{S.symbol}}">￥</span>
                                    <span class="{{S.price}}">{{state.kqfdPayPrice}}</span>
                                </sp:if>

                            </div>
                        </div>
                    </sp:if>

                    <sp:each for='{{state.recommendVip}}'>
                        <div class="{{S.itemB}}" data-href="{{$value.url}}" sp-on:click="gotoDetailRecommendVip">
                            <div class="{{S.top}}">
                                <div class="{{S.leftTitleMeme}}">
                                    {{$value.title}}
                                </div>
                                <div class="{{S.right}}"></div>
                            </div>
                            <div class="{{S.bottom}}">
                                <div class="{{S.left}}">
                                    <sp:each for='{{$value.topLessonTeacherList}}' value="$sonItem">
                                        <span class="{{S.avter}}"
                                            style="background-image: url({{Tools.calcImg($sonItem.avatar)}})"></span>
                                    </sp:each>

                                    <sp:if value='{{$value.topLessonTeacherList.length==1}}'>
                                        <span class="{{S.font}}">{{$value.topLessonTeacherList[0].name}}</span>
                                    </sp:if>

                                </div>
                                <div class="{{S.right}}">
                                    <sp:if value='{{$value.hasPermission}}'>
                                        <div class="{{S.money}}">已解锁</div>
                                        <sp:else />
                                        <div class="{{S.money}} {{S.money01}}" sp:if="{{$value.price>0}}">
                                            <span class="{{S.symbol}}">￥</span>
                                            <span class="{{S.price}}">{{$value.price}}</span>
                                        </div>
                                    </sp:if>


                                </div>
                            </div>
                        </div>
                    </sp:each>

                </div>
            </div>
            <sp:if value='{{props.nowGoodInfo.upgrade}}'>
                <div class="{{S.updateGuideHuiyuan}}" sp-on:click="pay" data-from="50" data-fragmentName1="会员优选课程"
                    data-fragmentName2="升级按钮" data-noCoupCode="noCoupCode">
                    <div class="{{S.centerTitle}}">
                        <div class="{{S.price}}">
                            <span class="{{S.pricePrice}}">{{props.nowGoodInfo.payPrice}}
                            </span>
                            <i class="{{S.swallconfigName}}">元</i>升级<i
                                class="{{S.swallconfigName}}">{{props.swallConfig.name}}</i>，解锁所有付费课程
                        </div>


                    </div>
                    <div class="{{S.rightIcon}}"></div>

                </div>
            </sp:if>
        </div>
    </sp:if>
</div>