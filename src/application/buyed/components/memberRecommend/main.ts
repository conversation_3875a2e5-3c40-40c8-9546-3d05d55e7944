import { openVipWebView, openWeb } from ':common/core';
import { URLCommon, URLParams } from ':common/env';
import { FAMOUS_TEACHER, KQFD, ZDST_HISTORY } from ':common/navigate';
import { trackEvent } from ':common/stat';
import { dateFormat } from ':common/utils';
import { getLatelyLesson, getRecommendVip } from ':store/buyed';
import { GroupKey } from ':store/goods';
import { squirrelPrice } from ':store/newGoods';
import { getGroupSessionInfo, GoodsInfo } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
interface State {
    recommendVip: any[],
    kqfdPayPrice: number | string,
    bought: boolean,
    lesson: any,
    kemu: string | number
}
interface Props {
    payBtnCall?(e: Event),
    swallConfig: any
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            recommendVip: [],
            kqfdPayPrice: '',
            bought: false,
            lesson: {},
            kemu: ''
        };
    }
    async recommendVipRequest(kemu: number) {
        const groupKeys = [];
        const recommendVip = await getRecommendVip(kemu);
        recommendVip && recommendVip.forEach((res) => {
            if (res.channelCode) {
                groupKeys.push(res.channelCode);
            }
        });
        // 返回channelCode ,在根据channelCode去squiire查价格
        const goodsPriceConfig = await this.recommengVipGoodsInfo(groupKeys);
        recommendVip.forEach((res) => {
            res.price = goodsPriceConfig[res.channelCode]?.payPrice || res.price;
        });
        this.setState({ recommendVip, kemu });
        if (!kemu) {
            return;
        }
        const resLess: any = await getLatelyLesson({
            carType: URLCommon.tiku,
            kemu: kemu,
            lessonType: 3
        });
        // 时间取第一条开始时间和最后一条结束时间
        let timeStering = '';
        if (resLess.liveDataList && resLess.liveDataList[0]?.beginTime) {
            timeStering = dateFormat(resLess.liveDataList[0]?.beginTime, 'yyyy.MM.dd HH:mm') + '-' + dateFormat(resLess.liveDataList[resLess.liveDataList.length - 1]?.endTime, 'HH:mm');
        }

        resLess.timeStering = timeStering;
        this.setState({ lesson: resLess });
        if (resLess.goodsKey) {
            trackEvent({
                fragmentName1: '会员推荐课程',
                fragmentName2: '考前辅导',
                actionName: '曝光',
                actionType: ''
            });
            await this.getGoodInfo(resLess.goodsKey);
        }

    }
    async getGoodInfo(goodsKey) {
        await getGroupSessionInfo({ groupKeys: [goodsKey] }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                this.setState({ kqfdPayPrice: goodInfo.payPrice, bought: goodInfo.bought });

            });
        });
    }
    async recommengVipGoodsInfo(groupKeys: GroupKey[]) {
        const goodsConfig = {};
        const goodsListInfo = await squirrelPrice({ groupKeys });
        goodsListInfo && goodsListInfo.forEach((res) => {
            goodsConfig[res.channelCode] = res;
        });
        return goodsConfig;
    }
    gotoRecommendVip() {
        openWeb({
            url: FAMOUS_TEACHER + '?from=44'
        });
    }
    gotoDetailRecommendVip(e) {
        const gotoUrl = e.refTarget.getAttribute('data-href');
        openWeb({
            url: gotoUrl
        });
    }
    pay(e) {
        openWeb({
            url: this.props.swallConfig.url
        });
        // this.props.payBtnCall && this.props.payBtnCall(e);
    }
    gotoKaoqian() {
        // 进入已购买的科目是不变的，但tab切换会变科目
        let paramsStr = '?kemuStyle=' + this.state.kemu + '&from=44&';
        for (const key in URLParams) {
            if (key !== 'kemuStyle' && key !== 'from' && key !== 'statistics') {
                paramsStr += key + '=' + encodeURIComponent(URLParams[key]) + '&';
            }
        }
        trackEvent({
            fragmentName1: '会员推荐课程',
            actionName: '重点刷题',
            actionType: '点击'
        });
        openVipWebView({
            url: ZDST_HISTORY + paramsStr.substring(0, paramsStr.length - 1)
        });

    }
    willReceiveProps() {
        return true;
    }
}
