import { setPageName } from ':common/env';
import { trackDialogShow, trackEvent } from ':common/stat';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import Texts from ':common/features/texts';
interface State {
    show: boolean
}
interface Props {
    closeReserveDialog(),
    gotoBuchgang()
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            show: false
        };
    }
    willReceiveProps() {
        return true;
    }
    didMount() {
        setPageName(Texts.BUYED_PAGE);
    }
    tcClose() {
        this.hide();
        trackEvent({
            fragmentName1: '补偿资料填写弹窗',
            actionName: '下次再说',
            actionType: '点击'
        });
        this.props.closeReserveDialog();
    }
    reserveInfo() {
        this.hide();
        trackEvent({
            fragmentName1: '补偿资料填写弹窗',
            actionName: '立即填写',
            actionType: '点击'
        });
        this.props.gotoBuchgang();

    }
    hide() {
        this.setState({
            show: false
        });
    }
    show() {
        trackDialogShow({
            fragmentName1: '补偿资料填写弹窗'
        });
        this.setState({
            show: true
        });
    }
}
