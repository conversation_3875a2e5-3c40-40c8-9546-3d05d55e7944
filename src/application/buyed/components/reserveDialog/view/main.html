<import name="style" content="./main" module="S" />
<div class="{{S.reserveDialogWrap}}">
    <sp:if value="state.show">
        <div class="{{S.reserveDialog}}">
            <div class="{{S.reserveContent2}}">
                <div class="{{S.buttonContainer}}">
                    <div class="{{S.topBg}}"></div>
                    <div class="{{S.title}}">您的补偿权益未激活</div>
                    <div class="{{S.desc}}">请至少在参加<span class="{{S.active}}">正式考试前24小时</span>激活<br>以免遗憾未过时无法申请补偿</div>
                    <div class="{{S.reserveInfo}}" ref="reserve-info" sp-on:click="reserveInfo">了解如何激活</div>
                    <div class="{{S.cancel}}" sp-on:click="tcClose">暂不了解</div>
                    <div class="{{S.close}}" sp-on:click="tcClose">
                        <div class="{{S.icon}}"></div>
                    </div>
                </div>

            </div>
        </div>
    </sp:if>
</div>