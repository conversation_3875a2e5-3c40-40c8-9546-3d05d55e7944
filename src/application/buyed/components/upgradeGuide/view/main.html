<import name="style" content="./main" module="S" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payType" content=":component/payType/main" />
<div class="{{S.upgradeGuideContainer}}  {{state.showDialog&&props.nowGoodInfo.upgrade?'':S.hide}}">
    <div class="{{S.fixBottomDialog}}">
        <div class="{{S.fixBottom}} {{Platform.isIOS?S.paddingBootom:''}}">
            <div class="{{S.content}}">
                <div class="{{S.close2}}" sp-on:click="closeDialog"></div>
                <div class="{{S.top}}">
                    <div class="{{S.avatar}}" style="background-image: url({{props.userData.avatar}})"></div>
                    <div class="{{S.topTitle}}">
                        <p class="{{S.title1}}">
                            亲爱的会员{{self.handleUserName}}，邀请您
                        </p>
                        <p class="{{S.title2}}">超值升级为{{props.swallConfig.name}}</p>
                    </div>

                </div>
                <div class="{{S.goodsContainer}}">
                    <p class="{{S.goodsTitle}}">- 即刻开通，享超值特权 -</p>
                    <div class="{{S.bgContainer}}">
                        <div class="{{S.first}}">
                            比分开买
                            <br />
                            立省
                            <br />
                            <span class="{{S.priceFirst}}"><i>￥</i>
                                {{props.comparePricePool[props.nowGoodInfo.groupKey].diffPrice}}
                            </span>
                        </div>
                        <sp:each for='{{props.comparePricePool[props.nowGoodInfo.groupKey].groupItems}}' value="item"
                            index="index">
                            <div
                                class="{{S.seconde}} {{index==0?S.secondeQita:''}}  {{index==props.comparePricePool[props.nowGoodInfo.groupKey].groupItems.length-1?S.last:''}}">
                                {{item.name}}

                                <span class="{{S.priceSeconde}}">
                                    <sp:if value='{{item.price}}'>
                                        <i>￥</i>
                                        {{item.price}}
                                    </sp:if>
                                    <sp:if value='{{!item.price}}'>
                                        <span class="{{S.labelName}}">{{item.description}}</span>
                                    </sp:if>


                                </span>
                                <sp:if
                                    value="index !== props.comparePricePool[props.nowGoodInfo.groupKey].groupItems.length-1">
                                    <div class="{{S.yuanClose}}"></div>
                                </sp:if>

                            </div>
                        </sp:each>
                    </div>
                </div>

            </div>
            <com:buyButton name="buyButton">
                <div sp:slot="couponEntry" class="go_coupon">
                    {{props.nowCouponInfo.couponCode?'已优惠' +
                    props.nowCouponInfo.priceCent + '元':''}}
                </div>
            </com:buyButton>
        </div>
    </div>
</div>