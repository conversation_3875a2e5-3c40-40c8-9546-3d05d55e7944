.upgrade-guide-container {
    &.hide {
        display: none;
    }
}
.fix-bottom-dialog {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 100;
    height: 100%;
    width: 100%;
    .paytype-box {
        padding: 0 15px;
    }

    .fix-bottom {
        position: absolute;
        width: 100%;
        bottom: 0px;
        background: linear-gradient(
            180deg,
            #ffeddc 0%,
            #fffaf0 38%,
            #ffffff 77%,
            #ffffff 100%
        );
        box-shadow: 0px 2/2px 0px 0px #ffffff;

        &.padding-bootom {
            padding-bottom: 80/2px;
        }

        .content {
            width: 100%;
            min-height: 300/2px;
            position: relative;
            padding-top: 40/2px;

            .close2 {
                position: absolute;
                top: 20/2px;
                right: 20/2px;
                width: 40/2px;
                height: 40/2px;
                background: url("../images/pxzj_guanbi.png") no-repeat center;
                background-size: 100%;
            }

            .goods-container {
                background: #ffffff;

                .goods-title {
                    font-size: 30/2px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #692204;
                    line-height: 42/2px;
                    text-align: center;
                    padding: 22/2px 0px 18/2px 0px;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                }

                .bg-container {
                    margin: 0px 28/2px;
                    height: 200/2px;
                    background: url("../images/bj.png") no-repeat center;
                    background-size: 100%;
                    display: flex;
                    align-items: center;

                    .first {
                        width: 164/2px;
                        text-align: center;
                        font-size: 28/2px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #692204;

                        .price-first {
                            font-size: 40/2px;
                            font-family: PingFangSC-Semibold, PingFang SC;
                            font-weight: 600;
                            color: #f73b31;

                            i {
                                font-style: normal;
                                font-size: 30/2px;
                            }
                        }
                    }

                    .seconde {
                        width: 148/2px;
                        height: 172/2px;
                        background: linear-gradient(
                            163deg,
                            #fffbf6 0%,
                            #fff7ee 100%
                        );
                        border-radius: 9/2px;
                        border: 1px solid #ffcc8a;
                        font-size: 24/2px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #a03c1c;
                        text-align: center;
                        margin-left: 10/2px;
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-direction: column;

                        &.seconde-qita {
                            margin-left: 0px;
                        }

                        .price-seconde {
                            font-size: 34/2px;
                            font-family: PingFangSC-Medium, PingFang SC;
                            font-weight: 500;
                            line-height: 48/2px;
                            color: #a03c1c;
                            margin-top: 5/2px;

                            i {
                                font-style: normal;
                                font-size: 23/2px;
                            }

                            .label-name {
                                font-size: 23/2px;
                            }
                        }

                        &.last {
                            width: 200/2px;
                            height: 172/2px;
                            background: linear-gradient(
                                163deg,
                                #fffbf6 0%,
                                #fff7ee 100%
                            );
                            border-radius: 9/2px;
                            border: 1px solid #ffcc8a;
                        }

                        .yuan-close {
                            position: absolute;
                            right: -25/2px;
                            width: 38/2px;
                            height: 38/2px;
                            background: url("../images/ic-jh.png") no-repeat
                                center;
                            background-size: 100%;
                            z-index: 1;
                        }
                    }
                }
            }

            .top {
                padding-left: 30/2px;
                display: flex;
                align-items: center;
                padding-bottom: 40/2px;

                .avatar {
                    display: inline-block;
                    width: 108/2px;
                    height: 108/2px;
                    border: 2/2px solid #ffffff;
                    border-radius: 50%;
                    background-repeat: no-repeat;
                    background-size: 108/2px 108/2px;
                    background-position: center center;
                    margin-right: 20/2px;
                }

                .top-title {
                    width: calc(100% - 128 / 2px);

                    .title1 {
                        font-size: 28/2px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #333333;
                    }

                    .title2 {
                        width: 100%;
                        font-size: 44/2px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #692204;
                        line-height: 60/2px;
                        text-shadow: 0px 1/2px 0px #9a6a5b;
                        margin-top: 8/2px;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        overflow: hidden;
                    }
                }
            }
        }

        .pay-style-select {
            font-size: 30/2px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            height: 80/2px;
            display: flex;
            align-items: center;
            color: #333333;
            padding-left: 50/2px;

            .span-select {
                display: inline-block;
                width: 36/2px;
                height: 36/2px;
                vertical-align: middle;
                border: 2/2px solid #a2580c;
                border-radius: 50%;
                margin-right: 20/2px;

                &.active {
                    background: url("../images/checked.png") no-repeat center
                        center;
                    background-size: 36/2px 36/2px;
                }
            }

            .weixin {
                display: inline-block;
                margin-right: 80/2px;

                span {
                }
            }

            .zhifubao {
                display: inline-block;
            }
        }
    }
}
