import { PayType, setPageName } from ':common/env';
import { typeCode } from ':common/features/bottom';
import { hiddenIOSPayButton } from ':common/features/ios_pay';
import { getDefaultPayType, PayBoundType, startSiriusPay } from ':common/features/pay';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayTypeComponent from ':component/payType/main';
import { GoodsInfo } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import Texts from ':common/features/texts';
import { trackEvent } from ':common/stat';
import { Coupon, goodsInfoWithCoupon } from ':common/features/coupon';
import { reload } from ':common/features/jump';
interface State {
    showDialog: boolean
}
interface Props {
    userData: {
        nickname: string,
        avatar: string
    },
    nowCouponInfo: any,
    nowGoodInfo: GoodsInfo,
    saveUpgradeGuideKey: string,
    reserveDialogHide()
}
export default class extends Component<State, Props> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeComponent
    }
    get handleUserName() {
        if (this.props.userData.nickname.length > 8) {
            // eslint-disable-next-line no-unused-expressions
            this.props.userData.nickname.substring(0, 8) + '...';
        } else {
            return this.props.userData.nickname;
        }
        return '';
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            showDialog: false
        };
    }
    didMount() {
        setPageName(Texts.BUYED_PAGE);
        // 注册底部支付方法
        this.children.buyButton.setPay({
            isInDialog: true,
            intercepter: async () => {
                return false;
            },
            androidPay: this.pay,
            iosPaySuccess: () => {
                reload();
            }
        });
    }
    closeDialog() {
        const newsaveItemKey = this.props.saveUpgradeGuideKey;
        localStorage.setItem(newsaveItemKey, new Date().getTime() + '');
        hiddenIOSPayButton();
        this.setState({ showDialog: false });
    }
    show() {
        this.setState({ showDialog: true });
        this.setPageInfo();
        trackEvent({
            fragmentName1: '升级弹窗',
            actionName: '',
            actionType: '出现'
        });
    }
    getShowState() {
        return this.state.showDialog;
    }
    willReceiveProps() {
        return true;
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { nowGoodInfo, nowCouponInfo } = this.props;
        const nowPayPrice = nowGoodInfo.payPrice;
        if (nowCouponInfo?.couponCode) {
            const showPrice = goodsInfoWithCoupon(nowGoodInfo, { code: nowCouponInfo.couponCode, price: nowCouponInfo.priceCent } as Coupon).payPrice;

            return +showPrice > 0 ? String(showPrice) : '0';
        }
        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const { nowGoodInfo } = this.props;
        const bottomType: typeCode = typeCode.type1;
        const fragmentName1 = '升级弹窗';
        switch (bottomType) {
            case typeCode.type1:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    price: this.showPrice,
                    validDays: nowGoodInfo.validDays,
                    fragmentName1,
                    actionName: '去支付',
                    payPathType: 0
                });
                break;
            default:
                break;
        }
    }
    pay = async (stat: PayStatProps) => {
        const { nowGoodInfo, nowCouponInfo } = this.props;
      
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: nowGoodInfo.groupKey,
            sessionIds: nowGoodInfo.sessionIds,
            activityType: nowGoodInfo.activityType,
            couponCode: nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            reload();
        }).catch(async () => {
            console.log('支付报错');
        });
    }
}
