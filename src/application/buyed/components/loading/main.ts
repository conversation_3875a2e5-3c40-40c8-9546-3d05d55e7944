import { GoodsInfo } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
interface State {
    show: boolean
}
interface Props {
    goodsList: GoodsInfo[]
    comparePricePool: any[]
    tiku: any
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            show: false
        };
    }
    show() {
        this.setState({ show: true });
    }
    hide() {
        this.setState({ show: false });
    }
    willReceiveProps() {
        return true;
    }
}
