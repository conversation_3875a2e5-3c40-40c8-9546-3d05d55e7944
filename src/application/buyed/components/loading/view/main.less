.loading-container-dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .5);
  z-index: 103;
  height: 100%;
  width: 100%;

  .loading-container-content2 {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    width: 640/2px;
    height: auto;

    .container {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }

    .loading-bg {
      display: inline-block;
      width: 100/2px;
      height: 100/2px;
      background: url(../images/1.gif) no-repeat center center;
      background-size: 100% 100%;
    }

    .loading-text {
      font-size: 22/2px;
      color: #fff;
      margin-top: 10/2px;
    }
  }

}