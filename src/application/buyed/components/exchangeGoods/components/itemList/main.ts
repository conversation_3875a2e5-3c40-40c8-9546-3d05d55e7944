import { makeToast } from ':common/features/dom';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    itemData: any
    getGrouprenKey(e: Event)
}
export default class extends Component<any, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
        };
    }
    // 普通购买
    gotoBuy(e) {
        this.props.getGrouprenKey(e);
    }
    // 换购
    exchangGoodBuy(e) {
        // 活动已结束，不可购买,因为倒计时结束会刷新页面，重新请求接口，所以用接口返回的服务器时间做活动结束的判断
        if (this.props.itemData.validEndTime - this.props.itemData.serverTime <= 0) {
            makeToast('活动已结束');
            return;
        }
        this.props.getGrouprenKey(e);
    }
    willReceiveProps() {
        return true;
    }
}
