<import name="style" content="./main" module="S" />
<import name="Count" content=":component/count/main" />
<div class="{{S.changGoodsDivItemList}} {{props.index%2!=0&&S.changGoodsDivBg}}">
    <div class="{{S.changeGoodTitle}}">- {{props.itemData.name}} -
        <span class="{{S.time}}">有效期{{props.itemData.validDays}}天</span>
    </div>

    <div class="{{S.goodsItemList}}" key="goodList">
        <sp:each for='props.itemData.barterGoodsUiConfigList' value="$sonitem">
            <div class="{{S.itemList}}" key="{{$index}}">
                <span class="{{S.bg}}" style="background-image:url({{$sonitem.icon}})"></span>
                {{$sonitem.title}}
            </div>
        </sp:each>
    </div>
    <div class="{{S.buyButtonContainer}}">
        <div class="{{S.zhijie}}" data-groupKey="{{props.itemData.groupKey}}" sp-on:click="gotoBuy" data-type="common" data-fragmentName1="更换商品弹窗">
            直接购买 {{props.itemData.dailyPriceData&&props.itemData.dailyPriceData.priceDTO.payPrice}}元
        </div>

        <div class="{{S.change}} {{props.itemData.validEndTime-props.itemData.serverTime<=0&&S.changeGray}}"
            data-groupKey="{{props.itemData.groupKey}}" data-type="exchange" sp-on:click="exchangGoodBuy" data-fragmentName1="更换商品弹窗">
            <span>+{{props.itemData.barterPriceData&&props.itemData.barterPriceData.priceDTO.payPrice}}元立即更换
            </span>
            <sp:if key="Count{{$index}}" value='{{props.itemData.validEndTime-props.itemData.serverTime>0}}'>
                <div class="{{S.youhui}}" key="Count{{$index}}">
                    <com:Count name="Count{{$index}}" key="Count{{$index}}" startTime="{{props.itemData.serverTime}}"
                        endTime="{{props.itemData.validEndTime}}" />
                    后优惠结束
                </div>
            </sp:if>
        </div>
    </div>

</div>