.chang-goods-div-item-list {
    margin-top: 30/2px;
    margin-left: 30/2px;
    margin-right: 30/2px;
    height: 340/2px;
    background: url("../../../images/k2_kp.png") no-repeat center;
    background-size: 100% 100%;
    position: relative;

    &.changGoods-div-bg {
        background: url("../../../images/k3_kp.png") no-repeat center;
        background-size: 100% 100%;
    }

    .change-good-title {
        font-size: 34/2px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #853A23;
        line-height: 48/2px;
        width: 100%;
        text-align: center;
        padding-top: 20/2px;
        position: relative;

        .time {
            position: absolute;
            right: 30/2px;
            font-size: 22/2px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #A95122;
        }
    }

    .goods-item-list {
        display: flex;
        margin-top: 24/2px;
        padding: 0px 30/2px;

        .item-list {
            flex: 1;
            font-size: 24/2px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8F3112;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;

            .bg {
                display: inline-block;
                width: 64/2px;
                height: 66/2px;
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center center;

                margin-bottom: 14/2px;
            }
        }
    }

    .buy-button-container {
        margin-top: 36/2px;
        margin-left: 24/2px;
        margin-right: 24/2px;
        display: flex;
        // position: absolute;
        // bottom: 20px;
        // left: 24px;
        // right: 24px;
    }

    .zhijie {
        width: 312/2px;
        height: 84/2px;
        line-height: 84/2px;
        text-align: center;
        background: #FFFFFF;
        border-radius: 12/2px;
        border: 2/2px solid #F4C2A2;
        font-size: 30/2px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #843405;
        margin-right: 18/2px;

    }

    .change {
        width: 312/2px;
        height: 84/2px;
        line-height: 84/2px;
        text-align: center;
        background: linear-gradient(113deg, #353B4E 0%, #1D222B 100%);
        border-radius: 12/2px;
        font-size: 30/2px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        position: relative;

        &.change-gray {
            background: #b1aaaa;
        }

        .youhui {
            position: absolute;
            right: 0px;
            top: -20/2px;
            padding-left: 10/2px;
            padding-right: 10/2px;
            height: 40/2px;
            line-height: 40/2px;

            background: linear-gradient(90deg, #FF7810 0%, #FE3C29 55%, #FE6164 100%);
            border-radius: 40/2px 40/2px 40/2px 4/2px;
            font-size: 20/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #FFFFFF;
        }
    }
}