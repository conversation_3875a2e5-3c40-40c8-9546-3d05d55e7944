.change-goods-list-dialog {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, .5);
    z-index: 99;
    height: 100vh;
    overflow: hidden;
    width: 100%;

    .changeContent {
        width: 100%;
        height: 71%;
        padding-bottom: 30/2px;
        padding-bottom: calc(~ "30/2px + constant(safe-area-inset-bottom)");
        overflow: hidden;
        position: absolute;
        bottom: 0px;
        background: linear-gradient(180deg, #FFEDDC 0%, #FFFAF0 25%, #FFFFFF 73%, #FFFFFF 100%);
        box-shadow: inset 0px 2/2px 0px 0px #FFFFFF;
        border-radius: 12/2px 12/2px 0px 0px;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .content-flex {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .dialog-title {
            font-size: 36/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            display: flex;
            align-items: center;
            padding: 40/2px 30/2px 0px 30/2px;
            justify-content: space-between;
            font-size: 30/2px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            position: relative;

            .title {
                flex: 1;

                .span-title {
                    color: #853A23;
                    margin: 0px 10/2px;
                }
            }

            .close-container {
                position: absolute;
                width: 80/2px;
                top: 10/2px;
                right: 20/2px;
                text-align: right;

                .close {
                    display: inline-block;
                    width: 40/2px;
                    height: 40/2px;
                    background: url("../../../images/close.png") no-repeat center;
                    background-size: 100% 100%;

                }
            }

        }

        .scrollDiv {
            flex: 1;
            overflow-y: auto;
        }

        .footer-container {
            width: 100%;
            padding-top: 15/2px;
            line-height: 40/2px;
            font-size: 26/2px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            text-align: center;
            display: flex;
            justify-content: center;
            padding-bottom: calc(~ "10/2px + constant(safe-area-inset-bottom)");

            .protocol-box {
                .check-box {
                    display: inline-block;
                    width: 20/2px !important;
                    height: 20/2px !important;
                }
            }

            .xieyi {
                color: #984321;
            }
        }



    }



}