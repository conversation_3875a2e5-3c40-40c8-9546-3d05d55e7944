<import name="style" content="./main" module="S" />
<import name="ItemList" content=":application/buyed/components/exchangeGoods/components/itemList/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<div class="{{S.changeGoodsListWrap}}">
    <sp:if value="state.show">
        <div class="{{S.changeGoodsListDialog}}">
            <div class="{{S.changeContent}}">
                <div class="{{S.contentFlex}}">
                    <div class="{{S.dialogTitle}}">
                        <div class="{{S.title}}">
                            <p>如果您<span class="{{S.spanTitle}}">{{props.showKemuTitleArray.join(',')}}</span>已考完</p>
                            <p>可考虑以更优惠的价格更换为以下任一VIP商品；</p>
                            <p>更换成功后您的<span
                                    class="{{S.spanTitle}}">{{props.showKemuTitleArray.join(',')}}VIP</span>权益将被回收，无法使用。
                            </p>
                        </div>
                        <div class="{{S.closeContainer}}" sp-on:click="tcClose">
                            <span class="{{S.close}}"></span>
                        </div>
                    </div>
                    <div class="{{S.scrollDiv}}">
                        <sp:each for='{{props.barterGoodsList}}'>
                            <com:ItemList getGrouprenKey="{{self.getGrouprenKey}}" itemData="{{$value}}}"
                                index="{{$index}}"></com:ItemList>
                        </sp:each>
                    </div>
                </div>
                <div class="{{S.footerContainer}}">
                    <com:readProtocol protocolUrl="{{state.config.protocolUrl}}" hasRead="{{self.hasReadMethod()}}"
                        styleType="changGoods" protocolText1="{{state.config.protocolText1}}"
                        protocolText2="{{state.config.protocolText2}}" protocolText3="{{state.config.protocolText3}}" />
                </div>


            </div>

        </div>
    </sp:if>

</div>