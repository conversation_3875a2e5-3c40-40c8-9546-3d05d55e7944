import { Component } from '@simplex/simple-core';
import View from './view/main.html';
interface State {
    show: boolean
}
interface Props {
    getGrouprenKey(e: Event)
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            show: false
        };
    }
    show() {
        this.setState({ show: true });
    }
    tcClose(e) {
        e.stopPropagation();
        this.hide();
    }
    hide() {
        this.setState({
            show: false
        });
    }
    getGrouprenKey = (e) => {
        this.props.getGrouprenKey(e);
    }
    willReceiveProps() {
        return true;
    }

}
