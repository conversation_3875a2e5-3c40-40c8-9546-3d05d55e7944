<import name="style" content="./main" module="S" />
<import name="ExchangGoodsDialog"
    content=":application/buyed/components/exchangeGoods/components/exchangeGoodsDialog/main" />

<import name="loading" content=":application/buyed/components/loading/main" />
<import name="payDialog" content=":component/payDialog/main" />
<div>
    <sp:if value='{{state.barterableOrders.length>0}}'>

        <div class="{{S.changeGoodsContainer}}">
            <div class="{{S.packageTop}}" sp-on:click="moreView">
                <div class="{{S.title}}"><span class="{{S.leftIcon}}"></span><span
                        class="{{S.leftIconTitle}}">{{state.showKemuTitleArray.join(',')}}已考完，更换为其它VIP试试吧</span>
                </div>
                <div class="{{S.desc}}">去更换</div>
            </div>
        </div>
    </sp:if>
    <com:ExchangGoodsDialog name="exchangeGoodsDialog" getGrouprenKey="{{self.getGrouprenKey}}"
        showKemuTitleArray="{{state.showKemuTitleArray}}" barterGoodsList="{{state.barterGoodsList}}">
    </com:ExchangGoodsDialog>
    <com:payDialog />
    <com:loading />
</div>