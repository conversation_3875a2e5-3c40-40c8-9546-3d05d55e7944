.change-goods-container {
  margin-bottom: 50/2px;

  .package-top {
    display: flex;
    justify-content: space-between;
    height: 74/2px;
    align-items: center;
    background: linear-gradient(270deg, #FFF1D9 0%, #FFE6BF 100%);
    border-radius: 12/2px;


    .title {
      font-size: 26/2px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #692204;

      .left-icon {
        display: inline-block;
        width: 30/2px;
        height: 30/2px;
        background: url("../images/dagou.png") no-repeat center;
        background-size: 100% 100%;
        margin-left: 20/2px;
        margin-right: 5/2px;
        vertical-align: middle;
      }
      .left-icon-title{
        vertical-align: bottom;
      }
    }

    .desc {
      width: 136/2px;
      height: 48/2px;
      line-height: 48/2px;
      text-align: center;
      background: linear-gradient(99deg, #FFD493 0%, #E5AD4C 100%);
      border-radius: 24/2px;
      font-size: 26/2px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #701E06;
      margin-right: 20/2px;
    }
  }

  .vip-goods-first {
    background: linear-gradient(131deg, #F8D8A7 0%, #E5AD4C 100%);
    border-radius: 10/2px;
    padding: 30/2px 0px 0px 0px;
    position: relative;
    margin-top: 30/2px;

    .tag-tips {
      position: absolute;
      right: 0px;
      top: -18/2px;
      height: 36/2px;
      background: linear-gradient(90deg, #FF7810 0%, #FE3C29 55%, #FE6164 100%);
      border-radius: 66/2px 66/2px 66/2px 4/2px;
      font-size: 20/2px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 36/2px;
      text-align: center;
      padding-left: 14/2px;
      padding-right: 14/2px;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
    }

    .top-content {
      display: flex;
      justify-content: space-between;
      padding: 0px 30/2px 20/2px 30/2px;

      .left {
        .title {
          font-size: 36/2px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #501504;
          line-height: 50/2px;
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .desc {
          font-size: 28/2px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #692204;
          white-space: normal !important;
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          -webkit-box-orient: vertical;
          margin-top: 22/2px;

          .sym {
            font-size: 26/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #D00F1B;
          }

          .price {
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #D00F1B;
            font-size: 44/2px;
          }

          .tag-button {
            background: #2E2E2E;
            padding: 0px 12/2px;
            border-radius: 22/2px;
            font-size: 20/2px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            position: relative;
            top: -2/2px;
            text-align: center;
            margin-left: 10/2px;

            i {
              font-style: normal;
              color: #FFCB48;
            }
          }
        }
      }

      .right {
        .button {
          display: inline-block;
          width: 180/2px;
          height: 72/2px;
          line-height: 72/2px;
          background: url("../images/button-tehuigoumai-tesu.png") no-repeat center;
          background-size: 180/2px 72/2px;
          font-size: 28/2px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #831C1C;
          text-align: center;
        }
      }

    }

    .bottom-content {
      height: 48/2px;
      line-height: 48/2px;
      background: rgba(255, 255, 255, 0.32);
      text-align: center;
      font-size: 24/2px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #692204;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }

  .vip-goods-other-container {
    width: 100%;
    overflow: hidden;
  }

  .vip-goods-other {
    width: 100%;
    overflow-x: scroll;
    overflow-y: hidden;
    position: relative;
    white-space: nowrap;

    .item-content {
      width: 320/2px;
      vertical-align: top;
      margin-right: 22/2px;
      position: relative;
      display: inline-block;
      margin-top: 30/2px;
      background: #FFFBF6;
      border-radius: 10/2px;
      border: 3/2px solid #F4C2A2;
      padding: 20/2px 15/2px 30/2px 15/2px;

      .tag-tips-other {

        position: absolute;
        top: -18/2px;
        right: 0px;
        padding-left: 14/2px;
        padding-right: 14/2px;
        height: 36/2px;
        font-size: 20/2px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 36/2px;
        background: linear-gradient(90deg, #FF7810 0%, #FE3C29 55%, #FE6164 100%);
        border-radius: 22/2px 22/2px 22/2px 4/2px;
        text-overflow: -o-ellipsis-lastline;
        max-width: 280/2px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        display: inline-block;
        line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      .top-content {

        width: 100%;
        position: relative;

        .left {

          font-size: 32/2px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #171717;
          padding-right: 100/2px;
          white-space: normal !important;
          line-height: 44/2px;
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          height: 88/2px;
          -webkit-box-orient: vertical;
        }

        .right {
          width: 100/2px;
          text-align: right;
          position: absolute;
          right: 10/2px;
          top: -5/2px;

          .title {
            font-size: 30/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #D00F1B;

            .price {
              font-size: 48/2px;

            }

          }

          .desc {
            font-size: 20/2px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #AA4120;
            margin-top: -2/2px;
          }
        }
      }

      .text-align {
        justify-content: center !important;
      }

      .button-con {

        text-align: center;
        height: 36/2px;
        display: flex;
        align-items: center;
        padding-top: 5/2px;
        padding-bottom: 5/2px;
        background: #FEF0E7;
        border-radius: 18/2px;
        font-size: 22/2px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #AA4120;
        margin-top: 30/2px;
        padding-left: 5/2px;
        padding-right: 5/2px;
        box-sizing: border-box;
        word-break: break-all;
        white-space: nowrap;
        overflow: hidden;

        .button-con-text {
          display: inline-block;
          padding-left: 100%;
          /* 从右至左开始滚动 */
          animation: marqueeTransform 16s linear infinite;

        }

        @keyframes marqueeTransform {
          0% {
            transform: translate(0, 0);
          }

          100% {
            transform: translate(-100%, 0);
          }
        }
      }

      .goto-style {
        width: 180/2px;
        text-align: center;
        line-height: 72/2px;
        height: 72/2px;
        background: url("../images/button-tehuigoumai-qita.png") no-repeat center;
        background-size: 180/2px 72/2px;
        font-size: 32/2px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #692204;
        margin: 20/2px auto 0px auto;

      }
    }
  }
}