import { PayType, Platform, setPageName } from ':common/env';
import { trackEvent, trackGoPay } from ':common/stat';
import { getBarterableOrders, getBarterGoodsList } from ':store/buyed';
import { GoodsInfo } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import Texts from ':common/features/texts';
import ExchangeGoodsDialog from ':application/buyed/components/exchangeGoods/components/exchangeGoodsDialog/main';
import Loading from ':application/buyed/components/Loading/main';
import { formatPrice } from ':common/utils';
import { getDefaultPayType, PayBoundType, startExchangeSiriusPay, startSiriusPay } from ':common/features/pay';
import { PayStatProps } from ':component/buyButton/main';
import { iosPay } from ':common/features/ios_pay';
import PayDialog from ':component/payDialog/main';
import { checkReaded } from ':common/features/agreement';
import { reload } from ':common/features/jump';
/** 加购订单四个不同的参数*/
interface State {
    nowGoodInfo: GoodsInfo & {
        groupId?: number | string,
        preBarterOrderNumbers?: any[],
        barterStrategyCode?: string,
        dailyActivityType?: string,
        barterAppId: number | string
    }
    // 换购有没有数据
    barterableOrders: any[],
    showKemuTitleArray: any[],
    // 换购商品列表
    barterGoodsList: any[]
}
interface Props {

}
export default class extends Component<State, Props> {
    declare children: {
        exchangeGoodsDialog: ExchangeGoodsDialog;
        payDialog: PayDialog;
        loading: Loading
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            barterableOrders: [],
            showKemuTitleArray: [],
            barterGoodsList: [],
            nowGoodInfo: null
        };
    }
    didMount() {
        setPageName(Texts.BUYED_PAGE);
        this.barterableOrdersRequest();
    }
    async barterableOrdersRequest() {
        const barterableOrders = await getBarterableOrders();
        if (barterableOrders.length <= 0) {
            return;
        }
        const showKemuTitleArray = [];
        barterableOrders.forEach((res) => {
            showKemuTitleArray.push(res.kemu);
        });
        trackEvent({
            fragmentName1: '更换商品入口',
            actionName: '',
            actionType: '出现'
        });
        this.barterGoodsListRequest();
        this.setState({ barterableOrders: barterableOrders, showKemuTitleArray });
    }
    async barterGoodsListRequest() {
        const barterGoodsList = await getBarterGoodsList();
        barterGoodsList && barterGoodsList.forEach((res) => {
            res.dailyPriceData.priceDTO.payPrice = formatPrice(res.dailyPriceData.priceDTO.price);
            res.barterPriceData.priceDTO.payPrice = formatPrice(res.barterPriceData.priceDTO.price);
        });
        this.setState({ barterGoodsList });
    }
    moreView() {
        trackEvent({
            fragmentName1: '更换商品入口',
            actionName: '跳转',
            actionType: '点击'
        });
        trackEvent({
            fragmentName1: '更换商品弹窗',
            actionName: '',
            actionType: '出现'
        });
        this.children.exchangeGoodsDialog?.show();
    }
    getGrouprenKey = (e) => {
        // 先勾选协议
        checkReaded(() => {
            const { barterableOrders } = this.state;
            const groupkey = e.refTarget.getAttribute('data-groupKey');
            const fragmentName1 = e.refTarget.getAttribute('data-fragmentName1');
            const type = e.refTarget.getAttribute('data-type');
            const goodsInfo = this.byGroupKeyGoodsInfo(groupkey);
            goodsInfo.sessionIds = goodsInfo.sessionIdList;
            // type==change是换购商品，换购商品不走以前的支付逻辑
            if (type === 'exchange') {
                const preBarterOrderNumbersArray = [];
                barterableOrders && barterableOrders.forEach((res) => {
                    res.orderNumberList && res.orderNumberList.forEach((item) => {
                        preBarterOrderNumbersArray.push(item);
                    });
                });
                goodsInfo.preBarterOrderNumbers = preBarterOrderNumbersArray;
                goodsInfo.activityType = goodsInfo.barterPriceData.salesChannel;
                goodsInfo.barterAppId = goodsInfo.barterPriceData.appleId || '';
                goodsInfo.dailyActivityType = goodsInfo.dailyPriceData.salesChannel || '';
                goodsInfo.payPrice = goodsInfo.barterPriceData.priceDTO.payPrice;
                this.setState({ nowGoodInfo: goodsInfo }, () => {
                    this.onExchangePayBtnCall({
                        stat: {
                            fragmentName1
                        }
                    });
                });
            } else {
                // 普通购买处理
                goodsInfo.activityType = goodsInfo.dailyPriceData.salesChannel;
                goodsInfo.payPrice = goodsInfo.dailyPriceData.priceDTO.payPrice;
                this.setState({ nowGoodInfo: goodsInfo }, () => {
                    this.onPayBtnCall({
                        stat: {
                            fragmentName1
                        }
                    });
                });
            }
        });

    }
    // 换购购买，换购购买先创建订单在调ios支付
    onExchangePayBtnCall(config) {
        const { nowGoodInfo } = this.state;
        trackGoPay({
            groupKey: nowGoodInfo.groupKey,
            ...config?.stat
        });
        this.onExchangePay({ stat: config?.stat });
    }
    // 换购商品
    onExchangePay = async (config: { stat: PayStatProps }) => {
        const { nowGoodInfo } = this.state;
        startExchangeSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: nowGoodInfo.groupKey,
            sessionIds: nowGoodInfo.sessionIds,
            activityType: nowGoodInfo.activityType,
            // 换购的四个新参数
            groupId: nowGoodInfo.groupId,
            preBarterOrderNumbers: nowGoodInfo.preBarterOrderNumbers,
            barterStrategyCode: nowGoodInfo.barterStrategyCode,
            dailyActivityType: nowGoodInfo.dailyActivityType,
            // ios支付时需要这个参数
            barterAppId: nowGoodInfo.barterAppId,
            couponCode: '',
            ...config.stat
        }, false).then(() => {
            this.children.loading.show();
            setTimeout(() => {
                reload();
            }, 2000);
        }).catch(async () => {
            if (Platform.isAndroid) {
                this.children.payDialog.show({
                    groupKey: nowGoodInfo.groupKey,
                    payPrice: nowGoodInfo.payPrice,
                    onPay: () => {
                        this.exchangePay(config.stat);
                    },
                    ...config.stat
                });
            }

        });
    }
    exchangePay = async (stat: PayStatProps) => {
        this.onExchangePay({
            stat: {
                ...stat
            }
        });
    }
    byGroupKeyGoodsInfo(groupKey) {
        const { barterGoodsList } = this.state;
        let newGoodsInfo;
        barterGoodsList && barterGoodsList.forEach((res) => {
            if (res.groupKey === groupKey) {
                newGoodsInfo = res;
            }
        });
        return newGoodsInfo;
    }
    onPayBtnCall(config) {
        const { nowGoodInfo } = this.state;
        if (Platform.isIOS) {
            iosPay(nowGoodInfo.groupKey, {
                ...config?.stat
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            // 点击支付按钮打点
            trackGoPay({
                groupKey: nowGoodInfo.groupKey,
                ...config?.stat
            });
            this.onPay({ stat: config?.stat });
        }
    }
    pay = async (stat: PayStatProps) => {
        this.onPay({
            stat: {
                ...stat
            }
        });
    }
    onPay = async (config: { stat: PayStatProps}) => {
        const { nowGoodInfo } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: nowGoodInfo.groupKey,
            sessionIds: nowGoodInfo.sessionIds,
            activityType: nowGoodInfo.activityType,
            couponCode: '',
            ...config.stat
        }, false).then(() => {
            reload();
        }).catch(async () => {
            this.children.payDialog.show({
                groupKey: nowGoodInfo.groupKey,
                payPrice: nowGoodInfo.payPrice,
                onPay: () => {
                    this.pay(config.stat);
                },
                ...config.stat
            });
        });
    }
    willReceiveProps() {
        return true;
    }
}
