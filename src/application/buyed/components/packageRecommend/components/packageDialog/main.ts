import { trackEvent } from ':common/stat';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
interface State {
    show: boolean
}
interface Props {
    getChildrenKey(e: Event)
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            show: false
        };
    }
    tcClose(e) {
        e.stopPropagation();
        this.hide();
    }
    hide() {
        this.setState({
            show: false
        });
    }
    show() {
        this.setState({
            show: true
        });
    }
    gotoBuyChildren(e) {
        this.props.getChildrenKey(e);
    }
    willReceiveProps() {
        return true;
    }
}
