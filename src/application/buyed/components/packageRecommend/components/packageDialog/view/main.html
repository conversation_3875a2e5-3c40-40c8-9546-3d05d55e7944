<import name="style" content="./main" module="S" />
<import name="payType" content=":component/payType/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<div class="{{S.packageRecommendWrap}}" sp-on:touchmove="touchmoveMethod">
    <sp:if value="state.show">
        <div class="{{S.packageRecommendDialog}}">
            <div class="{{S.packageContent}}">
                <div class="{{S.dialogTitle}}">
                    <span>会员专属优惠套餐推荐</span>
                    <div class="{{S.closeContainer}}" sp-on:click="tcClose">
                        <span class="{{S.close}}"></span>
                    </div>
                </div>
                <div class="{{S.scrollDiv}}">
                    <sp:each for='{{props.recommendGoodsList}}'>
                        <div data-groupkey="{{$value.groupKey}}" sp-on:click="gotoBuyChildren"
                            class="{{S.dialogVipGoodsFirst}} {{S['dialog-vip-goods-first-' + props.theme]}} {{$index!==0&&S.dialogVipGoodsFirstOther}}">
                            <sp:if value='{{$value.tips}}'>
                                <div class="{{S.tagTips}}">
                                    {{$value.tips}}
                                </div>
                            </sp:if>
                            <div class="{{S.topContent}}">
                                <div class="{{S.left}}">
                                    <p class="{{S.title}} {{$index==0&&S.titleOther}}">{{$value.newName}}</p>
                                    <p class="{{S.desc}}"><span class="{{S.sym}}">￥</span><span
                                            class="{{S.price}}">{{$value.newPrice}}</span>/{{$value.validDays}}天
                                        <sp:if value='{{$index==0&&$value.newpriceDiff}}'>
                                            <span
                                                class="{{S.tagButton}} {{$index!==0&&S.tagButtonOther}}">更划算，立省<i>{{$value.newpriceDiff}}</i>元</span>

                                        </sp:if>
                                        <sp:if value='{{$index!=0&&$value.newpriceDiff}}'>
                                            <span
                                                class="{{S.tagButton}} {{$index!==0&&S.tagButtonOther}}">立省<i>{{$value.newpriceDiff}}</i>元</span>
                                        </sp:if>

                                    </p>
                                </div>
                                <div class="{{S.right}}">
                                    <span class="{{S.button}} {{$index==0?S.buttonTesu:S.buttonQita}}">
                                        {{$value.canUpgrade?'去升级':'开通'}}
                                    </span>
                                </div>

                            </div>
                            <div class="{{S.bottomContent}}">
                                {{$value.newDescript}}
                            </div>
                        </div>
                    </sp:each>
                </div>
                <sp:if value='{{Platform.isIOS}}'>
                    <div class="{{S.readProtocol}}">
                        <com:readProtocol />
                    </div>
                </sp:if>
            </div>

        </div>
    </sp:if>
</div>