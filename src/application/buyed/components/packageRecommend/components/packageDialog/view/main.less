.package-recommend-dialog {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 99;
    height: 100vh;
    overflow: hidden;
    width: 100%;
    .packageContent {
        width: 100%;
        max-height: 80%;
        min-height: 30%;
        padding-bottom: 30/2px;
        overflow: hidden;
        position: absolute;
        bottom: 0px;
        background: #ffffff;
        border-radius: 20/2px 20/2px 0px 0px;
        display: flex;
        flex-direction: column;
        .dialog-title {
            font-size: 36/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            display: flex;
            align-items: center;
            padding: 15/2px 30/2px 0px 30/2px;
            justify-content: space-between;
            .close-container {
                padding: 20/2px;
                .close {
                    display: inline-block;
                    width: 36/2px;
                    height: 36/2px;
                    background: url("../../../images/close.png") no-repeat
                        center;
                    background-size: 36/2px 36/2px;
                }
            }
        }
        .scrollDiv {
            flex: 1;
            overflow-y: auto;
            padding-top: 30/2px;
        }
        .read-protocol {
            padding-left: 15px;
            margin-top: 10px;
            margin-bottom: 10px;
        }
        .dialog-vip-goods-first {
            background: linear-gradient(131deg, #f8d8a7 0%, #e5ad4c 100%);

            border-radius: 10/2px;
            padding: 30/2px 0px 0px 0px;
            position: relative;
            margin-left: 30/2px;
            margin-right: 30/2px;
            margin-bottom: 30/2px;

            .tag-tips {
                position: absolute;
                right: 0px;
                top: -18/2px;
                height: 36/2px;
                background: linear-gradient(
                    90deg,
                    #ff7810 0%,
                    #fe3c29 55%,
                    #fe6164 100%
                );
                border-radius: 66/2px 66/2px 66/2px 4/2px;
                font-size: 20/2px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #ffffff;
                line-height: 36/2px;
                text-align: center;
                padding-left: 14/2px;
                padding-right: 14/2px;
                text-overflow: -o-ellipsis-lastline;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                line-clamp: 1;
                -webkit-box-orient: vertical;
            }

            .top-content {
                display: flex;
                justify-content: space-between;
                padding: 0px 30/2px 20/2px 30/2px;
                .left {
                    .title {
                        font-size: 36/2px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #333333;
                        line-height: 50/2px;
                        margin-bottom: 4/2px;
                        &.title-other {
                            color: #501504;
                        }
                    }
                    .desc {
                        font-size: 28/2px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #692204;
                        .sym {
                            font-size: 26/2px;
                            font-family: PingFangSC-Medium, PingFang SC;
                            font-weight: 500;
                            color: #d00f1b;
                        }
                        .price {
                            font-family: PingFangSC-Medium, PingFang SC;
                            font-weight: 500;
                            color: #d00f1b;
                            font-size: 44/2px;
                        }
                        .tag-button {
                            padding: 0px 12/2px;
                            background: #2e2e2e;
                            border-radius: 22/2px;
                            font-size: 20/2px;
                            font-family: PingFangSC-Regular, PingFang SC;
                            font-weight: 400;
                            color: #ffffff;
                            text-align: center;
                            margin-left: 10/2px;
                            position: relative;
                            top: -2px;
                            &.tag-button-other {
                                background: rgba(219, 153, 39, 0.28);
                                color: #692204;

                                i {
                                    font-style: normal;
                                    color: #d00f1b;
                                }
                            }
                            i {
                                font-style: normal;
                                color: #ffcb48;
                            }
                        }
                    }
                }
                .right {
                    .button {
                        display: inline-block;
                        width: 180/2px;
                        height: 72/2px;
                        line-height: 72/2px;

                        font-size: 28/2px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;

                        text-align: center;
                        &.button-tesu {
                            background: url("../../../images/button-tehuigoumai-tesu.png")
                                no-repeat center;
                            color: #831c1c;
                            background-size: 180/2px 72/2px;
                        }
                        &.button-qita {
                            background: url("../../../images/button-tehuigoumai-qita.png")
                                no-repeat center;
                            background-size: 180/2px 72/2px;
                            color: #831c1c;
                        }
                    }
                }
            }
            .bottom-content {
                height: 48/2px;
                line-height: 48/2px;
                background: rgba(255, 255, 255, 0.32);
                text-align: center;
                font-size: 24/2px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #692204;
                border-bottom-left-radius: 10/2px;
                border-bottom-right-radius: 10/2px;
                text-overflow: -o-ellipsis-lastline;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                line-clamp: 1;
                -webkit-box-orient: vertical;
            }
            &.dialogVipGoodsFirstOther {
                background: #fffbf6;
                border: 2/2px solid #f4c2a2;
                .bottom-content {
                    background: #fef0e7;
                }
            }
        }
        .dialog-vip-goods-first-ty-vip {
            background: linear-gradient(135deg,#ffe4c9 1%, #ffe2d2 30%, #ffc7b2 87%, #ffa4b0);
        }
    }
    .paytypeBox {
        padding: 0 15px;
    }
}
