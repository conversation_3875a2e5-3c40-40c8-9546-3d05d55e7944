import { formatPrice } from ':common/utils';
import { getRecommendGoodsRequest } from ':store/buyed';
import { getGroupSessionInfo, GoodsInfo } from ':store/goods';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { Component } from '@simplex/simple-core';
import PackageDialog from ':application/buyed/components/packageRecommend/components/packageDialog/main';
import View from './view/main.html';
import { PayType, Platform, URLCommon, URLParams, Version } from ':common/env';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import { trackEvent, trackGoPay } from ':common/stat';
import { getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import PayDialog from ':component/payDialog/main';
import { MCProtocol } from '@simplex/simple-base';
import Loading from ':application/buyed/components/Loading/main';
import { reload } from ':common/features/jump';
const timerObject = {};
interface State {
    recommendGoodsList: any[],
    isRequest: boolean,
    goodsInfoPool: GoodsInfo
}
interface Props {
    propPageType: string
}
export default class extends Component<State, Props> {
    declare children: {
        payDialog: PayDialog;
        packageDialog: PackageDialog;
        buyButton: BuyButton;
        loading: Loading,
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            recommendGoodsList: [],
            isRequest: true,
            goodsInfoPool: null
        };
    }
    didMount() {
        // 注册底部支付方法
        if (this.props.propPageType !== 'tyVipPage') {
            this.children.buyButton.setPay({
                androidPay: this.pay,
                intercepter: async () => {
                    return false;
                },
                iosPaySuccess: () => {
                    reload();
                }
            });
        }
    }
    // 获取考试记录区间
    getExamRecordRange(kemu) {
        return new Promise((resolve) => {
            MCProtocol.data.exam.recordRange({
                kemu: 'kemu' + kemu,
                kemuStyle: kemu,
                carStyle: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                callback: (res) => {
                    console.log('获取到协议参数', res);
                    console.log('获取到协议参数', res.data);
                    console.log('获取到协议参数', JSON.stringify(res));
                    resolve(JSON.stringify(res.data));
                }
            });
        });
    }
    async getRecommendGoodsData(kemu) {
        if (!kemu) {
            return;
        }
        let examRecords: any = '';
        console.log('bizVersion版本', Version.bizVersion);
        if (Version.bizVersion >= 16) {
            examRecords = await this.getExamRecordRange(kemu);
        }
        console.log('异步方法获取到协议参数', examRecords);
        const res = await getRecommendGoodsRequest({ examRecords, kemu: kemu });
        const newItemList = [];
        res.itemList && res.itemList.forEach((ele) => {
            const allName = [];
            let countTotal = 0;
            ele.comparedGoodsItems && ele.comparedGoodsItems.forEach((res) => {
                allName.push(res.name);
                countTotal += res.price;
            });
            // 对数据进行进行处理
            ele.newDescript = allName.length > 0 ? allName.join('+') + '=' + formatPrice(countTotal) + '元' : ele.description;
            ele.newPrice = +formatPrice(ele.price);
            ele.newpriceDiff = +formatPrice(ele.priceDiff);
            ele.newName = ele.canUpgrade ? '升级' + ele.name : ele.name;
            // 展示未购买或者已购买还能再次复购的数据
            if (ele.canUpgrade || !ele.bought || (ele.bought && ele.canRebuy)) {
                newItemList.push(ele);
            }
        });
        this.setState({
            recommendGoodsList: newItemList
        }, () => {
            // 设置从第二个商品以后，超出一行滚动的设置
            // if (newItemList && newItemList.length >= 2) {
            //     for (let index = 1; index < newItemList.length; index++) {
            //         const wrapper = document.getElementById('wrapper' + (index));
            //         const inner = document.getElementById('wrapper-inner' + (index));
            //         const p = document.getElementById('wrapper-p' + (index));
            //         let pw = p && p.offsetWidth;

            //         const wrapperw = wrapper && wrapper.offsetWidth;
            //         if (wrapperw < pw) {
            //             p.style.paddingRight = '20px';
            //             pw = p && p.offsetWidth;
            //             inner.innerHTML += inner.innerHTML;
            //             wrapper.scrollLeft = 0;
            //             wrapper.style.paddingLeft = '100%';
            //             this.fun1(pw, wrapper, index);

            //         }
            //     }
            // }
        });
    }
    animate(cb, time, index) {
        let i = 1;
        cancelAnimationFrame(timerObject['timer' + index]);
        timerObject['timer' + index] = requestAnimationFrame(function fn() {
            // eslint-disable-next-line
            if (i % parseInt((60 / (1000 / time) + '')) == 0) {
                cb();
            }
            i++;
            timerObject['timer' + index] = requestAnimationFrame(fn);
        });
    }
    fun1(pw, wrapper, index) {
        if (!wrapper) {
            return;
        }
        this.animate(() => {

            if (pw >= wrapper.scrollLeft) {
                wrapper.scrollLeft += 1;

            } else {
                wrapper.scrollLeft = 0;
                wrapper.style.paddingLeft = '100%';
            }
        }, 35, index);
    }
    moreView() {
        this.children.packageDialog.show();
    }
    getChildrenKey = (e) => {
        const groupkey = e.refTarget.getAttribute('data-groupkey');
        this.groupKeyByGoods(groupkey, {
            fragmentName1: '更多套餐',
            payPathType: 0
        });

    }
    gotoBuy(e) {
        const groupkey = e.refTarget.getAttribute('data-groupkey');
        this.groupKeyByGoods(groupkey, {
            fragmentName1: '会员专属优惠套餐推荐'
        });
    }
    groupKeyByGoods(groupkey, stat) {
        if (Platform.isAndroid) {
            this.children.loading.show();
        }
        getGroupSessionInfo({ groupKeys: [groupkey] }).then(goodsListInfo => {
            this.children.loading.hide();
            this.setState({ goodsInfoPool: goodsListInfo[0] }, () => {
                this.onPayBtnCall({
                    stat
                });
            });
        });
        setTimeout(() => {
            this.children.loading.hide();
        }, 3000);

    }
    onPayBtnCall(config) {
        const { goodsInfoPool } = this.state;

        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool.groupKey,
            ...config?.stat
        });
        
        if (Platform.isIOS) {
            iosPay(goodsInfoPool.groupKey, {
                ...config?.stat
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            console.log(goodsInfoPool);

            this.children.payDialog.show({
                groupKey: goodsInfoPool.groupKey,
                payPrice: goodsInfoPool.payPrice,
                onPay: () => {
                    this.onPay({ stat: config?.stat });
                },
                ...config.stat
            });

        }
    }
    pay = async (stat: PayStatProps) => {
        this.onPay({
            stat: {
                ...stat
            }
        });
    }
    onPay = async (config: { stat: PayStatProps }) => {
        const { goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool.groupKey,
            sessionIds: goodsInfoPool.sessionIds,
            activityType: goodsInfoPool.activityType,
            couponCode: '',
            ...config.stat
        }, false).then(() => {
            if (this.props.propPageType === 'tyVipPage') {
                newBuySuccess({ groupKey: this.state.goodsInfoPool.groupKey });
            } else {
                reload();
            }
        }).catch(async () => {
            this.children.payDialog.show({
                groupKey: goodsInfoPool.groupKey,
                payPrice: goodsInfoPool.payPrice,
                onPay: () => {
                    this.pay(config.stat);
                },
                ...config.stat
            });
        
        });
    }
    willReceiveProps() {
        return true;
    }
}
