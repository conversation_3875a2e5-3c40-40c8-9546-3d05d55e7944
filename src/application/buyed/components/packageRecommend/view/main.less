.package-recommend-container {
    margin-bottom: 0px;
    margin-top: 17px;
    &.margin-bottom {
        margin-bottom: 15px;
    }

    .package-top {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            font-size: 36/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 600;
            color: #333333;
            line-height: 50/2px;
        }

        .desc {
            font-size: 28/2px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            display: flex;
            align-items: center;

            .right-icon {
                display: inline-block;
                width: 16/2px;
                height: 28/2px;
                background: url("../images/right-icon.png") no-repeat center;
                background-size: 16/2px 28/2px;
                margin-left: 15/2px;
            }
        }
    }
   
    .vip-goods-first {
        background: linear-gradient(131deg, #f8d8a7 0%, #e5ad4c 100%);
        border-radius: 10/2px;
        padding: 30/2px 0px 0px 0px;
        position: relative;
        margin-top: 30/2px;

        .tag-tips {
            position: absolute;
            right: 0px;
            top: -18/2px;
            height: 36/2px;
            background: linear-gradient(
                90deg,
                #ff7810 0%,
                #fe3c29 55%,
                #fe6164 100%
            );
            border-radius: 66/2px 66/2px 66/2px 4/2px;
            font-size: 20/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 36/2px;
            text-align: center;
            padding-left: 14/2px;
            padding-right: 14/2px;
            text-overflow: -o-ellipsis-lastline;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
        }

        .top-content {
            display: flex;
            justify-content: space-between;
            padding: 0px 30/2px 20/2px 30/2px;

            .left {
                .title {
                    font-size: 36/2px;
                    font-family: PingFangSC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #501504;
                    line-height: 50/2px;
                    text-overflow: -o-ellipsis-lastline;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

                .desc {
                    font-size: 28/2px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #692204;
                    white-space: normal !important;
                    text-overflow: -o-ellipsis-lastline;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    line-clamp: 1;
                    -webkit-box-orient: vertical;
                    margin-top: 22/2px;

                    .sym {
                        font-size: 26/2px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #d00f1b;
                    }

                    .price {
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #d00f1b;
                        font-size: 44/2px;
                    }

                    .tag-button {
                        background: #2e2e2e;
                        padding: 0px 12/2px;
                        border-radius: 22/2px;
                        font-size: 20/2px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #ffffff;
                        position: relative;
                        top: -2/2px;
                        text-align: center;
                        margin-left: 10/2px;

                        i {
                            font-style: normal;
                            color: #ffcb48;
                        }
                    }
                }
            }

            .right {
                display: flex;
                align-items: center;
                .button {
                    display: inline-block;
                    width: 180/2px;
                    height: 72/2px;
                    line-height: 72/2px;
                    background: url("../images/button-tehuigoumai-tesu.png")
                        no-repeat center;
                    background-size: 180/2px 72/2px;
                    font-size: 28/2px;
                    font-family: PingFangSC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #831c1c;
                    text-align: center;
                }
            }
        }

        .bottom-content {
            height: 48/2px;
            line-height: 48/2px;
            background: rgba(255, 255, 255, 0.32);
            text-align: center;
            font-size: 24/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #692204;
            text-overflow: -o-ellipsis-lastline;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
        }
    }

    .vip-goods-first-ty-vip{
        background: linear-gradient(135deg,#ffe4c9 1%, #ffe2d2 30%, #ffc7b2 87%, #ffa4b0);
    }
    
    .read-protocol {
        margin-top: 10px;
    }

    .vip-goods-other-container {
        width: 100%;
        overflow: hidden;
    }

    .vip-goods-other {
        width: 100%;
        margin-top: 15px;
        overflow-x: scroll;
        overflow-y: hidden;
        position: relative;
        white-space: nowrap;

        .item-content {
            width: 235px;
            height: 89px;
            background: #fffbf6;
            border: 1px solid #f4c2a2;
            border-radius: 5px;
            display: inline-block;
            margin-right: 8px;

            position: relative;

            .tag-tips-other {
                position: absolute;
                top: -1px;
                right: 0px;
                padding-left: 14/2px;
                padding-right: 14/2px;
                height: 18px;
                background: linear-gradient(
                    90deg,
                    #ff7810,
                    #fe3c29 55%,
                    #fe6164
                );
                border-radius: 0px 4px 0px 9px;
                font-size: 20/2px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #ffffff;
                line-height: 18px;
                text-overflow: -o-ellipsis-lastline;
                max-width: 100px;
                overflow: hidden;

                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                display: inline-block;
                line-clamp: 1;
                -webkit-box-orient: vertical;
            }

            .top-content {
                width: 100%;
                padding: 10px 10px 0px;
                position: relative;
                display: flex;
                justify-content: space-between;

                .left {
                    flex: 1;
                    padding-right: 24px;
                    .left-goods-name {
                        font-size: 14px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #6e2c12;
                        white-space: normal !important;
                        text-overflow: -o-ellipsis-lastline;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }
                    .left-goods-desc {
                        display: flex;
                        margin-top: 7px;
                        align-items: flex-end;

                        .title-desc {
                            color: #d00f1b;
                            font-size: 11px;
                            .price {
                                font-size: 17px;
                            }
                        }
                        .desc-desc {
                            font-size: 10px;
                            font-family: PingFangSC, PingFangSC-Regular;
                            font-weight: 400;
                            text-align: center;
                            color: #aa4120;
                            line-height: 14px;
                        }
                    }
                }

                .right-button {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 64px;
                    height: 28px;
                    margin-top: 15px;
                    background: linear-gradient(
                        102deg,
                        #f5cbad 5%,
                        #efaf8b 91%
                    );
                    border-radius: 18px;
                    font-size: 14px;
                    font-family: PingFangSC, PingFangSC-Medium;
                    font-weight: 500;
                    text-align: left;
                    color: #692204;
                }
            }
            .button-con {
                height: 24px;
                background: linear-gradient(100deg, #ffddc9 7%, #fff7ec 92%);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 11px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #aa4120;
                position: absolute;
                bottom: 0px;
                border-radius: 0px 0px 5px 5px;
                left: 0px;
                right: 0px;
                box-sizing: border-box;
                word-break: break-all;
                white-space: nowrap;
                overflow: hidden;

                .button-con-inner {
                    white-space: nowrap;
                    padding-left: 5px;
                    padding-right: 5px;
                    text-align: center;
                }

                .button-con-text {
                    display: inline-block;
                }
            }
        }
    }
}
.hide {
    display: none;
}
.paytype-box {
    padding: 0 0px;
    margin-bottom: 15px;
}
