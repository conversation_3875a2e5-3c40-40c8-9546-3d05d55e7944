<import name="style" content="./main" module="S" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="PackageDialog" content=":application/buyed/components/packageRecommend/components/packageDialog/main" />
<import name="loading" content=":application/buyed/components/loading/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="payType" content=":component/payType/main" />
<import name="readProtocol" content=":component/readProtocol/main" />

<div class="{{state.recommendGoodsList.length>0?'':S.hide}}">
    <div class="{{S.packageRecommendContainer}} {{Platform.isIOS?S.marginBottom:''}}">
        <div class="{{S.packageTop}}">
            <div class="{{S.title}}">会员专属优惠套餐推荐</div>
            <div class="{{S.desc}}" sp-on:click="moreView">
                更多优惠<span class="{{S.rightIcon}}"></span>
            </div>
        </div>
        <sp:each for="{{state.recommendGoodsList}}">
            <sp:if value="{{$index==0}}">
                <div class="{{S.vipGoodsFirst}} {{S['vip-goods-first-' + props.theme]}}"
                    data-groupkey="{{$value.groupKey}}" sp-on:click="gotoBuy">
                    <sp:if value="{{$value.tips}}">
                        <div class="{{S.tagTips}}">{{$value.tips}}
                        </div>
                    </sp:if>
                    <div class="{{S.topContent}}">
                        <div class="{{S.left}}">
                            <p class="{{S.title}}">{{$value.newName}}</p>
                            <p class="{{S.desc}}">
                                <span class="{{S.sym}}">￥</span><span
                                    class="{{S.price}}">{{$value.newPrice}}</span>/{{$value.validDays}}天
                                <sp:if value="{{$value.newpriceDiff}}">
                                    <span class="{{S.tagButton}}">更划算，立省<i>{{$value.newpriceDiff}}</i>元</span>
                                </sp:if>
                            </p>
                        </div>
                        <div class="{{S.right}}">
                            <span class="{{S.button}}">
                                {{$value.canUpgrade?'去升级':'开通'}}
                            </span>
                        </div>
                    </div>
                    <div class="{{S.bottomContent}}">
                        {{$value.newDescript}}
                    </div>
                </div>
            </sp:if>
        </sp:each>
        <sp:if value='{{Platform.isIOS}}'>
            <div class="{{S.readProtocol}}">
                <com:readProtocol />
            </div>

        </sp:if>

    </div>

    <com:buyButton></com:buyButton>
    <com:PackageDialog name="packageDialog" getChildrenKey="{{self.getChildrenKey}}"
        recommendGoodsList="{{state.recommendGoodsList}}" theme="{{props.theme}}"></com:PackageDialog>
    <com:payDialog />
    <com:loading />

</div>