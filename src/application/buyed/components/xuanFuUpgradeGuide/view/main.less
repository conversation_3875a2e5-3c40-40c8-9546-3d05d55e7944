.xuanfuDialog {
    // position: fixed;
    // bottom: 0px;
    width: 100%;
    left: 0px;
    right: 0px;
    min-height: 55px;
    height: calc(~"55px + constant(safe-area-inset-bottom)/2") !important;
    /* 兼容 iOS < 11.2 */
    height: calc(~"55px + env(safe-area-inset-bottom)/2");
    background: url("../images/xuan-fu.png") no-repeat center;
    background-size: 100% 100%;
    z-index: 98;

    .xunfu-content {
        display: flex;
        justify-content: space-between;
        padding: 20/2px 24/2px 18/2px 24/2px;
        align-items: center;
        position: relative;
    }

    .left {
        width: calc(100% - 200 / 2px);

        .title {
            font-size: 22/2px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #99581d;
            vertical-align: middle;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;

            .icon-span {
                display: inline-block;
                width: auto;
                height: 36/2px;
                margin-right: 10/2px;
                vertical-align: middle;
            }
        }

        .desc {
            font-size: 26/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 36/2px;
            letter-spacing: 1px;
            margin-top: 6/2px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;

            span {
                color: #f2200e;
            }
        }
    }

    .right {
        width: 200/2px;

        .button {
            width: 192/2px;
            height: 54/2px;
            line-height: 54/2px;
            background: linear-gradient(90deg, #53292a 0%, #3e1a1b 100%);
            border-radius: 27/2px;
            border: 2/2px solid #ffddbc;
            font-size: 24/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #fcdab8;
            text-align: center;
        }
    }
    .close-container {
        width: 80/2px;
        height: 36/2px;
        position: absolute;
        top: -44/2px;
        right: 12/2px;
        display: flex;
        justify-content: flex-end;
        z-index: 88;
        .close {
            width: 36/2px;
            height: 36/2px;
            background: url("../images/close.png") no-repeat center;
            background-size: 36/2px 36/2px;
        }
    }
}
