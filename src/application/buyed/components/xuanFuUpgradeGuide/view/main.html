<import name="style" content="./main" module="S" />
<div>
    <sp:if value='{{state.showXuanfu&&props.prevScrollTop>5&&props.nowGoodInfo.upgrade}}'>
        <div class="{{S.xuanfuDialog}}" sp-on:click="gotoBuyXunafu">
            <div class="{{S.xunfuContent}}">
                <div class="{{S.left}}">
                    <p class="{{S.title}}"><img class="{{S.iconSpan}}"
                            src="{{props.swallConfig.icon}}" />亲爱的会员{{props.userData.nickname}}
                    </p>
                    <p class="{{S.desc}}">邀请您升级为<span>{{props.swallConfig.name}}</span></p>
                </div>
                <div class="{{S.right}}">
                    <div class="{{S.button}}">{{props.nowGoodInfo.payPrice}}元立即升级</div>
                </div>
                <div class="{{S.closeContainer}}" sp-on:click="closeXuanfu">
                    <div class="{{S.close}}"></div>
                </div>
            </div>
        </div>
    </sp:if>
</div>