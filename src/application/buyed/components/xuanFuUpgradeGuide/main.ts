import { setPageName } from ':common/env';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import Texts from ':common/features/texts';
import { trackEvent } from ':common/stat';
import { openWeb } from ':common/core';
interface State {
    showXuanfu: boolean
}
interface Props {
    swallConfig: any
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            showXuanfu: false
        };
    }
    didMount() {
        setPageName(Texts.BUYED_PAGE);
    }
    closeXuanfu(e) {
        e && e.stopPropagation();
        this.setState({ showXuanfu: false });
    }
    show() {
        trackEvent({
            fragmentName1: '升级悬浮条',
            actionName: '',
            actionType: '出现'
        });
        this.setState({ showXuanfu: true });
    }
    gotoBuyXunafu() {
        trackEvent({
            fragmentName1: '升级悬浮条',
            actionName: '跳转',
            actionType: '点击'
        });
        openWeb({
            url: this.props.swallConfig.url
        });
    }
    willReceiveProps() {
        return true;
    }
}
