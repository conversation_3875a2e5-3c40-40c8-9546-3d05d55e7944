import { openWeb } from ':common/core';
import { Platform } from ':common/env';
import { handleIosZigezhen } from ':common/utils';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
interface State {
}
interface Props {
    kemu: string | number
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
        };
    }
    gotoRightDetail(e) {
        const actionUrl = e.refTarget.getAttribute('data-action');
        const uniqueCode = e.refTarget.getAttribute('data-uniqueCode');
        if (Platform.isIOS) {
            if (actionUrl.indexOf('https://laofuzi.kakamobi.com/') === 0) {
                if (
                    uniqueCode === 'goods-planB-introduction' ||
                    uniqueCode === 'goods-kemu2-question'
                ) {
                    if (handleIosZigezhen(actionUrl)) {
                        return;
                    }
                    openWeb({
                        url: actionUrl,
                        title: '补偿说明'
                    });
                    return;
                }
                if (handleIosZigezhen(actionUrl)) {
                    return;
                }
                openWeb({
                    url: actionUrl
                });
            } else if (actionUrl) {
                if (handleIosZigezhen(actionUrl)) {
                    return;
                }
                // 地址不为空就跳转
                openWeb({
                    url: actionUrl
                });
            }
        } else if (
            uniqueCode === 'goods-jiakaovideo' ||
            uniqueCode === 'goods-kemu2-ksmj' ||
            uniqueCode === 'goods-SecurityVideo' ||
            uniqueCode === 'goods-introduction' ||
            uniqueCode === 'goods-exam'
        ) {
            openWeb({
                url: actionUrl
            });
        } else if (uniqueCode === 'goods-planB-introduction') {
            openWeb({
                url: actionUrl,
                title: '补偿说明'
            });
        } else if (actionUrl) {
            openWeb({
                url: actionUrl
            });
        } else {
            // 空地址点击就不跳转了
        }
    }
    willReceiveProps() {
        return true;
    }
}
