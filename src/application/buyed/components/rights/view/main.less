.content3 {
    .big-title {
        font-size: 36/2px;
        font-weight: 500;
        color: hsl(0, 0%, 20%);
        line-height: 50/2px;
        margin: 50/2px 0px 0px 0px;
    }

    &.xuetang-padding-bottom {
        padding-bottom: 30/2px;
    }

    .content3-div {
        height: 160/2px;
        background: url("../images/content3_bg.png") no-repeat center;
        background-size: 100% 160/2px;
        padding: 0px 40/2px 0px 30/2px;
        margin-top: 40/2px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
            flex: 1;

            .title {
                font-size: 34/2px;
                font-weight: 500;
                color: #8f5333;
                line-height: 48/2px;
                margin-bottom: 8/2px;
            }

            .desc {
                font-size: 26/2px;
                font-weight: 400;
                color: #b46938;
                line-height: 36/2px;
            }
        }

        .right {
            width: 200/2px;
            height: 68/2px;
            line-height: 68/2px;
            text-align: center;
            background: linear-gradient(115deg, #ffffff 0%, #fff6e9 100%);
            border-radius: 40/2px;
            font-size: 30/2px;
            font-weight: 500;
            color: #8f5333;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

.content4 {
    padding-top: 20/2px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    &.boottom-container-other {
        padding-bottom: 50/2px;
    }

    .content4-div {
        width: 25%;
        display: flex;
        align-items: center;
        text-align: center;
        flex-direction: column;
        padding: 30/2px 10/2px 0px 10/2px;

        .top-bg {
            width: 92/2px;
            height: 92/2px;
            background-repeat: no-repeat;
            background-size: 92/2px 92/2px;
            background-position: center center;
        }

        .title {
            margin-top: 10/2px;
            font-size: 28/2px;
            font-weight: 400;
            color: #333333;
            line-height: 40/2px;
        }
    }
}

.lipei-dialog-wrap {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    height: 100%;
    width: 100%;
    padding: 0 40/2px;
    box-sizing: border-box;

    &.hide {
        display: none;
    }

    .lipei-dialog {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        -webkit-transform: translate(-50%, -50%);
        box-sizing: border-box;
        padding: 40/2px 5/2px 25/2px 5/2px;
        position: relative;
        background: #ffffff;
        border-radius: 10/2px;
        display: flex;
        align-items: center;
        justify-content: space-around;
    }

    .lp-close {
        position: absolute;
        top: 0;
        right: 0;
        padding: 8/2px;
        width: 44/2px;
        height: 44/2px;
        background: url(../images/close.png) no-repeat center center;
        background-size: 44/2px 44/2px;
        box-sizing: content-box;
    }

    .sec-btn {
        span {
            padding: 0 40/2px;
        }
    }

    .sec-btn {
        display: flex;
        align-items: center;
        justify-content: center;

        span {
            color: #333333;
            font-size: 28/2px;
            height: 64/2px;
            width: 200/2px;
            border-radius: 64/2px;
            border: 2/2px solid;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
