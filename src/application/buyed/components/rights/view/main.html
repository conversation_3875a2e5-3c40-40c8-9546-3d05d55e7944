<import name="style" content="./main" module="S" />
<div>
    <div class="{{S.content3}} {{Platform.isNoProtocol&&S.xuetangPaddingBottom}}">
        <sp:if
            value='{{props.nonecompensate.length>0&&!Platform.isNoProtocol}}'>
            <p class="{{S.bigTitle}}">其它增值权益</p>
        </sp:if>
    </div>
    <sp:if value='{{!Platform.isNoProtocol}}'>
        <div class="{{S.content4}} {{S.boottomContainerOther}}">
            <div class="{{S.content4Div}}" sp-on:click="gotoRightDetail" sp:each={{props.nonecompensate}}
                data-kemu="{{$value.kemu}}" data-uniqueCode="{{$value.uniqueCode}}" data-action="{{$value.actionUrl}}">
                <img class="{{S.topBg}}" src="{{$value.icon}}" />

                <p class="{{S.title}}">{{$value.funcName}}</p>
            </div>
        </div>
    </sp:if>
</div>