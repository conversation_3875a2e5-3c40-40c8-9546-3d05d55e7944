<import name="style" content="./main" module="S" />
<import name="Zhiboke" content=":application/buyed/components/zhiboke/main" />
<import name="ElderStudyStep" content=":application/buyed/components/elderStudyStep/main" />
<div>
    <div
        class=":swiper-box {{URLCommon.tiku === CarType.CAR && (state.showHomework)?'':'hide'}}">
        <div class="swiper :swiper-container" skip-attribute="class|style" ref="swiper">
            <div class="swiper-wrapper" skip-attribute="class|style">
                <div
                    class="swiper-slide {{ URLCommon.tiku === CarType.CAR && state.showHomework ? '': 'hide'}}">
                    <div class=":homework" sp-on:click="goHomework"></div>
                </div>
                <div class="swiper-slide {{state.courseInfo?'':'hide'}}">
                    <div class=":im" sp-on:click="goTeach">
                        <div class=":title">{{state.courseInfo.subject}}</div>
                        <div class=":time">
                            {{Tools.dateFormat(state.courseInfo.beginTime, 'yyyy/MM/dd hh:mm')}}-{{Tools.dateFormat(state.courseInfo.endTime, 'hh:mm')}}
                        </div>
                        <div class=":teacher-box">
                            <div class=":user-img"
                                style="background: url({{state.courseInfo.lecturerAvatar}}) no-repeat center center/cover;">
                            </div>
                            <div class=":info">{{state.courseInfo.lecturerName}} 丨
                                {{self.showCourseTime(state.courseInfo.endTime - state.courseInfo.beginTime)}}分钟</div>
                        </div>
                        <div class=":btn">进入教室</div>
                        <sp:if value="{{state.courseInfo.status === 1}}">
                            <div class=":status">即将开始</div>
                        </sp:if>
                        <sp:if value="{{state.courseInfo.status === 2}}">
                            <div class=":status">上课中</div>
                        </sp:if>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <sp:if value='{{state.steps.length}}'>
        <div class="{{S.content2Step}}">
            <p class="{{S.bigTitle}} {{URLCommon.isElder?S.elderTitle:''}}">{{state.kemuList[props.kemu]}}</p>
            <sp:if value='{{!Platform.isNoProtocol&&!URLCommon.isZigezheng && (props.kemu==1 || props.kemu==4)}}'>
                <div class="{{S.examInfoContainer}}">
                    <sp:if value='{{state.examTimeDoneStatus !== 0}}'>
                        <div class="{{S.examInfo}}">
                            <span class="{{S.examFont}}" sp-on:click="gotoWriteExamTime">
                                <sp:if value="{{state.examTimeDoneStatus === 3}}">
                                    已过考试日期{{self.hasDay}}天
                                    <sp:elseif value="{{state.examTimeDoneStatus === 2}}" />
                                    预祝您今天考试通过！
                                    <sp:else />
                                    距离考试还有{{self.hasDay}}天
                                </sp:if>
                                >
                            </span>
                        </div>
                        <sp:else />
                        <div class="{{S.examInfo}}">
                            <span data-kemu="{{props.kemu}}" class="{{S.examFont}}"
                                sp-on:click="gotoWriteExamTime">请填写预约考试时间></span>
                        </div>
                    </sp:if>
                </div>
                <sp:elseif
                    value="{{!Platform.isNoProtocol&&!URLCommon.isZigezheng && (props.kemu==2 || props.kemu==3)}}" />
                <div class="{{S.examInfoContainer}}">
                    <div class="{{S.examInfo}}">
                        <span class="{{S.examFont}}">
                            {{props.kemu==2?'仅需三步，专项攻克':'专项攻克，拆解难点'}}
                        </span>
                    </div>
                </div>
            </sp:if>
            <sp:if value='{{!URLCommon.isZigezheng&&!Platform.isNoProtocol && state.config.icon}}'>
                <div class="{{S.showExamGrade}}" style="background-image: url({{state.config.icon}});"
                    sp-on:click="goExamGrade"></div>
            </sp:if>
            <div sp:if="{{state.passRate}}"
                class="{{S.passrateDiv}} {{state.passRateTips.indexOf('可以')!==-1?S.passratePass:''}}">
                <sp:if value='{{state.passRateTips.indexOf("可以")!==-1}}'>
                    <span class="{{S.passrateImg}} {{S.passrateSuccess}}"></span>
                    <sp:else />
                    <span class="{{S.passrateImg}} "></span>
                </sp:if>

                <span class="{{S.passratefont}}">{{state.passRateTips}}</span>
            </div>
            <sp:if value='{{!URLCommon.isElder&&!Platform.isNoProtocol}}'>
                <sp:if value='{{props.kemu==1||props.kemu==4}}'>
                    <com:Zhiboke name="zhiboke" fragmentName1="{{(props.kemu==1 ? '科一' : '科四')+ 'VIP专属学习方案'}}"
                        actionName="去上课">
                    </com:Zhiboke>
                </sp:if>
            </sp:if>
            <sp:if value='{{(URLCommon.isElder&&props.kemu==1)||(URLCommon.isElder&&props.kemu==4)}}'>
                <com:ElderStudyStep kemu="{{props.kemu}}" steps="{{self.getSteps}}" name="elderStudyStep">
                </com:ElderStudyStep>
                <sp:else />
                <sp:each for="{{state.steps}}">
                    <div class="{{S.content2Div}}">
                        <div class="{{S.left}}">
                            <p class="{{S.title}}">{{$value.name}}</p>
                            <p class="{{S.desc}}">{{$value.desc}}<span>{{$value.hint}}</span></p>
                        </div>
                        <sp:if value='{{$value.sceneCode=="102"}}'>
                            <div data-action="{{$value.action[URLCommon.tiku]}}" data-type="{{$value.type}}"
                                class="{{S.right}}" sp-on:click="gotoStudentMethod">
                                {{props.isScore12View?'去学习':'未开通'}}
                            </div>
                        </sp:if>
                        <sp:if value='{{!$value.sceneCode}}'>
                            <div data-action="{{$value.action[URLCommon.tiku]}}" data-type="{{$value.type}}"
                                class="{{S.right}}" sp-on:click="gotoStudentMethod">
                                {{$value.btnName?$value.btnName:'去学习'}}
                            </div>
                        </sp:if>

                        <div
                            class="{{!(URLCommon.tiku=='moto')?S['tubiao1Type'+$index]:S['tubiao1Type'+($index-1)]}} {{S.tubiao1}}">
                        </div>
                        <div
                            class="{{$value.tag!=0?S.tubiao2:''}} {{$value.tag==1&&S.tubiao2Tag1}} {{$value.tag==2&&S.tubiao2Tag2}}">
                        </div>
                    </div>
                </sp:each>
            </sp:if>

        </div>
    </sp:if>
    <!-- 没数据时布局占位 -->
    <sp:if value='{{!state.steps.length}}'>
        <div class="{{S.noDataZhanwei}}">
            <sp:each for="3">
                <div class="{{S.step}}">
                </div>
            </sp:each>
        </div>
    </sp:if>
</div>
