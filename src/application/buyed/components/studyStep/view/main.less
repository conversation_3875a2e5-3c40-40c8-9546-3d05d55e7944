.no-data-zhanwei {
    margin-top: 15px;

    .step {
        height: 100px;
        background: #f3f3f3;
        margin-bottom: 10px;
        border-radius: 4px;
    }
}

.swiper-box {
    margin-top: 15px;
    margin-bottom: 20px;
    
    .swiper-container {
        overflow: hidden;
    
        .homework {
            width: 345px;
            height: 95px;
            background: url(../images/banner_ewm.png) no-repeat center center;
            background-size: contain;
        }
    
        .im {
            width: 345px;
            min-height: 95px;
            padding: 11px 15px;
            background: linear-gradient(90deg, #FFF6F1, #FBD8CF);
            position: relative;
            border-radius: 8px;
    
            .title {
                color: #483737;
                font-size: 17px;
                line-height: 24px;
                font-weight: bold;
                max-width: 270px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
    
            .time {
                color: #FF0D00;
                margin-top: 3px;
                font-size: 13px;
                line-height: 18px;
                display: flex;
                align-items: center;
    
                &::before {
                    content: '';
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    background: url(../images/1.png) no-repeat center center/cover;
                    margin-right: 4px;
                }
            }
    
            .teacher-box {
                margin-top: 9px;
                display: flex;
                align-items: center;
    
                .user-img {
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    overflow: hidden;
                    margin-right: 5px;
                }
    
                .info {
                    font-size: 12px;
                    color: #6F504E;
                }
            }
    
            .btn {
                position: absolute;
                bottom: 12px;
                right: 15px;
                width: 100px;
                height: 34px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: linear-gradient(135deg, #ff4d1c, #ff1c54);
                border-radius: 17px;
            }
    
            .status {
                position: absolute;
                top: 2px;
                right: 2px;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 56px;
                height: 18px;
                background: #ffffff;
                border-radius: 0px 8px 0px 6px;
                font-size: 12px;
                color: #C43634;
    
                &.end {
                    color: #6E6E6E;
                }
            }
        }
    }
}

.content2-step {
    font-size: 22/2px;
    white-space: pre-wrap;
    vertical-align: top;
    position: relative;

    &.hide {
        display: none;
    }

    .show-exam-grade {
        position: absolute;
        top: 0px;
        right: 0px;
        width: 158px;
        height: 48px;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center center;
    }

    .big-title {
        margin: 16px 0px 0px 0px;
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        line-height: 50/2px;

        &.elder-title {
            margin-top: 16px;
        }
    }

    .exam-info-container {
        margin-top: 8px;

        .exam-info {
            font-size: 13px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            color: #333333;
            line-height: 18px;

            .exam-font {
                font-size: 13px;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                color: #2390fe;
                line-height: 18px;
                margin-left: 10px;
            }
        }
    }

    .passrate-div {
        padding: 20/2px;
        background: linear-gradient(270deg, #fff7f6 0%, #feedeb 100%);
        border-radius: 8/2px;
        margin-bottom: 32/2px;
        margin-top: 8px;
        font-size: 26/2px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ff4a40;
        vertical-align: middle;

        &.passrate-pass {
            color: #13498e;
            background: linear-gradient(270deg, #e2f4ff 0%, #d0e6ff 100%);
        }

        .passrate-img {
            display: inline-block;
            width: 30/2px;
            height: 30/2px;
            background: url("../../../images/vip-buyed/passrate-err.png") no-repeat center center;
            background-size: 100% 100%;
            margin-right: 14/2px;
            vertical-align: middle;

            &.passrate-success {
                background: url("../../../images/vip-buyed/passrate-success.png") no-repeat center center;
                background-size: 100% 100%;
            }
        }

        .passratefont {
            vertical-align: middle;
        }
    }

    .content2-div {
        // // min-height: 164px;
        // min-height: 364px;
        margin-top: 15px;
        padding-top: 15px;
        padding-bottom: 15px;
        background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
        box-shadow: 0px 0px 12/2px 0px rgba(0, 0, 0, 0.09);
        border-radius: 8/2px;
        display: flex;
        align-items: center;
        padding-left: 30/2px;
        padding-right: 30/2px;
        justify-content: space-between;
        position: relative;
        margin-bottom: 20/2px;

        &:last-child {
            margin-bottom: 0px;
        }

        .tubiao1 {
            position: absolute;
            width: 52px;
            height: 43px;
            top: 0px;
            left: 0px;
            background-repeat: no-repeat;
            background-size: 100%;
            background-position: center center;
            z-index: 0;

            &.tubiao1Type0 {
                background-image: url("../../../images/vip-buyed/content_01.png");
            }

            &.tubiao1Type1 {
                background-image: url("../../../images/vip-buyed/content_02.png");
            }

            &.tubiao1Type2 {
                background-image: url("../../../images/vip-buyed/content_03.png");
            }

            &.tubiao1Type3 {
                background-image: url("../../../images/vip-buyed/content_04.png");
            }
        }

        .tubiao2 {
            position: absolute;
            width: 102/2px;
            height: 36/2px;
            top: 0px;
            right: 0px;
            background-repeat: no-repeat;
            background-size: 102/2px 36/2px;
            background-position: center center;

            &.tubiao2Tag1 {
                background-image: url("../../../images/vip-buyed/vip-ygm-bq-wdb.png");
            }

            &.tubiao2Tag2 {
                background-image: url("../../../images/vip-buyed/vip-ygm-bq-ydb.png");
            }
        }

        .left {
            flex: 1;
            position: relative;
            z-index: 1;

            .title {
                font-size: 32/2px;
                font-weight: 500;
                color: #333333;
                line-height: 44/2px;
                margin-bottom: 8/2px;
            }

            .desc {
                font-size: 26/2px;
                font-weight: 400;
                color: #666666;
                line-height: 15px;

                span {
                    color: #2390fe;
                    font-size: 26/2px;
                    margin-left: 5/2px;
                }
            }
        }

        .right {
            width: 80px;
            height: 30px;
            margin-left: 10/2px;
            line-height: 30px;
            text-align: center;
            border-radius: 40/2px;
            font-size: 30/2px;
            font-weight: 500;
            color: #2390fe;
            border: 2/2px solid #2390fe;
        }
    }
}
