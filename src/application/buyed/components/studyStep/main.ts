import {
    COURSE_TEACH_URL,
    FAMOUS_TEACHER, JING_JIAN, MI_JU<PERSON>, PASS_ANALYSIS, PLIVATE_TEACH_HOMEWORK, REAL_ROOM, SCORE12,
    STEP1_BUS, STEP1_CAR, STEP1_K3_BUS, STEP1_K3_CAR, STEP1_K3_MOTO,
    STEP1_K3_TRUCK, STEP1_MOTO, STEP1_TRUCK, STEP1_ZHIBOKE, STEP2_BUS,
    STEP2_CAR, STEP2_K3_BUS, STEP2_K3_CAR, STEP2_K3_TRUCK, STEP2_TRUCK,
    STEP3_BUS, STEP3_K3_BUS, STEP3_K3_TRUCK, STEP4_K3_CAR
} from ':common/navigate';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
import { URL<PERSON>ommon, URLParams, CarType, Platform, getPageName, Features, KemuType } from ':common/env';
import Zhiboke from ':application/buyed/components/zhiboke/main';
import { openVipWebView, openWeb } from ':common/core';
import { dateFormat, getHasDay, handleIosZigezhen } from ':common/utils';
import { trackEvent } from ':common/stat';
import { getKe2PassRate, getPermission, getStudentBaseInfo, getSwallowConfig, kemuParamsMap } from ':store/chores';
import throttle from 'lodash/throttle';
import Swiper from 'swiper';
import 'swiper/swiper-bundle.css';
import { getLatestCourse, getUserIdentity } from ':store/buyed';

interface State {
    steps: any[],
    passRate: number,
    minfeishu: number,
    passRateTips: string,
    kemuList: any,
    /**
     * 0： 未填写
     * 1： 已经填写未过期（并且不是当天）
     * 2： 已经填写并且是当天  
     * 3： 已经过期
     * */
    examTimeDoneStatus: 0 | 1 | 2 | 3
    examTime: number;
    // 是否展示家庭作业入口
    showHomework: boolean
    // 课程信息
    courseInfo: any
    config: any
}
interface Props {
    kemuAllList: any[],
    kemu: number
}
// eslint-disable-next-line
const oneTime = 24 * 60 * 60 * 1000;
export default class extends Component<State, Props> {
    declare children: {
        zhiboke: Zhiboke
    }
    get getSteps() {
        let newSteps = [];
        const quaOrMoto = URLCommon.tiku === CarType.MOTO || URLCommon.isZigezheng;
        if (!quaOrMoto && this.props.kemuAllList?.length > 0) {
            this.state.steps && this.state.steps.forEach((res, index) => {
                if (index !== 0) {
                    newSteps.push(res);
                }

            });
        } else {
            newSteps = this.state.steps;
        }
        return newSteps || [];
    }
    // 剩余天数
    get hasDay() {
        const { examTime, examTimeDoneStatus } = this.state;
        const nowDateMax = new Date(dateFormat(new Date(), 'yyyy/MM/dd')).getTime() + oneTime - 1;

        const diffTime = Math.abs(examTime - nowDateMax);

        if (examTimeDoneStatus !== 0) {
            return Math.ceil(diffTime / oneTime);
        }
        return '';
    }
    showCourseTime(time = 0) {
        const sec = Math.floor(time / 1000);

        return Math.floor(sec / 60);
    }
    Swiper: any;
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            steps: [],
            passRate: 0,
            minfeishu: 95,
            passRateTips: '',
            examTimeDoneStatus: 0,
            examTime: 0,
            kemuList: {
                0: '全科VIP专属学习方案',
                1: '科一VIP专属学习方案',
                2: '科二VIP专属学习方案',
                3: '科三VIP专属学习方案',
                4: '科四VIP专属学习方案'
            },
            showHomework: false,
            courseInfo: null,
            config: {}
        };
    }
    studyStepRequestion = throttle((kemu) => {
        switch (kemu) {
            case 1:
            case 4:
                this.getKe14LearningData(kemu);

                break;
            case 2:
                this.getKe2LearningData(kemu);
                break;
            case 3:
                this.getKe3LearningData(kemu);
                break;
            default:
                break;
        }
    }, 200)
    async didMount() {
        if (!URLCommon.isZigezheng) {
            trackEvent({
                fragmentName1: '邀请填成绩',
                actionName: '',
                actionType: '出现'
            });
            MCProtocol['jiakao-global'].web.showExamTipAlert({
                pageName: getPageName(),
                kemuStyle: URLCommon.kemu,
                carStyle: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                callback: (data) => {
                    // 本来应该保持双端数据一致的，由于历史协议的影响，只能前端兼容了
                    data = Platform.isAndroid ? data : data.data;
                    // 刷新考试时间数据
                    this.getExamTime(data.kemu);
                }
            });
        }
    }
    createSwiper() {

        // if (this.Swiper) {
        //     return;
        // }

        setTimeout(() => {
            const $dom = this.getDOMNode().swiper as HTMLElement;

            this.Swiper = new Swiper($dom, {
                loop: false,
                autoplay: true,
                pagination: {
                    el: '.swiper-pagination'
                }
            });
            // 自动轮播无效
            clearInterval(this.autoplay);
            this.autoplay = setInterval(() => {
                this.Swiper.slideNext();
            }, 3000);
        }, 1000);
    }
    goHomework() {
        trackEvent({
            fragmentName1: '私教班课后作业入口',
            actionType: '点击',
            actionName: '去查看'
        });
        openVipWebView({
            url: PLIVATE_TEACH_HOMEWORK
        });
    }
    goTeach() {
        const { courseInfo } = this.state;

        openVipWebView({
            url: `${COURSE_TEACH_URL}?roomNo=${courseInfo.courseNo}&courseId=${courseInfo.id}`
        });

    }
    // 中间通过率和学习步骤
    async getClientData(kemu: number, type?: string) {
        this.getExamTime(kemu);
        // 扣满12分学习标题更改
        if (this.getSceneCode() === 102) {
            this.state.kemuList[kemu] = '满分学习VIP专属学习方案';
        }
        // 资格证修改标题
        if (URLCommon.isZigezheng) {
            this.state.kemuList[kemu] = '资格证专属学习方案';
        }
        // 课后作业
        getUserIdentity({
            type: 4,
            kemu
        }).then(itemList => {
            if (itemList.length > 0) {
                trackEvent({
                    fragmentName1: '私教班课后作业入口',
                    actionName: '',
                    actionType: '曝光'
                });
                // 课后作业
                this.setState({
                    showHomework: true
                });

                // 直播教案
                getLatestCourse().then(data => {
                    this.setState({
                        courseInfo: data
                    });

                    this.createSwiper();
                });
            } else {
                this.setState({
                    showHomework: false,
                    courseInfo: null
                });
            }

        });
       
        const swallConfig = await getSwallowConfig({
            key: 'jk_elder_vippage_icon',
            kemu
        }) || {};

        if (swallConfig.icon) {
            trackEvent({
                fragmentName1: '悬浮运营位',
                actionName: '',
                actionType: '出现'
            });
        }

        // 点击徽章切换科目,以及onshow时，清空数据，必须要等steps空了才请求，不然渲染会错乱，文字重叠
        this.setState({ steps: [], config: swallConfig }, () => {
            this.studyStepRequestion(kemu);
        });
    }
    getExamTime(kemu) {
        MCProtocol['jiakao-global'].web.getExamDate({
            kemu: kemu,
            kemuStyle: kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            callback: (res) => {
                let examTime = res.data.date;
                let examTimeDoneStatus: State['examTimeDoneStatus'] = 0;

                if (examTime) {
                    // 取值预约考试的23时59分59秒（需要计算是否是今天）
                    examTime = new Date(dateFormat(examTime, 'yyyy/MM/dd')).getTime() + oneTime - 1;
                    const diffTime = examTime - new Date().getTime();

                    if (diffTime > 0) {
                        examTimeDoneStatus = 1;

                        if (diffTime < oneTime) {
                            examTimeDoneStatus = 2;
                        }
                    } else {
                        examTimeDoneStatus = 3;
                    }
                }

                this.setState({
                    examTime,
                    examTimeDoneStatus
                });
            }
        });
    }
    viewPassAnaysis() {
        openVipWebView({
            url: `${PASS_ANALYSIS}?kemuStyle=${this.props.kemu}&from=146`
        });
    }
    /**
     * 科一科四当天或者还没到考试日期的就跳转通过率分析页
     * 科二科三比较特殊， examTimeDoneStatus===1的时候  要弹出选择日期弹窗否则就由客户端弹出过期或者当天的弹窗（科二科三暂时去掉）
    */
    gotoWriteExamTime = (e) => {
        const { examTimeDoneStatus } = this.state;
        const { kemu } = this.props;

        const writeExamTime = () => {
            trackEvent({
                fragmentName1: '填写考试日期弹窗',
                actionName: '',
                actionType: '出现'
            });
            MCProtocol['jiakao-global'].web.showExamDateAlert({
                kemu: kemu,
                kemuStyle: kemu,
                carStyle: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                from: URLParams.from,
                fromPageCode: URLParams.from,
                pageName: getPageName(),
                callback: (data) => {
                    const date = Platform.isAndroid ? data.date : data.data.date;
                    trackEvent({
                        fragmentName1: '填写考试日期弹窗',
                        actionName: '确定',
                        actionType: '点击',
                        targetContent: dateFormat(
                            date,
                            'yyyy-MM-dd'
                        )
                    });
                    // 刷新考试时间数据
                    this.getExamTime(kemu);
                }
            });
        };

        if (examTimeDoneStatus === 0) {
            writeExamTime();
        }

        if (kemu === 1 || kemu === 4) {

            if ((examTimeDoneStatus === 1 || examTimeDoneStatus === 2)) {
                this.viewPassAnaysis();
            }

            if (examTimeDoneStatus === 3) {
                // 历史版本不支持这个协议就跳转通过率分析
                if (Features.indexOf('jiakao-global.luban.mucang.cn/web/showExamTipAlert') > -1 || Features.indexOf('jiakao-global.luban.mucang.cn/web/showexamtipalert') > -1) {
                    MCProtocol['jiakao-global'].web.showExamTipAlert({
                        kemu,
                        force: true,
                        kemuStyle: kemu,
                        carStyle: URLCommon.tiku,
                        sceneCode: URLParams.sceneCode,
                        patternCode: URLParams.patternCode,
                        pageName: getPageName(),
                        callback: (data) => {
                            data = Platform.isAndroid ? data : data.data;
                            // 刷新考试时间数据
                            this.getExamTime(data.kemu);
                        }
                    });
                } else {
                    this.viewPassAnaysis();
                }
            }

        }
        // 科二科三暂时去掉
        // else {
        //     // eslint-disable-next-line no-lonely-if
        //     if (examTimeDoneStatus === 1) {
        //         writeExamTime();
        //     } else if (Features.indexOf('jiakao-global.luban.mucang.cn/web/showExamTipAlert') > -1 || Features.indexOf('jiakao-global.luban.mucang.cn/web/showexamtipalert') > -1) {
        //         MCProtocol['jiakao-global'].web.showExamTipAlert({
        //             kemu,
        //             force: true,
        //             pageName: getPageName(),
        //             callback: (data) => {
        //                 data = Platform.isAndroid ? data : data.data;
        //                 // 刷新考试时间数据
        //                 this.getExamTime(data.kemu);
        //             }
        //         });
        //     }
        // }
    }
    gotoStudentMethod(e) {
        const dataAction = e.refTarget.getAttribute('data-action');
        const dataType = e.refTarget.getAttribute('data-type');
        if (handleIosZigezhen(dataAction)) {
            return;
        }
        if (dataType === 'zhiboke') {
            // eslint-disable-next-line max-len
            const url = dataAction + '?carStyle=' + URLCommon.tiku + '&kemuStyle=' + this.props.kemu + '&sceneCode=' + URLParams.sceneCode + '&patternCode=' + URLParams.patternCode + '&authToken=' + URLParams.authToken + '&orientation=portrait&from=44';
            openWeb({
                url: url
            });
        } else {
            openWeb({
                url: dataAction
            });
        }

    }
    goExamGrade() {
        const { config } = this.state;
        trackEvent({
            fragmentName1: '悬浮运营位',
            actionType: '点击',
            actionName: '去查看'
        });

        openWeb({
            url: config.url
        });
    }
    // 获取科一科四通过率
    getKe14PassRate(kemu: number) {

        return new Promise((resolve) => {
            MCProtocol.Vip.getPassRate({
                car: URLCommon.tiku,
                kemu: kemu,
                kemuStyle: kemu,
                carStyle: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                callback: function (ret) {
                    let data = ret.data * 100;
                    data = data >= 100 ? 100 : parseInt(data + '');
                    resolve(data);
                }
            });
        });
    }
    // 获取科一科四练习记录
    getPracticeRecord(type: string, kemu: number) {
        return new Promise((resolve) => {
            MCProtocol.Vip.getPracticeRecord({
                car: URLCommon.tiku,
                kemu: kemu,
                kemuStyle: kemu,
                carStyle: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                type: type,
                callback: function (ret: any) {
                    let data: any;
                    // 以前版本的要先解析下，后面版本的不用解析，做下兼容
                    try {
                        if (typeof ret.data === 'string') {
                            data = JSON.parse(ret.data);
                        } else {
                            data = ret.data;
                        }
                    } catch (e) {
                        data = {
                            rightCount: 0,
                            errorCount: 0
                        };
                    }
                    resolve(data);
                }
            });
        });
    }
    // 扣满12分第三步骤，三套券协议
    getPracticeProgress(kemu) {
        return new Promise((resolve) => {
            MCProtocol.Vip.getPracticeProgress({
                car: URLCommon.tiku,
                kemu: kemu,
                kemuStyle: kemu,
                carStyle: URLCommon.tiku,
                patternCode: URLParams.patternCode,
                sceneCode: this.getSceneCode(),
                callback: (data) => {
                    resolve(data);
                }
            });

        });
    }
    getSceneCode() {
        let score12Type: number;
        // 判断以前版本传了score12为1代表扣满12分
        if (+URLParams.score12 === 1) {
            score12Type = 102;
        } else {
            score12Type = 101;
        }
        return +URLParams.sceneCode || score12Type;
    }
    // 获取科一科四考试记录
    getExamRecord(type: string, kemu: number) {
        return new Promise((resolve) => {
            // 资格证不知道最低分数，所以要先查最低分数，再查合格次数,资格证传的科目是8
            if (
                URLCommon.isZigezheng ||
                (URLCommon.tiku === CarType.MOTO && this.getSceneCode() === 102)
            ) {
                MCProtocol.Vip.getExamRule({
                    car: URLCommon.tiku,
                    kemu: URLCommon.tiku === CarType.MOTO ? 1 : 8,
                    kemuStyle: URLCommon.tiku === CarType.MOTO ? 1 : 8,
                    carStyle: URLCommon.tiku,
                    patternCode: URLParams.patternCode,
                    sceneCode: this.getSceneCode(),
                    callback: (ret: any) => {
                        let data: any;
                        try {
                            if (typeof ret.data === 'string') {
                                data = JSON.parse(ret.data);
                            } else {
                                data = ret.data;
                            }
                        } catch (e) {
                            data = {};
                        }
                        this.setState({ minfeishu: +data.passScore });
                        console.log('MCProtocol.Vip.getExamRecord参数', {
                            car: URLCommon.tiku,
                            kemu: kemu,
                            kemuStyle: kemu,
                            carStyle: URLCommon.tiku,
                            sceneCode: URLParams.sceneCode,
                            patternCode: URLParams.patternCode,
                            type: type,
                            minScore: data.passScore
                        });
                        MCProtocol.Vip.getExamRecord({
                            car: URLCommon.tiku,
                            kemu: kemu,
                            kemuStyle: kemu,
                            carStyle: URLCommon.tiku,
                            sceneCode: URLParams.sceneCode,
                            patternCode: URLParams.patternCode,
                            type: type,
                            minScore: data.passScore,
                            callback: function (ret: any) {
                                console.log('MCProtocol.Vip.getExamRecord', ret);
                                let newdata: any;
                                // 以前版本的要先解析下，后面版本的不用解析，做下兼容
                                try {
                                    if (typeof ret.data === 'string') {
                                        newdata = JSON.parse(ret.data);
                                    } else {
                                        newdata = ret.data;
                                    }
                                } catch (e) {
                                    newdata = [];
                                }
                                resolve(newdata);
                            }
                        });
                    }
                });
            } else {
                console.log('MCProtocol.Vip.getExamRecord参数', {
                    car: URLCommon.tiku,
                    kemu: kemu,
                    kemuStyle: kemu,
                    carStyle: URLCommon.tiku,
                    sceneCode: URLParams.sceneCode,
                    patternCode: URLParams.patternCode,
                    type: type,
                    // 兼容，ios是大于等于最小分数，andriod是大于
                    minScore: Platform.isIOS ? this.state.minfeishu : 94
                });
                MCProtocol.Vip.getExamRecord({
                    car: URLCommon.tiku,
                    kemu: kemu,
                    kemuStyle: kemu,
                    carStyle: URLCommon.tiku,
                    sceneCode: URLParams.sceneCode,
                    patternCode: URLParams.patternCode,
                    type: type,
                    // 兼容，ios是大于等于最小分数，andriod是大于
                    minScore: Platform.isIOS ? this.state.minfeishu : 94,
                    callback: (ret: any) => {
                        console.log('MCProtocol.Vip.getExamRecord', ret);
                        let data: any;
                        // 以前版本的要先解析下，后面版本的不用解析，做下兼容
                        try {
                            if (typeof ret.data === 'string') {
                                data = JSON.parse(ret.data);
                            } else {
                                data = ret.data;
                            }
                        } catch (e) {
                            data = [];
                        }
                        resolve(data);
                    }
                });
            }
        });
    }

    // 获取科二科三练习数据
    getJiakao3dPassRate(kemu: number) {
        return new Promise((resolve) => {
            MCProtocol.Vip.getJiakao3dPassRate({
                car: URLCommon.tiku,
                kemu: kemu,
                kemuStyle: kemu,
                carStyle: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                callback: function (ret: any) {
                    let data: any;
                    // 以前版本的要先解析下，后面版本的不用解析，做下兼容
                    try {
                        if (typeof ret.data === 'string') {
                            data = JSON.parse(ret.data);
                        } else {
                            data = ret.data || {};
                        }
                    } catch (e) {
                        data = {};
                    }
                    resolve(data);
                }
            });
        });
    }
    jianOrMijuan(data: any, type: string) {
        let totalCount: string | number;
        // 简练题
        if (type === 'jingjian500') {
            if ('totalCount' in data) {
                totalCount = data.totalCount <= 0 ? '--' : data.totalCount;
            } else if (Platform.isXueTang &&
                URLParams.tiku === 'moto'
            ) {
                // 学堂，摩托车总数为150
                totalCount = 150;
            } else {
                // 以前老版本没有传totalCount总数，默认为500
                totalCount = 500;
            }
        } else {
            // 密卷，以前版本没有传totalCount总数，默认为100
            // eslint-disable-next-line no-lonely-if
            if ('totalCount' in data) {
                totalCount = data.totalCount <= 0 ? '--' : data.totalCount;
            } else {
                totalCount = 100;
            }
        }
        let rightCount = +data.rightCount === -1 ? '--' : data.rightCount;
        // 正确数可能会超过总数
        rightCount = +data.rightCount >= +totalCount ? totalCount : data.rightCount;
        const tag = +data.rightCount >= +totalCount ? 2 : 1;
        return {
            totalCount,
            rightCount: rightCount || '--',
            errorCount: data.errorCount || '--',
            tag
        };
    }
    getKe14LearningData(kemu: number) {
        const action1 = JING_JIAN;
        const action2 = REAL_ROOM;
        const action3 = MI_JUAN;
        const action4 = FAMOUS_TEACHER;
        const actionScore12 = SCORE12;
        let passRate = 0;
        let passRateTips = '';
        let examPassCount = 0;
        const score12 = {
            name:
                URLCommon.tiku === CarType.MOTO
                    ? '第三步，考前两套卷'
                    : '第三步，考前三套卷',
            desc: '全部做对',
            hint: '0/100',
            sceneCode: '102',
            tag: 1,
            action: (() => {
                const obj = {};
                for (const key in CarType) {
                    obj[CarType[key]] = '';

                    if (CarType[key] === CarType.CAR || CarType[key] === CarType.TRUCK || CarType[key] === CarType.BUS || CarType[key] === CarType.MOTO) {
                        obj[CarType[key]] = actionScore12;
                    }
                }
                return obj;
            })()
        };
        const mijuan = {
            name: '第三步，临考冲刺检查',
            desc: '考前秘卷全部做对',
            hint: '0/100',
            tag: 1,
            action: (() => {
                const obj = {};
                for (const key in CarType) {
                    obj[CarType[key]] = action3;

                }
                return obj;
            })()
        };
        let steps = [
            {
                name: '第一步，只练精简题库',
                desc: '500题全部做对为达标',
                hint: '0/500',
                isType: 'nomoto',
                // 0:无标签  1: 未达标 2: 达标
                tag: 1,
                action: (() => {
                    const obj = {};
                    for (const key in CarType) {
                        obj[CarType[key]] = action1;
                    }
                    return obj;
                })()
            },
            {
                name: '第二步，真实考场模拟',
                desc: '达到10次95分以上为达标',
                hint: '0/10',
                tag: 1,
                action: (() => {
                    const obj = {};
                    for (const key in CarType) {
                        obj[CarType[key]] = action2;
                    }
                    return obj;
                })()
            }
        ];
        const quaOrMoto = URLCommon.tiku === CarType.MOTO || URLCommon.isZigezheng;
        steps.push(this.getSceneCode() === 102 ? score12 : mijuan);

        // 通过率
        this.getKe14PassRate(kemu)
            .then((data: number) => {
                passRate = data;
                if (passRate >= 90) {
                    passRateTips = '当前通过率' + passRate + '%，考前巩固更有底气';
                } else {
                    passRateTips = '当前通过率' + passRate + '%，不建议参加真实考试';
                }
                // 精简题
                return this.getPracticeRecord('jingjian500', +kemu);
            })
            .then((data) => {
                const jinglianData = this.jianOrMijuan(data, 'jingjian500');

                steps[0].hint = jinglianData.rightCount + '/' + jinglianData.totalCount;
                if (quaOrMoto) {
                    // moto车，资格证总数不显示
                    steps[0].desc = '题全部做对为达标';
                } else {
                    steps[0].desc = jinglianData.totalCount + '题全部做对为达标';
                }
                steps[0].tag = jinglianData.tag;
                // 考试记录
                return this.getExamRecord('realRoom', +kemu);
            })
            .then((data: any[]) => {

                data.forEach((item) => {
                    if (parseInt(item.score) >= this.state.minfeishu) {
                        examPassCount++;
                    }
                });
                // 考试获取考试记录里考试分数大于最小分数的10条
                if (examPassCount >= 10) {
                    examPassCount = 10;
                }
                steps[1].hint = examPassCount + '/10';
                if (URLCommon.isZigezheng) {
                    steps[1].desc = '达到10次合格以上为达标';
                } else if (URLCommon.tiku === CarType.MOTO && this.getSceneCode() === 102) {
                    steps[1].desc = '达到10次' + this.state.minfeishu + '分以上为达标';
                }
                steps[1].tag = examPassCount >= 10 ? 2 : 1;
                // 第三步协议不一样的，这里要做另一个协议的判断，扣满12分
                if (this.getSceneCode() === 102) {
                    return this.getPracticeProgress(+kemu);
                }
                // 密卷
                return this.getPracticeRecord('mijuan', +kemu);
            })
            .then((data: any) => {
                if (this.getSceneCode() === 102) {
                    if (+data.rightCount >= +data.questionCount) {
                        data.rightCount = data.questionCount;
                    }
                    if (+data.rightCount === -1) {
                        data.rightCount = '--';
                    }
                    if (+data.questionCount === -1) {
                        data.questionCount = '--';
                    }

                    steps[2].hint = data.rightCount + '/' + data.questionCount;
                    steps[2].tag =
                        +data.rightCount >= +data.questionCount ? 2 : 1;

                } else {
                    const mijuanData = this.jianOrMijuan(data, 'mijuan');
                    steps[2].hint = mijuanData.rightCount + '/' + mijuanData.totalCount;
                    steps[2].tag = mijuanData.tag;
                }

                // moto车，资格证，没有直播课的判断,直播课的判断是根据是否有全科(kemu=0)购买
                if (!quaOrMoto && this.props.kemuAllList?.length > 0) {
                    steps[0].name = '第二步，' + steps[0].name.split('，')[1];
                    steps[1].name = '第三步，' + steps[1].name.split('，')[1];
                    steps[2].name = '第四步，' + steps[2].name.split('，')[1];
                    steps = ([
                        {
                            name: '第一步，必学精华课程',
                            desc: '观看解锁所有精品课',
                            hint: '',
                            tag: 0,
                            type: 'zhiboke',
                            action: {
                                car: STEP1_ZHIBOKE,
                                truck: STEP1_ZHIBOKE,
                                bus: STEP1_ZHIBOKE,
                                moto: STEP1_ZHIBOKE
                            }
                        }
                    ] as any).concat(steps);
                }

                this.setState({
                    passRate: passRate,
                    steps: steps,
                    passRateTips: passRateTips
                }, () => {
                    this.children.zhiboke?.vipExclusiveLiveRequest(+kemu);
                });
            });
    }
    // 处理科二科三的数据
    handlek2k3Data = function (config) {
        const { passNumber, totalNumber, simulateionMastery, maxTotal } = config;
        const stepMap: any = {};
        if (totalNumber <= 0) {
            stepMap.hint1 = '--';
            stepMap.tag1 = 1;
        } else {
            const newPassNumber = passNumber >= totalNumber ? totalNumber : passNumber;
            stepMap.hint1 = (newPassNumber || 0) + '/' + totalNumber;
            stepMap.tag1 = newPassNumber >= totalNumber ? 2 : 1;
        }
        if (simulateionMastery <= 0) {
            stepMap.hint2 = '--';
            stepMap.tag2 = 1;
        } else {
            const newSimulateionMastery = simulateionMastery >= maxTotal ? maxTotal : simulateionMastery;
            stepMap.hint2 = newSimulateionMastery + '%';
            stepMap.tag2 = newSimulateionMastery >= maxTotal ? 2 : 1;
        }
        return stepMap;
    }

    getKe2LearningData(kemu: number) {
        let passRate = 0;
        let steps = [];
        if (URLCommon.tiku === CarType.GUACHE || URLCommon.tiku === CarType.MOTO) {
            steps = [
                {
                    name: '观看必考项目视频讲解',
                    desc: '必考项目视频（单个车型）全部看完',
                    hint: '',
                    tag: 0,
                    action: {
                        car: STEP1_CAR,
                        truck: STEP1_TRUCK,
                        bus: STEP1_BUS,
                        moto: STEP1_MOTO,
                        // 挂车用的小车的跳转链接
                        light_trailer: STEP1_CAR
                    }
                }
            ];
        } else {
            steps = [
                {
                    name: '第一步，观看必考项目视频讲解',
                    desc: '必考项目视频（单个车型）全部看完',
                    hint: '',
                    tag: 0,
                    action: {
                        car: STEP1_CAR,
                        truck: STEP1_TRUCK,
                        bus: STEP1_BUS,
                        // 挂车目前和小车用的是一个链接
                        light_trailer: STEP1_CAR
                    }
                },
                {
                    name: '第二步，科二单项3D练车',
                    desc: '每个项目熟练度达到80%：',
                    hint: '',
                    tag: 0,
                    action: {
                        car: STEP2_CAR,
                        truck: STEP2_TRUCK,
                        bus: STEP2_BUS,
                        // 挂车用的小车的跳转链接
                        light_trailer: STEP2_CAR
                    },
                    btnName: '去练车'
                },
                {
                    name: '第三步，科二3D考场模拟',
                    desc: '熟练度达到100%：',
                    hint: '',
                    tag: 0,
                    action: {
                        // 第三步小车，truck用的是第二步的 挂车用的小车的跳转链接
                        car: STEP2_CAR,
                        truck: STEP2_TRUCK,
                        bus: STEP3_BUS,
                        light_trailer: STEP2_CAR
                    },
                    btnName: '去练车'
                }
            ];
        }
        this.getJiakao3dPassRate(kemu).then(async (data: any) => {
            let passRateTips = '';
            // 小于3版本的会提示强制更新,所以版本判断不需要了
            if (URLCommon.tiku === CarType.GUACHE || URLCommon.tiku === CarType.MOTO
            ) {
                this.setState({
                    passRate: 0,
                    steps: steps,
                    passRateTips: ''
                });
                return;
            }
            // 处理科二返回的数据
            const stepMap: any = this.handlek2k3Data({
                passNumber: data.k2PassNumber || '',
                totalNumber: data.k2TotalNumber || '',
                simulateionMastery: data.k2SimulateionMastery || '',
                maxTotal: 100
            });
            steps[1].hint = stepMap.hint1;
            steps[1].tag = stepMap.tag1;
            steps[2].hint = stepMap.hint2;
            steps[2].tag = stepMap.tag2;

            passRate = await this.getKe2PassRate();

            if (passRate >= 100) {
                passRate = 100;
            } else if (passRate === -1) {
                passRate = 0;
            }
            if (passRate >= 75) {
                passRateTips = '当前通过率' + passRate + '%，考前巩固更有底气';
            } else {
                passRateTips = '当前通过率' + passRate + '%，不建议参加真实考试';
            }
            this.setState({
                passRate: passRate,
                steps: steps,
                passRateTips: passRateTips
            });
        });
    }
    getKe2PassRate() {
        return getKe2PassRate().then(data => {
            return Math.floor(data.passRate);
        });
    }
    getKe3LearningData(kemu) {
        let steps = [];
        if (URLCommon.tiku === CarType.MOTO) {
            steps = [
                {
                    name: '观看必考项目视频讲解',
                    desc: '必考项目视频（单个车型）全部看完',
                    hint: '',
                    tag: 0,
                    action: {
                        car: STEP1_K3_CAR,
                        truck: STEP1_K3_TRUCK,
                        bus: STEP1_K3_BUS,
                        moto: STEP1_K3_MOTO,
                        // 挂车的用小车的跳转链接
                        light_trailer: STEP1_K3_CAR
                    }
                }
            ];
        } else {
            steps = [
                {
                    name: '第一步，观看必考项目视频讲解',
                    desc: '必考项目视频（单个车型）全部看完',
                    hint: '',
                    tag: 0,
                    action: {
                        car: STEP1_K3_CAR,
                        truck: STEP1_K3_TRUCK,
                        bus: STEP1_K3_BUS,
                        // 挂车用的小车的跳转链接
                        light_trailer: STEP1_K3_CAR
                    }
                },
                {
                    name: '第二步，科三单项3D练车',
                    desc: '每个项目熟练度达到80%：',
                    hint: '',
                    tag: 0,
                    action: {
                        car: STEP2_K3_CAR,
                        truck: STEP2_K3_TRUCK,
                        bus: STEP2_K3_BUS,
                        light_trailer: STEP2_K3_CAR
                    },
                    btnName: '去练车'
                },
                {
                    name: '第三步，科三3D考场模拟',
                    desc: '熟练度达到100%：',
                    hint: '',
                    tag: 0,
                    action: {
                        // 小车用第二步的跳转地址
                        car: STEP2_K3_CAR,
                        truck: STEP3_K3_TRUCK,
                        bus: STEP3_K3_BUS,
                        light_trailer: STEP2_K3_CAR
                    },
                    btnName: '去练车'
                }
            ];
            if (URLCommon.tiku !== CarType.TRUCK && URLCommon.tiku !== CarType.BUS) {
                steps.push({
                    name: '第四步，提前看考场路线',
                    desc: '本地考场路线视频实拍',
                    hint: '',
                    tag: 0,
                    isKemu3: 'isRouteVideo',
                    action: {
                        car: STEP4_K3_CAR,
                        truck: '',
                        bus: ''
                    }
                });
            }
        }

        this.getJiakao3dPassRate(kemu).then((data: any) => {
            // 小于3版本的会提示强制更新,所以版本判断不需要了
            console.log('科目三获取的数据', JSON.stringify(data));
            if (URLCommon.tiku === CarType.MOTO) {
                this.setState({
                    passRate: 0,
                    steps: steps,
                    passRateTips: ''
                });
                return;
            }

            const stepMap: any = this.handlek2k3Data({
                passNumber: data.k3PassNumber || '',
                totalNumber: data.k3TotalNumber || '',
                simulateionMastery: data.k3SimulateionMastery || '',
                maxTotal: 100
            });
            steps[1].hint = stepMap.hint1;
            steps[1].tag = stepMap.tag1;
            steps[2].hint = stepMap.hint2;
            steps[2].tag = stepMap.tag2;
            this.setState({
                passRate: 0,
                steps: steps,
                passRateTips: ''
            });

        });
    }
    willReceiveProps() {
        return true;
    }
}
