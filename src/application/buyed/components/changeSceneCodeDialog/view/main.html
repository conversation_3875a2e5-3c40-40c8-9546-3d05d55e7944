<import name="style" content="./main" module="S" />
<div class="{{S.changeSceneCodeDialogWrap}}">
    <sp:if value="state.show&&props.reseveStatus==0">
        <div class="{{S.changeSceneCodeDialog}}">
            <div class="{{S.changeSceneCodeDialogContent2}}">
                <div style="position:relative">
                    <div class="{{S.topBannerContainer}}"></div>
                    <div class="{{S.centerContainer}}">
                        <div class="{{S.bizTitle}}">感谢您选择扣满12分VIP</div>
                        <div class="{{S.bizDesc}}">
                            为了更好的助力您拿回驾照，
                            我们为您定制了扣满12分场景
                        </div>
                        <div class="{{S.updateButton}}" sp-on:click="changeSceneCode">切换场景</div>
                    </div>
                    <div class="{{S.close}}" sp-on:click="hide"></div>
                </div>


            </div>
        </div>
    </sp:if>

</div>