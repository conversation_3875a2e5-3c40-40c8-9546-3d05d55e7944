.change-sceneCode-dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .5);
  z-index: 103;
  height: 100%;
  width: 100%;

  .change-sceneCode-dialog-content2 {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    width: 640/2px;
    height: auto;

    .top-banner-container {
      height: 308/2px;
      background: url("../images/dialog-tips.png") no-repeat center center;
      background-size: 100% 100%;
    }

    .center-container {
      background: #FFFFFF;
      height: auto;
      border-bottom-left-radius: 20/2px;
      border-bottom-right-radius: 20/2px;
      padding-bottom: 54/2px;
      margin-top: -4/2px;

      .bizTitle {
        font-size: 36/2px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
        text-align: center;
        padding-top: 56/2px;
      }

      .biz-desc {
        font-size: 28/2px;
        font-family: PingFangSC-Regular, <PERSON><PERSON>ang SC;
        font-weight: 400;
        color: #6E6E6E;
        padding: 36/2px 50/2px 44/2px 50/2px;
        text-align: center;
      }

      .update-button {
        width: 458/2px;
        height: 88/2px;
        line-height: 88/2px;
        background: linear-gradient(135deg, #67CEF8 0%, #1E74FA 100%);
        box-shadow: 0px 8/2px 24/2px -8/2px rgba(4, 165, 255, 0.5);
        border-radius: 44/2px;
        font-size: 32/2px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        margin: 0 auto;

        &.gray {
          background: #6E6E6E;
        }

      }
    }

    .close {
      position: absolute;
      bottom: -102/2px;
      right: 0px;
      left: 0px;
      margin: auto;
      width: 72/2px;
      height: 72/2px;
      background: url('../images/close.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }

}