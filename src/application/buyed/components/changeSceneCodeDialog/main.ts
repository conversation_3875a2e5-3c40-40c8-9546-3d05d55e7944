import { CarType, setPageName, URLCommon, URLParams } from ':common/env';
import { trackEvent } from ':common/stat';
import { getPermission } from ':store/chores';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import Texts from ':common/features/texts';
import { openWeb } from ':common/core';
import jump from ':common/features/jump';
interface State {
    show: boolean
}
interface Props {

}
export default class extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            show: false
        };
    }
    didMount() {
        setPageName(Texts.BUYED_PAGE);
        this.showChangeScene();
    }
    async showChangeScene() {
        if (+URLParams.kemuStyle !== 1) {
            return;
        }
        if (+URLParams.sceneCode !== 101) {
            return;
        }
        if (+URLParams.bizVersion <= 6) {
            return;
        }
        let permission = '';
        switch (URLCommon.tiku) {
            case CarType.CAR:
                permission = 'vipKouman';
                break;
            case CarType.BUS:
                permission = 'vipKoumanKc';
                break;
            case CarType.TRUCK:
                permission = 'vipKoumanHc';
                break;
            case CarType.MOTO:
                permission = 'vipKoumanMt';
                break;
            default:
                break;
        }
        if (!permission) {
            return;
        }
        const { hasPromission } = await getPermission(permission);
        if (hasPromission) {
            this.show();
        }
    }
    tcClose(e) {
        e.stopPropagation();
        this.hide();
    }
    changeSceneCode() {
        trackEvent({
            fragmentName1: '引导切换扣满12分场景弹窗',
            actionName: '切换场景',
            actionType: '点击'
        });

        openWeb({
            url: 'http://jiakao.nav.mucang.cn/switchType?kemu=kemu1&sceneCode=102&backhome=0'
        });
        
        const urlHorst = window.location.href.split('?')[0];
        let paramsStr = '';
        for (const key in URLParams) {
            if (key === 'sceneCode') {
                // eslint-disable-next-line no-useless-concat
                paramsStr += key + '=' + '102' + '&';
            } else if (key === 'kemuStyle') {
                paramsStr += key + '=' + 1 + '&';
            } else {
                paramsStr += key + '=' + URLParams[key] + '&';
            }
        }
        const url = urlHorst + '?' + paramsStr.substring(0, paramsStr.length - 1);
        setTimeout(() => {
            window.location.replace(url);
        }, 500);
    }
    hide() {
        this.setState({
            show: false
        });
    }
    show() {
        trackEvent({
            fragmentName1: '引导切换扣满12分场景弹窗',
            actionName: '',
            actionType: '出现'
        });
        this.setState({
            show: true
        });
    }
    willReceiveProps() {
        return true;
    }
}
