import { queryAuthRes } from ':store/student';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
}
interface Props {

}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            showInfo: false
        };
    }
    didMount() {
        queryAuthRes().then((data) => {
            this.setState({
                studentInfo: data
            });
        });
    }
    showInfoModal() {
        this.setState({
            showInfo: true
        });
    }
    closeInfoModal() {
        this.setState({
            showInfo: false
        });
    }
    willReceiveProps() {
        return true;
    }
}
