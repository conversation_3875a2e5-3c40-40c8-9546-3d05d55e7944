<import name="style" content="./main" module="S" />

<div class="{{S.studentIdentified}}" sp:if="state.studentInfo.authStatus === 20">
    <div class="{{S.txt}}" sp-on:click="showInfoModal"></div>
    <sp:if value="state.showInfo">
        <div class=":identify-w">
            <div class=":id-content">
                <div class=":close" sp-on:click="closeInfoModal"></div>
                <div class=":id-confirm">
                    <h1 class=":title">您的学生信息</h1>
                    <div class=":con">
                        <label>真实姓名：</label>
                        <span>{{state.studentInfo.userNameMask}}</span>
                    </div>
                    <div class=":con">
                        <label>身份证号：</label>
                        <span>{{state.studentInfo.idCardMask}}</span>
                    </div>
                    <div class=":con">
                        <label>学&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;校：</label>
                        <span>{{state.studentInfo.schoolNameMask || '未知'}}</span>
                    </div>
                </div>
            </div>
        </div>
    </sp:if>
</div>
