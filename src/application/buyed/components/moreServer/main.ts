import { openWeb } from ':common/core';
import { Platform } from ':common/env';
import { handleIosZigezhen } from ':common/utils';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { getSwallowConfig } from ':store/chores';
import { trackEvent } from ':common/stat';
interface State {
    list: any[]
}
interface Props {
    kemu: string | number
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            list: []
        };
    }
    didMount() {
        trackEvent({
            payStatus: 0,
            fragmentName1: '更多服务',
            actionType: '曝光'
        });
    }
    goDetail(e) {
        const url = e.refTarget.getAttribute('data-url');
        const index = +e.refTarget.getAttribute('data-index');

        trackEvent({
            payStatus: 0,
            fragmentName1: '更多服务',
            actionType: '点击', 
            actionName: '服务' + (index + 1)
        });
      
        openWeb({
            url: url
        });
    }
    willReceiveProps() {
        return true;
    }
}
