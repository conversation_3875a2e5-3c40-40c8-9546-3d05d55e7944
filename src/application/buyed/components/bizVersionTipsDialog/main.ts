import { Platform, URLParams } from ':common/env';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
interface State {
    show: boolean
}
interface Props {

}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            show: false
        };
    }
    tcClose(e) {
        e.stopPropagation();
        this.hide();
    }
    gotoChoujiang() {
        const pkgName = URLParams._pkgName || 'com.handsgo.jiakao.android';
        if (Platform.isIOS) {
            window.open('https://apps.apple.com/cn/app/id491024740');
        } else {
            window.location.href = 'market://details?id=' + pkgName;
        }

    }
    hide() {
        this.setState({
            show: false
        });
    }
    show() {
        this.setState({
            show: true
        });
    }
    willReceiveProps() {
        return true;
    }
}
