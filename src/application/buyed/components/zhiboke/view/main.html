<import name="style" content="./main" module="S" />
<div>
    <sp:if value='{{state.vipExclusiveLive.title}}'>
        <div class="{{S.zhiboke}}">
            <div class="{{S.zhibokeDiv}}" data-scheme="{{state.vipExclusiveLive.scheme}}" sp-on:click="gotoZhibo">
                <div class="{{S.imgAvter}}">
                    <div class="{{S.imgAvterBg}}"
                        style="background-image: url({{state.vipExclusiveLive.teacherAvatar}})">
                    </div>
                    <div class="{{S.imgGif}}">
                        <div class="{{S.imgGifBg}}"></div>
                    </div>
                </div>
                <div class="{{S.titleContainer}}">
                    <div class="{{S.title}}">{{state.vipExclusiveLive.title}}</div>
                    <div class="{{S.desc}}">{{state.vipExclusiveLive.subTitle}}</div>
                </div>
                <div class="{{S.buttonDiv}}">
                    <div class="{{S.buttonTips}}">VIP专享</div>
                </div>
            </div>
        </div>
    </sp:if>
</div>