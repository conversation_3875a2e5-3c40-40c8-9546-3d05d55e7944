import { setPageName } from ':common/env';
import { trackEvent } from ':common/stat';
import { getVipExclusiveLive } from ':store/buyed';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import Texts from ':common/features/texts';
import { openWeb } from ':common/core';
interface State {
    vipExclusiveLive: any
}
interface Props {
    fragmentName1: string,
    actionName:string
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            vipExclusiveLive: {}
        };
    }
    didMount() {
        setPageName(Texts.BUYED_PAGE);
    }
    async vipExclusiveLiveRequest(kemu) {
        // 获取直播课入口显示的科目写死的1
        const vipExclusiveLive = await getVipExclusiveLive({ kemu: kemu });
        this.setState({ vipExclusiveLive: vipExclusiveLive || {} });
    }
    gotoZhibo(e: any) {
        const { fragmentName1, actionName } = this.props;
        trackEvent({
            fragmentName1,
            actionName,
            actionType: '点击'
        });
        const scheme = e.refTarget.getAttribute('data-scheme');
        openWeb({
            url: scheme + '&from=216'
        });
    }
    willReceiveProps() {
        return true;
    }
}
