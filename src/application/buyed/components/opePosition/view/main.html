<import name="style" content="./main" />
<div class="ope-position-container">
    <sp:if value='{{!state.isClose&&state.configData.icon}}'>
        <span  skip="{{state.configData.icon?'true':'false'}}" class="img-style"  ref="ope" draggable="true"
            sp-on:click="goto">
            <span class="img-container">
                <span class="img-close" sp-on:click="closeMethod"></span>
                <img class="img-size" src="{{state.configData.icon}}"  skip="{{state.configData.icon?'true':'false'}}" />
            </span>
        </span>
    </sp:if>
</div>