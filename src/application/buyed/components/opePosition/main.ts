import { setPageName } from ':common/env';
import { trackDialogShow, trackEvent } from ':common/stat';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import Texts from ':common/features/texts';
import { getSwallowConfig } from ':store/chores';
import { openWeb } from ':common/core';
interface State {
    configData: any,
    isClose: boolean
}
interface Props {
    closeReserveDialog(),
    gotoBuchgang()
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            configData: {},
            isClose: false
        };
    }
    willReceiveProps() {
        return true;
    }
    didMount() {
        setPageName(Texts.BUYED_PAGE);
    }
    closeMethod(e) {
        e.stopPropagation();  
        this.setState({isClose: true});
    }
    async getConfigData(kemu: number) {
        this.setState({configData: {}});
        const swallConfig = await getSwallowConfig({
            key: 'jk_elder_vippage_icon',
            kemu: kemu
        });
        this.setState({configData: (swallConfig && swallConfig[0]) || {}});
        if (!swallConfig) {
            return;
        }
        trackEvent({
            fragmentName1: '悬浮运营位',
            actionName: '',
            actionType: '出现'
        });
        setTimeout(()=>{
            this.drap();
        }, 500);
    }
    drap() {
        const target = this.getDOMNode().ope as HTMLElement;
        let startX = 0;
        let startY = 0;
        const parentW = window.screen.width;
        const parentH = window.screen.height;
        target.addEventListener('touchstart', function (e) {
            startX = e.targetTouches[0].pageX - this.offsetLeft;
            startY = e.targetTouches[0].pageY - this.offsetTop;
        });
        target.addEventListener('touchmove', function (e) {
            let leftX = e.targetTouches[0].pageX - startX;
            let topY = e.targetTouches[0].pageY - startY;
            const thisW = (e.targetTouches[0].target as HTMLElement).clientWidth;
            const thisH = (e.targetTouches[0].target as HTMLElement).clientHeight;
            if (leftX <= 0) {
                leftX = 0;
            }

            if (leftX >= parentW - thisW) {
                leftX = parentW - thisW;
            }

            if (topY <= 0) {
                topY = 0;
            }
            if (topY >= parentH - thisH) {
                topY = parentH - thisH;
            }
            this.style.left = leftX + 'px';
            this.style.top = topY + 'px';
        });
    }
    goto() {
        console.log('点击了');
        trackEvent({
            fragmentName1: '悬浮运营位',
            actionName: '去查看',
            actionType: '点击'
        });
        openWeb({
            url: this.state.configData.url
        });
    }
}
