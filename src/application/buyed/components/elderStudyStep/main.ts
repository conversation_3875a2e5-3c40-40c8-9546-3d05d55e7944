import { openWeb } from ':common/core';
import { AGED_500, EXAM_RECORD, ICON_SHORTHAND, KNOWLEDGE_LIST, MYERROR, TOP_LESSON } from ':common/navigate';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
interface State {
    show: boolean
}
interface Props {
    kemu: number | string
}
export default class extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            show: false
        };
    }
    gotoStudentMethod(e) {
        const dataAction = e.refTarget.getAttribute('data-action');
        openWeb({
            url: dataAction
        });
    }
    gotoToplesson() {
        const id = +this.props.kemu === 1 ? 783 : 779;
        openWeb({
            url: TOP_LESSON + '?id=' + id
        });
    }
    gotoEged() {
        openWeb({
            url: AGED_500
        });
    }
    gotoError() {
        openWeb({
            url: MYERROR
        });
    }
    gotoIcon() {
        openWeb({
            url: ICON_SHORTHAND
        });
    }
    gotoKnowledge() {
        openWeb({
            url: KNOWLEDGE_LIST + '?tab=unmastered'
        });
    }
    gotoHistory() {
        openWeb({
            url: EXAM_RECORD
        });
    }
    willReceiveProps() {
        return true;
    }
}
