.elder-study-step-container {
    margin-top: 15px;
    .elder-step1 {
        min-height: 166px;
        background: linear-gradient(100deg, #ffddc9 7%, #ffefe3 92%);
        border-radius: 10px;
        position: relative;
        &.elder-step2 {
            margin-top: 20px;
        }
        &.elder-step3 {
            margin-top: 19px;
            padding-bottom: 20px;
        }
        &.elder-step4 {
            margin-top: 20px;
        }
        &.elder-step5 {
            margin-top: 20px;
        }
    }
    .step2-bg {
        position: absolute;
        right: 0px;
        top: 0px;
        width: 65px;
        height: 55px;
        background: url(../images/step2-bg.png) no-repeat center;
        background-size: 100%;
    }
    .step-top {
        min-height: 42px;
        background: linear-gradient(96deg, #ff3f17 4%, #ff8262 95%);
        padding-left: 15px;
        display: flex;
        align-items: center;
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        text-align: right;
        color: #ffffff;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        &.step-top2 {
            flex-direction: column;
            align-items: flex-start;
            padding-top: 12px;
            padding-bottom: 12px;
        }
        .top-desc {
            display: flex;
            margin-top: 6px;
            padding-right: 20px;

            .number {
                font-size: 16px;
                font-family: SourceHanSansCN, SourceHanSansCN-Heavy;
                font-weight: 800;
                text-align: left;
                color: #ffffff;
                line-height: 24px;
                margin-right: 5px;
            }
            .tips {
                font-size: 15px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                color: #ffffff;
                line-height: 21px;
            }
        }
        .case {
            width: 38px;
            height: 20px;
            background: #ffffff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Semibold;
            font-weight: 600;
            color: #ff461e;
            margin-right: 6px;
            vertical-align: middle;
        }
    }
    .content {
        padding: 0px 15px;
        .content-title {
            font-size: 18px;
            font-family: PingFangSC, PingFangSC-Semibold;
            font-weight: 600;
            color: #501504;
            line-height: 25px;
            margin-top: 15px;
            position: relative;

            &.contentTitle3 {
                margin-top: 0px;
                padding-top: 15px;
            }
            .step1-img {
                display: inline-block;
                width: 79px;
                height: 27px;
                background: url(../images/step1.png) no-repeat center;
                background-size: 100%;
                margin-right: 5px;
                &.step2-img {
                    background: url(../images/step2.png) no-repeat center;
                    background-size: 100%;
                }
                &.step3-img {
                    background: url(../images/step3.png) no-repeat center;
                    background-size: 100%;
                }
                &.step4-img {
                    background: url(../images/step4.png) no-repeat center;
                    background-size: 100%;
                }
                &.step5-img {
                    background: url(../images/step5.png) no-repeat center;
                    background-size: 100%;
                }
            }
            .view-history {
                position: absolute;
                right: 0px;
                top: 0px;
                width: 130px;
                height: 28px;
                background: #ffceb0;
                border-radius: 25px;
                font-size: 15px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 500;
                color: #883802;
                display: flex;
                align-items: center;
                justify-content: center;
                .view-history-img {
                    display: inline-block;
                    width: 13px;
                    height: 13px;
                    background: url(../images/step4-icon.png) no-repeat center;
                    background-size: 100%;
                    margin-left: 4px;
                }
            }
            .title {
                position: relative;
                top: -3px;
            }
        }
        .content-desc {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 11px;
            padding-bottom: 20px;

            .left {
                font-size: 14px;
                font-family: PingFangSC, PingFangSC-Regular;
                font-weight: 400;
                color: #692204;
                line-height: 20px;
                flex: 1;
                .desc1 {
                    margin-bottom: 6px;
                    &.desc5 {
                        padding-right: 31px;
                    }
                }
                .desc-img {
                    display: inline-block;
                    width: 15px;
                    height: 15px;
                    background: url(../images/step1-xinzhuang.png) no-repeat
                        center;
                    background-size: 100%;
                    margin-right: 5px;
                }
                .desc1-number {
                    font-size: 15px;
                    font-family: PingFangSC, PingFangSC-Medium;
                    font-weight: 500;
                    text-align: left;
                    color: #ff542f;
                    line-height: 21px;
                    margin-top: 5px;
                }
            }
            .right {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                font-family: PingFangSC, PingFangSC-Semibold;
                font-weight: 700;
                color: #831c1c;
                width: 90px;
                height: 36px;
                border: 1px solid #831c1c;
                border-radius: 25px;
            }
            &.content-desc3 {
                background: #ffffff;
                border-radius: 8px;
                padding: 15px 10px;
                .desc1 {
                    font-size: 14px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    color: #692204;
                    line-height: 20px;
                }
                .desc3-img {
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    background: url(../images/step3-gou.png) no-repeat center;
                    background-size: 100%;
                    margin-right: 10px;
                }
                .left3 {
                    margin-right: 19px;
                }
            }
        }
        .step3-title {
            font-size: 18px;
            font-family: PingFangSC, PingFangSC-Semibold;
            font-weight: 700;
            text-align: left;
            color: #501504;
            line-height: 25px;
            margin-top: 3px;
        }
        .step4-title {
            font-size: 18px;
            font-family: PingFangSC, PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            color: #501504;
            line-height: 25px;
            margin-top: 3px;
            position: relative;
            .tag {
                position: absolute;
                right: 0px;
                bottom: 0px;
                width: 54px;
                height: 24px;
                background: linear-gradient(152deg, #ffd38b 9%, #fcbb59 95%);
                border-radius: 4px;
                font-size: 13px;
                font-family: PingFangSC, PingFangSC-Semibold;
                font-weight: 600;
                text-align: right;
                color: #501504;
                text-align: center;
                line-height: 24px;
                &.tag-no {
                    background: linear-gradient(120deg, #ff966d, #ff7547 85%);
                    border-radius: 4px;
                    color: #ffffff;
                }
            }
        }
        .content-title2 {
            position: relative;
            .tag {
                position: absolute;
                right: 0px;
                bottom: 0px;
                width: 54px;
                height: 24px;
                background: linear-gradient(152deg, #ffd38b 9%, #fcbb59 95%);
                border-radius: 4px;
                font-size: 13px;
                font-family: PingFangSC, PingFangSC-Semibold;
                font-weight: 600;
                text-align: right;
                color: #501504;
                text-align: center;
                line-height: 24px;
                &.tag-no {
                    background: linear-gradient(120deg, #ff966d, #ff7547 85%);
                    border-radius: 4px;
                    color: #ffffff;
                }
            }
        }
        .content-title5 {
            position: relative;
            padding-top: 15px;
            .tag {
                position: absolute;
                right: 0px;
                bottom: 0px;
                width: 54px;
                height: 24px;
                background: linear-gradient(152deg, #ffd38b 9%, #fcbb59 95%);
                border-radius: 4px;
                font-size: 13px;
                font-family: PingFangSC, PingFangSC-Semibold;
                font-weight: 600;
                text-align: right;
                color: #501504;
                text-align: center;
                line-height: 24px;
                &.tag-no {
                    background: linear-gradient(120deg, #ff966d, #ff7547 85%);
                    border-radius: 4px;
                    color: #ffffff;
                }
            }
        }
    }
}
