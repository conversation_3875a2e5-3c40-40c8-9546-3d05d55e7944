<import name="style" content="./main" module="S" />
<div class="{{S.elderStudyStepContainer}}">
    <div class="{{S.elderStep1}}">
        <div class="{{S.stepTop}}">
            <span class="{{S.case}}">
                注意
            </span>
            不要上来就刷题，先跟着老师学！
        </div>
        <div class="{{S.content}}">
            <div class="{{S.contentTitle}}">
                <span class="{{S.step1Img}}"></span>
                <span class="{{S.title}}">必学课程+课后练习</span>
            </div>
            <div class="{{S.contentDesc}}">
                <div class="{{S.left}}">
                    <p class="{{S.desc1}}">
                        <span class="{{S.descImg}}"></span>
                        跟着老师学必学课程
                    </p>
                    <p class="{{S.desc2}}">
                        <span class="{{S.descImg}}"></span>
                        课后作业做完
                    </p>
                </div>
                <div class="{{S.right}}" sp-on:click="gotoToplesson">去学习</div>
            </div>
        </div>
    </div>
    <div class="{{S.elderStep1}} {{S.elderStep2}}">
        <div class="{{S.step2Bg}}"></div>
        <div class="{{S.stepTop}} {{S.stepTop2}}">
            <span class="{{S.case}}">
                注意
            </span>
            <div class="{{S.topDesc}}">
                <div class="{{S.number}}">01.</div>
                <div class="{{S.tips}}">
                    做题最重要的是审题，80%的学员做错就是题目没看明白！
                </div>
            </div>
            <div class="{{S.topDesc}}">
                <div class="{{S.number}}">02.</div>
                <div class="{{S.tips}}">
                    做题考试时，遇到做错的题，记得看技巧和视频讲解，多看多记！
                </div>
            </div>
        </div>
        <div class="{{S.content}}">
            <div class="{{S.contentTitle}} {{S.contentTitle2}}">
                <span class="{{S.step1Img}} {{S.step2Img}}"></span>
                <span class="{{S.title}}">精简500题 全做对</span>
                <span class="{{S.tag}} {{props.steps[0].tag==1?S.tagNo:''}}">
                    {{props.steps[0].tag==1?'未达标':'已达标'}}
                </span>
            </div>
            <div class="{{S.contentDesc}}">
                <div class="{{S.left}}">
                    <p class="{{S.desc1}}">精简500题覆盖了驾考的所有考<br />点，500题需要全做对</p>
                    <p class="{{S.desc1Number}}">{{props.steps[0].hint}}</p>

                </div>
                <div class="{{S.right}}" sp-on:click="gotoEged">去学习</div>
            </div>
        </div>
    </div>
    <div class="{{S.elderStep1}} {{S.elderStep3}}">
        <div class="{{S.content}}">
            <div class="{{S.contentTitle}} {{S.contentTitle3}}">
                <span class="{{S.step1Img}} {{S.step3Img}}"></span>
            </div>
            <div class="{{S.step3Title}}">老是出错的、容易记混的、不好记的题，重点记</div>
            <div class="{{S.contentDesc}} {{S.contentDesc3}}">
                <div class="{{S.desc3Img}}"></div>
                <div class="{{S.left}} {{S.left3}}">
                    <p class="{{S.desc1}}">
                        做题考试中出错的题，都在【错题·收藏】中，要重点做、重点记。
                    </p>
                </div>
                <div class="{{S.right}}" sp-on:click="gotoError">去清空</div>
            </div>
            <div class="{{S.contentDesc}} {{S.contentDesc3}}">
                <div class="{{S.desc3Img}}"></div>
                <div class="{{S.left}} {{S.left3}}">
                    <p class="{{S.desc1}}">
                        有图标认识不清的，多看几遍【图标技巧】里对应的图标。
                    </p>
                </div>
                <div class="{{S.right}}" sp-on:click="gotoIcon">去学习</div>
            </div>
            <div class="{{S.contentDesc}} {{S.contentDesc3}}">
                <div class="{{S.desc3Img}}"></div>
                <div class="{{S.left}} {{S.left3}}">
                    <p class="{{S.desc1}}">
                        在【专项练习】-【考点练习】中把【未熟练掌握】的知识点做一遍。
                    </p>
                </div>
                <div class="{{S.right}}" sp-on:click="gotoKnowledge">去学习</div>
            </div>
        </div>
    </div>
    <div class="{{S.elderStep1}} {{S.elderStep4}}">
        <div class="{{S.stepTop}}">
            <span class="{{S.case}}">
                注意
            </span>
            模拟考试认真审题，看清题目再作答！
        </div>
        <div class="{{S.content}}">
            <div class="{{S.contentTitle}}">
                <span class="{{S.step1Img}} {{S.step4Img}}"></span>
                <div class="{{S.viewHistory}}" sp-on:click="gotoHistory">
                    查看历史成绩
                    <span class="{{S.viewHistoryImg}}"></span>
                </div>
            </div>
            <div class="{{S.step4Title}}">
                真实考场模拟10次95分以上
                <span class="{{S.tag}} {{props.steps[1].tag==1?S.tagNo:''}}">
                    {{props.steps[1].tag==1?'未达标':'已达标'}}
                </span>
            </div>
            <div class="{{S.contentDesc}}">
                <div class="{{S.left}}">
                    <p class="{{S.desc1}}">
                        了解真实考场的操作、界面和<br />抽题逻辑
                    </p>
                    <p class="{{S.desc1Number}}">{{props.steps[1].hint}}</p>
                </div>
                <div class="{{S.right}}" data-action="{{props.steps[1].action[URLCommon.tiku]}}"
                    sp-on:click="gotoStudentMethod">去学习</div>
            </div>
        </div>
    </div>
    <div class="{{S.elderStep1}} {{S.elderStep5}}">
        <div class="{{S.content}}">
            <div class="{{S.contentTitle}} {{S.contentTitle5}}">
                <span class="{{S.step1Img}} {{S.step5Img}}"></span>
                <span class="{{S.title}}">考前秘卷全做对</span>
                <span class="{{S.tag}} {{props.steps[2].tag==1?S.tagNo:''}}">
                    {{props.steps[2].tag==1?'未达标':'已达标'}}
                </span>
            </div>
            <div class="{{S.contentDesc}}">
                <div class="{{S.left}}">
                    <p class="{{S.desc1}} {{S.desc5}}">
                        考试之前一定要做一遍，考前秘卷包含了考试时比较容易出错的考点试题和其他学员反馈考试过程中经常遇到的难题
                    </p>
                    <p class="{{S.desc1Number}}">{{props.steps[2].hint}}</p>
                </div>
                <div class="{{S.right}}" data-action="{{props.steps[2].action[URLCommon.tiku]}}"
                    sp-on:click="gotoStudentMethod">去学习</div>
            </div>
        </div>
    </div>

</div>