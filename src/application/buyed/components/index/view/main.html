<import name="style" content="./main" module="S" />

<import name="StudentIdentified" content=":application/buyed/components/studentIdentified/main" />

<div class="ipad-box :ipad-box">
    <div class="phone-box :phone-box">
        <div class="{{S.indexPageContainer}}">
            <div class="{{S.userAvatar}}">
                <div class="{{S.avatar}}" style="background-image: url({{props.userData.avatar}})"></div>
                <div class="{{S.avatarTitle}}">
                    <div class="{{S.username}}">{{props.userData.nickname||"***"}} </div>
                    <div class="{{S.userStatus}}">
                        <sp:if value="props.kemuAllList.length>0">
                            <img class="{{S.iconSpan}}" src="{{props.kemuAllList[0].icon}}" />
                            <sp:else />
                            <sp:each for="{{props.bagesList}}">
                                <img sp:if="{{$value.buyStatus!==0&&$value.icon}}" class="{{S.iconSpan}}"
                                    src="{{$value.icon}}" />

                            </sp:each>
                        </sp:if>
                        <sp:if value="true || state.isStudentIdentified">
                            <com:StudentIdentified />
                        </sp:if>
                    </div>
                    <sp:if value='{{props.isClaimsRight && !URLCommon.isScore12}}'>
                        <sp:if value="props.kemu !== 3 || (props.kemu == 3 && props.ke3ClaimOpen)">
                            <span sp-on:click="gotoBuchgang"
                                class="{{S.reserveBtn}} {{props.unreservedKemu.indexOf(props.kemu)===-1?S.topMargin:''}}"
                                ref="reserve-btn">我的不过补偿{{props.vipClaimsActiveInfo.amount &&
                                props.vipClaimsActiveInfo.amount + '元' }}<span class="{{S.imgIcon}}"></span></span>
                            <span class="{{S.tipsButtonContainer}}">
                                <sp:if value='{{props.unreservedKemu.indexOf(props.kemu)!==-1}}'>
                                    <span class="{{S.tipsButton}}">为了不影响您的正常补偿请尽快填写</span>
                                    <span class="{{S.sanjiaoxiang}}"></span>
                                </sp:if>
                            </span>
                        </sp:if>

                    </sp:if>

                </div>
            </div>
            <div class="{{S.overfolwDiv}}">
                <div class="{{S.tabContainer}}" id="tabContainerScroll">
                    <div class="{{S.tabScroll}}" ref="tabScroll">
                        <sp:each for='{{props.bagesList}}'>
                            <div data-index="{{$value.groupKey}}" data-action="{{$value.detailUrl}}"
                                id="tabNumber{{$index+1}}" data-kemu="{{$value.kemu}}"
                                data-buyStatus="{{$value.buyStatus}}" sp-on:click="tabClickMethod"
                                class="{{S.tabItem}} {{URLCommon.isZigezheng&&S.zigezhengbg}} {{S['v'+($index+1)]}} {{props.kemu==$value.kemu?S.on:''}}">
                                <span
                                    class="{{S.tag}} {{$value.buyStatus==-1&&S.tagGuoQi}} {{$value.buyStatus==0&&S.tagWg}}"></span>
                                <sp:if value='{{$value.buyStatus==-1}}'>
                                    <span class="{{S.timeGuoQi}}">{{$value.expirtStringTime}}已过期</span>
                                </sp:if>
                                <span class="{{S.tag}}" sp:if="$value.buyStatus==1"></span>
                                <div class="{{S.title}}">{{$value.title}}</div>
                                <sp:if value='{{$value.buyStatus==1}}'>
                                    <div class="{{S.time}} {{S.time1}}" sp:if="$value.buyStatus==1">
                                        {{$value.expirtStringTime}}到期
                                    </div>
                                    <sp:else />
                                    <div class="{{S.goBuyButton}}">
                                        <div class="{{S.goBuyButtonSon}}">{{$value.price/100}}元开通</div>
                                    </div>
                                </sp:if>
                            </div>
                        </sp:each>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
