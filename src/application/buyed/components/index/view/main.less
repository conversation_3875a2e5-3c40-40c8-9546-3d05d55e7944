.ipad-box {
    background: linear-gradient(to right, #383B4C, #20222F); //backgournd可以设置所有背景属性
}

.index-page-container {
    height: auto;
    width: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    background-image: url("../../../images/vip-buyed/vip_top_bg.png");
    padding-top: 90px;

    .user-avatar {
        width: 100%;
        display: flex;
        align-items: center;
        position: relative;
        padding-left: 15px;
        padding-right: 15px;

        .sanjiaoxiang {
            position: absolute;
            right: 35/2px;
            top: 50/2px;
            display: inline-block;
            width: 0px;
            height: 0px;
            /*兼容低版本*/
            line-height: 0px;
            font-size: 0px;
            border-top: 10/2px solid rgba(0, 0, 0, 0.5);
            border-left: 10/2px solid transparent;
            border-bottom: 10/2px solid transparent;
            border-right: 10/2px solid transparent;
        }

        .tips-button {
            display: inline-block;
            height: 50/2px;
            line-height: 50/2px;
            background: rgba(0, 0, 0, 0.5);
            opacity: 0.89;
            font-size: 20/2px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            padding: 0px 10/2px;
            z-index: 22;
        }

        .tips-button-container {
            position: absolute;
            right: -15px;
            top: -20/2px;
            transition: all 1s;
            animation: jump2 0.5s ease-in-out infinite alternate;
        }

        @keyframes jump2 {
            from {
                transform: translateY(-15px);
            }

            to {
                transform: translateY(-2px);
            }
        }

        .reserveBtn {
            position: absolute;
            right: -15px;
            top: 20px;
            display: flex;
            font-size: 13px;
            line-height: 13px;
            height: 23px;
            padding: 0 10px 0 15px;
            background: linear-gradient(101deg, #f4cba8 9%, #e8b186 90%);
            border-radius: 12px 0px 0px 12px;
            color: #28262b;
            align-items: center;
            justify-content: center;

            &.top-margin {
                top: 20/2px;
            }

            .img-icon {
                display: inline-block;
                width: 6px;
                height: 10px;
                background: url(../images/right_icon.png) no-repeat;
                background-size: 100% 100%;
                margin-left: 4px;
            }
        }

        .avatar {
            flex: 0 0 96/2px;
            height: 96/2px;
            border: 2/2px solid #ffffff;
            border-radius: 50%;
            background-repeat: no-repeat;
            background-size: 96/2px 96/2px;
            background-position: center center;
        }

        .avatar-title {
            flex: 1;
            padding-left: 20/2px;
            position: relative;

            .username {
                font-size: 40/2px;
                font-weight: 500;
                color: #ffffff;
                line-height: 56/2px;
            }

            .user-status {
                display: flex;
                margin-top: 8/2px;
                position: relative;

                .iconSpan {
                    display: inline-block;
                    width: auto;
                    height: 36/2px;
                    margin-right: 20/2px;
                    vertical-align: middle;
                }
            }
        }
    }

    .tab-scroll {
        max-width: 1400/2px;
        height: 165/2px;
        position: absolute;
        top: 0px;
        left: 30/2px;
        padding-right: 30/2px;
        transition: alll 0.5s ease;
        display: flex;
        align-items: center;

        &::-webkit-scrollbar {
            background-color: transparent;
        }

        ::-webkit-scrollbar {
            // 直接复制黏贴到样式页.css，完美解决
            display: none;
            /* background-color:transparent; */
        }
    }

    .overfolwDiv {
        overflow-y: hidden;
        height: 220/2px;
    }

    .tab-container {
        margin-top: 18px;
        height: 165/2px;
        padding-left: 30/2px;
        overflow-y: hidden;
        overflow-x: scroll;
        position: relative;
        white-space: nowrap;

        &::-webkit-scrollbar {
            display: none;
        }

        ::-webkit-scrollbar {
            // 直接复制黏贴到样式页.css，完美解决
            display: none;
            background-color: transparent;
        }

        .tab-item {
            display: inline-block;
            vertical-align: middle;
            width: 280/2px;
            height: 140/2px;
            margin-right: 14/2px;
            background-repeat: no-repeat;
            background-size: 280/2px 140/2px;
            background-position: center center;
            position: relative;
            border-radius: 11/2px;

            &:last-child {
                margin-right: 0px;
            }

            .time-guo-qi {
                position: absolute;
                top: 6px;
                right: 0px;
                font-size: 18/2px;
                transform: scale(0.9);
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #5c6476;
            }

            .tag {
                width: 88/2px;
                height: 32/2px;
                background-repeat: no-repeat;
                background-size: 88/2px 32/2px;
                background-position: center center;
                position: absolute;
                top: 0px;
                left: 0px;
                background-image: url("../../../images/vip-bages/jk-bq-ykt.png");

                &.tag-guoQi {
                    background-image: url("../../../images/vip-bages/jk-bq-ygq.png");
                }

                &.tag-wg {
                    background-image: url("../../../images/vip-bages/jk-bq-wkt.png");
                }
            }

            .title {
                font-size: 30/2px;
                font-weight: 500;
                color: #2f3646;
                line-height: 42/2px;
                text-align: center;
                padding-left: 15/2px;
                padding-right: 15/2px;
                margin-top: 32/2px;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
            }

            .go-buy-button {
                width: 140/2px;
                height: 44/2px;
                line-height: 44/2px;
                margin: 10/2px auto 0px auto;
                background-image: linear-gradient(90deg,
                        rgba(255, 242, 216, 1),
                        rgba(255, 255, 255, 0.35));
                padding: 2/2px;
                border-radius: 22/2px;
                text-align: center;
                box-sizing: border-box;

                .go-buy-button-son {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 22/2px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #701e06;
                    background: linear-gradient(99deg,
                            #ffd493 0%,
                            #e5ad4c 100%);
                    border-radius: 22/2px;
                }
            }

            .time {
                font-size: 18/2px;
                font-weight: 400;
                color: #3e424b;
                line-height: 28/2px;
                position: absolute;
                bottom: 34/2px;
                display: flex;
                justify-content: space-between;
                width: 100%;
                padding: 0px 15/2px;

                &.time1 {
                    width: 100%;
                    padding: 0px 0px;
                    font-size: 18/2px;
                    justify-content: center;
                }

                .price {
                    font-size: 32/2px;
                    font-style: normal;
                }

                .tag-name {
                    font-size: 20/2px;
                    font-weight: 600;
                }
            }

            &.v1 {
                background-image: url("../../../images/vip-bages/VIP-k1-x.png");
            }

            &.v2 {
                background-image: url("../../../images/vip-bages/VIP-k2-x.png");
            }

            &.v3 {
                background-image: url("../../../images/vip-bages/VIP-k3-x.png");
            }

            &.v4 {
                background-image: url("../../../images/vip-bages/VIP-k4-x.png");
            }

            &.v5 {
                background-image: url("../../../images/vip-bages/VIP-k1-x.png");
            }

            &.v6 {
                background-image: url("../../../images/vip-bages/VIP-k2-x.png");
            }

            &.on {
                height: 160/2px;
                width: 320/2px;
                background-size: 320/2px 160/2px;

                .title {
                    font-size: 34/2px;
                    margin-top: 40/2px;
                    line-height: 48/2px;
                }

                .time-guo-qi {
                    font-size: 20/2px;
                }

                .time {
                    bottom: 30/2px;
                    font-size: 22/2px;
                    width: 100%;
                    text-align: center;
                    margin-left: 0px;
                }
            }

            &.v1 {
                background-image: url("../../../images/vip-bages/VIP-k1-d.png");
            }

            &.v2 {
                background-image: url("../../../images/vip-bages/VIP-k2-d.png");
            }

            &.v3 {
                background-image: url("../../../images/vip-bages/VIP-k3-d.png");
            }

            &.v4 {
                background-image: url("../../../images/vip-bages/VIP-k4-d.png");
            }

            &.zigezhengbg {
                height: 145/2px;
                width: 340/2px;
                background-size: 340/2px 145/2px;
                background-image: url("../../../images/vip-buyed/zigezhneg-vip-bg.png");
            }
        }
    }
}
