import { GoodsInfo } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { openVipWebView } from ':common/core';
import { BGBC_URL } from ':common/navigate';
import { URLParams } from ':common/env';
interface State {
}
interface Props {
    goodsList: GoodsInfo[]
    comparePricePool: any[]
    tiku: any,
    reseveStatus: string | number,
    kemu: string | number,
    tabClickMethod(e: Event),
    gotoBuchgang(),
    ke3ClaimOpen: boolean
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
        };
    }
    tabClickMethod(e) {
        this.props.tabClickMethod(e);
    }
    gotoBuchgang() {
        this.props.gotoBuchgang();
    }
    willReceiveProps() {
        return true;
    }
}
