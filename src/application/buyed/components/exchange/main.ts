import { Component } from '@simplex/simple-core';
import View from './view/main.html';
interface State {
}
interface Props {
    closeExchangeDialog()
}
export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
        };
    }
    close(e) {
        e.stopPropagation();
        this.props.closeExchangeDialog();
    }
    willReceiveProps() {
        return true;
    }
}
