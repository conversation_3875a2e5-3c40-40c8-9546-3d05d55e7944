import { getAuthToken, getSystemInfo, openVipWebView, openWeb, webClose } from ':common/core';
import { CarType, PayType, Platform, setPageName, URLCommon, URLParams, Version } from ':common/env';
import { hiddenIOSPayButton, iosPay } from ':common/features/ios_pay';
import { getDefaultPayType, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackEvent, trackGoPay, trackPageLoad } from ':common/stat';
import { dateFormat, getCityName } from ':common/utils';
import Header from ':component/header/main';
import PayDialog from ':component/payDialog/main';
import StudyStep from ':application/buyed/components/studyStep/main';
import MemberRecommend from ':application/buyed/components/memberRecommend/main';
import ReserveDialog from ':application/buyed/components/reserveDialog/main';
import BizVersionTipsDialog from ':application/buyed/components/bizVersionTipsDialog/main';
import UpgradeGuide from ':application/buyed/components/upgradeGuide/main';
import PackageRecommend from ':application/buyed/components/packageRecommend/main';
import XuanFuUpgradeGuide from ':application/buyed/components/xuanFuUpgradeGuide/main';
import { getPayOutInfo, getRights, getVipBages, hasClaimsRight, hasClaimsVipActiveInfo, userInit } from ':store/buyed';
import { getGroupSessionInfo, GoodsInfo, GroupKey, comparePrice } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { checkRestriction, getSwallowConfig } from ':store/chores';
import { getBestCoupon } from ':common/features/coupon';
import { MCProtocol } from '@simplex/simple-base';
const swallowKey = 'jk_vip_upgrade';
import Texts from ':common/features/texts';
import { BGBC_URL, HELP_VIP, OUTLIMIT, PROTOCOL1_URL } from ':common/navigate';
import { PayStatProps } from ':component/buyButton/main';
import { onWebBack } from ':common/features/persuade';
import { onPageShow } from ':common/features/page_status_switch';
import { setEmbeddedHeight } from ':common/features/embeded';
import { reload } from ':common/features/jump';
import jump from ':common/features/jump';
import { isKe3ClaimOpen } from ':store/kemu3';

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    prevScrollTop: number,
    showFooter: boolean,
    kemu: number | string,
    // 全科,根据是否有数据这个判断是否有直播课
    kemuAllList: any[],
    // 徽章和卡片数据
    bagesList: any[],
    // 填写基本信息的状态 0是已填写 ，1是未填写
    reseveStatus: string | number,
    // 填写基本信息，数组长度大于0，代表没有填写基本信息
    unreservedKemu: any[],
    // 判断三套卷是否有权限去查看
    isScore12View: boolean,
    // 判断科目三，考场路线视频是否开通
    isRouteVideo: boolean,
    userData: {
        nickname: string,
        avatar: string
    },
    // 没有补偿
    nonecompensate: any,
    // 远程配置
    swallConfig: any,
    // 是否有不过补偿权益
    isClaimsRight: any,
    // vip活动的相关信息
    vipClaimsActiveInfo: {
        activityClaims?: boolean
        amount?: number
    },
    showExchangeDialog: boolean,
    ke3ClaimOpen: boolean,
    userCityName: ''
    moreServiceList: any[]
}
let timer;
// 必须放到最前面，组件内部有加载点,会有pageName没有赋值的情况
setPageName(Texts.BUYED_PAGE);

export default class extends Application<State> {
    declare children: {
        header: Header;
        payDialog: PayDialog;
        // 学习步骤
        studyStep: StudyStep;
        // 会员推荐课程
        memberRecommend: MemberRecommend;
        // 填写信息弹窗
        reserveDialog: ReserveDialog;
        // 版本强制更新弹窗
        bizVersionTipsDialog: BizVersionTipsDialog;
        // 半截升级引导弹窗
        upgradeGuide: UpgradeGuide;
        // 会员专属优惠套餐推荐
        packageRecommend: PackageRecommend;
        // 升级引导悬浮框
        xuanFuUpgradeGuide: XuanFuUpgradeGuide;
    };
    // 半截弹窗关闭储存时间的key,根据这个取值，判断是否展示半截弹窗的条件之一，区分车型
    get saveUpgradeGuideKey() {
        return URLCommon.tiku + '&&&goodsInfoUpdateGuideDialog';
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo?.groupKey];
    }

    get compensation() {
        const money = {
            1: 50,
            2: 40,
            4: 50
        };
        return money[this.state.kemu];
    }

    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            goodsInfoPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            // 滚动距离
            prevScrollTop: 0,
            showFooter: false,
            kemu: '',
            kemuAllList: [],
            bagesList: [],
            reseveStatus: 'default',
            unreservedKemu: [],
            isScore12View: false,
            isRouteVideo: false,
            swallConfig: {},
            nonecompensate: [],
            userData: {
                nickname: '***',
                // avatar: ''
                avatar:
                    'https://jiakao-audit.image.mucang.cn/jiakao-audit/2022/01/24/11/8a66545b8d3a49f5af0b3dc1c5fd87f5.png'
            },
            isClaimsRight: false,
            vipClaimsActiveInfo: {},
            showExchangeDialog: false,
            ke3ClaimOpen: false,
            userCityName: '',
            moreServiceList: []
        };
    }
    async didMount() {
        hiddenIOSPayButton();

        await this.checkRestriction();

        await this.getSystemInfo();
        if (!Version.bizVersion || Version.bizVersion < 3) {
            this.children.bizVersionTipsDialog.show();
            return;
        }
        onPageShow(async () => {
            await this.vipBagesRequest();
            this.payOutInfoRequest('show');
        });
        MCProtocol.Core.Web.setting({
            titleBar: false,
            toolbar: false,
            menu: false,
            button: false,
            orientation: 'portrait',
            statusBarTheme: 'light'
        });
        this.getUserData();
        // app代理方法
        this.appEventProxy();

        this.swallowConfigRequest(swallowKey, 1).then((swallConfig) => {
            if (swallConfig?.show) {
                const goodsInfoPool: GoodsInfo[] = [];
                goodsInfoPool.push({
                    groupKey: swallConfig.groupKey
                } as GoodsInfo);
                this.setState({ swallConfig, goodsInfoPool }, () => {
                    this.getGoodInfo();
                });
            }
        });

        await this.vipBagesRequest();
        await this.payOutInfoRequest();
        // 页面进出时长打点
        trackPageLoad();
        if (Platform.isIOS) {
            // ios禁用优化返回手势
            onWebBack(() => {
                return;
            });
        }
        // //版本bizVersion大于等于8才支持的协议
        if (Version.bizVersion >= 8) {
            MCProtocol['jiakao-global'].fixVipPermissions({
                kemuStyle: URLCommon.kemu,
                carStyle: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode
            });
        }

        this.doExchange();
    }
    async getSystemInfo() {
        const systemInfo = await getSystemInfo();
        const userCityCode = systemInfo._userCity || systemInfo._cityCode;
        const userCityName = await getCityName(+userCityCode) || '';

        this.setState({
            userCityName
        });

        const res = await isKe3ClaimOpen({ cityCode: userCityCode });
        this.state.ke3ClaimOpen = res.value;
        this.setState({
            ke3ClaimOpen: res.value
        });
    }
    /** 进入页面先判断是否设备超限 */
    async checkRestriction() {
        const { pass } = await checkRestriction();
        if (!pass) {
            jump.replace(OUTLIMIT);
        }
    }
    // 其他页面进入已购买页，拦截了物理返回键，导致已购买物理返回键点击没反应
    appEventProxy() {

        onWebBack(() => {
            webClose();
            return Promise.resolve();
        });

    }
    // 判断半截弹窗升级是否弹出
    isShowUpgradeGuide() {
        const time = localStorage.getItem(this.saveUpgradeGuideKey);
        if (!time) {
            return true;
        }
        const now = new Date().getTime();
        const dayTime = this.state.swallConfig.disabledDays * 24 * 60 * 60 * 1000;
        // var dayTime = this.state.swallConfig.disabledDays * 2 * 1000;
        const totalTime = +time + dayTime;
        return now >= totalTime;

    }
    // 关闭填写基本信息的弹窗，满足条件，半截弹窗才弹出，解决弹窗冲突的问题
    closeReserveDialog = () => {
        if (this.isShowUpgradeGuide() && this.nowGoodInfo?.upgrade) {
            this.children.upgradeGuide?.show();
        }
    }
    gotoBuchgang = () => {
        let paramsStr = '?kemuStyle=' + this.state.kemu + '&kemu=' + this.state.kemu + '&';
        for (const key in URLParams) {
            if (key !== 'kemuStyle' && key !== 'kemu') {
                paramsStr += key + '=' + encodeURIComponent(URLParams[key]) + '&';
            }
        }
        openVipWebView({
            url: (BGBC_URL + paramsStr)
        });
    }
    onBuchgangClick() {
        trackEvent({
            fragmentName1: '不过补偿申请入口',
            actionName: '去申请',
            actionType: '点击'
        });
        this.gotoBuchgang();
    }
    async vipBagesRequest() {
        const vipBagesData = await getVipBages();
        let kemuAllList = [];
        // 徽章list
        let bagesList = [];
        let kemu = this.state.kemu ? this.state.kemu : URLCommon.kemu;
        // kemuStyle = 6的是满分学习的，因为vip徽章只有1到4，所以处理为1
        // 资格证只有kemuStyle为1,但传的参数kemuStyle可能不为1，处理下默认为1
        if (+URLParams.kemuStyle === 6 || URLCommon.isZigezheng) {
            kemu = 1;
        }
        vipBagesData && vipBagesData.forEach((ele) => {
            ele.expirtStringTime = dateFormat(ele.expireTime, 'yyyy.MM.dd');
            // 全科徽章
            if (ele.kemu === 0 && ele.buyStatus === 1) {
                kemuAllList.push(ele);
            }
            // 徽章list(不包含kemu为0的全科)
            if (ele.kemu !== 0) {
                bagesList.push(ele);
            }
        });
        // // 针对小包jiakaokemuer,jiakaokemusan进行处理，只展示当前包的徽章
        const xiaobaoMap = {
            jiakaokemuyi: [1, 4],
            jiakaokemuer: [2],
            jiakaokemusan: [3]
        };
        if (xiaobaoMap[URLParams._appName]) {
            bagesList = bagesList.filter((ele) => {
                return xiaobaoMap[URLParams._appName].includes(+ele.kemu);
            });
            // jiakaokemuer,jiakaokemusan没有全科
            kemuAllList = [];
        }
        // 摩托车8bizVersion只需要展示科一，科四的数据卡片和徽章
        if (URLCommon.tiku === CarType.MOTO && Version.bizVersion < 8) {
            bagesList = bagesList.filter((ele) => {
                return +ele.kemu === 1 || +ele.kemu === 4;
            });
        }
        // 先找state.kemu有值的(因为要保留高亮状态)或是url地址上传过来的科目高亮
        let highBagesList = bagesList.filter((ele) => {
            return +ele.kemu === kemu && +ele.buyStatus === 1;
        });
        // 没有找到的话，找默认购买状态下的高亮
        if (highBagesList && highBagesList.length <= 0) {
            highBagesList = bagesList.filter((ele) => {
                return +ele.buyStatus === 1;
            });
        }
        // 以上都没找到的话，默认徽章的第一个数据高亮
        const highlightkemu = highBagesList.length > 0 ? highBagesList[0].kemu : (bagesList[0] && bagesList[0].kemu);
        this.switchKemu(highlightkemu);
        this.setState({
            kemuAllList,
            bagesList,
            kemu: highlightkemu
        }, () => {
            // 因为科目3，科目4不在屏幕中，所以需要滚动到科目3，科目4可见
            if (+highlightkemu === 4 || +highlightkemu === 3) {
                setTimeout(() => {
                    document.getElementById('tabContainerScroll').scrollLeft =
                        document.getElementById('tabNumber' + highlightkemu) &&
                        document.getElementById('tabNumber' + highlightkemu).offsetLeft;
                }, 500);
            }
            this.children.memberRecommend.recommendVipRequest(+highlightkemu);
            this.children.packageRecommend.getRecommendGoodsData(+highlightkemu);
            this.children.studyStep.getClientData(+highlightkemu);
            // this.children.opePosition.getConfigData(+highlightkemu);
            this.getRightsRequest(+highlightkemu);
            this.getClaimsRight(+highlightkemu);
            this.getClaimsVipActiveInfo(+highlightkemu);

            this.getMoreServerList();
        });

    }
    async payOutInfoRequest(source?) {
        const payOutInfoData = await getPayOutInfo();
        let reseveStatus;
        const itemList = payOutInfoData;
        const currentKemu = this.state.kemu ? +this.state.kemu : +URLCommon.kemu;
        if (itemList.length > 0) {
            reseveStatus = 1;

            // 有选择微信或支付宝支付方式的弹窗弹出,不展示填写信息弹窗,弹窗冲突
            // onvisibilitychange升级半截弹窗弹出就不展示填写信息弹窗，场景，去支付，取消支付返回
            const upgradeDialog = this.children.upgradeGuide?.state?.showDialog || false;
            // 取消支付 弹选择支付弹窗
            const payDialog = this.children.payDialog.children.dialog.state?.visible || false;
            // 会员推荐升级取消支付 弹选择支付弹窗
            const payChildrenDialog = this.children.packageRecommend?.children.payDialog.children.dialog.state?.visible;

            if ((upgradeDialog && this.nowGoodInfo?.upgrade) || payDialog || payChildrenDialog) {
                this.children.reserveDialog.hide();
            } else if (itemList.indexOf(currentKemu) !== -1 && !URLCommon.isScore12 && source !== 'show') {
                if (currentKemu !== 3 || (currentKemu === 3 && this.state.ke3ClaimOpen)) {
                    this.children.reserveDialog.show();
                }
            }

        } else {
            reseveStatus = 0;
        }
        this.setState({
            unreservedKemu: itemList || [],
            reseveStatus: reseveStatus
        });

    }
    // 获取是否有补偿权益
    async getClaimsRight(kemu: number) {
        this.setState({ isClaimsRight: false });
        const data = await hasClaimsRight({ kemu });
        this.setState({ isClaimsRight: data.value });

    }
    // VIP活动补偿的数据
    getClaimsVipActiveInfo(kemu: number) {
        hasClaimsVipActiveInfo({ kemu }).then(data => {
            this.setState({
                vipClaimsActiveInfo: data
            });
        });
    }
    // 获取权益接口
    async getRightsRequest(kemu: number) {
        const data: any = await getRights({ kemu });
        // 去除没有补偿，只留不是补偿的数据（有补偿数据（ele.kemu不为空并且等于当前高亮的kemu））
        const nonecompensate = data.filter((ele) => {
            // 判断科目三，考场路线视频是否开通
            if (ele.uniqueCode === 'goods-route-video' || ele.uniqueCode === 'goods-route-video1'
            ) {
                this.setState({
                    isRouteVideo: true
                });
            }
            // 判断三套卷是否有权限去查看
            if (
                ele.uniqueCode === 'goods-exam-kc' ||
                ele.uniqueCode === 'goods-exam-hc' ||
                ele.uniqueCode === 'goods-exam' ||
                ele.uniqueCode === 'goods-exam-mt'
            ) {
                this.setState({
                    isScore12View: true
                });
            }
            return ele;
            // return (
            //     (ele.uniqueCode === 'goods-lightSimulation-huoche' &&
            //         Version.bizVersion >= 5 &&
            //         !ele.kemu) ||
            //     (ele.uniqueCode !== 'goods-lightSimulation-huoche' && !ele.kemu)
            // );
        });
        this.setState({
            nonecompensate
        });
    }
    // 调用切换kemu的协议
    switchKemu(kemu: number) {
        let paramsStr = location.pathname.split('/').at(-1) + '?';

        const newURLParams = Object.assign(URLParams, {
            kemuStyle: kemu
        });

        for (const key in newURLParams) {
            paramsStr += key + '=' + encodeURIComponent(newURLParams[key]) + '&';
        }
        // 把全局的kemuStyle给修改为当前的点击的kemu
        URLCommon.kemu = kemu;
        history.replaceState(null, null, paramsStr);
        if (!URLCommon.isZigezheng) {
            // 切换徽章,对应切换外面的kemu,客户端需要知道当前的kemu
            if (Platform.isMuCang) {
                openWeb({
                    url: 'http://jiakao.nav.mucang.cn/switchKemu?kemu=' + kemu,
                    params: {
                        kemuStyle: kemu
                    }
                });
            }
        }
    }
    async swallowConfigRequest(key, kemu) {

        const swallConfig = await getSwallowConfig({
            key,
            kemu
        });
        return swallConfig;
    }
    getMoreServerList() {
        const { kemu } = this.state;

        this.setState({
            moreServiceList: []
        });

        setTimeout(() => {
            this.swallowConfigRequest('jk_bought_more_servcices', kemu).then(data => {
                this.setState({
                    moreServiceList: data || []
                });
            });
        }, 100);
    }
    tabClickMethod = (e) => {
        const refTarget = e.refTarget;
        const kemu = +refTarget.getAttribute('data-kemu');
        const dataAction = refTarget.getAttribute('data-action');
        const buyStatus = refTarget.getAttribute('data-buyStatus');
        this.switchKemu(kemu);
        if (+buyStatus !== 1) {
            // 未购买和已过期的，跳到对应的购买页
            setTimeout(() => {
                openWeb({
                    url: dataAction
                });
            }, 500);
            return;
        }

        this.setState({
            kemu: kemu
        });

        this.children.studyStep.getClientData(+kemu, 'click');
        this.children.memberRecommend.recommendVipRequest(+kemu);
        this.children.packageRecommend.getRecommendGoodsData(+kemu);
        // this.children.opePosition.getConfigData(+kemu);
        this.getRightsRequest(+kemu);
        this.getClaimsRight(+kemu);
        this.getClaimsVipActiveInfo(+kemu);
        this.getMoreServerList();
    }
    getUserData() {
        MCProtocol.Core.User.get((data: any) => {
            this.setState({
                userData: data.data
            });
        });
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });
        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo) => {
                newGoodsPool.push(goodInfo);
            });
            this.setState({
                goodsInfoPool: newGoodsPool
            });

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getComparePrice();
                // // 解决填写信息和升级引导弹窗冲突问题，初始化有填写信息弹窗就不展示，关闭填写信息弹窗在展示
                if (this.isShowUpgradeGuide() && !this.children.reserveDialog.state.show && this.nowGoodInfo?.upgrade) {
                    this.children.upgradeGuide?.show();
                }
                if (this.nowGoodInfo?.upgrade) {
                    this.children.xuanFuUpgradeGuide?.show();
                }
            }, 60);
        });

    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    // 比价必须在商品详情之后
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice,
                        groupItems: item.groupItems
                    };
                }
            });
            this.setState({ comparePricePool });
        });
    }
    pageScroll(e) {
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            this.children.header.setScrollBg(prevScrollTop, '#000000');
            this.setState({
                prevScrollTop
            });

            const compensationDom = this.getDOMNode().compensation as HTMLElement;
            if (prevScrollTop + window.innerHeight >= compensationDom.offsetTop) {
                trackEvent({
                    fragmentName1: '不过补偿申请入口',
                    actionType: '曝光'
                });
            }

        }, 30);

    }
    payBtnCall = (e) => {
        const from = e.refTarget.getAttribute('data-from');
        const fragmentName1 = e.refTarget.getAttribute('data-fragmentName1');
        const fragmentName2 = e.refTarget.getAttribute('data-fragmentName2');
        const noCoupCode = e.refTarget.getAttribute('data-noCoupCode');
        // 会员推荐课程的下的购买没有优惠券),但是会员推荐课程，半截弹窗(有优惠券购买)，以及悬浮框用的都是用一个商品groupkey
        const isNoCoupCode = noCoupCode === 'noCoupCode';
        this.onPayBtnCall({
            stat: {
                from,
                fragmentName1,
                fragmentName2
            }
        }, isNoCoupCode);
    }
    onPayBtnCall = (config?: { stat: any }, isNoCoupCode?: boolean) => {
        const { tabIndex, goodsInfoPool } = this.state;
        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                ...config?.stat
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            // 点击支付按钮打点
            trackGoPay({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                ...config?.stat
            });
            this.onPay({ stat: config?.stat }, isNoCoupCode);
        }
    }
    pay = async (stat: PayStatProps) => {
        this.onPay({
            stat: {
                ...stat
            }
        });
    }
    openProtocolMethod() {
        openWeb({
            url: PROTOCOL1_URL,
            title: '查看《 ' + Texts.productName + ' VIP服务协议》'
        });
    }
    onPay = async (config: { stat: PayStatProps }, isNoCoupCode?: boolean) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo?.groupKey,
            sessionIds: this.nowGoodInfo?.sessionIds,
            activityType: this.nowGoodInfo?.activityType,
            couponCode: isNoCoupCode ? '' : this.nowCouponInfo?.couponCode,
            ...config.stat
        }, false).then(() => {
            reload();
        }).catch(async () => {
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: goodsInfoPool[tabIndex].payPrice,
                onPay: () => {
                    this.pay(config.stat);
                },
                ...config.stat
            });
        });
    }
    backCall = () => {
        webClose();
    }
    async doExchange() {
        const authToken = await getAuthToken();
        if (authToken) {
            const initData = await userInit();
            const cityBound = initData.cityBound;
            if (cityBound) {
                setTimeout(() => {
                    this.setState({
                        showExchangeDialog: cityBound
                    }, () => {
                        trackEvent({
                            fragmentName1: '自动兑换弹窗',
                            actionType: '出现'
                        });
                    });
                }, 50);
            }
        }
    }
    closeExchangeDialog() {
        this.setState({
            showExchangeDialog: false
        });
    }
}
