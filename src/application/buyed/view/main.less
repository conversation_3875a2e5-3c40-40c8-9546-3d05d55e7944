body {
    max-width: none;
}
.page-buyed {
    background: #fff;
    .page-header {
        position: absolute;
        z-index: 1000;
        top: 0;
        left: 0;
        width: 100%;
        .right {
            width: 44/2px;
            height: 44/2px;
            background: url("../images/vip-buyed/vip_ic_kefu.png") no-repeat
                center;
            background-size: 44/2px 44/2px;
        }

        .right-container-o {
            margin-right: -30/2px;
            padding-right: 30/2px;
            padding-top: 30/2px;
            padding-bottom: 30/2px;
            padding-left: 30/2px;
        }
    }

    .body-panel {
        flex: 1;
        overflow-y: scroll;

        >.ipad-box-bg{
            background: linear-gradient(to right, #FFFFFF, #EEF9FF); 
        }
        .content-container {
            padding: 0px 30/2px 0px 30/2px;
            background: #ffffff;
            overflow: hidden;
            .compensation{
                width: 345px;
                height:70px;
                margin-top: 15px;
                background:url(../images/vip-buyed/<EMAIL>) no-repeat;
                background-size: cover;
                padding: 15px 0 0  85px;
                position: relative;
                .title{
                    font-size: 15px;
                    font-weight: bold;
                    color: #a95122;     
                    line-height: 21px;
                }
                .dec{
                    font-size: 13px;
                    color: #6e6e6e;
                    line-height: 18px;
                    padding-right: 1rem;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    line-clamp: 1;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
                .btn{
                    width: 88px;
                    height: 34px;
                    background: linear-gradient(102deg,#f5cbad 5%, #efaf8b 91%);
                    border-radius: 20px;
                    position: absolute;
                    top: 18px;
                    right: 9px;
                    font-size: 15px;
                    font-weight: bold;
                    text-align: center;
                    color: #692204;
                    line-height: 34px;
                }
            }
            .compensation-old{
                .title{
                    font-size: 17px;
                }
                .dec{
                    font-size: 15px;
                }
                .btn{
                    font-size: 18px;
                }
            }
        }
        .view-rule-style-qi {
            text-align: center;
            background: #f0f2f5;
            padding-bottom: 10px;
            padding-bottom: calc(
                ~"10px + constant(safe-area-inset-bottom)/2"
            ) !important;
            /* 兼容 iOS < 11.2 */
            padding-bottom: calc(~"10px + env(safe-area-inset-bottom)/2");
            .view-title {
                font-size: 26/2px;
                padding-top: 25/2px;
                font-weight: 400;
                color: #8f5333;
            }

            .isandrios-margin {
                padding-top: 64/2px;
                padding-bottom: 132/2px;
            }
        }
    }

    .footer {
        position: relative;
        z-index: 10;

        .go_coupon {
            font-size: 0.12rem;
            line-height: 0.3rem;
            color: #741b01;
            text-align: right;
        }
    }
}
