<import name="style" content="./main" />
<import name="header" content=":component/header/main" />
<import name="IndexPage" content=":application/buyed/components/index/main" />
<import name="StudyStep" content=":application/buyed/components/studyStep/main" />
<import name="ReserveDialog" content=":application/buyed/components/reserveDialog/main" />
<import name="ChangeSceneCodeDialog" content=":application/buyed/components/changeSceneCodeDialog/main" />
<import name="BizVersionTipsDialog" content=":application/buyed/components/bizVersionTipsDialog/main" />
<import name="MemberRecommend" content=":application/buyed/components/memberRecommend/main" />
<import name="UpgradeGuide" content=":application/buyed/components/upgradeGuide/main" />
<import name="PackageRecommend" content=":application/buyed/components/packageRecommend/main" />
<import name="XuanFuUpgradeGuide" content=":application/buyed/components/xuanFuUpgradeGuide/main" />
<import name="rights" content=":application/buyed/components/rights/main" />
<import name="moreServer" content=":application/buyed/components/moreServer/main" />
<import name="exchange" content=":application/buyed/components/exchange/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="ElderStudyStep" content=":application/buyed/components/elderStudyStep/main" />

<div class="page-container  page-buyed">
    <div class="page-header">
        <com:header title="VIP课程" theme="black" endTheme="black" scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}">
        </com:header>
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <!-- 徽章和卡片 -->
        <com:IndexPage unreservedKemu="{{state.unreservedKemu}}" isClaimsRight="{{state.isClaimsRight}}"
            vipClaimsActiveInfo="{{state.vipClaimsActiveInfo}}" userData="{{state.userData}}"
            payOutInfoData="{{state.payOutInfoData}}" kemuAllList="{{state.kemuAllList}}"
            reseveStatus="{{state.reseveStatus}}" bagesList="{{state.bagesList}}" kemu="{{state.kemu}}"
            tabClickMethod="{{self.tabClickMethod}}" gotoBuchgang="{{self.gotoBuchgang}}"
            ke3ClaimOpen="{{state.ke3ClaimOpen}}">
        </com:IndexPage>
        <div class="ipad-box ipad-box-bg">
            <div class="phone-box">
                <div class="content-container">
                    <!-- 会员推荐套餐 -->
                    <com:PackageRecommend name="packageRecommend"></com:PackageRecommend>
                    <!-- 学习步骤 -->
                    <!-- <com:ElderStudyStep name="elderStudyStep"></com:ElderStudyStep> -->
                    <com:StudyStep name="studyStep" isScore12View="{{state.isScore12View}}" kemu="{{state.kemu}}"
                        isRouteVideo="{{state.isRouteVideo}}" kemuAllList="{{state.kemuAllList}}"></com:StudyStep>
                    <!-- 会员推荐课程 -->
                    <com:MemberRecommend kemuAllList="{{state.kemuAllList}}" nowGoodInfo="{{self.nowGoodInfo}}"
                        swallConfig="{{state.swallConfig}}" name="memberRecommend">
                    </com:MemberRecommend>
                    <sp:if value="{{state.isClaimsRight  && !URLCommon.isScore12 }}">
                        <sp:if value="state.kemu !== 3">
                            <div class="compensation {{URLCommon.isElder && 'compensation-old'}}" ref="compensation">
                                <div class="title">
                                    科{{Tools.numZh(state.kemu)}}考不过补偿{{state.vipClaimsActiveInfo.amount ||
                                    self.compensation}}元
                                </div>
                                <div class="dec">安心备考 考不过补偿</div>
                                <div class="btn" sp-on:click="onBuchgangClick">申请补偿</div>
                            </div>
                            <sp:elseif value="(state.kemu == 3 && state.ke3ClaimOpen)" />
                            <div class="compensation {{URLCommon.isElder && 'compensation-old'}}" ref="compensation">
                                <div class="title">
                                    科{{Tools.numZh(state.kemu)}}考不过补偿60元
                                </div>
                                <div class="dec">{{state.userCityName}}学员 限时特享</div>
                                <div class="btn" sp-on:click="onBuchgangClick">申请补偿</div>
                            </div>

                        </sp:if>
                    </sp:if>
                    <!-- 权益 -->
                    <com:rights kemu="{{state.kemu}}" nonecompensate="{{state.nonecompensate}}"></com:rights>
                    <sp:if value="{{state.moreServiceList && state.moreServiceList.length}}">
                        <!-- 更多服务 -->
                        <com:moreServer moreServiceList="{{state.moreServiceList}}" />
                    </sp:if>
                </div>
                <!-- 协议 -->
                <!-- <div class="view-rule-style-qi">
                    <div class="view-title" sp-on:click="openProtocolMethod">查看《{{Texts.productName}}VIP服务协议》</div>
                </div> -->
            </div>
        </div>
    </div>
    <div class="footer">
        <!-- 升级引导悬浮框 -->
        <com:XuanFuUpgradeGuide userData="{{state.userData}}" nowGoodInfo="{{self.nowGoodInfo}}"
            prevScrollTop="{{state.prevScrollTop}}" swallConfig="{{state.swallConfig}}" name="xuanFuUpgradeGuide">
        </com:XuanFuUpgradeGuide>
    </div>
    <!-- 填写信息弹窗 -->
    <com:ReserveDialog name="reserveDialog" closeReserveDialog="{{self.closeReserveDialog}}"
        gotoBuchgang="{{self.gotoBuchgang}}"></com:ReserveDialog>
    <!-- 切换场景弹窗 -->
    <com:ChangeSceneCodeDialog reseveStatus="{{state.reseveStatus}}" name="changeSceneCodeDialog">
    </com:ChangeSceneCodeDialog>
    <!-- version小于3的强制更新弹窗 -->
    <com:BizVersionTipsDialog name="bizVersionTipsDialog"></com:BizVersionTipsDialog>
    <!-- 升级引导底部半截弹窗 ，这期8.17.0迭代不需要了-->
    <!-- <com:UpgradeGuide reserveDialogHide="{{self.reserveDialogHide}}" saveUpgradeGuideKey="{{self.saveUpgradeGuideKey}}"
        nowGoodInfo="{{self.nowGoodInfo}}" comparePricePool="{{state.comparePricePool}}" name="upgradeGuide"
        nowCouponInfo="{{self.nowCouponInfo}}" swallConfig="{{state.swallConfig}}" userData="{{state.userData}}">
    </com:UpgradeGuide> -->
    <com:payDialog />
    <sp:if value="{{state.showExchangeDialog}}">
        <com:exchange closeExchangeDialog="{{self.closeExchangeDialog.bind(self)}}" />
    </sp:if>
</div>
