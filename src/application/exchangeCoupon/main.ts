/*
 * ------------------------------------------------------------------
 * 兑换优惠券
 * ------------------------------------------------------------------
 */

import { setStatusBarTheme } from ':common/core';
import { makeToast } from ':common/features/dom';
import jump from ':common/features/jump';
import { BUYED_URL, STATUS_URL } from ':common/navigate';
import { exchangeCoupon } from ':store/coupon';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';

export default class extends Application {
    $constructor(params, context) {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.context = context;
        this.state = {

        };
    }
    didMount() {
        setStatusBarTheme('dark');
    }
    exchangeCoupon() {
        const $exCode = this.getDOMNode().exCode as HTMLInputElement;
        const exCode = $exCode.value;
        // console.log(exCode);
        if (!exCode) {
            makeToast('请先输入兑换码！');
            return;
        }

        exchangeCoupon({ couponCode: exCode }).then(data => {
            // 兑换服务成功
            if (data.exchange?.toString() === 'true') {
                setTimeout(() => {
                    jump.replace(BUYED_URL);
                }, 500);
            }
        });
    }
}
