<import name="style" content="./main" module="S" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="kqfdBuyDialog" content=":application/kqfd/component/kqfdBuyDialog/main" />
<import name="onlineDialog" content=":application/kqfd/component/onlineDialog/main" />
<div class=":kqfd-index">
    <div class=":index-top {{props.hasPromission?S.hasIndexTop:''}}">
        <div class=":index-top-time">
            最新直播：
            <sp:if value='{{state.lessonSchedule.length<=0}}'>
                暂无直播
                <sp:else />
                {{self.newSchelTime}}
            </sp:if>

        </div>
    </div>
    <sp:if value='{{!props.hasPromission}}'>
        <div class=":index-buy-button" data-fragment="主图" sp-on:click="dialogBuy">
            <span class=":buy-title">{{props.goodsInfoPool[0].payPrice}}元立即开通</span>
            <span class=":buy-desc">{{props.goodsInfoPool[0].validDays}}天内可观看所有急训直播</span>
            <div class=":buy-tips">考不过补偿50元</div>
        </div>
        <div class=":history-bg"></div>
        <div class=":history-video-container">
            <div class=":history-title">
                <div class=":left">超速扣分总结来了 快截图！</div>
                <div class=":right {{S.right2}}">剪不断理还乱的考点，帮…</div>
            </div>

        </div>
        <div class=":history-video-container {{S.history2}}">
            <div class=":history-title">
                <div class=":left">技巧给你铺满，还怕学不…</div>
                <div class=":right">重点记一记，考试更容易</div>
            </div>
        </div>
        <div class=":mashangkaoshi-bg"></div>
        <div class=":mashangkaoshi-container"></div>
    </sp:if>

   
    <sp:if value='{{state.lessonSchedule.length}}'>
        <div class=":zhibo-time-container">
            <div class=":time-title"></div>
            <sp:each for='{{state.lessonSchedule}}'>
                <div class=":time-tabs {{$index==state.lessonSchedule.length-1?S.timeTabs2:''}}">
                    <sp:each for='{{$value.liveDataList}}' value="$sonitem" index="$sonIndex">
                        <div class=":tabs-step1" data-liveSessionId="{{$sonitem.liveSessionId}}" data-fragment="直播时间表"
                            sp-on:click="gotoZhibojian">
                            <div class=":top">
                                <span class=":left">第{{Tools.numZh($sonIndex+1)}}场</span>
                                <div class=":right">
                                    <span>
                                        <sp:if value="{{Tools.dateFormat($sonitem.beginTime,
                                                                        'MM月dd日')==Tools.dateFormat(new Date(),
                                                                        'MM月dd日')}}">
                                            今天
                                            <sp:else />
                                            {{Tools.dateFormat($sonitem.endTime, 'MM月dd日')}}
                                        </sp:if>
                                        <span class=":right-time">
                                            {{Tools.dateFormat($sonitem.beginTime,
                                            'HH:mm')}}-{{Tools.dateFormat($sonitem.endTime, 'HH:mm')}}
                                        </span>
                                    </span>
                                    <sp:if value='{{$sonitem.status==1}}'>
                                        <span class=":zhizho1">直播中</span>
                                    </sp:if>
                                    <sp:if value='{{$sonitem.status==2}}'>
                                        <sp:if value='{{$sonitem.subscribeStatus==1}}'>
                                            <span class=":zhizho2">
                                                已预约
                                            </span>
                                            <sp:else />
                                            <span class=":zhizho2" data-liveSessionId="{{$sonitem.liveSessionId}}"
                                                data-index="{{$index}}" data-sonIndex="{{$sonIndex}}"
                                                sp-on:click="makeappointment">
                                                预约直播
                                            </span>
                                        </sp:if>
                                    </sp:if>
                                    <sp:if value='{{$sonitem.status==3}}'>
                                        <span class=":zhizho3">看回放</span>
                                    </sp:if>

                                </div>
                            </div>
                            <div class=":center">主讲内容：{{$sonitem.liveContent}}</div>
                        </div>
                    </sp:each>
                    <div class=":tabs-icon-left"></div>
                    <div class=":tabs-icon-center">{{$value.title}}</div>
                    <div class=":tabs-icon-right"></div>
                </div>
            </sp:each>
            <div class=":time-more" sp-on:click="viewHistory">查看历史直播记录></div>


        </div>
    </sp:if>
    <sp:if value='{{!props.hasPromission}}'>
        <div class=":vip-right-bg-container"></div>
    </sp:if>
    <sp:if value='{{props.hasPromission}}'>
        <div class=":zhibo-fuli-container">
            <div class=":zhibo-title"></div>
            <div class=":fuli-tabs">
                <div class=":fuli-item">
                    <div class=":item-title-container {{props.hasPromission?S.container2:''}}">
                        <div class=":item-title">
                            <span class=":item-title-icon"></span>
                            <span>科一真实考场模拟1次使用权</span>
                        </div>
                        <sp:if value='{{props.hasPromission}}'>
                            <div class=":item-title {{S.itemTitle2}}">
                                <span class=":has-title">
                                    {{props.expireTimeString}}到期
                                    <span class=":go-study" sp-on:click="gotoStu">去使用</span>
                                </span>
        
                            </div>
        
                            <sp:else />
                            <div class=":item-title">
                                <span class=":item-title-icon"></span>
                                领取后30天内可使用1次
                            </div>
                        </sp:if>
        
                    </div>
                    <sp:if value='{{props.hasPromission}}'>
                        <div class=":has-icon"></div>
                    </sp:if>
        
                </div>
                <div class=":fuli-item {{S.fuliItem1}} {{props.hasPromission?S.qita:''}}" sp-on:click="gotoZiliao"
                    data-fragment="资料包">
                    <div class=":item-container">
                        <div class=":item-icon-fuli"></div>
                        <div class=":item-icon-title">核心知识点<br />资料包</div>
                    </div>
                    <div class=":item-container {{S.itemContainer2}}">
                        <div class=":item-icon-fuli {{S.fuli2}}"></div>
                        <div class=":item-icon-title">答题技巧<br />资料包</div>
                    </div>
                    <sp:if value='{{props.hasPromission}}'>
                        <div class=":has-icon"></div>
                    </sp:if>
        
                </div>
            </div>
        </div>
    </sp:if>

    <!-- <div class=":zhibo-liuyan-container">
        <div class=":liuyan-title"></div>
        <div class=":liuyan-item">
            <div class=":liuyan-item-title">即将考试了，您最希望讲师们提供哪些考试技巧?</div>
            <div class=":liuyan-item-title">{{props.hasPromission?'欢迎留言，我们将在直播间内集中讲解？':'开通后即可留言，我们将在直播间内集中讲解。'}}</div>
            <div class=":liuyan-button" sp-on:click="gotoLiuyan" data-fragment="留言板">去留言></div>
        </div>
    </div> -->
    <div class=":zhibo-common-question">
        <div class=":common-question-title"></div>
        <com:commonQuestion type="12" />
    </div>
</div>
<com:kqfdBuyDialog goodsInfoPool="{{props.goodsInfoPool}}" fragmentName1="{{state.fragmentName1}}"></com:kqfdBuyDialog>
<com:onlineDialog></com:onlineDialog>