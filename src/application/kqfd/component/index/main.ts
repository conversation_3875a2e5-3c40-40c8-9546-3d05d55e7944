/*
 * main
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 16/3/24
 */
import { URLCommon, URLParams } from ':common/env';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import KqfdBuyDialog from ':application/kqfd/component/kqfdBuyDialog/main';
import { trackEvent, trackGoPay } from ':common/stat';
import { GoodsInfo } from ':store/goods';
import { getLiveLessonSchedule, getLiveWithPriority, subscribe } from ':store/kqfd';
import { dateFormat } from ':common/utils';
import OnlineDialog from ':application/kqfd/component/onlineDialog/main';
import { getAuthToken, openVipWebView, openWeb } from ':common/core';
import { KQFD_HISTORY, REAL_ROOM, SECRET, VIP_LIVE } from ':common/navigate';
import { makeToast } from ':common/features/dom';
import { onPageShow } from ':common/features/page_status_switch';
interface State {
    fragmentName1: string,
    lessonSchedule: any[]
}
interface Props {
    payBtnCall?(e: Event),
    goodsInfoPool: GoodsInfo[],
    hasPromission: boolean,
    viewHistory()
}

export default class extends Component<State, Props> {
    declare children: {
        kqfdBuyDialog: KqfdBuyDialog,
        onlineDialog: OnlineDialog
    }
    // 获取最新直播时间
    get newSchelTime() {
        const { lessonSchedule } = this.state;
        // 去最新的直播中，预约，回放
        const newZhiboData = [];
        lessonSchedule && lessonSchedule.forEach((res) => {
            newZhiboData.push(...res.liveDataList);
        });
        // status:1直播中，2预约，3回放
        const sortByData: any = newZhiboData && newZhiboData.sort((a, b) => {
            return +a.status - +b.status;
        });
        console.log('最新时间也按，status,1,2,3', sortByData);
        // eslint-disable-next-line max-len
        const timeString = dateFormat(sortByData[0]?.beginTime, 'yyyy.MM.dd') + ' ' + dateFormat(sortByData[0]?.beginTime, 'HH:mm') + '-' + dateFormat(sortByData[0]?.endTime, 'HH:mm');
        return timeString;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            fragmentName1: '',
            lessonSchedule: []
        };

    }
    willReceiveProps() {
        return true;
    }
    async didMount() {
        await this.getZhiboData();

    }
    async getZhiboData() {
        const lessonSchedule: any = await getLiveLessonSchedule({
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            limit: 2
        });
        this.setState({ lessonSchedule });
        if (lessonSchedule.length <= 0) {
            this.children.onlineDialog.show();
        }
    }
    dialogBuy = (e) => {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        // 点击支付按钮打点
        trackGoPay({
            groupKey: this.props.goodsInfoPool[0].groupKey,
            fragmentName1,
            fragmentName2: ''
        });
        this.setState({ fragmentName1 }, () => {
            this.children.kqfdBuyDialog.show();
        });
    }
    gotoZiliao = (e) => {
        if (this.props.hasPromission) {
            trackEvent({
                fragmentName1: '',
                actionName: '资料包',
                actionType: '点击'
            });
            openWeb({
                url: SECRET + window.location.search.replace('noTopInset', 'removenoTopInset')
            });
        } else {
            this.dialogBuy(e);
        }

    }
    gotoZhibojian = (e) => {
        const liveSessionId = e.refTarget.getAttribute('data-liveSessionId');
        if (this.props.hasPromission) {
            openWeb({
                url: 'http://jiakao.nav.mucang.cn/topLesson/live?id=' + liveSessionId + '&from=' + URLParams.from + '&backHome=0&fromItemCode=' + URLParams.fromItemCode
            });
        } else {
            this.dialogBuy(e);
        }
    }

    parentZhibojian() {
        getLiveWithPriority({
            lessonType: 1
        }).then(data => {
            openWeb({
                url: 'http://jiakao.nav.mucang.cn/topLesson/live?id=' + data.value + '&from=' + URLParams.from + '&backHome=0&fromItemCode=' + URLParams.fromItemCode
            });
        });
    }
    gotoLiuyan = (e) => {
        if (this.props.hasPromission) {
            trackEvent({
                fragmentName1: '',
                actionName: '留言板',
                actionType: '点击'
            });
            openWeb({
                url: 'https://www.wjx.cn/vm/to34ebr.aspx#' + window.location.search
            });
        } else {
            this.dialogBuy(e);
        }
    }
    async makeappointment(e) {
        e.stopPropagation();
        const lessonId = e.refTarget.getAttribute('data-liveSessionId');
        const index = e.refTarget.getAttribute('data-index');
        const sonIndex = e.refTarget.getAttribute('data-sonIndex');
        await subscribe({ lessonId });
        this.state.lessonSchedule[index].liveDataList[sonIndex].subscribeStatus = 1;
        this.setState({
            lessonSchedule: this.state.lessonSchedule
        });

    }
    viewHistory() {
        openVipWebView({
            url: KQFD_HISTORY + window.location.search
        });

        this.props.viewHistory && this.props.viewHistory();
    }
    gotoStu = (e) => {
        if (this.props.hasPromission) {
            trackEvent({
                fragmentName1: '真实考场模拟',
                actionName: '去使用',
                actionType: '点击'
            });
            openWeb({
                url: REAL_ROOM
            });
        }
    }

}
