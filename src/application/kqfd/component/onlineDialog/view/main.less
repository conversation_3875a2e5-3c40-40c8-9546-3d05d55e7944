.kqfd-online-dialog {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 103;
    height: 100%;
    width: 100%;
    .kqfd-online-dialog-warp {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        -webkit-transform: translate(-50%, -50%);
        width: 286px;
        height: 298px;
        background: #f2f8fd;
        border: 1px solid #000000;
        border-radius: 13px;
        padding-top: 59px;
        .icon {
            width: 105px;
            height: 79px;
            background: url(../images/nodata.png) no-repeat center;
            background-size: 100%;
            margin: 0px auto;
        }
        .title {
            font-size: 15px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: center;
            color: #333333;
            line-height: 21px;
            margin-top: 24px;
        }
        .zhidao {
            width: 130px;
            height: 45px;
            background: linear-gradient(131deg, #5ac1ff 3%, #0083ff 92%);
            border-radius: 23px;
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Semibold;
            font-weight: 600;
            text-align: center;
            color: #ffffff;
            line-height: 45px;
            text-align: center;
            margin: 30px auto 0px auto;
        }
    }
}
