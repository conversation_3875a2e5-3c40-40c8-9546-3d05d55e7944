/*
 * ------------------------------------------------------------------
 * 下线弹窗
 * ------------------------------------------------------------------
 */
import View from './view/main.html';

import { Component } from '@simplex/simple-core';
import { goBack, webClose } from ':common/core';
interface State {
    show: boolean
}
interface Props {

}

export default class extends Component<State, Props> {
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {
            show: false
        };
    }
    didMount() {
        console.log('');
    }
    close() {
        this.setState({ show: false });
    }
    show() {
        this.setState({ show: true });
    }
    closeWeb() {
        webClose();
    }

    willR<PERSON>eiveP<PERSON>() {
        return true;
    }
}
