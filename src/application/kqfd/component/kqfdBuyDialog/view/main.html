<import name="style" content="./main" />
<import name="payType" content=":component/payType/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<div>
    <sp:if value='{{state.show}}'>
        <div class="kqfd-buy-dialog">
            <div class="kqfd-buy-dialog-warp">
                <div class="tab-container">
                    <div class="title">
                        购买
                        <div class="close" sp-on:click="close">
                            <span class="close-icon"></span>
                        </div>
                    </div>
                    <div class="goods-contanier">
                        <sp:each for='{{props.goodsInfoPool}}'>
                            <div data-idx="{{$index}}" sp-on:click="tabChange"
                                class="goods {{self.nowGoodInfo&&self.nowGoodInfo.groupKey==$value.groupKey?'click-goods':''}}">
                                <div class="goods-left">
                                    <div class="goods-left-title">{{$value.name}}</div>
                                    <div class="goods-left-desc">{{$value.validDays}}天有效期</div>
                                </div>
                                <div class="goods-right">
                                    <div class="goods-right-title"><span class="price">￥</span>{{$value.payPrice}}</div>
                                </div>
                                <sp:if value="$index === 1">
                                    <div class="tag"></div>
                                </sp:if>


                            </div>
                        </sp:each>
                    </div>





                </div>
                <div class="pay-type {{Platform.isIOS && 'hide'}}">
                    <com:payType theme="kqfd-dialog" />
                </div>
                <div sp-on:click="payBtnBuy" class="buy-btn {{Platform.isIOS?'':'buy-btn-and'}}">
                    立即开通
                </div>
                <div class="kqfd-readProtocol">
                    <com:readProtocol theme="kqfd-dialog" />
                </div>

            </div>


        </div>
    </sp:if>
</div>