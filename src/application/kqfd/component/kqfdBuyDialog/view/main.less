.kqfd-buy-dialog {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 103;
    height: 100%;
    width: 100%;
    .kqfd-buy-dialog-warp {
        position: absolute;
        bottom: 0;
        width: 100%;
        background: #ffffff;
        height: auto;
        .tab-container {
            flex: 1;
            position: relative;

            .title {
                padding: 0px 18px 0px 18px;
                height: 49px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 16px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 600;
                color: #333333;
                position: relative;
                border-bottom: 1px solid #f2f2f2;
                .close {
                    height: 49px;
                    width: 30px;
                    line-height: 49px;
                    text-align: right;
                    position: relative;
                    right: -8px;
                    .close-icon {
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        background: url("../images/close.png");
                        background-size: cover;
                    }
                }
            }
            .goods-contanier {
                padding: 15px 18px 0px 18px;
            }
            .goods {
                height: 76px;
                border-radius: 4px;
                border: 1px solid #dddddd;
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                padding: 0px 15px;
                position: relative;
                &:last-child {
                    margin-bottom: 0px;
                }

                .tag {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 98px;
                    height: 20px;
                    background: url(../images/tag.png) no-repeat center
                        center/cover;
                    transform: translateY(-50%);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 12px;
                    color: #fff;
                    white-space: nowrap;
                }

                .goods-left {
                    flex: 1;

                    .goods-left-title {
                        font-size: 18px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 600;
                        color: #333333;
                        line-height: 25px;
                    }

                    .goods-left-desc {
                        font-size: 13px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #666666;
                        margin-top: 3px;
                        line-height: 18px;
                    }
                }

                .goods-right {
                    .goods-right-title {
                        font-size: 26px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 600;
                        color: #333333;
                        line-height: 25px;

                        .price {
                            font-size: 18px;
                        }
                    }

                    .goods-right-desc {
                        font-size: 10px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #666666;
                        line-height: 14px;
                    }
                }

                &.click-goods {
                    background: #fff4f4;
                    border: 1px solid #f25247;
                    border-radius: 4px;

                    .goods-right-title {
                        color: #f25247;
                    }
                }

                .click-icon {
                    width: 30px;
                    height: 30px;
                    background: url(../images/ic-xuanzhong.png) no-repeat center;
                    background-size: 100% 100%;
                    position: absolute;
                    right: -1px;
                    bottom: 0px;
                }
            }
        }

        .pay-type {
            margin: 10px 23px;
        }

        .buy-btn {
            width: 345px;
            height: 48px;
            background: linear-gradient(315deg, #ff4a40, #ff7d76);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 25px auto 8px auto;
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            color: #ffffff;
            &.buy-btn-and {
                margin-top: 10px;
            }
        }
        .kqfd-readProtocol {
            padding: 0px 18px 10px 18px;
            padding-bottom: calc(~"10px + constant(safe-area-inset-bottom)/2");
            /* 兼容 iOS < 11.2 */
            padding-bottom: calc(~"10px + env(safe-area-inset-bottom)/2");
        }
    }
}
