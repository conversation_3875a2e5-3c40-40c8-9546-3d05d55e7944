/*
 * ------------------------------------------------------------------
 * 考前辅导购买弹窗
 * ------------------------------------------------------------------
 */

import { PayType, Platform } from ':common/env';
import { GoodsInfo } from ':store/goods';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayTypeComponent from ':component/payType/main';
import View from './view/main.html';
import { iosPay } from ':common/features/ios_pay';
import { getDefaultPayType, PayBoundType, startSiriusPay } from ':common/features/pay';
import { Component } from '@simplex/simple-core';
import { trackConfirmPay } from ':common/stat';
import { checkReaded } from ':common/features/agreement';
import { reload } from ':common/features/jump';
interface State {
    tabIndex: number,
    show: boolean
}
interface Props {
    goodsInfoPool: GoodsInfo[],
    fragmentName1: string
}

export default class extends Component<State, Props> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeComponent
    };
    get nowGoodInfo() {
        const { tabIndex } = this.state;
        const { goodsInfoPool = [] } = this.props;
        return goodsInfoPool[tabIndex] || {};
    }
    /**
    * 如果有优惠券的价格为0的就显示0
    * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
   */
    get showPrice() {
        const { tabIndex } = this.state;
        const { goodsInfoPool } = this.props;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;
        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {
            tabIndex: 0,
            show: false
        };
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex } = this.state;
        const { goodsInfoPool } = this.props;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: '',
            ...stat
        }, false).then(() => {
            reload();
        });
    }
    close() {
        this.setState({ show: false });
    }
    show() {
        this.setState({ show: true });
    }
    payBtnBuy() {
        checkReaded(() => {
            this.payBtnCall();
        });
    }
    payBtnCall() {
        const { goodsInfoPool } = this.props;
        const { tabIndex } = this.state;
        trackConfirmPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1: this.props.fragmentName1,
            fragmentName2: '支付弹窗',
            payPathType: 0
        });
        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1: this.props.fragmentName1,
                fragmentName2: '支付弹窗'
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造
            this.pay({
                fragmentName1: this.props.fragmentName1,
                fragmentName2: '支付弹窗'
            });
        }
    }
    tabChange(e) {
        const tabIndex = +e.refTarget.getAttribute('data-idx');
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        this.setState({
            tabIndex
        });
    }

    willReceiveProps() {
        return true;
    }
}
