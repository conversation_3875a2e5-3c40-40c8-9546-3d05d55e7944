/*
 * ------------------------------------------------------------------
 * 考前辅导直播课
 * ------------------------------------------------------------------
 */

import { CarType, KemuType, PayType, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import IndexPage from ':application/kqfd/component/index/main';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { iosPay } from ':common/features/ios_pay';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackGoPay, trackPageLoad } from ':common/stat';
import { onWebBack } from ':common/features/persuade';
import Header from ':component/header/main';
import { checkReaded } from ':common/features/agreement';
import { getGoodsConfig } from ':store/kqfd';
import { checkRestriction, getPermission } from ':store/chores';
import { getAuthToken } from ':common/core';
import { dateFormat } from ':common/utils';
import { login } from ':common/features/login';
import Loading from ':application/buyed/components/Loading/main';
import { onPageShow } from ':common/features/page_status_switch';
import { Coupon, goodsInfoWithCoupon } from ':common/features/coupon';
import jump, { reload } from ':common/features/jump';
import { OUTLIMIT } from ':common/navigate';

URLCommon.kemu = +URLParams.kemu || +URLParams.kemuStyle;

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    hasPromission: boolean,
    expireTimeString: string,
    theme: string
}
let timer;
const qaKeyMap = {
    [GroupKey.ChannelKemuAll]: 'qaKey6'
};
export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog,
        header: Header,
        indexPage: IndexPage,
        loading: Loading

    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey] || {};
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    get qaKey() {
        return qaKeyMap[this.nowGoodInfo.groupKey] || 'qaKey1';
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {
            tabIndex: 0,
            goodsInfoPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            hasPromission: false,
            expireTimeString: '',
            theme: 'white'
        };

    }
    async didMount() {
        setPageName('考前辅导直播课程主页');

        const authToken = await getAuthToken();
        if (authToken) {
            await this.hasPromissionMethod();
        }
        // 页面进出时长打点
        trackPageLoad({ payStatus: this.state.hasPromission ? '1' : '2' });
        // 如果有权限，就不请求商品信息了
        if (this.state.hasPromission) {
            return;
        }

        // 获取返回的商品groupkey
        await this.getGroupKey();
        await this.getGoodInfo();
        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: async () => {
                const authToken = await getAuthToken();
                if (authToken) {
                    this.children.loading.show();
                    setTimeout(() => {
                        reload();
                    }, 4000);

                } else {
                    await login();
                }

            }
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: this.state.goodsInfoPool[this.state.tabIndex]?.groupKey
        });
    }
    async hasPromissionMethod() {
        let permission = '';
        switch (URLCommon.tiku) {
            case CarType.CAR:
                permission = +URLCommon.kemu === 1 ? 'kqfdLiveK1' : 'kqfdLiveK4';
                break;
            default:
                break;
        }
        if (!permission) {
            return;
        }
        const checkPermission: any = await getPermission(permission);
        console.log('checkPermission', checkPermission);
        console.log('checkPermission', JSON.stringify(checkPermission));
        this.state.expireTimeString = dateFormat(checkPermission.validEndTime, 'yyyy.MM.dd');
        const { hasPromission } = checkPermission;
        if (hasPromission) {
            await this.checkRestriction();
        }
        this.setState({ hasPromission, expireTimeString: this.state.expireTimeString });
    }

    /** 进入页面先判断是否设备超限 */
    async checkRestriction() {
        const { pass } = await checkRestriction();
        if (!pass) {
            jump.replace(OUTLIMIT);
        }
    }

    pageScroll(e) {
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            if (prevScrollTop >= 50) {
                this.setState({ theme: 'black' });
            } else {
                this.setState({ theme: 'white' });
            }
            this.children.header.setScrollBg(prevScrollTop, '#000000');
        }, 0);
    }
    async getGroupKey() {
        const groupKyConfig: any = await getGoodsConfig({
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu
        });
        const goodsInfoPool: GoodsInfo[] = [];
        goodsInfoPool.push({
            groupKey: groupKyConfig.singleGoodsKey
        } as GoodsInfo);
        goodsInfoPool.push({
            groupKey: groupKyConfig.allKey
        } as GoodsInfo);
        this.state.goodsInfoPool = goodsInfoPool;
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });
        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                // // 如果第一个商品已购买就跳走
                // if (index === 0 && goodInfo.bought) {

                // }
                // 商品未购买才push,全科可升级也能买
                if (goodInfo.groupKey === GroupKey.ChannelKemuAll && goodInfo.upgrade) {
                    newGoodsPool.push(goodInfo);
                } else if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });
            this.setState({
                goodsInfoPool: newGoodsPool
            });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: '',
            ...stat
        }, false).then(() => {
            this.children.loading.show();
            setTimeout(() => {
                reload();
            }, 4000);
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    buttonBuy = (e) => {
        checkReaded(() => {
            const tabIndex = e.refTarget.getAttribute('data-tabIndex');
            this.setState({ tabIndex }, () => {
                this.payBtnCall(e);
            });

        });
    }
    payBtnCall = (e) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造
            this.pay({ fragmentName1 });
        }
    }
    gotoZhibojian() {
        this.children.indexPage.parentZhibojian();
    }
    // 从历史页返回刷新页面
    viewHistory = async () => {

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        const authToken = await getAuthToken();
        if (authToken) {
            await this.hasPromissionMethod();
        }
        this.children.indexPage.getZhiboData();
        // 如果有权限，就不请求商品信息了
        if (this.state.hasPromission) {
            return;
        }

        // 获取返回的商品groupkey
        await this.getGroupKey();
        await this.getGoodInfo();
        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: this.state.goodsInfoPool[this.state.tabIndex]?.groupKey
        });
    }
}
