.page-kqfd {
    background: #4bb8ff;
    height: 100%;

    .page-header {
        position: absolute;
        z-index: 1000;
        top: 0;
        left: 0;
        width: 100%;
    }

    .body-panel {
        flex: 1;
        overflow-y: scroll;
    }

    .footer {
        position: relative;
        z-index: 10;
        border-radius: 16px 16px 0 0;
        background-size: 100%;
        padding: 20px 15px 10px 15px;
        padding-bottom: calc(~"10px + constant(safe-area-inset-bottom)/2"
        ) !important;
        /* 兼容 iOS < 11.2 */
        padding-bottom: calc(~"10px + env(safe-area-inset-bottom)/2");
                background: linear-gradient(180deg, #9be6ff, #1e9df1);
        .pay-type-box {
            padding-bottom: 15px;
        }

        &.has-footer {
            padding-top: 20px;
            height: 90px;
            background: url(../images/has-footer.png) no-repeat center;
            background-size: 100%;
        }

        .buy-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding-bottom: 10px;

            .goods-button {
                width: 130px;
                height: 49px;
                background: url(../images/button-left.png) no-repeat center;
                background-size: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
            }

            .goods-button01 {
                width: 205px;
                height: 49px;
                background: url(../images/button-right.png) no-repeat center;
                background-size: 100%;
                margin-left: 10px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
            }

            .goods-button02 {
                width: 335px;
                height: 49px;
                background: url(../images/single.png) no-repeat center;
                background-size: 100%;
                margin-left: 0px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
            }

            .button-xuan-fu {
                position: absolute;
                top: -18px;
                right: 0px;
                width: 100px;
                height: 22px;
                background: linear-gradient(180deg, #ffee6d, #ffc806);
                border-radius: 14px 14px 14px 0px;
                font-size: 11px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 500;
                text-align: center;
                color: #333330;
                line-height: 22px;
                text-align: center;
            }

            .goods-button-title {
                font-size: 14px;
                font-family: PingFangSC, PingFangSC-Semibold;
                font-weight: 600;
                color: #ffffff;
                line-height: 20px;
            }

            .goods-button-desc {
                font-size: 10px;
                font-family: PingFangSC, PingFangSC-Regular;
                font-weight: 400;
                color: #ffffff;
                line-height: 14px;
            }
        }
    }
}
