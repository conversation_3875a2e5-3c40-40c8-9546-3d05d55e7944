<import name="style" content="./main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="header" content=":component/header/main" />
<import name="indexPage" content=":application/kqfd/component/index/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="loading" content=":application/buyed/components/loading/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="payType" content=":component/payType/main" />

<import name="buyButton" content=":component/buyButton/main" />
<div class="page-container page-kqfd">
    <div class="page-header">
        <com:header title="{{Texts.kemuTxt[URLCommon.kemu]+'临考急训直播'}}" theme="{{state.theme}}"
            endTheme="{{state.theme}}" qaKey="{{self.qaKey}}" scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}" />
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <com:indexPage expireTimeString="{{state.expireTimeString}}" hasPromission="{{state.hasPromission}}"
            goodsInfoPool="{{state.goodsInfoPool}}" viewHistory="{{self.viewHistory}}"></com:indexPage>
    </div>
    <div class="footer {{state.hasPromission?'has-footer':''}}">
        <sp:if value="Platform.isAndroid && !state.hasPromission">
            <div class="pay-type-box">
                <com:payType theme="horizontal" />
            </div>
        </sp:if>
        <div class="buy-button">
            <sp:if value='{{state.hasPromission}}'>
                <div class="goods-button02" sp-on:click="gotoZhibojian">
                    <div class="goods-button-title">进入当前直播</div>
                    <div class="goods-button-desc">{{state.expireTimeString}}到期</div>
                </div>
                <sp:else />
                <sp:each for='{{state.goodsInfoPool}}'>
                    <div sp-on:click="buttonBuy" data-tabIndex="{{$index}}"
                        data-fragment="{{$index==0?'底部吸底左侧按钮':'底部吸底右侧按钮'}}"
                        class="{{state.goodsInfoPool.length>=2&&$index===0?'goods-button':'goods-button01'}}   {{state.goodsInfoPool.length===1?'goods-button02':''}}">
                        <sp:if value='{{$index===0}}'>
                            <div class="goods-button-title">{{$value.payPrice}}元立即开通</div>
                            <sp:else />
                            <div class="goods-button-title">
                                {{$value.payPrice}}元{{$value.upgrade?'升级':'开通'}}{{$value.name}}
                            </div>
                        </sp:if>

                        <div class="goods-button-desc">{{$value.validDays}}天有效期</div>
                        <sp:if value='{{state.goodsInfoPool.length>=2&&$index===1}}'>
                            <div class="button-xuan-fu">考不过补偿140元</div>
                        </sp:if>

                    </div>
                </sp:each>
            </sp:if>

        </div>
        <sp:if value='{{!state.hasPromission}}'>
            <com:readProtocol theme="kqfd" />
        </sp:if>

    </div>
    <com:payDialog />

    <com:buyButton />
    <com:loading />
    <com:expiredDialog />
</div>