<import name="style" content="./main" />
<import name="IndexPage" content=":application/kqfdHistoryDialog/component/index/main" />
<import name="loading" content=":application/buyed/components/loading/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<div class="page-container page-kqfd-history-dialog">
  
    <div class="body-panel" skip="true">
        <com:IndexPage expireTimeString="{{state.expireTimeString}}" hasPromission="{{state.hasPromission}}"
            goodsInfoPool="{{state.goodsInfoPool}}" pageScroll="{{self.pageScroll}}"></com:IndexPage>
    </div>

    <com:loading />
    <com:expiredDialog />
</div>