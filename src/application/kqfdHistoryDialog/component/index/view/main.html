<import name="style" content="./main" module="S" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="onlineDialog" content=":application/kqfd/component/onlineDialog/main" />
<import name="loadingmore" content=":component/loading-more/main" />

<div class=":kqfd-index-history-dialog {{!props.hasPromission?S.history:''}}">
    <com:loadingmore scroll="{{self.scroll}}" loadData="{{self.requestData}}">
        <div class=":index-top">
            <div class=":index-top-time">
                最新直播：
                <sp:if value='{{state.lessonSchedule.length<=0}}'>
                    暂无直播
                    <sp:else />
                    {{self.newSchelTime}}
                </sp:if>

            </div>
        </div>

        <sp:if value='{{state.lessonSchedule.length}}'>
            <div class=":zhibo-time-container">
                <div class=":time-title"></div>

                <sp:each for='{{state.lessonSchedule}}'>
                    <div class=":time-tabs {{$index==state.lessonSchedule.length-1?S.timeTabs2:''}}">
                        <sp:each for='{{$value.liveDataList}}' value="$sonitem" index="$sonIndex" >
                            <div class=":tabs-step1" data-liveSessionId="{{$sonitem.liveSessionId}}"
                                data-fragment="直播时间表" sp-on:click="onPay">
                                <div class=":top">
                                    <span class=":left">第{{Tools.numZh($sonIndex+1)}}场</span>
                                    <div class=":right">
                                        <span>
                                            <sp:if value="{{Tools.dateFormat($sonitem.beginTime,
                                    'MM月dd日')==Tools.dateFormat(new Date(),
                                    'MM月dd日')}}">
                                                今天
                                                <sp:else />
                                                {{Tools.dateFormat($sonitem.endTime, 'MM月dd日')}}
                                            </sp:if>
                                            <span class=":right-time">
                                                {{Tools.dateFormat($sonitem.beginTime,
                                                'HH:mm')}}-{{Tools.dateFormat($sonitem.endTime, 'HH:mm')}}
                                            </span>
                                        </span>


                                        <sp:if value='{{$sonitem.status==1}}'>
                                            <span class=":zhizho1">直播中</span>
                                        </sp:if>
                                        <sp:if value='{{$sonitem.status==2}}'>
                                            <sp:if value='{{$sonitem.subscribeStatus==1}}'>
                                                <span class=":zhizho2">
                                                    已预约
                                                </span>
                                                <sp:else />
                                                <span class=":zhizho2" data-liveSessionId="{{$sonitem.liveSessionId}}"
                                                    data-index="{{$index}}" data-sonIndex="{{$sonIndex}}"
                                                    sp-on:click="makeappointment">
                                                    预约直播
                                                </span>
                                            </sp:if>

                                        </sp:if>
                                        <sp:if value='{{$sonitem.status==3}}'>
                                            <span class=":zhizho3">看回放</span>
                                        </sp:if>

                                    </div>
                                </div>
                                <div class=":center">主讲内容：{{$sonitem.liveContent}}</div>
                            </div>
                        </sp:each>
                        <div class=":tabs-icon-left"></div>
                        <div class=":tabs-icon-center">{{$value.title}}</div>
                        <div class=":tabs-icon-right"></div>
                    </div>
                </sp:each>
                <div class=":loading-more">
                    <span sp:if={{state.hasMore}} class=":loading"></span>
                    {{state.loadTextTips}}
                </div>

            </div>
        </sp:if>

    </com:loadingmore>
    <com:onlineDialog></com:onlineDialog>
</div>

