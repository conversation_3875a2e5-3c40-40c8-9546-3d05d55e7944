.coupon-detail {
    background: #FFFFFF;
    height: 100%;
    display: flex;
    flex-direction: column;
    .exchange-coupon{
        font-size: 0.12rem;
        color: #333;
    }

    .coupon-body {
        flex: 1;
        overflow: hidden;
        height: 100%;
        overflow-y: scroll;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 15px;
    }

    .coupon-nav {
        display: flex;
        height: 43px;
        border-bottom: 1px solid #F4F7F7;

        .co-nav {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;

            label {
                font-size: 15px;
                display: block;
                line-height: 43px;
                height: 43px;
                box-sizing: border-box;
                color: #999999;
            }

            &.active {
                label {
                    border-bottom: 1px solid #333333;
                    color: #333333;
                }
            }
        }
    }

    .nonuse {
        padding: 0 15px;

        p {
            height: 50px;
            line-height: 50px;
            font-size: 14px;
            box-sizing: border-box;
            color: #333333;
            background: url(../images/coupon_r2.png) no-repeat right center;
            background-size: 20px 20px;

            &.active {
                background: url(../images/coupon_r1.png) no-repeat right center;
                background-size: 20px 20px;
            }
        }
    }

    .coupon-intro {
        display: -webkit-box;
        padding: 0 15px;
        height: 20px;

        span {
            display: block;
            font-size: 12px;
            height: 28px;

            &.intro_txt {
                color: #999999;
            }

            &.intro_link {
                -webkit-box-flex: 1;
                color: #333333;
                text-align: right;
            }
        }

    }

    .coupon-list {
        border-bottom-width: 10px;
        border-style: solid;
        border-color: #f7f7f7;
        padding-bottom: 25px;

        .coupon_h {
            color: #333333;
            font-size: 16px;
            line-height: 20px;
            padding: 25px 15px 0 15px;
        }
    }

    .coupon-list:nth-last-child(1) {
        border-bottom-width: 0;
    }

    .coupon_item {
        display: flex;
        height: 98px;
        width: 345px;
        background: url(../images/coupon_1.png) no-repeat;
        background-size: 100% 100%;
        margin: 17px auto 0 auto;

        .co_item_l {
            display: -webkit-box;
            padding: 24px 0;
            width: 32.8%;
        }

        .unit {
            font-size: 18px;
            color: #8F7A3E;
            line-height: 30px;
            padding: 0 6px 0 15px;
        }

        .amount {
            font-size: 60px;
            color: #8F7A3E;
            display: block;
            height: 50px;
            line-height: 50px;
        }

        .amount1 {
            font-size: 42px;
        }

        .co_item_r {
            padding: 24px 15px 24px 16px;
            flex: 1;
            display: flex;
        }

        .item_r-wrap {
            width: 100%;
        }

        .txt-wrap {
            flex: 1;
        }

        .desc {
            font-size: 14px;
            color: #625328;
            line-height: 16px;
        }

        .expire {
            padding-top: 15px;
            line-height: 14px;
            font-size: 12px;
            color: #625328;
        }

        .get_wrap {
            display: flex;

            .expire {
                flex: 1;
            }

            .get_btn {
                padding-top: 10px;

                span {
                    border: 1px solid #625328;
                    border-radius: 30px;
                    font-size: 12px;
                    color: #625328;
                    letter-spacing: 0;
                    padding: 2px 6px;
                }
            }

            .getted {
                span {
                    border: 0;
                }
            }
        }

        .co_radio {
            width: 20px;
            background: url(../images/coupon_s2.png) no-repeat center;
            background-size: 20px 20px;

            &.active {
                background: url(../images/coupon_s1.png) no-repeat center;
                background-size: 20px 20px;
            }
        }
    }

    .coupon_used,
    .coupon_expire,
    .coupon_cannot {
        color: #ffffff;
        background: url(../images/coupon_2.png) no-repeat;
        background-size: 100% 100%;

        .unit,
        .amount,
        .desc,
        .expire {
            color: #ffffff;
        }
    }

    .no-data {
        margin: 0 auto;

        .img {
            width: 210px;
            height: 133px;
            background: url(../images/no_data.png) no-repeat;
            background-size: 100% 100%;
            margin: 70px auto 0 auto;
        }

        p {
            text-align: center;
            font-size: 12px;
            color: #999999;
            padding-top: 20px;
        }
    }
}

.trans-hide {
    transform: translate3d(100%, 0, 0);
}

.trans-show {
    transform: translate3d(0, 0, 0);
}

.show {
    display: block;
}

.hide {
    display: none;
}
