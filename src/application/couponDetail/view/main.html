<import name="style" content="./main" />

<import name="header" content=":component/header/main" />

<div class="coupon-detail">
    <com:header title="优惠券" back="{{self.goBackPage}}">
        <div
            sp:slot="right"
            class="exchange-coupon"
            sp-on:click="goReplaceVip"
        >
            兑换服务
        </div>
    </com:header>
    <div class="coupon-body">
        <div class="coupon-nav">
            <p
                class="co-nav {{state.tabIndex == 1 ? 'active': ''}}"
                data-panel="1"
                sp-on:click="changeTab"
            >
                <label>使用券</label>
            </p>
            <p
                class="co-nav {{state.tabIndex == 2 ? 'active': ''}}"
                data-panel="2"
                sp-on:click="changeTab"
            >
                <label>领取券</label>
            </p>
        </div>

        <div>
            <div class=" {{state.tabIndex == 1 ? 'show': 'hide'}} co-panel">
                <sp:if value="state.userCoupons.length == 0">
                    <div class="no-data">
                        <div class="img"></div>
                        <p>您还没有优惠券</p>
                    </div>
                    <sp:else />
                    <div class="nonuse">
                        <p
                            sp-on:click="selectCoupon"
                            data-price="0"
                            data-uniq=""
                            data-code=""
                            class="give_up co_radio {{state.selCouponCode? '': 'active'}}  "
                        >
                            不使用优惠券
                        </p>
                    </div>
                    <div class="coupon-intro">
                        <span class="intro_txt">一次只能使用一个优惠券</span>
                    </div>
                    <sp:if value="state.canUseCoupons.length>0">
                        <div class="coupon-list">
                            <sp:each for="state.canUseCoupons" value="item">
                                <div class="coupon_item coupon_useable">
                                    <div class="co_item_l">
                                        <label class="unit">¥</label>
                                        <span
                                            class="amount {{item.goodsCouponData.priceCent>=10000 ? 'amount1' : ''}}"
                                            >{{(item.goodsCouponData.priceCent/100).toFixed(0)}}</span
                                        >
                                    </div>
                                    <div class="co_item_r">
                                        <div class="txt-wrap">
                                            <p class="desc">
                                                {{item.goodsCouponData.desc}}
                                            </p>
                                            <p class="expire">
                                                限{{Tools.dateFormat(item.validEndTime, 'yyyy-MM-dd')}}前使用
                                            </p>
                                        </div>
                                        <div
                                            sp-on:click="selectCoupon"
                                            data-code="{{item.couponCode}}"
                                            data-uniq="{{item.goodsCouponData.uniqKey}}"
                                            data-price="{{(item.goodsCouponData.priceCent)}}"
                                            class="co_radio {{state.selCouponCode == item.couponCode ? 'active': ''}}"
                                        ></div>
                                    </div>
                                </div>
                            </sp:each>
                        </div>
                    </sp:if>

                    <sp:if value="state.expiredCoupons.length>0">
                        <div class="coupon-list">
                            <div class="coupon_h">已过期</div>
                            <sp:each for="state.expiredCoupons" value="item">
                                <div class="coupon_item coupon_expire">
                                    <div class="co_item_l">
                                        <label class="unit">¥</label>
                                        <span
                                            class="amount {{item.goodsCouponData.priceCent>=10000 ? 'amount1' : ''}}"
                                            >{{(item.goodsCouponData.priceCent/100).toFixed(0)}}</span
                                        >
                                    </div>
                                    <div class="co_item_r">
                                        <div class="txt-wrap">
                                            <p class="desc">
                                                {{item.goodsCouponData.desc}}
                                            </p>
                                            <p class="expire">
                                                限{{Tools.dateFormat(item.validEndTime, 'yyyy-MM-dd')}}前使用
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </sp:each>
                        </div>
                    </sp:if>

                    <sp:if value="state.usedCoupons.length>0">
                        <div class="coupon-list">
                            <div class="coupon_h">已使用</div>
                            <sp:each for="state.usedCoupons" value="item">
                                <div class="coupon_item coupon_used">
                                    <div class="co_item_l">
                                        <label class="unit">¥</label>
                                        <span
                                            class="amount {{item.goodsCouponData.priceCent>=10000 ? 'amount1' : ''}}"
                                            >{{(item.goodsCouponData.priceCent/100).toFixed(0)}}</span
                                        >
                                    </div>
                                    <div class="co_item_r">
                                        <div class="txt-wrap">
                                            <p class="desc">
                                                {{item.goodsCouponData.desc}}
                                            </p>
                                            <p class="expire">
                                                限{{Tools.dateFormat(item.validEndTime, 'yyyy-MM-dd')}}前使用
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </sp:each>
                        </div>
                    </sp:if>
                </sp:if>
            </div>
            <div class=" {{state.tabIndex == 2 ? 'show': 'hide'}} co-panel">
                <sp:if value="state.goodsCoupons.length == 0">
                    <div class="no-data">
                        <div class="img"></div>
                        <p>您还没有优惠券可领</p>
                    </div>
                    <sp:else />
                    <sp:each for="state.goodsCoupons" value="item">
                        <div class="coupon_item">
                            <div class="co_item_l">
                                <label class="unit">¥</label>
                                <span class="amount"
                                    >{{(item.priceCent/100).toFixed(0)}}</span
                                >
                            </div>
                            <div class="co_item_r">
                                <div class="item_r-wrap">
                                    <p class="desc">{{item.desc}}</p>
                                    <div class="get_wrap">
                                        <sp:if value="!item.send">
                                            <p class="expire">
                                                限{{Tools.dateFormat(item.validEndTime, 'yyyy-MM-dd') }}前使用
                                            </p>
                                            <p
                                                class="get_btn getted"
                                                data-uniq="{{item.uniqKey}}"
                                                data-actionurl="{{item.actionUrl}}"
                                            >
                                                <span>已领取</span>
                                            </p>
                                            <sp:else />
                                            <p class="expire">
                                                有效期{{(item.validTimeSecond/(60*60*24)).toFixed(0)}}天
                                            </p>
                                            <p
                                                sp-on:click="getCode"
                                                class="get_btn"
                                                data-uniq="{{item.uniqKey}}"
                                                data-actionurl="{{item.actionUrl}}"
                                            >
                                                <span>立即领取</span>
                                            </p>
                                        </sp:if>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </sp:each>
                </sp:if>
            </div>
        </div>
    </div>
</div>
