import { ensureLogin, openWeb, setStatusBarTheme, webClose } from ':common/core';
import { Platform } from ':common/env';
import { makeToast } from ':common/features/dom';
import jump from ':common/features/jump';
import { onWebBack } from ':common/features/persuade';
import { EX_CHANGE_COUPON } from ':common/navigate';
import { getCode, getGoodsCoupons, getUserCoupons } from ':store/coupon';
import { getGroupSessionInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { login } from ':common/features/login';

interface State {
    authToken: string
    groupKey: GroupKey
    selCouponCode: string | null
    sessionIds: number[]
    userCoupons: any[]
    canUseCoupons: any[]
    usedCoupons: any[]
    expiredCoupons: any
    selectCoupon: any
    isSelectedValid: boolean
    goodsCoupons: any[]
    tabIndex: '1' | '2',
    pickTag: boolean
}

export default class extends Application<State> {
    $constructor(params, context) {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.context = context;

        this.state = {
            authToken: '',
            groupKey: params.search.groupKey,
            selCouponCode: params.search.selCouponCode,
            sessionIds: JSON.parse(params.search.sessionIds),
            userCoupons: [],
            canUseCoupons: [],
            usedCoupons: [],
            expiredCoupons: [],
            selectCoupon: null,
            isSelectedValid: false,
            goodsCoupons: [],
            tabIndex: '1',
            pickTag: false
        };

    }
    async didMount() {
        const authToken = await ensureLogin();
        const { groupKey } = this.state;

        localStorage.removeItem(`${groupKey}_selectCoupon`);
        if (!authToken) {
            return;
        }

        this.setState({ authToken });
        // 由于squirrel接口需要商品参数，所以必须先请求商品信息放到全局
        await getGroupSessionInfo({ groupKeys: [groupKey] });
        this.getUserCoupons();
        this.getGoodsCoupons();
        this.appEventProxy();

    }
    appEventProxy() {
        setStatusBarTheme('dark');
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });
    }
    goBackPage = () => {
        const { groupKey, selectCoupon } = this.state;

        if (selectCoupon) {
            localStorage.setItem(`${groupKey}_selectCoupon`, JSON.stringify(selectCoupon));
        }

        webClose();
    }
    goReplaceVip() {
        jump.navigateTo(EX_CHANGE_COUPON);
    }
    selectCoupon(e) {
        const { userCoupons } = this.state;
        const code = e.refTarget.getAttribute('data-code');
        let selectCoupon: any = { priceCent: 0 };
        userCoupons.forEach(item => {
            if (item.couponCode === code) {
                selectCoupon = item;
            }
        });
        this.setState({
            selCouponCode: code || '',
            selectCoupon
        });
    }
    changeTab(e) {
        const type = e.refTarget.getAttribute('data-panel');

        this.setState({
            tabIndex: type
        });
    }
    // 领取优惠券
    getCode(e) {
        const { pickTag } = this.state;
        const actionUrl = e.refTarget.getAttribute('data-actionurl');
        const couponUniqKey = e.refTarget.getAttribute('data-uniq');
        if (actionUrl && Platform.isMuCang) {
            openWeb({
                url: actionUrl,
                title: null
            });
            return;
        }

        if (pickTag) {
            return;
        }
        this.setState({
            pickTag: true
        });

        getCode({ couponUniqKey }).then(() => {
            this.setState({
                pickTag: false
            });
            makeToast('领取成功');
            this.getGoodsCoupons();
            this.getUserCoupons();
        });
    }
    getGoodsCoupons() {
        getGoodsCoupons().then(info => {
            this.setState({
                goodsCoupons: info.goodsCoupons
            });
        });
    }
    getUserCoupons() {
        const { selCouponCode, sessionIds } = this.state;
        getUserCoupons({
            sessionIds: sessionIds.join(',')
        }).then(info => {
            const isSelectedValid = !!info.canUseCoupons.find(item => {
                return String(item.couponCode) === selCouponCode;
            });

            this.setState({
                userCoupons: info.userCoupons,
                isSelectedValid: isSelectedValid,
                canUseCoupons: info.canUseCoupons,
                usedCoupons: info.usedCoupons,
                expiredCoupons: info.expiredCoupons
            });
        });
    }
}
