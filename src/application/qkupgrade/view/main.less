.page-qkupgrade {
      background: linear-gradient(180deg, #FFEDDC 0%, #FFFAF0 38%, #FFFFFF 77%, #FFFFFF 100%);
    height: 100%;
    position: relative;
    overflow-y: auto;
    .close {
        position: absolute;
        z-index: 10;
        width: 40px;
        height: 40px;
        top: 0px;
        right: 0px;
        background: url(../images/close.png) no-repeat center center/20px 20px;
    }

    .section {
        display: flex;
        flex-direction: column;
        flex: 1;

        .content {
            flex: 1;
            overflow-y: scroll;
        }

        .sec1 {
            position: relative;
            display: flex;
            padding: 30px 15px 15px 15px;
            background: linear-gradient(180deg, #FFEDDC 0%, #FFFAF0 100%);

            .avatar {
                width: 54px;
                height: 54px;
                display: block;
                border-radius: 100%;
            }

            .desc {
                padding-left: 10px;

                .p1 {
                    font-size: 14px;
                    font-weight: bold;
                    color: #333333;
                    line-height: 20px;
                    overflow: hidden;
                    display: flex;

                    .username {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 1;
                        max-width: 175px;
                    }
                }

                .p2 {
                    font-size: 22px;
                    font-weight: bold;
                    padding-top: 4px;
                    color: #692204;
                    line-height: 30px;
                }
            }

            .close {
                width: 44px;
                height: 44px;
                background: url(../images/close.png) no-repeat center;
                background-size: 22px 22px;
                position: fixed;
                right: 0px;
                top: 0px;
            }


        }

        .sec2 {
            padding: 20px 15px 0 15px;
            background: #ffffff;
            border-radius: 10px 10px 0px 0px;

            h3 {
                font-size: 16px;
                font-weight: bold;
                line-height: 21px;
                text-align: center;
                color: #692204;

                b {
                    color: #F73B31;
                }
            }

            .steps {
                display: flex;
                justify-content: space-between;
                padding: 15px 3px 0 3px;

                .step {
                    width: 78px;
                    height: 86px;
                    border-radius: 5px;
                    // border: 1px solid #FDDEC8;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    box-sizing: border-box;
                    background: url(../images/13.png) no-repeat;
                    background-size: 100% 100%;

                    i {
                        width: 33px;
                        height: 15px;
                        box-sizing: border-box;
                        margin-top: 1px;
                        margin-left: 1px;
                    }

                    .icon1 {
                        background: url(../images/15.png) no-repeat;
                        background-size: 100% 100%;
                    }

                    .icon2 {
                        background: url(../images/16.png) no-repeat;
                        background-size: 100% 100%;
                    }

                    .icon3 {
                        background: url(../images/17.png) no-repeat;
                        background-size: 100% 100%;
                    }

                    .icon4 {
                        background: url(../images/21.png) no-repeat;
                        background-size: 100% 100%;
                    }

                    span {
                        font-size: 13px;
                        color: #8C3418;
                        font-weight: bold;
                        line-height: 16px;
                        padding: 0px 10px;
                        text-align: center;
                        flex: 1;
                        display: flex;
                        align-items: center;
                    }

                    label {
                        font-size: 12px;
                        color: #A03C1C;
                        line-height: 17px;
                        text-align: center;
                        padding: 3px 0;
                        // width: 37px;
                    }
                }

                .step4 {
                    background: url(../images/14.png) no-repeat;
                    background-size: 100% 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: none;

                    b {
                        font-size: 13px;
                        font-weight: 500;
                        color: #AA4120;
                        line-height: 18px;
                        text-align: center;
                        padding: 0 3px;
                    }
                }
            }

            .goods-w {
                width: 345px;
                height: 100px;
                background: url(../images/19.png) no-repeat;
                background-size: 100% 100%;
                display: flex;
                box-sizing: border-box;
                padding: 7px 0;
                margin-top: 10px;

                .desc {
                    width: 58px;
                    font-size: 12px;
                    font-weight: bold;
                    color: #692204;
                    line-height: 17px;
                    display: flex;
                    flex-direction: column;
                    text-align: center;
                    align-items: center;
                    justify-content: center;
                    padding: 0 3px;

                    label {
                        color: #F73B31;
                        font-size: 16px;

                        i {
                            font-size: 12px;
                        }
                    }
                }

                .goods {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    padding-right: 5px;

                    .item {
                        width: 65px;
                        height: 86px;
                        background: linear-gradient(163deg, #FFFBF6 0%, #FFF7EE 100%);
                        border-radius: 5px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        padding: 0 5px;
                        box-sizing: border-box;
                        flex: 1;

                        .p1 {
                            font-size: 13px;
                            font-weight: bold;
                            color: #A03C1C;
                            line-height: 16px;
                            text-align: center;
                            line-height: 1.5;
                        }

                        .p2 {
                            font-size: 16px;
                            font-weight: bold;
                            color: #A03C1C;
                            line-height: 16px;
                            text-align: center;
                            padding-top: 5px;

                            i {
                                font-size: 12px;
                            }
                        }

                        .p3 {
                            font-size: 12px;
                            font-weight: bold;
                            color: #A03C1C;
                            line-height: 16px;
                            text-align: center;
                            padding-top: 5px;

                            i {
                                display: none;
                            }
                        }
                    }

                    .con {
                        width: 19px;
                        height: 19px;
                        background: url(../images/18.png) no-repeat;
                        background-size: 100% 100%;
                        margin: 0 -7px;
                        position: relative;
                    }
                }
            }


        }
    }

    .paytype-box {
        padding: 0 15px;
    }
   
    .buy-footer {
        background-color: transparent;
        .coupon-pick {
            background-color: transparent;
        }
    }
}
