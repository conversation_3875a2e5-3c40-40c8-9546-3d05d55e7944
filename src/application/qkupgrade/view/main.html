<import name="style" content="./main" />

<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />

<div class="page-qkupgrade">
    <div class="section">
        <div class="content">
            <div class="sec1">
                <img class="avatar" src="{{state.userData.avatar}}" alt="" />
                <div class="desc">
                    <p class="p1">
                        <span class="username"
                            >亲爱的会员{{state.userData.nickname}}</span
                        ><span class="txt">，邀请您</span>
                    </p>
                    <p class="p2">超值升级为全科超级VIP</p>
                </div>
                <span class="close" sp-on:click="close"></span>
            </div>
            <div class="sec2">
                <h3>-- 即刻开通，享超值特权 --</h3>
                <div class="goods-w">
                    <div class="desc">
                        <span>比分开买立省</span>
                        <label><i>¥</i>{{state.comparePricePool[self.nowGoodInfo.groupKey].diffPrice}}</label>
                    </div>
                    <div class="goods">
                        <sp:each
                            for="state.comparePricePool[self.nowGoodInfo.groupKey].groupItems"
                            value="item"
                            index="index"
                        >
                            <div class="item">
                                <p class="p1">{{item.name}}</p>
                                <p class="{{item.price ? 'p2' : 'p3'}}">
                                    <i>¥</i
                                    ><b>{{item.price || item.description}}</b>
                                </p>
                            </div>
                            <sp:if value="index !== state.groupItems.length-1">
                                <div class="con"></div>
                            </sp:if>
                        </sp:each>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <com:buyButton>
        <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
            {{self.nowCouponInfo.couponCode?'已优惠' +
            self.nowCouponInfo.priceCent + '元':''}}
        </div>
    </com:buyButton>
</div>
