/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */
import { CarType, Platform, URLCommon } from ':common/env';
import Texts from ':common/features/texts';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
import { ActivityInfo, getActivityTime } from ':store/chores';
import { promiseList } from ':common/features/promise';
interface State {
    carText: string,
    statusBarHeight: number,
    activityInfo?: ActivityInfo
}
interface Props {
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    get authList() {

        const list = [
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/06/28/10/5109505fba99480095e619f86b3b5872.png',
                uniqkey: promiseList.jjtk,
                dec: (() => {
                    let text = '精简题库';
                    switch (URLCommon.tiku) {
                        case CarType.CAR:
                            text = '精简500题';
                            break;
                        case CarType.MOTO:
                            text = '精简题库';
                            break;
                        case CarType.TRUCK:
                            text = '精简600题';
                            break;
                        case CarType.BUS:
                            text = '精简600题';
                            break;
                        default:
                            break;
                    }
                    return text;
                })()
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/06/28/10/d6ede198bc5f43eabf1a076095ff6e89.png',
                uniqkey: promiseList.zskcmn,
                dec: '真实考场模拟'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/06/28/10/d0229cd5ba04413a8342aa64b7cafd40.png',
                uniqkey: promiseList.kqmj,
                dec: '考前秘卷'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/06/28/10/617539745bda4e56b0aaba343c1d31fd.png',
                uniqkey: promiseList.bgbc,
                dec: '不过补偿'
            }
        ];
        if (URLCommon.isZigezheng) {
            list.pop();
        }

        return list;
    }
    get getElderAuthList() {
        return [
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/08/02/10/38ef82f920944e5bb2374fe9f878c6a3.png',
                uniqkey: promiseList.jhk,
                dec: '精华课',
                dec2: '专属课程'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/08/02/10/f4170e6619bf4290b5ceef9f542b2aa1.png',
                uniqkey: promiseList.jjtk,
                dec: '精简题库',
                dec2: '高仿真'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/08/02/10/3603bf20f25e44d8b01ac95c650b4801.png',
                uniqkey: promiseList.zskcmn,
                dec: '真实考场模拟',
                dec2: '高效冲刺'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/08/02/10/b5f32a1cd65a45c789eeb6fbeb6fab45.png',
                uniqkey: promiseList.kqmj,
                dec: '考前秘卷',
                dec2: '查看说明'
            },
            {
                icon: 'http://exam-room.mc-cdn.cn/exam-room/2022/08/02/10/6a336a5d7acd4909a4ca7bd1b7a1bb3b.png',
                uniqkey: promiseList.bgbc,
                dec: '不过补偿',
                dec2: '专属课程'
            }
        ];
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        let carText = '';
        if (URLCommon.isZigezheng) {
            carText = '资格证考前秘卷';
        } else {
            carText = `${Texts.currentCarStyleTxt}${Texts.currentcarStyleLicenseTxt}${Texts.currentKemuTxt}考前秘卷`;
        }

        this.state = {
            carText: carText,
            statusBarHeight: 75
        };
        this.props = {
        };

    }
    willMount() {

        MCProtocol.Core.System.env((data) => {
            let statusBarHeight = data.data.statusBarHeight;

            if (Platform.isAndroid) {
                statusBarHeight /= window.devicePixelRatio;
            }

            this.setState({
                statusBarHeight: statusBarHeight + 45
            });
        });
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    didMount() {
        this.getActivityInfo();
    }

    async getActivityInfo() {
        const retData = await getActivityTime();
        this.setState({
            activityInfo: retData
        });
    }
}
