<import name="style" content="./main" />
<import name="SendVipEnter" content=":component/sendVipEnter/main" />
<import name="wenda" content=":component/wenda/main" />

<div
    class="panel-kqmj-kemu14"
    style="padding-top:{{ state.statusBarHeight }}px"
>
    <div class="ipad-box">
        <div class="phone-box">
            <div class="mj-img">
                <div class="m0"></div>
                <div class="m1">
                    <div
                        class="bg1 {{URLCommon.isZigezheng ? 'zgz': ''}}"
                    ></div>
                </div>

                <div class="m2">
                    <div class="bg1">
                        <div class="con">
                            <sp:if value="URLCommon.isElder">
                                <div class="elder_t"></div>
                                <div class="cartext elder_cartext">
                                    {{state.carText}}
                                </div>
                                <sp:else />
                                <div class="cartext">{{state.carText}}</div>
                            </sp:if>
                            <div
                                class="bg2 {{URLCommon.tiku}} {{URLCommon.isScore12 ? 'mf': ''}} {{URLCommon.isZigezheng ? 'zgz': ''}} {{URLCommon.isElder?'elder-bg2':''}}"
                            >
                            </div>
                        </div>
                    </div>
                </div>

                <div class="m3">
                    <sp:if value="URLCommon.isNormal">
                        <div class="promision">
                            <div class="title">
                                —— 尊享{{self.authList.length}}大权益 ——
                            </div>
                            <div class="icon-list">
                                <sp:each for="self.authList">
                                    <div
                                        class="icon"
                                        sp-on:click="goAuth"
                                        data-uniqkey="{{$value.uniqkey}}"
                                    >
                                        <img src="{{$value.icon}}" />
                                        <div class="dec-box">
                                            <span class="dec-span"
                                                >{{$value.dec}}</span
                                            >
                                        </div>
                                    </div>
                                </sp:each>
                            </div>
                        </div>
                    </sp:if>
                    <sp:if value="URLCommon.isElder">
                        <div class="elder-promision">
                            <div class="elder-icon-list">
                                <sp:each for="self.getElderAuthList">
                                    <div
                                        class="icon {{$index==2?'icon-font-length':''}}"
                                        sp-on:click="goAuth"
                                        data-uniqkey="{{$value.uniqkey}}"
                                    >
                                        <img src="{{$value.icon}}" />
                                        <div class="elder-dec-box">
                                            <span class="elder-dec-span"
                                                >{{$value.dec}}</span
                                            >
                                            <span class="elder-dec2"
                                                >{{$value.dec2}}</span
                                            >
                                        </div>
                                    </div>
                                </sp:each>
                            </div>
                        </div>
                    </sp:if>
                    <div class="bg1"></div>
                </div>

                <div class="m4 {{URLCommon.isElder?'elder-m4':''}}"></div>
            </div>
        </div>
    </div>
    <com:SendVipEnter name="right" entranceCode="{{URLCommon.kemu === 1?'ke1_kqmj_right':'ke4_kqmj_right'}}"
        position="right" />
</div>
