.panel-kqmj-kemu14 {
    flex: 1;
    background-color: #f9ead3;

    .ipad-box {
        height: 100%;

        .phone-box {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
    }

    .mj-img {
        flex: 1;
        position: relative;
        padding: 15px 0;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;

        .m0 {
            position: absolute;
            left: 0;
            top: -44px;
            width: 135px;
            height: 174px;
            background: url(../images/11.png) no-repeat;
            background-size: 100% 100%;
        }

        .m1 {
            padding: 0 15px;
            flex-shrink: 0;

            .bg1 {
                height: 140px;
                background: url(../images/3.png) no-repeat;
                background-size: 100% 100%;

                &.zgz {
                    background: url(../images/3_1.png) no-repeat;
                    background-size: 100% 100%;
                }
            }
        }

        .m2 {
            flex: 1;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding-bottom: 80px;
            min-height: 300px;
            box-sizing: content-box;

            .bg1 {
                padding: 0 15px;
                height: 100%;
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                display: flex;
                flex-direction: column;

                .con {
                    height: 100%;
                    background: url(../images/4.png) repeat-y;
                    background-size: 100% 100%;
                }
            }

            .elder_t {
                height: 31px;
                width: 161px;
                background: url(../images/10.png) no-repeat;
                background-size: 100% 100%;
                margin: -5px auto 0 auto;
            }

            .cartext {
                font-size: 16px;
                font-weight: bold;
                color: #88574c;
                line-height: 22px;
                letter-spacing: 1px;
                text-align: center;
                position: relative;

                &.elder_cartext {
                    top: 10px;
                }
            }

            .bg2 {
                position: relative;
                height: 275px;
                width: 100%;
                background: url(../images/7_1.png) no-repeat;
                background-size: 100% 100%;
                margin-top: -15px;

                &.mf {
                    background: url(../images/7_3.png) no-repeat;
                    background-size: 100% 100%;
                }

                &.zgz {
                    background: url(../images/7_2.png) no-repeat;
                    background-size: 100% 100%;
                }

                &.elder-bg2 {
                    background: url(../images/jk_img_kqyt_wenan.png) no-repeat;
                    background-size: 100% 100%;
                }

                .summer-icon {
                    width: 36px;
                    height: 16px;
                    background: url(https://jiakao-web.mc-cdn.cn/jiakao-web/2022/06/21/14/d20ba20c83df4aac90653c47a90a71d1.png) no-repeat;
                    background-size: 100% 100%;
                    position: absolute;
                    top: 238px;
                    right: 55px;
                }
            }
        }

        .m3 {
            padding: 0 15px;
            position: relative;

            .promision {
                position: absolute;
                z-index: 10;
                bottom: 40px;
                left: 35px;
                width: 302px;
                height: 90px;

                .title {
                    color: #85554a;
                    text-align: center;
                    font-size: 14px;
                    font-weight: bold;

                    &.elder-title {
                        font-size: 16px;
                        font-weight: 500;
                        text-align: center;
                        color: #88574c;
                        line-height: 22px;
                    }
                }

                .icon-list {
                    display: flex;
                    align-items: center;
                    justify-content: space-around;
                    position: absolute;
                    width: 100%;
                    bottom: 20px;

                    &.elder-icon-list {
                        bottom: 25px;
                    }

                    .icon {
                        position: relative;
                        display: flex;
                        align-items: center;
                        flex-direction: column;

                        img {
                            width: 32px;
                            height: 32px;
                            display: block;
                            margin: 0 auto;
                        }

                        .dec-box {
                            position: absolute;
                            left: 50%;
                            bottom: 0;
                            transform: translate(-50%, 100%);
                            text-align: center;

                            .dec-span {
                                color: #3f383c;
                                font-size: 13px;
                                line-height: 18px;
                                padding-top: 6px;
                                white-space: nowrap;
                                display: block;
                            }

                            &.elder-dec-box {
                                width: 100%;

                                .dec-span {
                                    font-size: 12px;
                                    font-weight: 400;
                                    text-align: center;
                                    color: #3f383c;
                                    padding-top: 0px;
                                }

                                .elder-dec2 {
                                    display: block;
                                    font-size: 11px;
                                    font-weight: 400;
                                    text-align: center;
                                    color: #85554a;
                                    line-height: 16px;
                                }
                            }
                        }
                    }
                }
            }

            .elder-promision {
                width: auto;
                height: 120px;
                position: absolute;
                left: 25px;
                right: 30px;
                margin: 0 auto;
                z-index: 10;
                bottom: 20px;
                background: url(../images/elder-bg.png) no-repeat center;
                background-size: 100% 100%;

                .elder-icon-list {
                    display: flex;
                    align-items: center;
                    justify-content: space-around;
                    width: 100%;
                    position: absolute;
                    bottom: 15px;

                    .icon {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-direction: column;
                        flex: 1;

                        &.icon-font-length {
                            flex: 0 0 80px;
                        }

                        img {
                            width: 32px;
                            height: 32px;
                            display: block;
                            margin: 0 auto;
                        }

                        .elder-dec-box {
                            text-align: center;
                            width: 100%;

                            .elder-dec-span {
                                line-height: 18px;
                                padding-top: 6px;
                                white-space: nowrap;
                                display: block;
                                font-size: 12px;
                                font-weight: 400;
                                text-align: center;
                                color: #3f383c;
                                padding-top: 0px;
                            }

                            .elder-dec2 {
                                transform: scale(0.9);
                                display: block;
                                font-size: 11px;
                                font-weight: 400;
                                text-align: center;
                                color: #85554a;
                                line-height: 16px;
                            }
                        }
                    }
                }
            }

            .bg1 {
                height: 62px;
                background: url(../images/5.png) no-repeat;
                background-size: 100% 100%;
            }

            .btn {
                width: 262px;
                height: 62px;
                background: url(../images/8.png) no-repeat;
                background-size: 100% 100%;
                position: absolute;
                top: -20px;
                left: 50%;
                transform: translate3d(-50%, 0, 0);
                font-size: 18px;
                font-weight: 600;
                color: #88574c;
                line-height: 58px;
                letter-spacing: 1px;
                text-align: center;

                .tag {
                    top: -6px;
                }
            }
        }

        .m4 {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 71px;
            height: 203px;
            background: url(../images/6.png) no-repeat;
            background-size: 100% 100%;

            &.elder-m4 {
                bottom: 40px;
            }
        }
    }
}
