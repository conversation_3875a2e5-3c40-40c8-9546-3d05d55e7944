/*
 * ------------------------------------------------------------------
 * 考前密卷
 * ------------------------------------------------------------------
 */

import { ABTestKey, ABTestType, CarType, KemuType, persuadeDialogAllow, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import { formatPrice } from ':common/utils';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import ElderCouponDialog from ':component/elderCoupon/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import { getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupComparePrice, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { typeCode } from ':common/features/bottom';
import Texts from ':common/features/texts';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import { setStatusBarTheme, webClose } from ':common/core';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackExit, trackGoPay, trackPageLoad, trackPageShow } from ':common/stat';
import { onWebBack } from ':common/features/persuade';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { BUYED_URL, openAuth } from ':common/navigate';
import { couponAnimate, pauseAllVideos, scrollTop } from ':common/features/dom';
import { setEmbeddedHeight } from ':common/features/embeded';
import { onPageShow } from ':common/features/page_status_switch';
import { zigezhengGroupKeyObj, zigezhengTextMap } from ':common/features/zigezheng';
import { isHubei } from ':common/features/locate';
import jump from ':common/features/jump';
import { getTabIndex } from ':common/features/cache';
import isNumber from 'lodash/isNumber';
import { hesitateUserPersuade } from ':common/features/hesitate';
import { getAbtest } from ':store/chores';

interface State {
    kemu: KemuType,
    tiku: CarType,
    isHubei: boolean,
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    standbyPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    prevScrollTop: number
}

// 标记是否展示过挽留弹窗
let flag = false;
let timer;
const pageNameMap = {
    [GroupKey.ChannelKemuAll]: '全科VIP页',
    [GroupKey.ChannelKe4Short]: '短时提分页',
    [GroupKey.ChannelKe1Ke4Group]: '科1科4组合包页',
    [GroupKey.HcChannelKemuAll]: '全科VIP页',
    [GroupKey.KcChannelKemuAll]: '全科VIP页',
    [GroupKey.MotoChannelKemuAll]: '全科VIP页',
    [GroupKey.KcChannelKe1Ke4Group]: '科1科4组合包页',
    [GroupKey.HcChannelKe1Ke4Group]: '科1科4组合包页',
    [GroupKey.MotoChannelKe1Ke4Group]: '科1科4组合包页'
};

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog,
        ElderCouponDialog: ElderCouponDialog
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    get pageName() {
        return pageNameMap[this.nowGoodInfo.groupKey] || '考前秘卷页';
    }
    get pageTitle() {
        const { tabIndex } = this.state;
        let title = '考前秘卷';
        if (tabIndex === 0) {
            if (URLCommon.isZigezheng) {
                title = `${zigezhengTextMap[URLCommon.tiku]}考前秘卷`;
            } else if (URLCommon.isScore12) {
                title = '考前秘卷';
            } else {
                title = `${Texts.currentKemuTxt}考前秘卷`;
            }
        } else {
            title = this.nowGoodInfo.name;
        }
        return title;
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    get qaKey() {
        return URLCommon.isZigezheng ? '' : 'qaKey3';
    }
    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {
        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;
        const goodsInfoPool: GoodsInfo[] = [];
        let tabIndex;

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            kemu,
            isHubei: true,
            tiku,
            tabIndex: tabIndex || 0,
            goodsInfoPool,
            standbyPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            prevScrollTop: 0
        };

    }

    async setGroupKey() {
        const { tiku, kemu, goodsInfoPool, standbyPool } = this.state;

        const discount: boolean = URLParams.discount === 'true';

        if (URLCommon.is3DSingle) {
            switch (tiku) {
                case CarType.CAR:
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1D3
                        } as GoodsInfo);
                    } else {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe4D3
                        } as GoodsInfo);
                    }
                    break;
                case CarType.TRUCK:
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe1D3
                        } as GoodsInfo);
                    } else {
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe4D3
                        } as GoodsInfo);
                    }

                    break;
                case CarType.BUS:
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.KcChannelKe1D3
                        } as GoodsInfo);
                    } else {
                        goodsInfoPool.push({
                            groupKey: GroupKey.KcChannelKe4D3
                        } as GoodsInfo);
                    }
                    break;
                default: break;
            }

        } else if (URLCommon.isElder) {
            if (kemu === 1) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ElderChannelKe1
                } as GoodsInfo);

                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe1Ke4Group
                } as GoodsInfo);

                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKemuAll
                } as GoodsInfo);
            } else {
                goodsInfoPool.push({
                    groupKey: GroupKey.ElderChannelKe4
                } as GoodsInfo);
            }
        } else if (URLCommon.isZigezheng) {
            goodsInfoPool.push({
                groupKey: zigezhengGroupKeyObj[tiku]
            } as GoodsInfo);
        } else if (URLCommon.isScore12) {
            switch (tiku) {
                case CarType.CAR:
                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKou12
                    } as GoodsInfo);
                    break;
                case CarType.TRUCK:
                    goodsInfoPool.push({
                        groupKey: GroupKey.HcChannelKou12
                    } as GoodsInfo);
                    break;
                case CarType.BUS:
                    goodsInfoPool.push({
                        groupKey: GroupKey.KcChannelKou12
                    } as GoodsInfo);
                    break;
                default: break;
            }
        } else {
            const { strategy } = await getAbtest(URLCommon.tiku);
            switch (tiku) {
                case CarType.CAR:
                    // 激励学员的场景是以更优惠的价格售卖
                    if (discount) {
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.ExcellentChannelKe1 : GroupKey.ExcellentChannelKe4
                        } as GoodsInfo);
                    } else {
                        // eslint-disable-next-line no-lonely-if
                        if (strategy[ABTestKey.key29] === ABTestType.B) {
                            goodsInfoPool.push({
                                groupKey: kemu === 1 ? GroupKey.ChannelKe1Month : GroupKey.ChannelKe4Month 
                            } as GoodsInfo);
                            standbyPool.push({
                                groupKey: kemu === 1 ? GroupKey.ChannelKe1 : GroupKey.ChannelKe4
                            } as GoodsInfo);
                        } else {
                            goodsInfoPool.push({
                                groupKey: kemu === 1 ? GroupKey.ChannelKe1 : GroupKey.ChannelKe4
                            } as GoodsInfo);
                        }

                    }

                    if (kemu === KemuType.Ke1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1Ke4Group
                        } as GoodsInfo);

                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKemuAll
                        } as GoodsInfo);
                    }

                    if (kemu === KemuType.Ke4) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe4Short
                        } as GoodsInfo);
                    }

                    break;
                case CarType.TRUCK:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.HcChannelKe1 : GroupKey.HcChannelKe4
                    } as GoodsInfo);

                    if (kemu === KemuType.Ke1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe1Ke4Group
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKemuAll
                        } as GoodsInfo);
                    }

                    break;
                case CarType.BUS:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.KcChannelKe1 : GroupKey.KcChannelKe4
                    } as GoodsInfo);
                    if (kemu === KemuType.Ke1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.KcChannelKe1Ke4Group
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: GroupKey.KcChannelKemuAll
                        } as GoodsInfo);
                    }
                    break;
                case CarType.MOTO:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.MotoChannelKe1 : GroupKey.MotoChannelKe4
                    } as GoodsInfo);

                    if (kemu === KemuType.Ke1) {
                        this.state.tabIndex = 1;
                        goodsInfoPool.push({
                            groupKey: GroupKey.MotoChannelKe1Ke4Group
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: GroupKey.MotoChannelKemuAll
                        } as GoodsInfo);
                    }
                    break;
                case CarType.GUACHE:
                    // 挂车只有科四
                    goodsInfoPool.push({
                        groupKey: GroupKey.GcChannelKe4
                    } as GoodsInfo);

                    goodsInfoPool.push({
                        groupKey: GroupKey.GcChannelKe4Short
                    } as GoodsInfo);

                    break;
                default: break;
            }
        }
    }
    async didMount() {

        await this.setGroupKey();

        await this.getGoodInfo();

        const { tabIndex, goodsInfoPool } = this.state;

        // 优先展示底部按钮再加载数据；
        this.setPageInfo();

        setPageName(this.pageName);

        // 页面进出时长打点
        trackPageLoad();

        onPageShow(() => {
            this.setPageInfo();
        });

        // 判断是否是湖北
        isHubei().then(isHubei => {
            this.setState({
                isHubei
            });
        });

        // app代理方法
        this.appEventProxy();

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: goodsInfoPool[tabIndex].groupKey
        });

    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;

            this.setState({
                prevScrollTop
            });
        }, 10);
    }
    appEventProxy() {
        // 可能是半截页面打开，所以设置一下全屏
        setEmbeddedHeight(0);

        setStatusBarTheme('dark');

        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        // 退出当前tab的打点
        this.leavePageCall();

        // 回到滚动的顶部
        scrollTop(document.querySelector('.page-kqmj .body-panel'));

        // 暂停所有视频
        pauseAllVideos();

        this.setState({
            tabIndex
        }, () => {
            this.children.sendKe2Dialog.show({ type: 'autoClose' });
            setPageName(this.pageName);

            trackPageShow();

            this.setPageInfo();

        });

    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const fragmentName1 = '底部吸底按钮';
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        let bottomType: typeCode = typeCode.type4;

        // 全科并且有活动的时候按钮不同
        if (nowGoodInfo.groupKey === GroupKey.ChannelKemuAll && nowGoodInfo.inActivity) {
            bottomType = typeCode.type5;
        }

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1
                });
                break;
            case typeCode.type5:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${nowGoodInfo.inActivity.discountedPrice}元`,
                    price: this.showPrice,
                    originalPrice: '日常价￥' + nowGoodInfo.inActivity.preDiscountPrice,
                    fragmentName1
                });
                break;
            default:
                break;
        }
    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const newGoodsPool = [];
        const groupKeys = this.state.standbyPool.concat(this.state.goodsInfoPool).map(item => item.groupKey);
        const standbyGoodsCount = this.state.standbyPool.length;

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            const [standbyGoodsListInfo, normalGoodsListInfo] = [goodsListInfo.slice(0, standbyGoodsCount), goodsListInfo.slice(standbyGoodsCount)];

            normalGoodsListInfo.forEach((goodInfo, index) => {
                if (index === 0) {
                    if (URLCommon.isZigezheng || URLCommon.isScore12) {
                        goodInfo.name = '考前秘卷';
                    } else {
                        goodInfo.name = `${Texts.currentKemuTxt}考前秘卷`;
                    }
                }
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    jump.replace(BUYED_URL);
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }

            // showPage用来控制模块的展示， 先渲染需要渲染的tabPage，加快首次渲染速度
            newGoodsPool[tabIndex].showPage = true;

            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool,
                standbyPool: standbyGoodsCount ? (() => {
                    return [
                        {
                            ...newGoodsPool[0],
                            name: `${Texts.currentKemuTxt}考前秘卷`,
                            tempName: '连续包月'
                        },
                        ...standbyGoodsListInfo.map(goodInfo => ({
                            ...goodInfo,
                            name: `${Texts.currentKemuTxt}考前秘卷`,
                            tempName: '半年卡'
                        }))
                    ];

                })() : []
            }, () => {
                this.children.sendKe2Dialog.show({ type: 'autoClose' });
            });
            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await Promise.all([
                    this.getCoupon(goodsListInfo),
                    this.getLabel(goodsListInfo),
                    this.getComparePrice(goodsListInfo)
                ]);

                couponAnimate({
                    couponTargetDomSelect: '.coupon-position-bottom',
                    compareTargetDomSelect: `.bottom-tabs .${newGoodsPool[2]?.groupKey}`,
                    couponData: this.nowCouponInfo,
                    compareData: this.state.comparePricePool[newGoodsPool[2]?.groupKey],
                    goodsData: this.nowGoodInfo,
                    compareGoodsData: newGoodsPool[2],
                    compareAnimateType: 4
                });

                this.setPageInfo();
            }, 60);

            // 500ms后再渲染其他tabPage，
            setTimeout(() => {
                newGoodsPool.forEach(item => {
                    item.showPage = true;
                });

                this.setState({
                    goodsInfoPool: newGoodsPool
                });
            }, 500);
        });
    }
    getCoupon = async (goodsInfoPool: GoodsInfo[]) => {
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel(goodsInfoPool: GoodsInfo[]) {
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice(goodsInfoPool: GoodsInfo[]) {
        await GroupComparePrice({ groupKeys: goodsInfoPool.map(item => item.groupKey).join(',') }).then(comparePricePool => {
            for (const k in comparePricePool) {
                const item = comparePricePool[k];
                comparePricePool[k] = {
                    diffPrice: item.savePrice,
                    allPrice: item.allPrice,
                    groupItems: item.groupItems
                };
            }
            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
        }).catch(async () => {
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                coupon: this.nowCouponInfo,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    payBtnCall = (e) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');

        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1
            });

        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });

        }
    }
    goAuth = async (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
    // 退出页面的回调
    async goBackPage() {
        const { tabIndex, goodsInfoPool, kemu, labelPool } = this.state;
        const nowGoodInfo = goodsInfoPool[tabIndex];

        if (await hesitateUserPersuade()) {
            return;
        }

        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: nowGoodInfo,
                groupKey: nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                title: `真的要放弃${nowGoodInfo.name}吗？`,
                txt1: '懒人必备',
                txt2: '省不少时间',
                txt3: '后悔开晚了',
                txt4: '简单好记',
                tag: {
                    text: labelPool[nowGoodInfo.groupKey]?.label
                },
                kemu
            }).then(payType => {
                if (payType === false) {
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            webClose();
        }
    }
    backCall = () => {
        this.goBackPage();
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });

            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    // 离开当前页面
    leavePageCall = () => {
        // 退出当前页面的打点
        setPageName(this.pageName);
        trackExit();
    }
    // 科四切换商品
    changeGoods = (goodsInfo) => {
        const { goodsInfoPool } = this.state;
        const tabIndex = 0;
        goodsInfoPool[tabIndex] = goodsInfo;

        this.setState({
            goodsInfoPool
        });
        this.setPageInfo();
        this.children.sendKe2Dialog.show({ type: 'autoClose' });
    }
}
