/* eslint-disable max-len */
/* eslint-disable no-bitwise */
/* eslint-disable newline-after-var */
/* eslint-disable quotes */
export const answerFn = function (question) {
    const answer = {
        selectList: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
        // 服务端定的A是16，B是32，也就是对应16<<(selectList对应的下标)，多选对应的数字之和
        selectListMap: { 'A': 16 << 0, 'B': 16 << 1, 'C': 16 << 2, 'D': 16 << 3, 'E': 16 << 4, 'F': 16 << 5, 'G': 16 << 6, 'H': 16 << 7 },
        optionTypes: ['判断题', '单选题', '多选题'],
        question: question,
        initQuestion: function () {
            const question = this.question;
            const options = this.selectList.map((item, index) => {
                let option = {};
                if (question[`option${item}`]) {
                    option = {
                        showKey: item,
                        value: question[`option${item}`],
                        correct: !!((16 << index) & question.answer)
                    };
                }
                return option;
            });
            // 当前题目的Id
            this.qId = question.questionId;

            this.starTitle = question.starTitle;
            this.starImgUrl = question.starImgUrl;
            // 展示题目标题
            this.title = this.handleQuestion();
            // 展示媒体类型
            this.mediaType = question.mediaType;
            // 展示题目类型
            this.showOptionType = this.optionTypes[question.style];
            // 题目类型
            this.optionType = question.style;
            // 选项列表
            this.options = options;
            // 媒体数据
            this.mediaContent = question.mediaUrl;
            // 正确选项列表
            this.correctOptions = this.getCorrectOptions();
            // 正确选项字符串
            this.StrCorrect = this.getStrCorrect();
            // 题目的状态[1,2,3] 1表示未做 2表示做对了 3表示做错了
            this.isCorrent = 1;
            // 用户选择答案
            this.userSelect = {};
            // 用户停留时长
            this.stayTime = 0;
            // 是否被收藏
            this.collection = false;
            // 难度
            this.difficulty = question.difficulty;
            // 错误率
            this.wrongRate = question.wrongRate;
            this.showExplain = question.explain || '暂无';

            // vip技巧
            this.illiteracyExplain = question.illiteracyExplain || '   ';

            //  vip技巧解析
            this.conciseExplain = question.conciseExplain;
            // 技巧标题
            this.artfulDetail = this.handleArtfulDetail();
            // 技巧描述
            this.conciseDetail = question.conciseDetail;
            // 题目解析
            this.detail = this.handleDetail();
        },
        getCorrectOptions() {
            return this.options.filter(function (item) {
                return item.correct;
            });
        },
        getStrCorrect() {
            return this.getCorrectOptions().map(function (item) {
                return item.showKey;
            }).join(',');
        },
        getUserSelect() {
            let userSelectAnswer = 0;
            Object.keys(this.userSelect).forEach((key) => {
                userSelectAnswer += Number(this.selectListMap[key]);
            });
            return userSelectAnswer || '';
        },
        handleDetail() {
            // explainKeywords 高亮的关键词
            const explainKeywords = question.explainKeywords?.split('|');
            explainKeywords && explainKeywords.forEach((res) => {
                question.detail = question.detail.replace(new RegExp(res, 'gi'), '<em>' + res + '</em>');
            });

            return question.detail;
        },
        handleArtfulDetail() {
            // artfulKeywords 高亮的关键词
            const artfulKeywords = question.artfulKeywords?.split('|');
            artfulKeywords && artfulKeywords.forEach((res) => {
                question.artfulDetail = question.artfulDetail.replace(new RegExp(res, 'gi'), '<em>' + res + '</em>');
            });

            return question.artfulDetail;
        },
        handleQuestion() {
            // keywords 高亮的关键词
            const keywords = question.keywords?.split('|');
            keywords && keywords.forEach((res) => {
                question.question = question.question.replace(new RegExp(res, 'gi'), '<em>' + res + '</em>');
            });

            return question.question;
        }

    };
    answer.initQuestion();
    return answer;
};
