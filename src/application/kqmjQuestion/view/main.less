.page-kqmj-question {
    background-color: #fff;
    .page-header {
        z-index: 1000;
        top: 0;
        left: 0;
        width: 100%;
        .right-container-o {
            margin-right: -0.15rem;
            padding-right: 0.15rem;
            padding-top: 0.15rem;
            padding-bottom: 0.15rem;
            padding-left: 0.15rem;
            width: 22px;
            height: 22px;
            box-sizing: content-box;
        }
    }
    .body-panel {
        flex: 1;
        overflow-y: hidden;
        padding-bottom: 20px;
        .swiper-box {
            padding: 10px 0px 0px 0px;
            height: 100%;
            .swiper {
                height: 100%;
            }
            .swiper-slide {
                width: 100%;
                height: 100%;
                padding: 0px 15px;
                overflow-y: scroll;
            }
            &.show {
                display: block;
            }
            &.hiddren {
                display: none;
            }
            .title {
                display: flex;
                align-items: center;
                .subject {
                    display: inline-block;
                    font-size: 18px;
                    font-weight: 400;
                    color: #000000;
                    padding-bottom: 15px;
                    line-height: 25px;

                    .type {
                        width: 32px;
                        display: inline-block;
                        height: 18px;
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
                        background-position: center center;
                        margin-right: 8px;
                        position: relative;
                        top: 2px;
                        &.type0 {
                            width: 42px;
                            background-image: url("../images/panduan.png");
                        }
                        &.type1 {
                            background-image: url("../images/danxuan.png");
                        }
                        &.type2 {
                            background-image: url("../images/duoxuan.png");
                        }
                    }
                    em {
                        color: #ff4a40;
                        font-style: normal;
                    }
                }
            }

            .select-div {
                display: flex;
                align-items: center;
                padding: 7px 0px;
                justify-content: center;
                &.buxianshi {
                    display: flex;
                }
                &.hide {
                    display: none;
                }
                &:last-child {
                    margin-bottom: 0px;
                }
                .no-checked {
                    display: flex;
                    width: 38px;
                    height: 38px;
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    background-position: center center;
                    background-image: url("../images/no_checked.png");
                    margin-right: 10px;
                    font-size: 16px;
                    color: #333333;
                    align-items: center;
                    justify-content: center;
                    &.has-checked {
                        background-image: url("../images/more_checked.png");
                        color: #ffffff;
                    }
                }
                .title {
                    font-size: 18px;
                    font-weight: 400;
                    color: #333333;
                    line-height: 25px;
                    flex: 1;
                    .number {
                        font-style: normal;
                    }
                }

                &.error {
                    .no-checked {
                        background-image: url("../images/error.png");
                        color: transparent;
                    }
                    .title {
                        color: #ff4a40;
                    }
                }
                &.right {
                    .no-checked {
                        background-image: url("../images/right.png");
                        color: transparent;
                    }
                    .title {
                        color: #00a0f4;
                    }
                }
                &.right-noselect {
                    .no-checked {
                        background-image: url("../images/lanse.png");
                        color: #ffffff;
                    }
                    .title {
                        color: #333333;
                    }
                }
            }
            .show-mediatype {
                padding: 0 0px;
                box-sizing: border-box;
                text-align: center;
                .image,
                .video {
                    max-width: 100%;
                }
                .img-zhanweifu {
                    max-width: 100%;
                    height: 162px;
                    background: url("../images/zhanweifu.png") no-repeat center
                        center;
                    background-size: 100% 162px;
                }
            }
            .more-select {
                height: 44px;
                background: linear-gradient(90deg, #00e0e5, #0086fa);
                border-radius: 22px;
                font-size: 17px;
                font-family: PingFangSC, PingFangSC-Regular;
                font-weight: 400;
                color: #ffffff;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 15px;
            }
            .show-other-info {
                margin-top: 13px;
                .answer {
                    height: 44px;
                    background: #f3f6f8;
                    border-radius: 2px;
                    font-size: 16px;
                    font-family: PingFangSC, PingFangSC-Medium;
                    font-weight: 700;
                    color: #333333;
                    line-height: 44px;
                    padding-left: 15px;
                    margin-bottom: 25px;
                    .select {
                        color: #ff4a40;
                    }
                }
                .jiqiao {
                    .jiqiao-title,
                    .question-title {
                        font-size: 16px;
                        font-family: PingFangSC, PingFangSC-Medium;
                        font-weight: 700;
                        color: #333333;
                        line-height: 22px;
                        margin-bottom: 15px;
                    }
                    .jiqiao-desc {
                        font-size: 20px;
                        font-family: PingFangSC, PingFangSC-Regular;
                        font-weight: 400;
                        color: #333333;
                        line-height: 26px;
                        margin-bottom: 15px;
                        em {
                            color: #ff4a40;
                            font-style: normal;
                        }
                    }
                    .jiqiao-jiexi {
                        font-size: 14px;
                        font-family: PingFangSC, PingFangSC-Regular;
                        font-weight: 400;
                        color: #464646;
                        line-height: 22px;
                        background: #f3f6f8;
                        padding: 10px;
                        margin-bottom: 18px;
                    }
                    .question-desc {
                        font-size: 16px;
                        font-family: PingFangSC, PingFangSC-Regular;
                        font-weight: 400;
                        text-align: left;
                        color: #333333;
                        line-height: 24px;
                        em {
                            font-style: normal;
                            color: #04a5ff;
                        }
                    }
                }
            }
        }
    }
    .footer-box {
        box-shadow: 1px -2px 12px 2px rgb(0 0 0 / 8%);
        background-color: #fff;
        border-radius: 8px 8px 0 0;
        position: relative;
        z-index: 3;
        .footer {
            height: 62px;
            margin: 0px 15px 30px 15px;
            background: url(../images/footer_bg.png) no-repeat center;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            .title {
                font-size: 14px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 500;
                color: #333333;
                line-height: 20px;
            }
            .desc {
                font-size: 12px;
                font-family: PingFangSC, PingFangSC-Regular;
                font-weight: 400;
                color: #88430f;
                line-height: 17px;
                margin-top: 1px;
                .xinxin {
                    display: inline-block;
                    width: 15px;
                    height: 15px;
                    background: url(../images/xinxin.png) no-repeat;
                    background-size: 100% 100%;
                    position: relative;
                    top: 3px;
                    &:last-child {
                        margin-right: 10px;
                    }
                    &:first-child {
                        margin-left: 3px;
                    }
                }
            }
        }
    }
    .showLookAllBuy-mask {
        position: fixed;
        z-index: 100;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .showLookAllBuy-box {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100;
        max-height: 100%;
        overflow-y: auto;
    }
}
