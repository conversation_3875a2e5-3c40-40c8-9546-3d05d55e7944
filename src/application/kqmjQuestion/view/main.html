<import name="style" content="./main" />
<import name="header" content=":component/header/main" />
<import name="BuyDialog" content=":component/buyDialog/main" />
<div class="page-container page-kqmj-question">
    <div class="page-header">
        <com:header title="考前秘卷" theme="white" endTheme="black">
            <div sp:slot="right" class="right-container-o">

            </div>
        </com:header>
    </div>
    <div class="body-panel" ref="bodyPanel">
        <div class="swiper-box">
            <div class="swiper" skip-attribute="class|style" ref="swiper">
                <div class="swiper-wrapper" skip-attribute="class|style">
                    <sp:each for="state.questionList">
                        <div class="swiper-slide" ref="{{'swiperSlide'+$index}}">
                            <div class="title">
                                <div class="subject" sp:if="$value.title">
                                    <span
                                        class="type {{$value.showOptionType==='判断题'&&'type0'}} {{$value.showOptionType==='单选题'&&'type1'}} {{$value.showOptionType==='多选题'&&'type2'}}"></span>
                                    {{#$value.title}}
                                </div>
                            </div>
                            <sp:if value="$value.mediaType != 0">
                                <div class="show-mediatype">
                                    <sp:if value="$value.mediaType == 1">
                                        <image class="image" src="{{$value.mediaContent}}" mode="heightFix" />
                                        <sp:elseif value="state.currentIndex == $index" />
                                        <video class="video" id="video" src="{{$value.mediaContent}}"
                                            autoplay="{{state.currentIndex == $index}}" controls></video>
                                        <sp:else />
                                    </sp:if>
                                </div>
                            </sp:if>
                            <sp:each for="$value.options" key="showKey" value="s$value" index="s$index">

                                <sp:if value="s$value.showKey">
                                    <div class="select-div {{s$value.showKey?'buxianshi':'hide'}} {{self.handleGaoliao($value,s$value)}} "
                                        sp-on:click="selectAnswer" data-select-key="{{s$value.showKey}}"
                                        data-select-value="{{s$value.value}}"
                                        data-select-correct="{{s$value.correct?'1':'0'}}">
                                        <span
                                            class="no-checked {{$value.userSelect[s$value.showKey].showKey==s$value.showKey&&'has-checked'}}">{{s$value.showKey}}</span>
                                        <span class="title">
                                            {{s$value.value}}
                                        </span>
                                    </div>
                                </sp:if>
                            </sp:each>
                            <sp:if value="{{$value.showOptionType==='多选题'&&$value.isCorrent===1}}">
                                <div class="more-select" sp-on:click="nextQuestion">确认答案</div>
                            </sp:if>
                            <sp:if value='{{$value.isCorrent===3||$value.isCorrent===2}}'>
                                <div class="show-other-info">
                                    <div class="answer">答案：{{$value.StrCorrect}}&nbsp;&nbsp;你选择：<span
                                            class="select">{{Object.keys($value.userSelect).sort().join(',')}}</span>
                                    </div>
                                    <div class="jiqiao">
                                        <div class="jiqiao-title">本题技巧</div>
                                        <div class="jiqiao-desc">{{#$value.artfulDetail}}</div>
                                        <div class="jiqiao-jiexi">
                                            {{#$value.conciseDetail}}
                                        </div>
                                        <div class="question-title">题目解析</div>
                                        <div class="question-desc">{{#$value.detail}}</div>
                                    </div>
                                </div>
                            </sp:if>


                        </div>
                    </sp:each>
                </div>
            </div>
        </div>
    </div>
    <div class="footer-box">
        <div class="footer" sp-on:click="buttonClick">
            <div class="title">可试答{{state.questionList.length}}题 当前为第{{state.currentIndex+1}}题</div>
            <div class="desc">本题难度
                <sp:each for='state.questionList[state.currentIndex].difficulty'>
                    <span class="xinxin"></span>
                </sp:each>
                有{{Math.floor((state.questionList[state.currentIndex].wrongRate)*100)}}%的学员答错
            </div>
        </div>
    </div>
    <sp:if value="state.showBuyDialog">
        <div class="showLookAllBuy-mask">
            <div class="showLookAllBuy-box">
                <com:BuyDialog fragmentName1="{{state.fragmentName1}}" type="component" title1="考前秘卷" subTitle1=""
                    close="{{self.closePayModal}}}" isKqmj="{{true}}" />
            </div>
        </div>
    </sp:if>
</div>