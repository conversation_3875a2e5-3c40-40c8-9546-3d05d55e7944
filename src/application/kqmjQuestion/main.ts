/*
 * ------------------------------------------------------------------
 * 考前密卷试卷做题
 * ------------------------------------------------------------------
 */

import { Platform, setPageName, URLCommon } from ':common/env';

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import Swiper from 'swiper';
import 'swiper/swiper-bundle.css';
import { getQuestionList } from ':store/kqmjQuestion';
import { getSystemInfo, openVipWebView, setStatusBarTheme } from ':common/core';
import { answerFn } from './question';
import { makeToast } from ':common/features/dom';
import Header from ':component/header/main';
import BuyDialog, { buyDialogCloseType } from ':component/buyDialog/main';
import { trackPageLoad } from ':common/stat';
import { iosBuySuccess } from ':common/features/ios_pay';
import { newBuySuccess } from ':common/features/pay';
interface State {
    currentIndex: number,
    questionList: any[],
    showBuyDialog: boolean,
    fragmentName1: string
}
let timer;
export default class extends Application<State> {
    swiper: Swiper
    declare children: {
        header: Header;
        BuyDialog: BuyDialog
    };
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {
            currentIndex: 0,
            questionList: [],
            showBuyDialog: false,
            fragmentName1: ''
        };
    }
    async didMount() {
        setStatusBarTheme('dark');
        setPageName('考前秘卷试用页');
        // 页面进出时长打点
        trackPageLoad();
        await this.getQuestion();
        setTimeout(() => {
            this.initSwiper();
        }, 200);

    }
    async getQuestion() {
        const { _userCity, _cityCode } = await getSystemInfo();
        const cityCode = _userCity || _cityCode;
        const questionList = await getQuestionList({
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            practiceType: 'vip-100',
            cityCode: cityCode
        });
        const list = questionList.map(function (item) {
            return answerFn(item);
        });
        this.setState({ questionList: list });
    }
    nextQuestion() {
        let { currentIndex } = this.state;
        const { questionList } = this.state;
        // 如果是只选了一个需要提醒
        if (Object.keys(questionList[currentIndex].userSelect).length <= 1 && questionList[currentIndex].optionType > 1) {
            makeToast('这是一个多选题，有多个答案');
            return;
        }
        // 判断之前要先sort，因为b,a != a,b
        const selectRight = questionList[currentIndex].StrCorrect === Object.keys(questionList[currentIndex].userSelect).sort().join(',');
        questionList[currentIndex].isCorrent = selectRight ? 2 : 3;
        // 做错了，显示解析
        if (questionList[currentIndex].isCorrent === 2) {
            currentIndex++;
            // 做对了并是最后一题
            if (currentIndex > questionList.length - 1) {
                this.endSwiperNext();
                return;
            }
        }
        this.setState({
            currentIndex: currentIndex,
            questionList
        });
        this.swiper.slideTo(currentIndex);
    }
    selectAnswer(e) {
        const list = this.state.questionList;
        const currentIndex = this.state.currentIndex;
        if (list[currentIndex].isCorrent !== 1) {
            return;
        }
        const target = e.refTarget;
        const userSelect = {
            showKey: target.getAttribute('data-select-key'),
            value: target.getAttribute('data-select-value'),
            correct: target.getAttribute('data-select-correct')
        };
        // 单选题
        if (list[currentIndex].optionType <= 1) {
            // 将选择的结果赋值给当前数组对应的数据
            for (const key in list[currentIndex].userSelect) {
                if (key) {
                    delete list[currentIndex].userSelect[key];
                }
            }
            list[currentIndex].userSelect[userSelect.showKey] = userSelect;

        } else if (list[currentIndex].userSelect[userSelect.showKey]) {
            // 多选的时候如果多次点击相同一个选项，第二次点击就是取消
            delete list[currentIndex].userSelect[userSelect.showKey];
        } else {
            list[currentIndex].userSelect[userSelect.showKey] = userSelect;
        }
        this.setState({
            questionList: list
        });
        if (list[currentIndex].optionType <= 1) {
            this.nextQuestion();
        }

    }
    buttonClick() {
        this.setState({ fragmentName1: '底部按钮' });
        this.showBuyDialog();
    }
    showBuyDialog() {
        this.setState({
            showBuyDialog: true
        }, () => {
            this.children.BuyDialog.setPageInfo();
        });
    }
    closePayModal = (closeType, info) => {
        this.setState({
            showBuyDialog: false
        });
        if (closeType === buyDialogCloseType.BOUGHT) {
            if (Platform.isIOS) {
                iosBuySuccess(info);
            } else {
                newBuySuccess(info);
            }
        }
    }
    endSwiperNext() {
        this.setState({ fragmentName1: '试用结束' });
        this.showBuyDialog();
    }
    initSwiper() {
        const $dom = this.getDOMNode().swiper as HTMLElement;
        this.swiper?.destroy();
        this.swiper = new Swiper($dom, {
            loop: false,
            slidesPerView: 1,
            centeredSlides: true,
            initialSlide: 0
        });
        this.swiper.on('slideChange', (swiper) => {

            this.setState({ currentIndex: swiper.activeIndex });
            (this.getDOMNode()['swiperSlide' + swiper.activeIndex] as HTMLElement).scrollTop = 0;
        });
        this.swiper.on('touchEnd', (swiper: any) => {
            if (this.state.questionList[this.state.currentIndex]?.isCorrent !== 1 && swiper.isEnd && swiper.swipeDirection === 'next') {
                this.endSwiperNext();
            }
        });
    }
    handleGaoliao = ($value, s$value) => {
        // 正确答案选项转数组
        const rightCorrect = $value.StrCorrect.split(',') || [];
        // 用户选择的答案在不在正确的选项里面索引
        const isInRight = rightCorrect.indexOf($value.userSelect[s$value.showKey]?.showKey) !== -1;
        // 用户是答题的状态下，不管是答错还是答对
        const isDone = $value.isCorrent !== 1;
        // 用户选择的选项和当前选项是一样的
        const isSelect = $value.userSelect[s$value.showKey]?.showKey === s$value.showKey;
        // 当前选项是答案
        const isAnswer = rightCorrect.indexOf(s$value.showKey) !== -1;

        if (isDone && !isAnswer && isSelect) {
            return 'error';
        }
        if (isDone && isAnswer && isInRight) {

            return 'right';
        }
        if (isDone && isAnswer && $value.optionType <= 1 && !isInRight) {
            return 'right';
        }
        if (isDone && isAnswer && $value.optionType > 1 && !isInRight) {
            return 'right-noselect';
        }
        return '';
    }
}
