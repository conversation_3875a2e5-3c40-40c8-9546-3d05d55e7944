/*
 * main
 *
 * name: xia<PERSON><PERSON>a
 * date: 16/3/24
 */
import { CarType, Platform, URLCommon, URLParams } from ':common/env';
import Texts from ':common/features/texts';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
import { ActivityInfo, getActivityTime } from ':store/chores';
import { promiseList } from ':common/features/promise';
import KqfdBuyDialog from ':application/kqfd/component/kqfdBuyDialog/main';
import OnlineDialog from ':application/kqfd/component/onlineDialog/main';
import { trackEvent, trackGoPay } from ':common/stat';
import { GoodsInfo } from ':store/goods';
import { getHistoryList, getLiveLessonSchedule, subscribe } from ':store/kqfd';
import { dateFormat } from ':common/utils';
import { getAuthToken, openVipWebView, openWeb } from ':common/core';
import { makeToast } from ':common/features/dom';
import { VIP_LIVE } from ':common/navigate';
interface State {
    fragmentName1: string,
    lessonSchedule: any[],
    hasMore: boolean,
    limit: number,
    page: number,
    loadTextTips: string,
}
interface Props {
    payBtnCall?(e: Event),
    pageScroll(e);
    goodsInfoPool: GoodsInfo[],
    hasPromission: boolean
}

export default class extends Component<State, Props> {
    declare children: {
        kqfdBuyDialog: KqfdBuyDialog,
        onlineDialog: OnlineDialog
    }
    // 获取最新直播时间
    get newSchelTime() {
        const { lessonSchedule } = this.state;
        // 去最新的直播中，预约，回放
        const newZhiboData = [];
        lessonSchedule && lessonSchedule.forEach((res) => {
            newZhiboData.push(...res.liveDataList);
        });
        // status:1直播中，2预约，3回放
        const sortByData: any = newZhiboData && newZhiboData.sort((a, b) => {
            return +a.status - +b.status;
        });
        // eslint-disable-next-line max-len
        const timeString = dateFormat(sortByData[0]?.beginTime, 'yyyy.MM.dd') + ' ' + dateFormat(sortByData[0]?.beginTime, 'HH:mm') + '-' + dateFormat(sortByData[0]?.endTime, 'HH:mm');
        return timeString;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            fragmentName1: '',
            lessonSchedule: [],
            hasMore: true,
            limit: 5,
            page: 0,
            loadTextTips: '加载中'
        };

    }
    willReceiveProps() {
        return true;
    }
    async didMount() {
        await this.getZhiboData();
    }
    async getHistory() {
        const { limit, page, lessonSchedule } = this.state;
        const resHistory: any = await getHistoryList({
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            page: page,
            limit: limit
        });
        if (resHistory.length <= 0) {
            this.setState({ hasMore: false, loadTextTips: '没有更多了~' });
        }
        this.setState({
            lessonSchedule: [
                ...lessonSchedule,
                ...resHistory
            ]
        }, () => {
            if (this.state.lessonSchedule.length <= 0) {
                this.children.onlineDialog.show();
            }
        });
    }
    async getZhiboData() {
        const lessonSchedule: any = await getLiveLessonSchedule({
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu,
            limit: -1
        });
        this.setState({ lessonSchedule });
        if (lessonSchedule.length <= 1) {
            this.setState({
                page: 1
            });
            this.getHistory();

        }
    }
    async makeappointment(e) {
        e.stopPropagation();
        const lessonId = e.refTarget.getAttribute('data-liveSessionId');
        const index = e.refTarget.getAttribute('data-index');
        const sonIndex = e.refTarget.getAttribute('data-sonIndex');
        await subscribe({ lessonId });
        this.state.lessonSchedule[index].liveDataList[sonIndex].subscribeStatus = 1;
        this.setState({
            lessonSchedule: this.state.lessonSchedule
        });
        // this.setState({
        //     page: 0,
        //     loadTextTips: '加载中',
        //     lessonSchedule: [],
        //     hasMore: true
        // });
        // this.getZhiboData();

    }
    dialogBuy = (e) => {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        // 点击支付按钮打点
        trackGoPay({
            groupKey: this.props.goodsInfoPool[0].groupKey,
            fragmentName1,
            fragmentName2: ''
        });
        this.setState({ fragmentName1 }, () => {
            this.children.kqfdBuyDialog.show();
        });
    }
    gotoZhibojian = (e) => {
        const liveSessionId = e.refTarget.getAttribute('data-liveSessionId');
        if (this.props.hasPromission) {
            openWeb({
                url: 'http://jiakao.nav.mucang.cn/topLesson/live?id=' + liveSessionId + '&from=' + URLParams.from + '&backHome=0&fromItemCode=' + URLParams.fromItemCode
            });
        } else {
            this.dialogBuy(e);
        }
    }
    scroll = (e) => {
        this.props.pageScroll && this.props.pageScroll(e);
    }
    requestData = async () => {
        if (!this.state.hasMore) {
            return;
        }
        this.setState({
            page: ++this.state.page
        });

        await this.getHistory();
    }

}
