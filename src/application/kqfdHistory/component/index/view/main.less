.kqfd-index-history {
    height: 100vh;
    padding-bottom: 10px;
    &.history {
        height: calc(100vh - 105px);
    }
    .index-top {
        height: 343px;
        background: url(../images/top.png) no-repeat center;
        background-size: 100%;
        position: relative;
        .index-top-time {
            position: absolute;
            top: 276px;
            height: 35px;
            display: flex;
            align-items: center;
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Semibold;
            font-weight: 600;
            color: #111b30;
            padding-left: 65px;
        }
    }
    .index-buy-button {
        width: 318px;
        height: 55px;
        background: url(../images/buy-button.png) no-repeat center;
        background-size: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: -10px auto 0px auto;
        z-index: 1;
        position: relative;
        .buy-tips {
            position: absolute;
            right: -6px;
            top: -11px;
            width: 100px;
            height: 22px;
            background: linear-gradient(180deg, #ffee6d, #ffc806);
            border-radius: 14px 14px 14px 0px;
            font-size: 11px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: center;
            color: #333330;
            line-height: 22px;
        }
        .buy-title {
            font-size: 17px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            color: #ffffff;
            line-height: 24px;
            margin-top: -5px;
        }
        .buy-desc {
            font-size: 10px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.8);
            line-height: 14px;
        }
    }
    .zhibo-time-container {
        margin-top: 24px;
        .time-title {
            width: 299px;
            height: 49px;
            background: url(../images/zhibo-time.png) no-repeat center;
            background-size: 100%;
            margin: 0 auto 38px auto;
        }
        .time-tabs {
            position: relative;
            background: #fffacc;
            border: 1px solid #111b30;
            border-radius: 13px;
            margin: 0px 15px 40px 15px;
            padding: 35px 8px 15px 8px;
            position: relative;
            &.time-tabs2 {
                margin-bottom: 0px;
            }

            .tabs-step1 {
                .top {
                    width: 330px;
                    // display: flex;
                    // justify-content: space-between;
                    // align-items: flex-end;
                    // background: url(../images/time-top-bg.png) no-repeat top;
                    // background-size: 100%;
                    position: relative;
                    height: 37px;
                    z-index: 2;
                    .left {
                        width: 66px;
                        height: 37px;
                        line-height: 37px;
                        text-align: center;
                        background: #00afff;
                        border: 1px solid #000000;
                        border-radius: 4px 16px 0px 0px;

                        font-size: 15px;
                        font-family: PingFangSC, PingFangSC-Semibold;
                        font-weight: 600;
                        color: #ffffff;
                        overflow: hidden;
                        position: absolute;
                        z-index: 2;
                        left: 0px;
                        top: 0px;
                    }
                    .right {
                        width: calc(330px - 61px);
                        height: 32px;
                        font-size: 16px;
                        font-family: PingFangSC, PingFangSC-Semibold;
                        font-weight: 600;
                        color: #111b30;
                        line-height: 32px;
                        padding-left: 15px;
                        background: linear-gradient(to left, #bafe62, #fffc56);
                        border: 1px solid #111b30;
                        border-left: none;
                        border-radius: 0px 10px 0px 0px;
                        position: absolute;
                        left: 61px;
                        bottom: 0px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        .right-time {
                            margin-left: 5px;
                        }
                        .zhizho1 {
                            display: inline-block;
                            width: 55px;
                            height: 22px;
                            line-height: 22px;
                            background: #fe674f;
                            border: 1px solid #111b30;
                            border-radius: 4px;
                            font-size: 13px;
                            font-family: PingFangSC, PingFangSC-Semibold;
                            font-weight: 600;
                            text-align: center;
                            color: #ffffff;
                            margin-right: 5px;
                        }
                        .zhizho2 {
                            width: 65px;
                            height: 22px;
                            line-height: 22px;
                            background: #fef04c;
                            border: 1px solid #111b30;
                            border-radius: 4px;
                            font-size: 13px;
                            font-family: PingFangSC, PingFangSC-Semibold;
                            font-weight: 600;
                            text-align: center;
                            color: #170a0a;
                            margin-right: 5px;
                        }
                        .zhizho3 {
                            width: 55px;
                            height: 22px;
                            background: #00afff;
                            border: 1px solid #000000;
                            border-radius: 4px;
                            font-size: 13px;
                            font-family: PingFangSC, PingFangSC-Semibold;
                            font-weight: 600;
                            text-align: center;
                            color: #ffffff;
                            line-height: 22px;
                            margin-right: 5px;
                        }
                    }
                }
                .center {
                    width: 330px;
                    background: #f2f8fd;
                    border: 1px solid #111b30;
                    border-top: none;
                    border-radius: 0px 0px 10px 10px;
                    font-size: 15px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #111b30;
                    line-height: 21px;
                    padding: 15px 16px 15px 10px;
                    margin-bottom: 10px;
                    z-index: 1;
                }
            }
            .tabs-icon-left {
                position: absolute;
                left: 56px;
                top: -9px;
                width: 35px;
                height: 17px;
                background: url(../images/time-left.png) no-repeat center;
                background-size: 100%;
            }
            .tabs-icon-center {
                width: 125px;
                height: 35px;
                background: linear-gradient(to left, #bafe62, #fffc56);
                border: 1px solid #111b30;
                border-radius: 8px;
                font-size: 18px;
                font-family: PingFangSC, PingFangSC-Semibold;
                font-weight: 600;
                text-align: center;
                color: #111b30;
                line-height: 35px;
                position: absolute;
                left: 50%;
                top: -18px;
                transform: translate3d(-50%, 0, 0);
                overflow: hidden;
            }
            .tabs-icon-right {
                position: absolute;
                right: 56px;
                top: -9px;
                width: 35px;
                height: 17px;
                background: url(../images/time-right.png) no-repeat center;
                background-size: 100%;
            }
            &:last-child {
                margin-bottom: 0px;
            }
        }
        .loading-more {
            width: 100%;
            height: 44px;
            line-height: 44px;
            text-align: center;
            font-size: 14px;
            color: #ffffff;
            padding: 20px auto;
            .loading {
                height: 20px;
                width: 20px;
                border-radius: 100%;
                display: inline-block;
                margin: 10px;
                border: 2px solid #ffffff;
                border-bottom-color: transparent;
                vertical-align: middle;
                -webkit-animation: rotate 1.1s infinite linear;
                animation: rotate 1.1s infinite linear;
            }
        }
    }
}
/**加载效果旋转**/
@-webkit-keyframes rotate {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes rotate {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
