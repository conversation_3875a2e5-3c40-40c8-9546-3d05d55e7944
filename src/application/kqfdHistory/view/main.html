<import name="style" content="./main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="header" content=":component/header/main" />
<import name="IndexPage" content=":application/kqfdHistory/component/index/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="loading" content=":application/buyed/components/loading/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="payType" content=":component/payType/main" />

<div class="page-container page-kqfd-history">
    <div class="page-header">
        <com:header title="{{Texts.kemuTxt[URLCommon.kemu]+'临考急训直播'}}" theme="{{state.theme}}"
            endTheme="{{state.theme}}" qaKey="{{self.qaKey}}" scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}" />
    </div>
    <div class="body-panel" skip="true">
        <com:IndexPage expireTimeString="{{state.expireTimeString}}" hasPromission="{{state.hasPromission}}"
            goodsInfoPool="{{state.goodsInfoPool}}" pageScroll="{{self.pageScroll}}"></com:IndexPage>
    </div>
    <sp:if value='{{!state.hasPromission}}'>
        <div class="footer">
            <sp:if value="Platform.isAndroid && !state.hasPromission">
                <com:payType theme="horizontal" />
            </sp:if>
            <div class="buy-button">

                <sp:each for='{{state.goodsInfoPool}}'>
                    <div sp-on:click="buttonBuy" data-tabIndex="{{$index}}"
                        data-fragment="{{$index==0?'底部吸底左侧按钮':'底部吸底右侧按钮'}}"
                        class="{{state.goodsInfoPool.length>=2&&$index===0?'goods-button':'goods-button01'}}   {{state.goodsInfoPool.length===1?'goods-button02':''}}">
                        <sp:if value='{{$index===0}}'>
                            <div class="goods-button-title">{{$value.payPrice}}元立即开通</div>
                            <sp:else />
                            <div class="goods-button-title">
                                {{$value.payPrice}}元{{$value.upgrade?'升级':'开通'}}{{$value.name}}
                            </div>
                        </sp:if>

                        <div class="goods-button-desc">{{$value.validDays}}天有效期</div>
                        <sp:if value='{{state.goodsInfoPool.length>=2&&$index===1}}'>
                            <div class="button-xuan-fu">考不过补偿140元</div>
                        </sp:if>

                    </div>
                </sp:each>

            </div>

            <sp:if value='{{!state.hasPromission}}'>
                <com:readProtocol theme="kqfd" />
            </sp:if>

        </div>
    </sp:if>


    <com:payDialog />

    <com:buyButton />
    <com:loading />
    <com:expiredDialog />
</div>