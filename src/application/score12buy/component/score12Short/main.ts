/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { promiseIconList, promiseList } from ':common/features/promise';
import jump from ':common/features/jump';
import { CarType, URLCommon } from ':common/env';
import { trackEvent } from ':common/stat';
import { getLessonDetail } from ':store/lesson';

interface State {
    fourStepData: any[],
    curIndex: number,
    iconList: any[],
    trialLesson: any
}
interface Props {
    goAuth?(id: any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            fourStepData: [],
            curIndex: 0,
            trialLesson: {},
            iconList: [
                {
                    icon: promiseIconList.dtjq,
                    uniqkey: promiseList.dtjq,
                    dec: '答题技巧',
                    dec1: '省时省力'
                },
                {
                    icon: promiseIconList.jj500t,
                    uniqkey: promiseList.jj500t,
                    dec: URLCommon.tiku === CarType.MOTO ? '精简200题' : '精简600题',
                    dec1: '记得住',
                    tip: URLCommon.tiku === CarType.TRUCK || URLCommon.tiku === CarType.BUS ? 'http://exam-room.mc-cdn.cn/exam-room/2022/07/12/11/baa34cadd2524f2088f3bad93e562116.png' : 'http://exam-room.mc-cdn.cn/exam-room/2022/04/25/16/cb19ff278044447d9c9554925121e946.png'
                },
                {
                    icon: promiseIconList.zskcmn,
                    uniqkey: promiseList.zskcmn,
                    dec: '真实考场模拟',
                    dec1: '高仿真'
                },
                {
                    icon: promiseIconList.kq3tj,
                    uniqkey: promiseList.kq3tj,
                    dec: URLCommon.tiku === CarType.MOTO ? '两套卷' : '三套卷',
                    dec1: '高效冲刺'
                },
                {
                    icon: promiseIconList.jpzbk,
                    uniqkey: promiseList.jpzbk,
                    dec: '精品直播课',
                    dec1: '讲师带学'
                }
            ]
        };
        this.props = {
        };

    }
    willMount() {
        this.getFourStep();
        this.getVideoUrl();
    }
    getVideoUrl() {
        getLessonDetail({
            id: 787
        }).then(data => {
            this.setState({
                trialLesson: data.trialLesson
            });
        });
    }
    getFourStep() {
        this.setState({
            fourStepData: [
                {
                    imgUrl: 'https://web-resource.mc-cdn.cn/web/vip/500ti/poster1.png',
                    lessonId: 787
                }
            ]
        });
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    goPlay() {
        const { fourStepData, curIndex } = this.state;
        setTimeout(() => {
            jump.navigateTo('http://jiakao.nav.mucang.cn/topLesson/detail', {
                id: fourStepData[curIndex].lessonId
            });
        }, 10);
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    videoPlay = () => {
        trackEvent({
            fragmentName1: '试看视频',
            actionType: '点击',
            actionName: '播放'
        });
    }
    willReceiveProps() {
        return true;
    }
}
