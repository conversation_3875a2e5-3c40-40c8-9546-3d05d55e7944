<import name="style" content="./main" />

<import name="wenda" content=":component/wenda/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="discuzz" content=":component/discuzz/main" />
<import name="news" content=":component/news/main" />
<import name="myVideo" content=":component/myVideo/main" />
<import name="PriceTag" content=":component/priceTag/main" />

<div class="panel-scoreshort">
    <div class="ipad-box">

        <div class="phone-box">

            <com:news top="3.75rem" name="短时特训班" groupKey="{{props.groupKey}}"/>
            
            <div class="head-banner">
                <div class="img1"></div>
                <div class="img2"></div>
                <sp:if value="state.trialLesson.url">
                    <div class="video-box">
                        <com:myVideo src="{{state.trialLesson.url}}" onPlay="{{self.videoPlay}}"/>
                    </div>
                </sp:if>
                <div class="auth-list">
                    <sp:each for="state.iconList">
                        <div class="auth-item" sp-on:click="goAuth" data-uniqkey="{{$value.uniqkey}}">
                            <img src="{{$value.icon}}" alt="">
                            <div class="dec-box">
                                <div class="dec">{{$value.dec}}</div>
                                <div class="dec1">{{$value.dec1}}</div>
                            </div>
                            <sp:if value="$value.tip">
                                <span class="icon-tip {{URLCommon.tiku}}" style="background-image:url({{$value.tip}})"></span>
                            </sp:if>
                        </div>
                    </sp:each>
                </div>
                <div class="auth-more" sp-on:click="goAuth" data-uniqkey="">
                    <span>查看更多权益</span>
                    <span class="auth-more-arrow" />
                </div>

                <div sp-on:click="pay" class="buy-btn-box" data-fragment="头图">
                    <div class="buy-btn">
                        ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
                        '--'}} 立即开通
                    </div>
                   <div class="validDays">有效期{{props.goodsInfo.validDays}}天</div>
                    <com:PriceTag 
                        class="noScale"
                        goodsInfo="{{props.goodsInfo}}"
                        comparePriceMap="{{props.comparePricePool}}"
                        labelMap="{{props.labelPool}}"
                    />
                </div>
            </div>
        
            <div class="k4-1">
                <div class="img1"></div>
                <div class="img2"></div>
                <div sp-on:click="pay" class="btn buy-btn" data-fragment="主图">
                    <label>{{props.payPrice || '--'}}元立即开通，节省300元
                        <sp:if value="props.labelPool[props.groupKey].label">
                            <span class="tip">{{props.labelPool[props.groupKey].label}}</span>
                        </sp:if>
                    </label>
                </div>
            </div>
            <div class="divider"><i></i><i></i></div>
            <div class="k4-2">
                <div class="img1"></div>
                <div sp-on:click="goPlay" class="img2">
                    <img src="{{state.fourStepData[state.curIndex].imgUrl}}" />
                    <!-- <img class="play" src="http://exam-room.mc-cdn.cn/exam-room/2022/02/07/21/5668605b6e884e43b62b42dc035f2ccd.png" /> -->
                </div>
                <div class="img3"></div>
            </div>
            <div class="k4-3">
                <div class="img1"></div>
                <div class="imgs">
                    <img src="https://web-resource.mc-cdn.cn/web/vip/20.png" alt="">
                </div>
            </div>
            <div class="k4-4">
                <div class="img1"></div>
                <div class="imgs">
                    <img src="{{URLCommon.tiku === CarType.MOTO?'http://exam-room.mc-cdn.cn/exam-room/2022/07/01/17/e6beec95f52c4a91baca90013db14597.png':'https://web-resource.mc-cdn.cn/web/vip/500ti/9.png'}}" />
                    <img src="{{URLCommon.tiku === CarType.MOTO?'http://exam-room.mc-cdn.cn/exam-room/2022/07/01/17/c512599ac07441b2bb93223b912b5d26.png':'https://web-resource.mc-cdn.cn/web/vip/500ti/10.png'}}" />
                    <img src="http://exam-room.mc-cdn.cn/exam-room/2022/02/07/21/6f66d0633cdf4736a7495cb124c5659a.png" />
                    <img src="https://web-resource.mc-cdn.cn/web/vip/500ti/8.png" />
                </div>
            </div>
            <div class="k4-5 {{props.isHubei || Platform.isXueTang ? 'hide': 'show'}}">
                <div class="img1"></div>
                <div class="sec-wenda-w">
                    <com:wenda name="wenda" groupKey="{{props.groupKey}}" type="2" />
                </div>
            </div>
            
            <com:commonQuestion type="3" />
        </div>
    </div>
</div>
