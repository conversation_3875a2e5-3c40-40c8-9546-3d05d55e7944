/*
 * ------------------------------------------------------------------
 * 购买按钮右上角的装饰
 * ------------------------------------------------------------------
 */

import { promiseIconList, promiseList } from ':common/features/promise';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    payBtnCall?(e: Event)
    goAuth?(any)
}

export default class Score12 extends Component<null, Props> {
    get iconList() {
        return [
            {
                icon: promiseIconList.jj500t,
                uniqkey: promiseList.jj500t,
                dec: '精简600题',
                dec1: '省时省力'
            },
            {
                icon: promiseIconList.zskcmn,
                uniqkey: promiseList.zskcmn,
                dec: '真实考场模拟',
                dec1: '高仿真'
            },
            {
                icon: promiseIconList.dtjq,
                uniqkey: promiseList.dtjq,
                dec: '考前秘卷',
                dec1: '高效冲刺'
            },
            {
                icon: promiseIconList.ztmgg,
                uniqkey: promiseList.ztmgg,
                dec: '做题免广告',
                dec1: '免广告'
            }
        ];
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
