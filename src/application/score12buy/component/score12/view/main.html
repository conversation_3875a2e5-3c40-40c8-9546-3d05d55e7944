<import name="style" content="./main" module="S" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="discuzz" content=":component/discuzz/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />
<import name="news" content=":component/news/main" />

<div class=":panel-score12">
    <com:news top="2.34rem" name="新规三套卷" groupKey="{{props.groupKey}}"/>
    
    <div class=":h-bg">
        <div class=":info-bg {{S[URLCommon.tiku]}}"></div>
    </div>
    <div class=":strong {{S[URLCommon.tiku]}}">
        <div class=":auth-box {{S[URLCommon.tiku]}}">
            <div class=":auth-list">
                <sp:each for="self.iconList">
                    <div class=":auth-item" sp-on:click="goAuth" data-uniqkey="{{$value.uniqkey}}">
                        <img src="{{$value.icon}}" alt="">
                        <div class=":dec-box">
                            <div class=":dec">{{$value.dec}}</div>
                            <div class=":dec1">{{$value.dec1}}</div>
                        </div>
                    </div>
                </sp:each>
            </div>
            <!-- 扣满12分的客货车icon加角标 -->
            <sp:if value="URLCommon.tiku === CarType.TRUCK || URLCommon.tiku === CarType.BUS">
            <div class=":icon-multi"></div>
            </sp:if>
        </div>
        <div class=":auth-more" sp-on:click="goAuth" data-uniqkey="">
            <span>查看更多权益</span>
            <span class=":auth-more-arrow" />
        </div>
        <div sp-on:click="pay" data-fragment="主图" class=":buy-box">
            ¥ <span class=":pay-price">{{props.payPrice}}</span> 确认协议并支付
            <sp:if value="props.labelPool[props.groupKey].label">
                <span class=":tip">{{props.labelPool[props.groupKey].label}}<i/></span>
            </sp:if>
        </div>
        <div class=":protocol-box">
            <com:middleProtocol />
        </div>
    </div>

    <div class=":other-info {{S[URLCommon.tiku]}}"></div>

    <com:discuzz />
    <com:commonQuestion type="3" />
</div>
