.panel-score12 {
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background: #B98A7B;
    position: relative;

    .h-bg {
        height: 300px;
        background: url(../images/h-bg.png) no-repeat center center/cover;
        position: relative;

        .info-bg {
            position: absolute;
            background: url(../images/info-bg.png) no-repeat center center/cover;
            left: 30px;
            top: 77px;
            width: 351px;
            height: 164px;
            &.moto{
                 background: url(../images/info-bg_moto.png) no-repeat center center/cover;
            }
        }
    }

    .strong {
        position: relative;
        width: 100%;
        height: 395px;
        margin: -60px auto 0;
        background: url(../images/strong.png) no-repeat center center/cover;
        &.moto{
            background: url(../images/strong_moto.png) no-repeat center center/cover;
        }
        .auth-box {
            position: relative;
            width: 298px;
            height: 79px;
            margin: 142px auto 0;
            background: url(../images/auth.png) no-repeat center center/cover;
            &.moto{
                  background: url(../images/auth_moto.png) no-repeat center center/cover;
            }
            .auth-list {
                opacity: 0;
                display: flex;
                align-items: center;
                justify-content: space-around;
                width: 100%;
                position: absolute;

                .auth-item {
                    position: relative;
                    display: flex;
                    align-items: center;
                    flex-direction: column;

                    img {
                        width: 36px;
                        height: 36px;
                        display: block;
                        margin: 0 auto;
                    }

                    .dec-box {
                        position: absolute;
                        left: 50%;
                        bottom: 0;
                        transform: translate(-50%, 100%);
                        text-align: center;

                        .dec {
                            color: #ffffff;
                            font-size: 13px;
                            line-height: 18px;
                            padding-top: 6px;
                            white-space: nowrap;
                            display: block;
                        }

                        .dec1 {
                            color: rgba(255, 255, 255, 0.7);
                            font-size: 12px;
                            line-height: 18px;
                            padding-top: 2px;
                            white-space: nowrap;
                            display: block;
                        }
                    }
                }
            }
            .icon-multi{
                width: 51px;
                height: 19px;
                background: url(http://exam-room.mc-cdn.cn/exam-room/2022/07/12/11/baa34cadd2524f2088f3bad93e562116.png) no-repeat center center/cover;
                position: absolute;
                left: 39px;
                top: -10px;
                animation: shakeX 1.5s infinite;
            }
        }

        .auth-more {
            position: absolute;
            top: 240px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 13px;
            color: #664848;
            line-height: 18px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &-arrow {
                margin-left: 6px;
                width: 8px;
                height: 13px;
                background: url('../images/arrow.png');
                background-size: cover;
            }
        }

        .buy-box {
            position: absolute;
            bottom: 70px;
            left: 50%;
            transform: translateX(-50%);
            width: 320px;
            height: 49px;
            background: url(../images/<EMAIL>) no-repeat center center/cover;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 18px;
            color: white;
            font-weight: bold;

            .pay-price {
                font-size: 26px;
                margin-left: 5px;
                margin-bottom: 3px;
            }

            .tip {
                position: absolute;
                top: -18px;
                right: 0;
                font-size: 12px;
                line-height: 14px;
                color: #6F2117;
                background: linear-gradient(90deg, #FFD878 0%, #FFC400 100%);
                padding: 5px 10px;
                border-radius: 30px;

                i {
                    position: absolute;
                    display: block;
                    width: 10px;
                    height: 7px;
                    background-color: red;
                    right: 28px;
                    bottom: -6px;
                    background: url(../images/11.png) no-repeat right center;
                    background-size: 100% 100%;
                }
            }
        }

        .protocol-box {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            white-space: nowrap;
            display: flex;
            justify-content: center;
        }
    }



    .other-info {
        width: 345px;
        height: 681px;
        margin: 18px auto 0px;
        background: url(../images/<EMAIL>) no-repeat center center/cover;
         &.moto {
               background: url(../images/bt-ky_moto.png) no-repeat center center/cover;
         }
    }
}

@keyframes shakeX {

    0%,
    to {
        transform: translateX(0)
    }

    50% {
        transform: translateX(0)
    }

    55%,
    65%,
    75%,
    85%,
    95% {
        transform: translateX(-5px)
    }

    60%,
    70%,
    80%,
    90% {
        transform: translateX(5px)
    }
}
