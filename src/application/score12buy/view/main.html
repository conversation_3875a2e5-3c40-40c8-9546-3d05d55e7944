<import name="style" content="./main" module="S" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="header" content=":component/header/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import
    name="PanelScore12L"
    content=":application/score12buy/component/score12/main"
/>
<import
    name="Score12Short"
    content=":application/score12buy/component/score12Short/main"
/>

<div class="page-container :page-score12">
    <div class=":page-header">
        <com:header
            title="{{state.prevScrollTop > 200 ?self.pageTitle:' '}}"
            theme="black"
            endTheme="black"
            scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}"
        >
        </com:header>
    </div>
    <div class=":body-panel" id="body-panel" sp-on:scroll="pageScroll">
        <div class="{{state.tabIndex === 0?'':'hide'}}">
            <com:PanelScore12L
                payPrice="{{self.showPrice}}"
                goAuth="{{self.goAuth}}"
                payBtnCall="{{self.payBtnCall}}"
                groupKey="{{state.goodsInfoPool[0].groupKey}}"
                labelPool="{{state.labelPool}}"
            />
        </div>
        <sp:if value="state.goodsInfoPool[1].showPage">
            <div class="{{state.tabIndex === 1?'':'hide'}}">
                <com:Score12Short
                    goodsInfo="{{state.goodsInfoPool[1]}}"
                    labelPool="{{state.labelPool}}"
                    isHubei="{{state.isHubei}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{state.goodsInfoPool[1].groupKey}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>
    </div>

    <div class="footer {{state.goodsInfoPool.length > 1?'':'hide'}}">
        <com:bottomTabs
            tabIndex="{{state.tabIndex}}"
            labelPool="{{state.labelPool}}"
            comparePricePool="{{state.comparePricePool}}"
            goodsList="{{state.goodsInfoPool}}"
            tabChange="{{self.tabChangeCall}}"
        />
    </div>
    <com:buyButton>
        <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
            {{self.nowCouponInfo.couponCode?'已优惠' +
            self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
        </div>
    </com:buyButton>

    <com:payDialog />
    <com:expiredDialog />
    <com:persuadeDialog />
</div>
