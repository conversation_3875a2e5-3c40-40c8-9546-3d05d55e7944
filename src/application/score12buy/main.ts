/*
 * ------------------------------------------------------------------
 * 扣满12分
 * 这个页面的购买成功很特殊，跟其他落地页不同，所以写在了当前页面
 * ------------------------------------------------------------------
 */
import { ABTestKey, ABTestType, CarType, PayType, persuadeDialogAllow, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import { formatPrice } from ':common/utils';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { typeCode } from ':common/features/bottom';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackExit, trackGoPay, trackPageLoad, trackPageShow } from ':common/stat';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { isHubei } from ':common/features/locate';
import { COUPON_DETAIL_URL, openAuth, SCORE12_BUYED_URL } from ':common/navigate';
import { pauseAllVideos, scrollTop } from ':common/features/dom';
import { onPageShow } from ':common/features/page_status_switch';
import { getTabIndex, toggleStatus } from ':common/features/cache';
import isNumber from 'lodash/isNumber';
import { getAuthToken, openVipWebView, webClose } from ':common/core';
import jump from ':common/features/jump';
import { BUY_STATUS } from ':common/features/trigger_page_switch';
import texts from ':common/features/texts';
import { login } from ':common/features/login';
import { onWebBack } from ':common/features/persuade';
import { getAbtest, getPermission, hasBoughtVip } from ':store/chores';

let timer;
// 标记是否展示过挽留弹窗
let flag = false;

interface State {
    isHubei: boolean,
    tabIndex: number
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    prevScrollTop: number,
    checkGroupKey: GroupKey
}

const pageNameMap = {
    [GroupKey.ChannelKou12]: '扣满12分三套卷页',
    [GroupKey.ChannelKou12V1]: '扣满12分三套卷页',
    [GroupKey.ChannelKou12Short]: '扣满12分短时特训页',

    [GroupKey.HcChannelKou12]: '扣满12分三套卷页',
    [GroupKey.HcChannelKou12V1]: '扣满12分三套卷页',
    [GroupKey.HcChannelKou12Short]: '扣满12分短时特训页',

    [GroupKey.KcChannelKou12]: '扣满12分三套卷页',
    [GroupKey.KcChannelKou12V1]: '扣满12分三套卷页',
    [GroupKey.KcChannelKou12Short]: '扣满12分短时特训页',

    [GroupKey.MotoChannelKou12]: '扣满12分三套卷页',
    [GroupKey.MotoChannelKou12V1]: '扣满12分三套卷页',
    [GroupKey.MotoChannelKou12Short]: '扣满12分短时特训页'

};

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog
        expiredDialog: ExpiredDialog
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    get pageTitle() {
        const { tabIndex, prevScrollTop } = this.state;
        let title = texts.currentCarStyleTxt + '扣满十二分';

        if (tabIndex === 1 && prevScrollTop < 200) {
            title = ' ';
        }

        return title;
    }
    $constructor() {
        const goodsInfoPool = [];
        let checkGroupKey;

        const tabIndex = URLParams.tabIndex;
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        switch (URLCommon.tiku) {
            case CarType.CAR:
                checkGroupKey = GroupKey.ChannelKe1;
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKou12
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKou12Short
                } as GoodsInfo);
                break;
            case CarType.TRUCK:
                checkGroupKey = GroupKey.HcChannelKe1;
                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKou12
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKou12Short
                } as GoodsInfo);
                break;
            case CarType.BUS:
                checkGroupKey = GroupKey.KcChannelKe1;
                goodsInfoPool.push({
                    groupKey: GroupKey.KcChannelKou12
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.KcChannelKou12Short
                } as GoodsInfo);
                break;
            case CarType.MOTO:
                checkGroupKey = GroupKey.MotoChannelKe1;
                goodsInfoPool.push({
                    groupKey: GroupKey.MotoChannelKou12
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.MotoChannelKou12Short
                } as GoodsInfo);
                break;
            default:
                break;
        }

        this.state = {
            isHubei: true,
            tabIndex: +tabIndex || 0,
            goodsInfoPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            prevScrollTop: 0,
            checkGroupKey
        };
    }
    async didMount() {

        const { goodsInfoPool } = this.state;

        // 先判断有没有三套卷的权限
        if (await this.checkPermission()) {
            jump.replace(SCORE12_BUYED_URL);
            return;
        }
        // abtest默认选中
        const { strategy } = await getAbtest(URLCommon.tiku);
        this.state.tabIndex = strategy[ABTestKey.key7] === ABTestType.B ? 1 : 0;

        // 再判断有没有买过科1,如果买过科1就卖3套卷
        if (await this.checkGroupKeyFn()) {
            switch (URLCommon.tiku) {
                case CarType.CAR:
                    goodsInfoPool[0] = {
                        groupKey: GroupKey.ChannelKou12V1
                    } as GoodsInfo;
                    break;
                case CarType.BUS:
                    goodsInfoPool[0] = {
                        groupKey: GroupKey.KcChannelKou12V1
                    } as GoodsInfo;
                    break;
                case CarType.TRUCK:
                    goodsInfoPool[0] = {
                        groupKey: GroupKey.HcChannelKou12V1
                    } as GoodsInfo;
                    break;
                case CarType.MOTO:
                    goodsInfoPool[0] = {
                        groupKey: GroupKey.MotoChannelKou12V1
                    } as GoodsInfo;
                    break;
                default:
                    break;
            }
        }

        this.appEventProxy();

        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                this.buySuccess();
            }
        });

        await this.getGoodInfo();

        setPageName(pageNameMap[this.nowGoodInfo.groupKey]);

        // 判断是否是湖北
        isHubei().then(isHubei => {
            this.setState({
                isHubei
            });
        });

        // 页面进出时长打点
        trackPageLoad();

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: this.nowGoodInfo.groupKey
        });

    }
    async checkPermission() {
        let permission;
        switch (URLCommon.tiku) {
            case CarType.CAR:
                permission = 'kqdt';
                break;
            case CarType.BUS:
                permission = 'kqdtkc';
                break;
            case CarType.TRUCK:
                permission = 'kqdthc';
                break;
            case CarType.MOTO:
                permission = 'kqdtMt';
                break;
            default:
                break;
        }

        const { hasPromission } = await getPermission(permission);
        if (permission && hasPromission) {
            return true;
        }

        return false;
    }
    checkGroupKeyFn() {
        const { checkGroupKey } = this.state;

        return getGroupSessionInfo({ groupKeys: [checkGroupKey] }).then(data => {
            return data[0].bought;
        });
    }
    appEventProxy() {

        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }

        // 退出当前tab的打点
        this.leavePageCall();

        // 回到滚动的顶部
        scrollTop(document.querySelector('#body-panel'));

        // 暂停所有视频
        pauseAllVideos();

        this.setState({
            tabIndex
        }, () => {

            setPageName(pageNameMap[this.nowGoodInfo.groupKey]);

            trackPageShow();

            this.setPageInfo();
        });

    }
    backCall = () => {
        this.goBackPage();
    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;

            this.setState({
                prevScrollTop
            });
        }, 60);
    }
    setPageInfo() {
        this.setBuyBottom();
    }
    async setBuyBottom() {
        const fragmentName1 = '底部吸底按钮';
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const bottomType: typeCode = typeCode.type4;

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1
                });
                break;
            default:
                break;
        }
    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {

                if (index === 0) {
                    goodInfo.name = `扣满12分${URLParams.carStyle === 'moto' ? ' 两' : ' 三'}套卷`;
                }

                if (index === 1) {
                    goodInfo.name = `${URLParams.carStyle === 'moto' ? ' 两' : ' 三'}套卷+精华课`;
                }

                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }

                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought && !goodInfo.upgrade) {
                    jump.replace(SCORE12_BUYED_URL);
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }
            newGoodsPool[tabIndex].showPage = true;
            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool
            });

            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getLabel();
                await this.getComparePrice();

                this.setPageInfo();
            }, 60);

            // 500ms后再渲染其他tabPage，
            setTimeout(() => {
                newGoodsPool.forEach(item => {
                    item.showPage = true;
                });

                this.setState({
                    goodsInfoPool: newGoodsPool
                });
            }, 500);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice
                    };
                }
            });

            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            couponCode: this.nowCouponInfo.couponCode,
            activityType: goodsInfoPool[tabIndex].activityType,
            ...stat
        }, false).then(() => {
            this.buySuccess();
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    goAuth = async (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
    payBtnCall = async (e) => {

        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');

        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });
        }
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    // 退出页面的回调
    goBackPage() {
        const { tabIndex, goodsInfoPool, labelPool } = this.state;
        const nowGoodInfo = goodsInfoPool[tabIndex];

        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: nowGoodInfo,
                groupKey: nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                title: `真的要放弃${nowGoodInfo.name}吗？`,
                txt1: '懒人必备',
                txt2: '省不少时间',
                txt3: '后悔开晚了',
                txt4: '简单好记',
                tag: {
                    text: labelPool[nowGoodInfo.groupKey]?.label
                },
                kemu: URLCommon.kemu
            }).then(payType => {
                if (payType === false) {
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            webClose();
        }
    }
    // 离开当前页面
    leavePageCall = () => {
        // 退出当前页面的打点
        setPageName(pageNameMap[this.nowGoodInfo.groupKey]);
        trackExit();
    }
    async buySuccess() {
        await toggleStatus(BUY_STATUS);
        if (await this.checkGroupKeyFn()) {
            jump.replace(SCORE12_BUYED_URL);
        } else if (Platform.isIOS) {
            iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
        } else {
            newBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
        }
    }
}
