/*
 * ------------------------------------------------------------------
 * 科三路线
 * ------------------------------------------------------------------
 */
import PayDialog from ':component/payDialog/main';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { getAuthToken, getSystemInfo, openWeb, setStatusBarTheme, webClose } from ':common/core';
import { ABTestKey, KemuType, persuadeDialogAllow, Platform, setPageName, URLCommon, URLParams, Version } from ':common/env';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { onWebBack } from ':common/features/persuade';
import { formatPrice, getCityName } from ':common/utils';
import { getAbtest, getRouteVideo, getSwallowConfig } from ':store/chores';
import PersuadeDialog from ':component/persuadeDialog/main';
import onlineDialog from ':application/ke3route/components/onlineDialog/main';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/mainb.html';
import { CAR_KE1_VIDEO, MOVE_GOODS_KEMU2, MOVE_GOODS_KEMU3, openAuth, PROTOCOL1_URL, PROTOCOL2_URL } from ':common/navigate';
import { newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { getRouteCoupon } from ':store/coupon';
import { MCProtocol } from '@simplex/simple-base';
import { trackEvent, trackExit, trackGoPay, trackPageLoad } from ':common/stat';
import { typeCode } from ':common/features/bottom';
import { iosDialogBuySuccess, iosPay } from ':common/features/ios_pay';
import { onPageShow } from ':common/features/page_status_switch';
import { getTabIndex } from ':common/features/cache';
import isNumber from 'lodash/isNumber';
import { couponAnimate, pauseAllVideos, scrollTop } from ':common/features/dom';
import { isKe3ClaimOpen } from ':store/kemu3';

URLParams.noReplacePay = 'true';
// 可能进来的时候是科目4，因为在科目4的情况推荐了科目3的vip（客户端的kemuStyle是选择当前的科目，但是可以进入到别的科目的vip页面）
URLCommon.kemu = KemuType.Ke3;
interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    interact: boolean
    couponPool: any,
    labelPool: object,
    comparePricePool: any,
    hasAnyPermission: boolean
    userCityCode: string,
    userCityName: string,
    videoList: any[],
    placeInfo: {
        cityCode: string
        cityName: string,
        areaName: string
    }
    placeName: string
    prevScrollTop: number
    hideHint: boolean
    showIconVideo: boolean
    shareInfo: any
    hasAnyTdRoute: boolean,
    ke3ClaimOpen: boolean
}
interface Props {
}

let timer;
// 标记是否展示过挽留弹窗
let flag = false;

export default class extends Application<State, Props> {
    declare children: {
        buyButton: BuyButton
        persuadeDialog: PersuadeDialog
        payDialog: PayDialog
        onlineDialog: onlineDialog
    };
    get protocolUrl() {
        return PROTOCOL1_URL;
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
    * 如果有优惠券的价格为0的就显示0
    * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
   */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    moveGoodsVideo = {
        [GroupKey.ChannelKe3Group]: {
            videoUrl: MOVE_GOODS_KEMU3,
            videoPoster: 'http://exam-room.mc-cdn.cn/exam-room/2024/03/12/14/ba5782bd3b4e42829109f03aeb76dfec.png',
            entrance: 'kemu3-sc',
            stat: {
                fromPageCode: '176',
                fromPathCode: '003159'
            }
        }
    }
    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            goodsInfoPool: [{
                groupKey: GroupKey.ChannelKe3RouteMeta
            } as GoodsInfo,
            {
                groupKey: GroupKey.ChannelKe3Group
            } as GoodsInfo,
            {
                groupKey: GroupKey.ChannelKe34
            } as GoodsInfo
            ],
            interact: false,
            userCityCode: '',
            userCityName: '',
            videoList: [],
            placeInfo: null,
            placeName: '',
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            hasAnyPermission: false,
            prevScrollTop: 0,
            hideHint: !!localStorage.getItem('ke3route-hideHint') || false,
            showIconVideo: false,
            shareInfo: {},
            hasAnyTdRoute: false,
            ke3ClaimOpen: false
        };

    }
    async didMount() {
        setPageName('路线视频考场详情页');

        this.appEventProxy();

        this.getSystemInfo().then(async () => {
            const { userCityCode, userCityName } = this.state;
            const claimOpenValue = await isKe3ClaimOpen({ cityCode: userCityCode });
            this.setState({
                ke3ClaimOpen: claimOpenValue.value
            });
            await this.getVideoList();
            await this.getConfigShare();
            await this.getGoodInfo();
            const { shareInfo, placeName } = this.state;

            MCProtocol.Core.Share.setting({
                channel: 'qq,weixin_friend,weixin_moment,sina',
                type: '',
                shareData: {
                    title: placeName + shareInfo.title,
                    description: shareInfo.desc,
                    // eslint-disable-next-line max-len
                    url: shareInfo.url + '?placeId=' + URLParams.placeId + '&_cityCode=' + userCityCode + '&carStyle=' + URLParams.carStyle + '&kemuStyle=' + URLParams.kemuStyle + '&_cityName=' + encodeURIComponent(userCityName),
                    iconUrl: 'https://sirius.mc-cdn.cn/mc-sirius/2022/10/25/17/0fe99e07f50c4c04a7b7b7fac2b08fc6.png'

                }
            });
        });

        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                if (this.state.tabIndex === 0) {
                    newBuySuccess({ groupKey: this.nowGoodInfo.groupKey, goUse: true, apiHost: 'squirrel' });
                } else {
                    newBuySuccess({ groupKey: this.nowGoodInfo.groupKey }, 2);
                }

            }
        });

        trackPageLoad();
    }

    async getSystemInfo() {
        const systemInfo = await getSystemInfo();
        const userCityCode = systemInfo._userCity || systemInfo._cityCode;
        const userCityName = await getCityName(+userCityCode) || '';

        this.setState({
            userCityCode,
            userCityName
        });

    }
    appEventProxy() {
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });
    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            const targetDom = this.children.routeLine.getDOMNode().kslc as HTMLElement;
            const targetTop = targetDom.offsetTop;

            if (prevScrollTop < 150) {
                setStatusBarTheme('light');
            } else {
                setStatusBarTheme('dark');
            }

            this.setState({
                prevScrollTop,
                showIconVideo: prevScrollTop > targetTop
            });
        }, 150);
    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }

        trackExit();

        // 回到滚动的顶部
        scrollTop(document.querySelector('#ke3route'));

        // 暂停所有视频
        pauseAllVideos();

        this.setState({
            tabIndex
        }, () => {
            this.setPageInfo();
        });
    }

    goAuth = async (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
    onPayBtnClick = (e) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');

        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1,
                goodsCityCode: this.nowGoodInfo.groupKey === GroupKey.ChannelKe3RouteMeta ? this.state.placeInfo?.cityCode : '',
                extraInfo: JSON.stringify({
                    placeId: URLParams.placeId
                })
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });
        }
    }
    getConfigShare() {
        return getSwallowConfig({
            key: 'jk_route_share'
        }).then(data => {
            this.setState({
                shareInfo: data?.route
            });
        });

    }

    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const { tabIndex } = this.state;
        const groupKeys: GroupKey[] = [];
        const newGoodsPool = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo[0].bought = this.state.hasAnyPermission;
            goodsListInfo[0].name = this.state.placeName;
            goodsListInfo.forEach((goodInfo, index) => {

                if (index === 0) {
                    goodInfo.name = this.state.placeName;
                }

                // 商品未购买才push
                if (index === 0 || !(goodInfo.bought && !goodInfo.upgrade)) {
                    newGoodsPool.push(goodInfo);
                }
            });

            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool
            });

            // showPage用来控制模块的展示， 先渲染需要渲染的tabPage，加快首次渲染速度
            newGoodsPool[tabIndex].showPage = true;

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getComparePrice();
                await this.getLabel();

                couponAnimate({
                    couponTargetDomSelect: '.bottom_coupon',
                    compareTargetDomSelect: `.bottom-tabs > .tab-box .${newGoodsPool[2]?.groupKey}`,
                    couponData: this.nowCouponInfo,
                    compareData: this.state.comparePricePool[newGoodsPool[2]?.groupKey],
                    compareGoodsData: newGoodsPool[2],
                    goodsData: this.nowGoodInfo,
                    compareAnimateType: 3
                });

                this.setPageInfo();
            }, 60);

            // 500ms后再渲染其他tabPage，
            setTimeout(() => {
                newGoodsPool.forEach(item => {
                    item.showPage = true;
                });

                this.setState({
                    goodsInfoPool: newGoodsPool
                });

            }, 500);

        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku: URLCommon.tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice
                    };
                }
            });

            this.setState({ comparePricePool });
        });
    }
    async getLabel() {
        const { goodsInfoPool, interact, hasAnyTdRoute } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            if (this.state.ke3ClaimOpen) {
                labelPool[GroupKey.ChannelKe3Group] = {
                    label: '考不过补偿60元'
                };
            } else if (hasAnyTdRoute) {
                labelPool[GroupKey.ChannelKe3Group] = {
                    label: '仿真模拟考1:1还原'
                };
            } else if (interact) {
                labelPool[GroupKey.ChannelKe3Group] = {
                    label: '互动视频在线练考场'
                };
            }

            this.setState({ labelPool });
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }

    setPageInfo(stat?: { fragmentName1?: string, fragmentName2?: string }) {
        this.setBuyBottom(stat);
    }
    setBuyBottom(stat) {
        const fragmentName1 = URLParams.fragmentName1 || '底部吸底按钮';
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const bottomType: typeCode = typeCode.type4;

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1,
                    protocolText2: '《考场视频会员协议》',
                    protocolUrl: this.protocolUrl,
                    goodsCityCode: this.nowGoodInfo.groupKey === GroupKey.ChannelKe3RouteMeta ? this.state.placeInfo?.cityCode : '',
                    extraInfo: JSON.stringify({
                        placeId: URLParams.placeId
                    }),
                    ...stat
                });
                break;
            default:
                break;
        }
    }
    async getVideoList() {
        const { userCityCode } = this.state;

        const { strategy } = await getAbtest(URLCommon.tiku);

        await getRouteVideo({
            cityCode: userCityCode,
            abTestGroup: strategy[ABTestKey.key23],
            placeId: URLParams.placeId || '',
            handleCoachName: true
        }).then(data => {
            if (!data.online) {
                this.children.onlineDialog.show();
            }
            this.setState({
                videoList: data.list.map(item => {
                    item.previewL = item.previewUrl;
                    return item;
                }),
                placeInfo: {
                    cityCode: data.cityCode,
                    cityName: data.cityName,
                    areaName: data.areaName
                },
                placeName: data.placeName,
                // 历史版本不做互动视频
                interact: Version.bizVersion > 14 && data.hasAnyPractice,
                hasAnyPermission: data.hasAnyPermission,
                // 是否有真是考场
                hasAnyTdRoute: data.hasAnyTdRoute
            });

            if (Version.bizVersion > 14 && data.hasAnyPractice) {
                trackEvent({
                    fragmentName1: '在线模拟',
                    actionName: '曝光',
                    actionType: ''
                });
            }
        });
    }
    switchkc() {
        openWeb({
            url: 'http://jiakao.nav.mucang.cn/examRouteLineVideo?from=' + URLParams.from + '&force=true'
        });
    }
    changeExam = () => {
        this.switchkc();
    }
    backCall = () => {
        this.goBackPage();
    }
    goBackPage() {
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo = goodsInfoPool[tabIndex];

        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: nowGoodInfo,
                groupKey: nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                title: '真的要放弃吗？',
                txt1: '懒人必备',
                txt2: '省不少时间',
                txt3: '后悔开晚了',
                txt4: '简单好记',
                kemu: URLCommon.kemu
            }).then(payType => {
                if (payType === false) {
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            webClose();
        }
    }
    closeHint() {
        localStorage.setItem('ke3route-hideHint', 'true');
        this.setState({
            hideHint: false
        });
    }
    pay = async (stat: PayStatProps) => {

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo?.couponCode || '',
            goodsCityCode: this.nowGoodInfo.groupKey === GroupKey.ChannelKe3RouteMeta ? this.state.placeInfo?.cityCode : '',
            extraInfo: JSON.stringify({
                placeId: URLParams.placeId
            }),
            ...stat
        }, false).then(() => {
            if (this.state.tabIndex === 0) {
                newBuySuccess({ groupKey: this.nowGoodInfo.groupKey, goUse: true, apiHost: 'squirrel' }, 2);
            } else {
                newBuySuccess({ groupKey: this.nowGoodInfo.groupKey }, 2);
            }
        });

    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    goVideo(e) {
        const id = e.refTarget.getAttribute('data-id');
        const placeId = e.refTarget.getAttribute('data-placeid');
        openWeb({
            url: `http://jiakao.nav.mucang.cn/show-exam-route-video?placeId=${placeId}&routeId=${id}&from=${URLParams.fromPageCode}`
        });
    }
    share() {
        trackEvent({
            fragmentName1: '右上角',
            fragmentName2: '分享入口',
            actionName: '分享',
            actionType: '点击'
        });

        MCProtocol.Core.Web.menu();
    }
}
