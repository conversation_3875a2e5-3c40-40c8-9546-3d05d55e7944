/*
 * ------------------------------------------------------------------
 * 科三路线
 * ------------------------------------------------------------------
 */
import PayDialog from ':component/payDialog/main';
import { getAuthToken, getSystemInfo, openVipWebView, openWeb, setStatusBarTheme, webClose } from ':common/core';
import { KemuType, persuadeDialogAllow, Platform, setPageName, URLCommon, URLParams, Version } from ':common/env';
import { Coupon, getBestCoupon, goodsInfoWithCoupon } from ':common/features/coupon';
import { onWebBack } from ':common/features/persuade';
import { getCityName } from ':common/utils';
import { getRouteVideo, getSwallowConfig } from ':store/chores';
import PersuadeDialog from ':component/persuadeDialog/main';
import ke3routeDialog from ':application/ke3routeDialog/component/ke3routeDialog/main';
import onlineDialog from ':application/ke3route/components/onlineDialog/main';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { PayStatProps } from ':component/buyButton/main';
import { HELP_VIP, PROTOCOL1_URL, PROTOCOL2_URL } from ':common/navigate';
import { login } from ':common/features/login';
import { newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { getRouteCoupon } from ':store/coupon';
import { MCProtocol } from '@simplex/simple-base';
import { trackEvent, trackGoPay, trackPageLoad } from ':common/stat';
// 可能进来的时候是科目4，因为在科目4的情况推荐了科目3的vip（客户端的kemuStyle是选择当前的科目，但是可以进入到别的科目的vip页面）
URLCommon.kemu = KemuType.Ke3;
interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    interact: boolean
    couponPool: object,
    userCityCode: string,
    userCityName: string,
    videoList: any[],
    placeInfo: {
        cityCode: string
        cityName: string,
        areaName: string
    }
    placeName: string
    prevScrollTop: number
    hideHint: boolean
    showIconVideo: boolean
    showLookAllBuy: boolean
    shareInfo: any
}
interface Props {
}

let timer;
// 标记是否展示过挽留弹窗
let flag = false;

export default class extends Application<State, Props> {
    declare children: {
        persuadeDialog: PersuadeDialog
        ke3routeDialog: ke3routeDialog
        payDialog: PayDialog
        onlineDialog: onlineDialog
    };
    get protocolUrl() {
        return PROTOCOL1_URL;
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
    * 如果有优惠券的价格为0的就显示0
    * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
   */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            goodsInfoPool: [{
                groupKey: GroupKey.ChannelKe3RouteMeta
            } as GoodsInfo],
            interact: false,
            userCityCode: '',
            userCityName: '',
            videoList: [],
            placeInfo: null,
            placeName: '',
            couponPool: {},
            prevScrollTop: 0,
            hideHint: !!localStorage.getItem('ke3route-hideHint') || false,
            showIconVideo: false,
            showLookAllBuy: false,
            shareInfo: {}
        };

    }
    async didMount() {
        setPageName('路线视频考场详情页');

        this.appEventProxy();

        this.children.ke3routeDialog.on('close', () => {
            this.setState({
                showLookAllBuy: false
            });
        });
        this.getSystemInfo().then(async () => {
            await this.getVideoList();
            await this.getConfigShare();

            const { shareInfo, placeName, userCityCode, userCityName } = this.state;

            MCProtocol.Core.Share.setting({
                channel: 'qq,weixin_friend,weixin_moment,sina',
                type: '',
                shareData: {
                    title: placeName + shareInfo.title,
                    description: shareInfo.desc,
                    // eslint-disable-next-line max-len
                    url: shareInfo.url + '?placeId=' + URLParams.placeId + '&_cityCode=' + userCityCode + '&carStyle=' + URLParams.carStyle + '&kemuStyle=' + URLParams.kemuStyle + '&_cityName=' + encodeURIComponent(userCityName),
                    iconUrl: 'https://sirius.mc-cdn.cn/mc-sirius/2022/10/25/17/0fe99e07f50c4c04a7b7b7fac2b08fc6.png'

                }
            });
        });

        await this.getGoodInfo();

        trackPageLoad();
    }
    async getSystemInfo() {
        const systemInfo = await getSystemInfo();
        const userCityCode = systemInfo._userCity || systemInfo._cityCode;
        const userCityName = await getCityName(+userCityCode) || '';

        this.setState({
            userCityCode,
            userCityName
        });

    }
    appEventProxy() {
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });
    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            const targetDom = this.children.routeLine.getDOMNode().kslc as HTMLElement;
            const targetTop = targetDom.offsetTop;

            if (prevScrollTop < 150) {
                setStatusBarTheme('light');
            } else {
                setStatusBarTheme('dark');
            }

            this.setState({
                prevScrollTop,
                showIconVideo: prevScrollTop > targetTop
            });
        }, 150);
    }
    getConfigShare() {
        return getSwallowConfig({
            key: 'jk_route_share'
        }).then(data => {
            this.setState({
                shareInfo: data.route
            });
        });

    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            this.setState({
                goodsInfoPool: goodsListInfo
            });
            // setTimeout(async () => {
            //     await this.getCoupon();
            // }, 60);
        });
    }
    // getCoupon = async () => {
    //     const { goodsInfoPool } = this.state;
    //     const promiseList = [];
    //     const couponPool = {};

    //     goodsInfoPool.forEach(item => {
    //         promiseList.push(getBestCoupon(item));
    //     });

    //     await Promise.all(promiseList).then(couponList => {
    //         couponList.forEach((item, index) => {
    //             couponPool[goodsInfoPool[index].groupKey] = {
    //                 couponCode: item.code,
    //                 priceCent: item.price
    //             };
    //         });
    //         this.setState({ couponPool });
    //     });

    // }
    async getVideoList() {
        const { userCityCode } = this.state;
        await getRouteVideo({
            cityCode: userCityCode,
            placeId: URLParams.placeId || '',
            handleCoachName: true
        }).then(data => {
            if (!data.online) {
                this.children.onlineDialog.show();
            }
            this.setState({
                videoList: data.list.map(item => {
                    item.previewL = item.previewUrl;
                    return item;
                }),
                placeInfo: {
                    cityCode: data.cityCode,
                    cityName: data.cityName,
                    areaName: data.areaName
                },
                placeName: data.placeName,
                // 历史版本不做互动视频
                interact: Version.bizVersion > 14 && data.hasAnyPractice
            });

            if (Version.bizVersion > 14 && data.hasAnyPractice) {
                trackEvent({
                    fragmentName1: '在线模拟',
                    actionName: '曝光',
                    actionType: ''
                });
            }
        });
    }
    switchkc() {
        location.href = 'http://jiakao.nav.mucang.cn/examRouteLineVideo?from=' + URLParams.from + '&force=true';
    }
    changeExam = () => {
        this.switchkc();
    }
    backCall = () => {
        this.goBackPage();
    }
    goBackPage() {
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo = goodsInfoPool[tabIndex];

        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: nowGoodInfo,
                groupKey: nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                title: '真的要放弃吗？',
                txt1: '懒人必备',
                txt2: '省不少时间',
                txt3: '后悔开晚了',
                txt4: '简单好记',
                kemu: URLCommon.kemu
            }).then(payType => {
                if (payType === false) {
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            webClose();
        }
    }
    closeHint() {
        localStorage.setItem('ke3route-hideHint', 'true');
        this.setState({
            hideHint: false
        });
    }
    pay = async (stat: PayStatProps) => {

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo?.couponCode || '',
            goodsCityCode: this.nowGoodInfo.groupKey === GroupKey.ChannelKe3RouteMeta ? this.state.placeInfo?.cityCode : '',
            extraInfo: JSON.stringify({
                placeId: URLParams.placeId
            }),
            ...stat
        }, false).then(() => {
            newBuySuccess({
                groupKey: this.nowGoodInfo.groupKey, goUse: true, apiHost: 'squirrel'
            }, 2);
        });

    }
    openLookAllBuy = (e) => {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        trackGoPay({
            groupKey: this.nowGoodInfo.groupKey,
            payPathType: 0,
            fragmentName1
        });
        this.children.ke3routeDialog.setPageInfo({ fragmentName1 });
        this.setState({
            showLookAllBuy: true
        });
        this.children.ke3routeDialog.setPageInfo({ fragmentName1 });
    }
    exchange() {
        const { userCityCode } = this.state;
        openWeb({
            url: 'http://jiakao.nav.mucang.cn/route-video/coupon?placeId=' + URLParams.placeId + '&cityCode=' + userCityCode + '&cityPack=false'
        });
    }

    goVideo(e) {
        const id = e.refTarget.getAttribute('data-id');
        const placeId = e.refTarget.getAttribute('data-placeid');
        openWeb({
            url: `http://jiakao.nav.mucang.cn/show-exam-route-video?placeId=${placeId}&routeId=${id}&from=${URLParams.fromPageCode}`
        });
    }

    async help() {
        const authToken = await getAuthToken();
        if (!authToken) {
            await login();
            return;
        }

        openVipWebView({
            url: HELP_VIP + location.search
        });
    }
    share() {
        trackEvent({
            fragmentName1: '右上角',
            fragmentName2: '分享入口',
            actionName: '分享',
            actionType: '点击'
        });

        MCProtocol.Core.Web.menu();
    }
}
