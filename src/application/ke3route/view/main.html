<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="videoList" content=":application/ke2route/component/videoList/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="barrage" content=":component/barrage/main" />
<import name="ke3routeDialog" content=":application/ke3routeDialog/component/ke3routeDialog/main" />
<import name="onlineDialog" content="../components/onlineDialog/main" />
<import name="routeLine" content="../components/routeLine/main" />

<div class="page-container ke3route-page">
    <div class="page-header">
        <com:header title="{{state.placeName}}" theme="black" endTheme="white" scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}">
            <div class="header-title {{state.prevScrollTop > 150?'active':''}}" sp:slot="title">
                <span class="text">{{state.placeName || '驾考宝典'}}</span>
                <span class="switch" sp-on:click="switchkc">
                    <span class="bgimg"></span>
                </span>
            </div>
            <sp:if value="{{!Platform.isHarmony}}">
                <div sp-on:click="share" class="share {{state.prevScrollTop > 150?'active':''}}" sp:slot="right">

                </div>
                <sp:else />
                <div sp:slot="right">

                </div>
            </sp:if>

            <div class="header-bottom" sp:slot="bottom">
                <div class="hint {{state.hideHint?'':'hide'}} {{state.showIconVideo ? '': 'hide'}}">
                    <label>注意：</label>
                    <span class="sp-w">
                        <span class="lamp">科目三会从以下考试路线中随机选择一条进行考试，请各位考生务必熟记全部路线考点</span>
                    </span>
                    <i sp-on:click="closeHint"></i>
                </div>
                <ul class="ul {{state.showIconVideo ? '': 'hide'}}">
                    <sp:each for="state.videoList" value="video" index="j">
                        <li class="li" sp-on:click="goVideo" data-index="{{index}}" data-id="{{video.id}}"
                            data-placeid="{{video.placeId}}">
                            <p>
                                <img src="{{video.videoImage}}" alt="">
                                <i></i>
                                <span class="route-name">{{video.name}}</span>
                            </p>
                        </li>
                    </sp:each>
                </ul>
            </div>
        </com:header>
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <com:routeLine placeInfo="{{state.placeInfo}}" videoList="{{state.videoList}}" interact="{{state.interact}}"
            placeName="{{state.placeName}}" pay="{{self.openLookAllBuy}}" />
    </div>
    <sp:if value="!(Platform.isIOS && Platform.isXueTang)">
        <com:barrage></com:barrage>
        <div class="footer">
            <div class="bg {{Platform.isIOS?'ios':''}}"></div>
            <div class="fl">
                <sp:if value="!Platform.isIOS">
                    <p class="p1" sp-on:click="exchange"><i></i><b>兑换</b></p>
                    <span class="line"></span>
                </sp:if>
                <p class="p2" key="help" sp-on:click="help"><i></i><b>咨询</b></p>
            </div>
            <div class="fr" sp-on:click="openLookAllBuy" data-fragment="底部吸底按钮">
                <p class="p1">{{self.showPrice || '--'}}元解锁考场</p>
                <p class="p2">有效期{{state.goodsInfoPool[0].validDays || 180}}天</p>
            </div>
        </div>
    </sp:if>
    <com:persuadeDialog protocolUrl="{{self.protocolUrl}}">
        <div class="pay-box" sp:slot="text-body">
            <div class="p-title"></div>
            <div class="p-bg"></div>
            <div class="pay-tag">
                <p>路线更新快</p>
                <p>我的教练说很准</p>
            </div>
            <div class="pay-tag">
                <p>跟考试一样的</p>
                <p>细节很多讲解详细</p>
            </div>
            <div class="pay-tag">
                <p>路线不对就退钱</p>
                <p>性价比很高</p>
            </div>
        </div>
    </com:persuadeDialog>

    <div class="showLookAllBuy-mask {{state.showLookAllBuy?'':'hide'}}">
        <div class="showLookAllBuy-box">
            <com:ke3routeDialog type="component" show="{{state.showLookAllBuy}}" />
        </div>
    </div>
    <com:onlineDialog changeExam="{{self.changeExam}}">

    </com:onlineDialog>
</div>
