<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="moveGoods" content=":component/moveGoods/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="barrage" content=":component/barrage/main" />
<import name="onlineDialog" content="../components/onlineDialog/main" />
<import name="routeLine" content="../components/routeLine/main" />
<import name="Kemu3BContent" content=":application/kemu3/component/kemu3BContent/main" />
<import name="Kemu34Content" content=":application/kemu3/component/kemu34Content/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />


<div class="page-container ke3route-page">
    <div class="page-header">
        <com:header title="{{state.placeName}}" theme="black" endTheme="white" scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}">
            <div class="header-title {{state.prevScrollTop > 150?'active':''}}" sp:slot="title">
                <span class="text">{{self.nowGoodInfo.name || '驾考宝典'}}</span>
                <span class="switch {{self.nowGoodInfo.groupKey ===  GroupKey.ChannelKe3RouteMeta?'':'hide'}}"
                    sp-on:click="switchkc">
                    <span class="bgimg"></span>
                </span>
            </div>
             <sp:if value="{{!Platform.isHarmony}}">
                 <div sp-on:click="share"
                     class="share {{state.prevScrollTop > 150?'active':''}} {{self.nowGoodInfo.groupKey ===  GroupKey.ChannelKe3RouteMeta?'':'hide'}}"
                     sp:slot="right">
     
                 </div>
                 <sp:else/>
                 <div sp:slot="right">

                 </div>
             </sp:if>
            <div class="header-bottom {{self.nowGoodInfo.groupKey ===  GroupKey.ChannelKe3RouteMeta?'':'hide'}}"
                sp:slot="bottom">
                <div class="hint {{state.hideHint?'':'hide'}} {{state.showIconVideo ? '': 'hide'}}">
                    <label>注意：</label>
                    <span class="sp-w">
                        <span class="lamp">科目三会从以下考试路线中随机选择一条进行考试，请各位考生务必熟记全部路线考点</span>
                    </span>
                    <i sp-on:click="closeHint"></i>
                </div>
                <ul class="ul {{state.showIconVideo ? '': 'hide'}}">
                    <sp:each for="state.videoList" value="video" index="j">
                        <li class="li" sp-on:click="goVideo" data-index="{{index}}" data-id="{{video.id}}"
                            data-placeid="{{video.placeId}}">
                            <p>
                                <img src="{{video.videoImage}}" alt="">
                                <i></i>
                                <span class="route-name">{{video.name}}</span>
                            </p>
                        </li>
                    </sp:each>
                </ul>
            </div>
        </com:header>
    </div>
    <div class="body-panel" id="ke3route" sp-on:scroll="pageScroll">
        <div class="{{self.nowGoodInfo.groupKey ===  GroupKey.ChannelKe3RouteMeta ? '' : 'hide'}}">
            <com:routeLine placeInfo="{{state.placeInfo}}" videoList="{{state.videoList}}" interact="{{state.interact}}" hasAnyTdRoute="{{state.hasAnyTdRoute}}"
                placeName="{{state.placeName}}" pay="{{self.onPayBtnClick}}" />
        </div>
        <sp:if value="self.getGroupKeyInfo(GroupKey.ChannelKe3Group).payPrice &&
            self.getGroupKeyInfo(GroupKey.ChannelKe3Group).showPage">
            <div class="{{self.nowGoodInfo.groupKey ===  GroupKey.ChannelKe3Group  ? '' : 'hide'}}">
                <com:Kemu3BContent goodsInfo="{{self.nowGoodInfo}}" payBtnCall="{{self.onPayBtnClick}}"
                    hasAnyTdRoute="{{state.hasAnyTdRoute}}" ke3ClaimOpen="{{state.ke3ClaimOpen}}" userCityName="{{state.userCityName}}"/>
            </div>
        </sp:if>

        <sp:if
            value="self.getGroupKeyInfo(GroupKey.ChannelKe34).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKe34).showPage">
            <div class="{{self.nowGoodInfo.groupKey === GroupKey.ChannelKe34 ? '' : 'hide'}}">
                <com:Kemu34Content goodsInfo="{{self.nowGoodInfo}}" payBtnCall="{{self.onPayBtnClick}}"
                    comparePricePool="{{state.comparePricePool}}" goAuth="{{self.goAuth}}" ke3ClaimOpen="{{state.ke3ClaimOpen}}"/>
            </div>
        </sp:if>
    </div>

    <div class="footer-b">
        <sp:if value="state.goodsInfoPool.length > 1">
            <com:bottomTabs tabIndex="{{state.tabIndex}}" labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}" goodsList="{{state.goodsInfoPool}}"
                tabChange="{{self.tabChangeCall}}" />
        </sp:if>
    </div>

    <div style="position: relative;z-index: 10;">
        <com:buyButton>
            <div sp:slot="couponEntry">
                <div class="bottom_coupon" sp-on:click="goCoupon">
                    {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
                </div>
            </div>
        </com:buyButton>
    </div>

    <com:persuadeDialog protocolUrl="{{self.protocolUrl}}">
        <div class="pay-box" sp:slot="text-body">
            <div class="p-title"></div>
            <div class="p-bg"></div>
            <div class="pay-tag">
                <p>路线更新快</p>
                <p>我的教练说很准</p>
            </div>
            <div class="pay-tag">
                <p>跟考试一样的</p>
                <p>细节很多讲解详细</p>
            </div>
            <div class="pay-tag">
                <p>路线不对就退钱</p>
                <p>性价比很高</p>
            </div>
        </div>
    </com:persuadeDialog>
    <com:payDialog />
    <com:onlineDialog changeExam="{{self.changeExam}}">

    </com:onlineDialog>

    <com:moveGoods info="{{(self.moveGoodsVideo[self.nowGoodInfo.groupKey] || {})}}"
        groupKey="{{self.nowGoodInfo.groupKey}}" />
</div>
