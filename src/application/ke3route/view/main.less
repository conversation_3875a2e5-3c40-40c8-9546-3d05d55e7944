.ke3route-page {
    padding-bottom: constant(safe-area-inset-bottom);
    /* 兼容 iOS < 11.2 */
    padding-bottom: env(safe-area-inset-bottom);

    .switch-box {
        padding: 10px 0px;
        margin-right: -15px;

        .switch {
            width: 86px;
            height: 26px;
            background: url(https://web-resource.mc-cdn.cn/web/route/switch.png) no-repeat;
            background-size: 100% 100%;
        }
    }

    .page-header {
        position: absolute;
        z-index: 1000;
        top: 0;
        left: 0;
        width: 100%;

        .header-title {
            display: flex;
            align-items: center;

            .text {
                display: inline-block;
                margin-left: 9px;
                font-size: 17px;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: white;
                line-height: 17px;
            }

            .switch {
                width: 28px;
                height: 17px;
                background: rgba(255, 255, 255, 0.90);
                border-radius: 9px;
                margin-left: 4px;
                display: flex;
                align-items: center;
                justify-content: center;

                .bgimg {
                    width: 12px;
                    height: 10px;
                    background-image: url(../images/<EMAIL>);
                    background-repeat: no-repeat;
                    background-position: center center;
                    background-size: 12px 10px;
                }
            }

            &.active {
                .text {
                    color: #333333;
                }

                .switch {
                    background: linear-gradient(139deg, #ff8149 6%, #ff2803 89%);

                    .bgimg {
                        background-image: url(../images/1.png);
                    }
                }
            }
        }

        .share {
            width: 24px;
            height: 24px;
            background: url(../images/<EMAIL>) no-repeat center center/cover;
            margin-left: auto;

            &.active {
                background: url(../images/2.png) no-repeat center center/cover;
            }
        }

        .list {
            position: absolute;
            left: 0;
            right: 0;
            top: 210px;

            .li {
                width: 345px;
                height: 70px;
                margin: 10px auto 0 auto;
                padding-left: 106px;
                box-sizing: border-box;

                &.li1 {
                    background: url(https://web-resource.mc-cdn.cn/web/route/ban1.png) no-repeat;
                    background-size: 100% 100%;
                }

                &.li2 {
                    background: url(https://web-resource.mc-cdn.cn/web/route/ban2.png) no-repeat;
                    background-size: 100% 100%;
                }

                &.li3 {
                    background: url(https://web-resource.mc-cdn.cn/web/route/ban3.png) no-repeat;
                    background-size: 100% 100%;
                }

                .p1 {
                    font-size: 16px;
                    font-weight: 500;
                    color: #41464F;
                    line-height: 22px;
                    padding-top: 12px;
                }

                .p2 {
                    font-size: 14px;
                    color: #777777;
                    line-height: 20px;
                    padding-top: 2px;
                }
            }
        }

        .header-bottom {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            transform: translateY(100%);
        }

        .hint {
            height: 36px;
            background: #FFF3F0;
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #FF4A40;
            box-sizing: border-box;
            padding-left: 15px;

            .sp-w {
                flex: 1;
                display: flex;
                overflow: hidden;
                height: 36px;
                align-items: center;

                span {
                    display: block;
                    white-space: nowrap;
                }

                .lamp {
                    animation: lamp 12s linear infinite;
                }
            }

            i {
                width: 36px;
                height: 36px;
                background: url(../images/22.png) no-repeat center center;
                background-size: 16px 16px;
            }
        }

        .ul {
            padding: 12px 15px;
            overflow-x: auto;
            overflow-y: hidden;
            background-color: #fff;
            display: -webkit-box;
            -webkit-overflow-scrolling: touch;

            .li {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 134px;
                height: 83px;
                margin-right: 12px;

                p {
                    position: relative;

                    img {
                        width: 134px;
                        height: 83px;
                        border-radius: 2px;
                    }

                    i {
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        margin-left: -11px;
                        margin-top: -11px;
                        width: 22px;
                        height: 22px;
                        display: block;
                        background: url(../images/4.png) no-repeat;
                        background-size: 100% 100%;
                    }

                    .route-name {
                        position: absolute;
                        left: 0;
                        top: 0;
                        color: #fff;
                        background: rgba(0, 0, 0, .6);
                        font-size: 12px;
                        line-height: 12px;
                        padding: 4px 6px;
                        -webkit-transform: scale3d(.8, .8, .8);
                        transform: scale3d(.8, .8, .8);
                        -webkit-transform-origin: left top;
                        transform-origin: left top;
                    }
                }
            }
        }
    }

    .body-panel {
        flex: 1;
        position: relative;
        overflow-y: auto;
    }

    .online-simulate {
        border-top: 10px solid #F4F7F7;
        padding: 12px 0 30px;

        .title {
            height: 40px;
            background: url(../images/3.png) no-repeat -10px center/70px 64px;
            padding-left: 45px;
            display: flex;
            align-items: center;

            .txt {
                display: inline-block;
                max-width: 200px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                font-size: 20px;
                color: #41464F;
                font-weight: bold;
            }

            .tip {
                display: inline-block;
                width: 87px;
                height: 23px;
                background: url(../images/5.png) no-repeat center center/cover;
                margin-left: 4px;
            }
        }

        .list {
            padding: 0 15px;

            .item {
                margin-top: 20px;
                display: flex;

                .img {
                    width: 126px;
                    height: 86px;
                    background-repeat: no-repeat;
                    background-size: cover;
                    background-position: center center;
                    flex-shrink: 0;
                    border-radius: 6px;
                }

                &:nth-of-type(1) .img {
                    background-image: url(../images/7.png);
                }

                &:nth-of-type(2) .img {
                    background-image: url(../images/8.png);
                }

                .info {
                    flex: 1;
                    position: relative;
                    margin-left: 11px;

                    .name {
                        color: #333333;
                        font-size: 15px;
                        font-weight: 500;
                        line-height: 21px;
                    }

                    .dec {
                        margin-top: 5px;
                        font-size: 13px;
                        color: #999;
                        line-height: 18px;

                    }

                    .rule {
                        margin-top: 24px;
                        width: 83px;
                        height: 19px;
                        background: #eef8ff;
                        border-radius: 2px;
                        color: #04A5FF;
                        font-size: 12px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    .use {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        width: 65px;
                        height: 24px;
                        background: linear-gradient(280deg, #00b8ff 7%, #24c8ff 89%);
                        border-radius: 12px;
                        font-size: 13px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        color: white;

                        &::after {
                            content: '';
                            border: 5px solid transparent;
                            border-left: 5px solid white;
                            margin-left: 5px;
                        }
                    }
                }
            }
        }
    }

    .footer {
        height: 47px;
        display: flex;
        position: relative;

        .bg {
            width: 143px;
            height: 52px;
            background: url(../images/15.png) no-repeat;
            background-size: 100%;
            position: absolute;
            left: 0;
            top: -5px;
            z-index: 30;

            &.ios {
                width: 100px;
                background-size: 286px 104px;
                background-position: right -10px;
            }
        }

        .hint {
            // width: 125px;
            height: 32px;
            background: url(../images/18.png) no-repeat;
            background-size: 100% 100%;
            color: #ffffff;
            text-align: center;
            position: absolute;
            right: 15px;
            top: -60px;
            font-size: 12px;
            z-index: 10;
            padding-top: 7px;
            padding: 7px 10px 0 10px;
            box-sizing: border-box;
        }

        .fl {
            position: relative;
            height: 47px;
            display: flex;
            align-items: center;
            box-sizing: border-box;
            // padding-right: 15px;
            z-index: 40;

            p {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-left: 15px;

                i {
                    width: 20px;
                    height: 20px;
                    background: url(../images/16.png) no-repeat;
                    background-size: 100%;
                }

                &.p2 {
                    i {
                        background: url(../images/17.png) no-repeat;
                        background-size: 100%;
                    }
                }

                b {
                    color: #FFDA88;
                    font-size: 12px;
                    line-height: 20px;
                }
            }

            .line {
                width: 1px;
                height: 25px;
                background-color: #FFDA88;
                margin-left: 15px;
            }

        }

        .fr {
            background: linear-gradient(90deg, #FA232F 0%, #FF5400 100%);
            flex: 1;
            display: flex;
            align-items: center;
            flex-direction: column;
            padding-top: 4px;

            .p1 {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                line-height: 25px;
            }

            .p2 {
                color: rgba(255, 255, 255, .8);
                font-size: 12px;
                line-height: 17px;
            }
        }
    }

    .pay-box {
        box-sizing: border-box;
        height: 158px;
        padding: 42px 15px 7px;
        background: #FFF4F4;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
        border-radius: 6px 6px 0px 0px;
        position: relative;
        box-sizing: border-box;

        .p-title {
            width: 110px;
            height: 19px;
            position: absolute;
            top: 13px;
            left: 15px;
            background: url(../images/大家怎么说@2x.png) no-repeat center center/cover;
        }

        .p-bg {
            width: 103px;
            height: 41px;
            position: absolute;
            top: 0;
            right: 5px;
            background: url(../images/<EMAIL>) no-repeat center center/cover;
        }

        .pay-tag {
            display: flex;

            &:nth-of-type(3) {
                margin-top: 4px;
            }

            &:nth-of-type(4) {
                margin-top: 12px;
                padding-left: 23px;
            }

            &:nth-of-type(5) {
                margin-top: 12px;
            }

            // flex-wrap: wrap;
            // justify-content: space-around;
            p {
                color: #F25247;
                background: white;
                font-size: 13px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 12px;
                padding: 0 15px;
                margin-right: 10px;
                white-space: nowrap;

                b {
                    display: block;
                    font-weight: normal;
                }

                i {
                    font-weight: normal;
                }
            }
        }
    }

    .showLookAllBuy-mask {
        position: fixed;
        z-index: 100;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .showLookAllBuy-box {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100;
        max-height: 100%;
        overflow-y: auto;
    }


    .footer-b {
        margin-top: -20px;
        position: relative;
        z-index: 99;
        background: white;
        border-radius: 10px 10px 0px 0px;
    }

    .hide{
        display: none!important;
    }

}




@keyframes lamp {
    0% {
        -webkit-transform: translate3d(10%, 0, 0);
        transform: translate3d(10%, 0, 0);
    }

    100% {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}
