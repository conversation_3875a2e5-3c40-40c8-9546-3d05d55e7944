import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { webClose } from ':common/core';
interface State {
    show: boolean
}
interface Props {
    changeExam()
}
export default class extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            show: false
        };
    }
    changeExam() {
        this.props.changeExam && this.props.changeExam();
    }
    hide() {
        this.setState({
            show: false
        });
    }
    show() {
        this.setState({
            show: true
        });
    }
    goback() {
        webClose();
    }
    willReceiveProps() {
        return true;
    }
}
