<import name="style" content="./main" />
<import name="videoList" content=":application/ke2route/component/videoList/main" />
<import name="header" content=":component/header/main" />

<div class="panel-route-line">
    <div class="banner">
        <!-- 占位header -->
        <com:header scrollTop="{{0}}">
            <div sp:slot="back"></div>
            <div sp:slot="title"></div>
            <div sp:slot="right"></div>
        </com:header>

        <label class="locate">该考场位于{{props.placeInfo.cityName}}{{props.placeInfo.areaName}}</label>

        <div class="auth-content">
            <div class="auth-two-box">
                <div class="look-auth auth-item">
                    <div class="top">
                        <div class="l">
                            <div class="t">看视频</div>
                            <div class="dec">本地教练现场实拍边看视频边学规则</div>
                        </div>
                        <div class="r"></div>
                    </div>
                    <div class="b"></div>
                </div>
                <div class="remember-auth auth-item">
                    <div class="top">
                        <div class="l">
                            <div class="t">记路线</div>
                            <div class="dec">动态轨迹全局预览项目考点一目了然</div>
                        </div>
                        <div class="r"></div>
                    </div>
                    <div class="b"></div>
                </div>
            </div>
            <sp:if value="{{props.interact}}">
                <div class="practice-auth">
                    <div class="img"></div>
                    <div class="center">
                        <div class="t">练考点 <div class="t-sitn">科三VIP专享</div>
                        </div>
                        <div class="dec">考场实景模拟练车</div>
                        <div class="dec">提前练习临考不慌</div>
                    </div>
                    <div class="sign"></div>
                </div>
            </sp:if>
            <sp:if value="{{props.hasAnyTdRoute}}">
                <div class="exam-auth">
                    <div class="img"></div>
                    <div class="center">
                        <div class="t">模拟考 <div class="t-sitn">科三VIP专享</div>
                        </div>
                        <div class="dec">全真场景1:1还原</div>
                        <div class="dec">多上考场顺利通过</div>
                    </div>
                    <div class="sign"></div>
                </div>
            </sp:if>
            <div class="auth-dec">-180天不限次 反复看 重复练 天天模考-</div>
        </div>
        <div class="last-top"></div>
    </div>
    <div class="route-list">
        <div class="route-t">
            <img class="icon"
                src="http://exam-room.mc-cdn.cn/exam-room/2024/01/02/13/fcc8b1dec3734533bfe01542b02cb9a8.png" alt="" />
            本考场共{{props.videoList.length}}条路线
        </div>
        <sp:if value="state.showVideo">
            <com:videoList list="{{props.videoList}}" lookAll="{{self.lookAll}}" />
        </sp:if>
        <div class="route-fdb">
            <label class="feedback" sp-on:click="feedback">路线视频纠错</label>
        </div>
    </div>
    <!-- <sp:if value="{{props.interact}}">
        <div class="online-simulate">
            <div class="title">
                <span class="txt">{{props.placeName}}在线模拟</span><span class="tip"></span>
            </div>
            <div class="list">
                <div class="item" sp-on:click="goExam" data-type="1">
                    <div class="img"></div>
                    <div class="info">
                        <div class="name">路考高仿真模拟考试</div>
                        <div class="dec">看视频练路考双管齐下</div>
                        <div class="rule">本地考试规则</div>
                        <div class="use">去考试</div>
                    </div>
                </div>
                <div class="item" sp-on:click="goExam" data-type="2">
                    <div class="img"></div>
                    <div class="info">
                        <div class="name">考试车灯光模拟练习</div>
                        <div class="dec">提前练不用慌</div>
                        <div class="rule">本地灯光题库</div>
                        <div class="use">去练习</div>
                    </div>
                </div>
            </div>
        </div>
    </sp:if> -->
    <div class="kslc-head">
        <div class="img0"></div>
    </div>
    <div class="kslc" ref="kslc">
        <div class="img img1"></div>
        <div class="img img2"></div>
        <sp:if value="{{props.interact}}">
            <div class="img img3"></div>
        </sp:if>
        <sp:if value="{{props.hasAnyTdRoute}}">
            <div class="img img4"></div>
        </sp:if>
    </div>
</div>
