.panel-route-line {
    .banner {
        min-height: 339px;
        background-color: #FEEBC6;
        position: relative;
        background-image: url(../images/head-bg.png);
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 375px 369px;

        .locate {
            color: #ffffff;
            margin-left: 15px;
            font-size: 14px;
            padding-left: 17px;
            background: url(../images/20.png) no-repeat left center;
            background-size: 12px 15px;
            line-height: 16px;
        }

        .auth-content {
            width: 341px;
            margin: 15px auto 0;

            .auth-two-box {
                display: flex;
                justify-content: space-between;

                .auth-item {
                    width: 162px;
                    padding: 9px 11px 11px;
                    background: linear-gradient(0deg, #ffffff 39%, #fff1e3 92%);
                    border-radius: 8px;

                    .top {
                        display: flex;
                        justify-content: space-between;

                        .l {
                            padding-top: 9px;

                            .t {
                                color: #010101;
                                font-size: 18px;
                                line-height: 25px;
                                font-weight: bold;
                            }

                            .dec {
                                font-size: 12px;
                                color: #777777;
                                line-height: 17px;
                            }
                        }

                        .r {
                            flex-shrink: 0;
                            width: 44px;
                            height: 58px;
                        }
                    }

                    .b {
                        margin-top: 6px;
                        width: 140px;
                        height: 79px;
                        border-radius: 4px;
                    }

                    &.look-auth {
                        .top {
                            .r {
                                background: url(../images/<EMAIL>) no-repeat center center/cover;
                            }
                        }

                        .b {
                            background: url(../images/gif.gif) no-repeat center center/cover;
                        }
                    }

                    &.remember-auth {
                        .top {
                            .r {
                                background: url(../images/<EMAIL>) no-repeat center center/cover;
                            }
                        }

                        .b {
                            background: url(../images/5.png) no-repeat center center/cover;
                        }
                    }
                }
            }

            .exam-auth,
            .practice-auth {
                margin-top: 10px;
                background: linear-gradient(0deg, #ffffff 39%, #fff1e3 92%);
                border-radius: 12px;
                width: 345px;
                height: 89px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px;

                .img {
                    width: 120px;
                    height: 68px;
                    border-radius: 4px;

                }

                .center {
                    width: 140px;

                    .t {
                        color: #010101;
                        font-size: 18px;
                        line-height: 25px;
                        font-weight: bold;
                        display: flex;
                        align-items: center;

                        .t-sitn {
                            display: inline-block;
                            color: #6C2B1F;
                            font-size: 12px;
                            transform: scale(0.9);
                            background: linear-gradient(143deg, #fcdccc 5%, #f4c3a7 94%);
                            border-radius: 4px 0px 4px 0px;
                            padding: 4px;
                            line-height: 1;
                        }
                    }

                    .dec {
                        font-size: 12px;
                        color: #777777;
                        line-height: 17px;
                    }
                }

                .sign {
                    width: 40px;
                    height: 61px;
                }

                &.exam-auth {
                    flex-direction: row-reverse;

                    .img {
                        background: url(../images/gif2.gif) no-repeat center center/cover;
                    }

                    .center {
                        margin-left: auto;
                    }

                    .sign {
                        margin-left: 14px;
                        background: url(../images/<EMAIL>) no-repeat center center/cover;
                    }
                }

                &.practice-auth {
                    .img {
                        background: url(../images/6.png) no-repeat center center/cover;
                    }

                    .sign {
                        margin-right: 14px;
                        background: url(../images/<EMAIL>) no-repeat center center/cover;
                    }
                }
            }

            .auth-dec {
                margin-top: 10px;
                color: #7F7F7F;
                opacity: 0.6;
                font-size: 15px;
                line-height: 21px;
                text-align: center;
            }


        }

        .last-top {
            margin-top: 10px;
            border-radius: 0 18px 0 0;
            height: 23px;
            background-color: #fff;
        }
    }

    .route-list {
        .route-t {
            display: flex;
            align-items: center;
            padding: 0 15px 15px;
            font-size: 14px;

            .icon {
                width: 17px;
                height: 18px;
                margin-right: 2px;
            }

        }

        .route-fdb {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 0 20px 0;

            .feedback {
                font-size: 13px;
                font-weight: 400;
                color: #41464F;
                line-height: 18px;
                background: url(https://web-resource.mc-cdn.cn/web/route/arrow.png) no-repeat right center;
                background-size: 8px 14px;
                padding-right: 14px;
            }
        }
    }

    .kslc-head{
        margin-top: 15px;
        height: 113px;
        background: linear-gradient(180deg, rgba(255, 217, 144,0.14) 2%, rgba(255, 244, 220,0.14) 71%, rgba(255, 247, 230, 0.00) 96%);
        padding-top: 36px;
        .img0 {
            margin: 0 auto;
            width: 319px;
            height: 59px;
            background: url(../images/7.png) no-repeat center center/cover;
        }
    }

    .kslc {
        width: 302px;
        margin: 0 auto;
        padding-bottom: 30px;




        .img {
            height: 204px;
            margin-top: 20px;


            &.img1 {
                margin-top: 2px;
                background: url(../images/1.png) no-repeat center center/cover;
            }

            &.img2 {
                background: url(../images/2.png) no-repeat center center/cover;
            }

            &.img3 {
                background: url(../images/3.png) no-repeat center center/cover;
            }

            &.img4 {
                background: url(../images/4.png) no-repeat center center/cover;
            }
        }
    }
}
