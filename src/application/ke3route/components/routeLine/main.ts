import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { getSystemInfo, openWeb, webClose } from ':common/core';
import { getExamProcess } from ':store/chores';
import { getCityName } from ':common/utils';
import { trackEvent } from ':common/stat';
import { URLParams } from ':common/env';

interface State {
    showVideo: boolean
    userCityCode: string,
    userCityName: string,
    examProcess: any[]
}
interface Props {
    placeName: string
    pay(e)
}

export default class extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            showVideo: false,
            userCityCode: '',
            userCityName: '',
            examProcess: []
        };
    }
    didMount() {
        setTimeout(() => {
            this.setState({
                showVideo: true
            });
        }, 500);

        this.getSystemInfo();
    }
    async getSystemInfo() {
        const systemInfo = await getSystemInfo();
        const userCityCode = systemInfo._userCity || systemInfo._cityCode;
        const userCityName = await getCityName(+userCityCode) || '';

        this.setState({
            userCityCode,
            userCityName
        });

    }
    feedback() {
        const { userCityName } = this.state;
        const { placeName } = this.props;

        const categoryList = [{ 'key': 'ke3kaochang', 'title': '科三考场' }, { 'key': 'ke3xianlu', 'title': '科三路线' }];
        openWeb({
            url: 'http://feedback.nav.mucang.cn/send-feedback?categoryList=' + encodeURIComponent(JSON.stringify(categoryList)) + '&category=ke3kaochang&content=' + encodeURIComponent(userCityName + placeName)
        });

    }
    lookAll = (e) => {
        const { pay } = this.props;

        pay && pay(e);
    }
    willReceiveProps() {
        return true;
    }
}
