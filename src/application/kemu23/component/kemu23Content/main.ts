/*
 * ------------------------------------------------------------------
 * 科目23内容
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GoodsInfo } from ':store/goods';
import Swiper from 'swiper/swiper-bundle.esm.js';
import { makeToast } from ':common/features/dom';
import ReadProtocol1 from ':component/readProtocol/main';
import { openWeb } from ':common/core';
interface State {
    played: boolean;
    played1: boolean;
    index: number,
    startTime: number,
    endTime: number
}

interface Props {
    goodsInfo: GoodsInfo;
    payBtnCall: any;
    videos: any[],
    routeSrc: string,
    routePoster: string,
    routeCity: string,
    startTime: number,
    endTime: number
}
export default class extends Component<State, Props> {
    declare children: {
        ReadProtocol1: ReadProtocol1;
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            index: 0,
            played: false,
            played1: false,
            startTime: null,
            endTime: null

        };
    }
    willReceiveProps() {
        return true;
    }
    didMount() {
        this.event.on('pay-btn', 'click', (e) => {
            const { hasRead, hasCheckbox } = this.children.ReadProtocol1.getReaded();
            if (hasCheckbox && !hasRead) {
                makeToast('请您先阅读会员协议！');
                return;
            }
            this.props.payBtnCall?.(e);
        });
        this.event.on('video-op', 'click', (e) => {
            const index = e.refTarget.getAttribute('data-index');
            this.setState({
                index: index
            });
        });
        this.event.on('view-video', 'click', (e) => {
            const id = e.refTarget.getAttribute('data-id');
            openWeb({
                url: 'http://jiakao.nav.mucang.cn/lampSimulatorVideo?videoId=' + id
            });
        });
        this.event.on('view-caozuo', 'click', () => {
            openWeb({
                url: 'http://jiakao.nav.mucang.cn/lightSimulation'
            });
        });
        this.event.on('view-3d', 'click', () => {
            openWeb({
                url: 'http://jiakao3d.nav.mucang.cn/main'
            });
        });
        this.setTime();
        this.setSwiperMethod();

    }
    setSwiperMethod() {
        setTimeout(function () {
            this.swiper = new Swiper('.swiper-container', {
                pagination: {
                    el: '.swiper-pagination'
                },
                loop: true,
                speed: 500,
                autoplay: {
                    disableOnInteraction: false,
                    delay: 3000
                },
                grabCursor: true

            });
        }, 500);
    }
    setTime() {
        let startTime = +window.localStorage.getItem('kemu23Time');
        const now = new Date().getTime();
        if (!startTime) {
            startTime = new Date().getTime();
            window.localStorage.setItem('kemu23Time', String(startTime));
        } else {
            startTime = parseFloat(String(startTime));
        }
        const passedTime = now - startTime;
        const leftTime = ((24 * 60 * 60 * 1000) - passedTime);
        if (leftTime <= 0) {
            window.localStorage.setItem('kemu23Time', String(new Date().getTime()));
            this.setTime();
            return;
        }
        const endTime = Number(startTime) + (24 * 60 * 60 * 1000);
        this.setState({ startTime, endTime });
    }
}
