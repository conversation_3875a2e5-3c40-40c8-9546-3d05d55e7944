.kemu23-page-content {
    flex: 1;
    background: #fff;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
}

.hd-wrap1 {
    background: #25262f;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    z-index: 20;
}

.hd-wrap {
    position: relative;

    .hd-banner {
        // padding-top: 15px;
        position: relative;
        background: #393128;
        height: 340px;
        background: url(../../../images/1.png) no-repeat bottom;
        background-size: 100% 320px;
        background-color: #292a34;
        box-sizing: border-box;
    }
}

.body-panel {
    flex: 1;
    -webkit-flex: 1;
    padding-bottom: 30px;
}

.sec-1 {
    margin: -100px 15px 0 15px;
    padding: 10px;
    background: linear-gradient(180deg, #FAD5B4 0%, #FFEEE1 100%);
    position: relative;
    z-index: 1;
    background: linear-gradient(180deg, rgba(250, 213, 180, 1) 0%, rgba(255, 238, 225, 1) 100%);
    box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.35);
    border-radius: 10px;

    .s1-c {
        height: 240px;
        padding: 10px 0 0 0;
        background: linear-gradient(0deg, #F7F1E8 0%, #FCFEFC 100%);
        border-radius: 4px;

        .c-h {
            display: flex;
            align-items: center;
            justify-content: center;

            .sp1 {
                width: 32px;
                height: 18px;
                background: url(../../../images/6.png) no-repeat;
                background-size: 100% 100%;
            }

            .p1 {
                font-size: 20px;
                line-height: 28px;
                font-weight: bold;
                color: #494455;
                padding: 0 12px;
            }

            .sp2 {
                width: 32px;
                height: 18px;
                background: url(../../../images/5.png) no-repeat;
                background-size: 100% 100%;
            }
        }

        .c-p {
            color: #B57548;
            font-size: 14px;
            text-align: center;
            line-height: 20px;
            padding-top: 6px;
        }

        .items {
            display: flex;
            margin-top: 8px;
            justify-content: space-around;
            padding: 0 10px;

            p {
                display: flex;
                flex-direction: column;
                align-items: center;

                b {
                    width: 56px;
                    height: 56px;
                    margin-bottom: 8px;
                }

                span {
                    color: #333333;
                    font-size: 13px;
                    line-height: 1.5;
                }
            }

            .p2 {
                margin-left: 14px;
            }

            .ic1 {
                background: url(../../../images/2.png) no-repeat;
                background-size: 100%;
            }

            .ic2 {
                background: url(../../../images/3.png) no-repeat;
                background-size: 100%;
            }

            .ic3 {
                background: url(../../../images/4.png) no-repeat;
                background-size: 100%;
            }
        }
    }

    .c-3 {
        height: 92px;
        width: 337px;
        background: url(../../../images/7.png) no-repeat;
        background-size: 100% 100%;
        box-sizing: border-box;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 0 20px 5px 15px;
        margin-top: -50px;
        position: relative;

        .div1 {
            b {
                color: #FDE2CB;
                font-weight: bold;
                font-size: 32px;
            }

            span {
                color: #FDE2CB;
                font-weight: bold;
                font-size: 16px;
                padding-left: 8px;
            }
        }

        .div2 {
            .p1 {
                color: #8A4824;
                font-size: 18px;
            }

            .count-w {
                display: flex;
                align-items: center;
                justify-content: center;
                color: #8A4824;
                font-size: 12px;
                text-align: right;

                .kemu23-count {
                    padding-top: 5px;
                }

                .p2 {
                    color: #8A4824;
                    font-size: 12px;
                    padding-top: 8px;
                    margin-left: 5px;
                    vertical-align: middle;
                }
            }
        }

        .kemu23tag {
            position: absolute;
            right: 20px;
            top: -4px;
            height: 20px;
            background: linear-gradient(90deg, #ff7d76 0%, #ff4a40 100%);
            font-size: 12px;
            color: #ffffff;
            border-radius: 6px 0px 6px 0px;
            line-height: 18px;
            padding: 2px 6px 0 6px;
            box-sizing: border-box;
        }
    }

    .proto {
        display: flex;
        justify-content: center;

        .pro-txt {
            padding-right: 0;
            ;
        }
    }
}

.sec-3 {
    margin: 0 auto;
    margin-top: 28px;
    position: relative;
    z-index: 1;
    width: 345px;
}

.sec-w {
    padding: 0 15px;
    margin-top: 30px;

    .sec-dp {
        background: #fff;
        border-radius: 10px;
        padding: 0 23px 10px 21px;
        box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.3);
    }

    .dp-hd {
        display: block;
        width: 165px;
        height: 47px;
        background: url(../../../images/14.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 auto;
    }

    .dp-li {
        display: flex;
        display: -webkit-flex;
        padding-bottom: 24px;
        padding-top: 24px;
        border-bottom: 1px dashed #D4D4D4;
        box-sizing: border-box;

        &:nth-last-child(1) {
            border: none;
        }

        .avatar {
            width: 26px;
            height: 26px;
            border-radius: 100%;
        }

        .content {
            -webkit-flex: 1;
            flex: 1;
            padding-left: 11px;
        }

        .p1 {
            font-size: 14px;
            color: #6b6870;
            padding: 2px 0 8px 0;
        }

        .p2 {
            font-size: 14px;
            color: #494455;
            line-height: 20px;
        }
    }

    .sec-qa {
        background: #fff;
        border-radius: 10px;
        padding: 0 21px 20px 18px;
        box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.3);
    }

    .qa-hd {
        display: block;
        width: 145px;
        height: 47px;
        background: url(../../../images/15.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 auto;
    }

    .qa-li {
        display: flex;
        display: -webkit-flex;
        padding-bottom: 12px;
        padding-top: 12px;
        box-sizing: border-box;

        .icon {
            width: 26px;
            height: 26px;
            border-radius: 100%;
            background: url(../../../images/17.png) no-repeat;
            background-size: 100% 100%;
        }

        .content {
            -webkit-flex: 1;
            flex: 1;
            padding-left: 5px;
            padding-top: 2px;
        }

        .p1 {
            font-size: 14px;
            color: #494455;
            padding: 0 0 8px 0;
            font-weight: bold;
        }

        .p2 {
            font-size: 14px;
            color: #75717d;
            line-height: 20px;
        }
    }
}

.video-wrap {
    width: 345px;
    height: 338px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.3);
    border-radius: 10px;
    box-sizing: border-box;
    margin: 0 auto;

    .t-1 {
        width: 221px;
        height: 36px;
        background: url(../../../images/8.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 auto;
    }

    h3 {
        color: #334873;
        font-size: 15px;
        text-align: center;
        padding: 15px 0 12px 0;
        line-height: 15px;
        font-weight: bold;
    }

    .video-w {
        width: 305px;
        height: 177px;
        border-radius: 10px;
        margin: 0 auto;
        overflow: hidden;

        img {
            width: 100%;
            // height: 100%;
        }
    }

    video {
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 10px;
    }

    .video-t-w {
        padding: 0 20px;
    }

    .video-t {
        display: flex;
        //background-image:linear-gradient(90deg, rgba(51, 72, 115, .6) 0%, #334873 50%, rgba(51, 72, 115, .6) 100%);
        //-webkit-background-clip:text;
        //-webkit-text-fill-color:transparent;
        justify-content: space-around;
        align-items: center;
        font-size: 12px;
        color: #334873;
        padding: 15px 0 0 0;
        display: -webkit-box;
        overflow-x: scroll;

        span {
            display: block;
            padding: 8px 12px;
            margin: 1px 0;
        }

        .active {

            color: #C17947;
            border: 1px solid #C17947;
            border-radius: 5px;
            font-size: 15px;
            line-height: 15px;
            font-weight: bold;
        }
    }
}

.slider-wrap {
    width: 345px;
    // height: 294px;
    padding-bottom: 20px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.3);
    border-radius: 10px;
    box-sizing: border-box;

    .t-2 {
        width: 221px;
        height: 36px;
        background: url(../../../images/9.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 auto;
    }

    h3 {
        color: #334873;
        font-size: 15px;
        text-align: center;
        padding: 15px 0 13px 0;
        line-height: 15px;
        font-weight: bold;
    }

    .con {
        width: 305px;
        height: 166px;
        margin: 0 auto;
    }

    .op {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 20px;

        p {
            color: #333333;
            font-size: 14px;
            flex: 1;
        }

        label {
            width: 82px;
            height: 31px;
            background: url(../../../images/18.png) no-repeat;
            background-size: 100% 100%;
        }
    }
}

.slider-wrap1 {
    width: 345px;
    height: 366px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.3);
    border-radius: 10px;
    box-sizing: border-box;

    .t-3 {
        width: 221px;
        height: 36px;
        background: url(../../../images/10.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 auto;
    }

    h3 {
        color: #334873;
        font-size: 15px;
        text-align: center;
        padding: 15px 0 13px 0;
        line-height: 15px;
        font-weight: bold;
    }

    .con {
        width: 305px;
        height: 168px;
        margin: 0 auto;

        img {
            width: 100%;
            border-radius: 5px;
        }
    }

    .items {
        display: flex;
        margin-top: 15px;
        justify-content: space-around;
        padding: 0 15px;

        p {
            display: flex;
            flex-direction: column;
            align-items: center;

            b {
                width: 56px;
                height: 56px;
                margin-bottom: 8px;
            }

            span {
                color: #333333;
                font-size: 13px;
                line-height: 1.5;
            }
        }

        .p2 {
            margin-left: 14px;
        }

        .ic1 {
            background: url(../../../images/11.png) no-repeat;
            background-size: 100%;
        }

        .ic2 {
            background: url(../../../images/12.png) no-repeat;
            background-size: 100%;
        }

        .ic3 {
            background: url(../../../images/3.png) no-repeat;
            background-size: 100%;
        }
    }
}

.divider {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 25px;
    margin: -15px 0;

    i {
        width: 13px;
        height: 49px;
        background: url(../../../images/13.png) no-repeat;
        background-size: 100% 100%;
    }
}

.swiper-container1 {
    flex: 1;
    -webkit-flex: 1;
    width: 100%;
    height: 100%;
    border-radius: 10px;
}

.swiper-page1 {
    width: 100%;
    text-align: center;
    line-height: 0;
    padding-top: 5px;
    position: relative;
    bottom: 25px !important;

    span {
        background: #B9BFCB;
        background: #cccccc;
        width: 16px;
        height: 4px;
        border-radius: 4px;
        margin: 0 2px;
    }
}

// .swiper-pagination-bullet {
//     background: #B9BFCB;
//     background: #cccccc;
//     width: 16px;
//     height: 4px;
//     border-radius: 4px;
//     margin: 0 2px;
// }

.swiper-pagination-bullet-active {
    background: #F2A569;
    background: #FFFFFF;
    width: 16px;
    height: 4px;
    border-radius: 4px;
    margin: 0 2px;
}

.swiper-slide-other {
    background: #fff;
    // padding(15, 0);
    box-sizing: border-box;
    transition: 300ms;
    -webkit-transition: 300ms;
    transform: scale3d(1, 1, 1) translate3d(0, 0, 0);
    -webkit-transform: scale3d(1, 1, 1) translate3d(0, 0, 0);
    //box-shadow: 1px 3px 6px 1px rgba(0, 0, 0, 1);
    //border-top-left-radius: 6px;
    //border-top-right-radius: 6px;
    // width: 295px;

    img {
        display: block;
        width: 100%;
        height: 100%;
    }

    .p1 {
        width: 100%;
        height: 100%;
        background: url(../../../images/deng3.png) no-repeat;
        background-size: 100% 100%;
    }

    .p2 {
        width: 100%;
        height: 100%;
        background: url(../../../images/deng1.png) no-repeat;
        background-size: 100% 100%;
    }

    .p3 {
        width: 100%;
        height: 100%;
        background: url(../../../images/deng2.png) no-repeat;
        background-size: 100% 100%;
    }

    .p4 {
        width: 100%;
        height: 100%;
        background: url(../../../images/deng4.png) no-repeat;
        background-size: 100% 100%;
    }

    .p5 {
        width: 100%;
        height: 100%;
        background: url(../../../images/deng6.png) no-repeat;
        background-size: 100% 100%;
    }
}

.swiper-slide-active,
.swiper-slide-duplicate-active {
    transform: scale(1);
    -webkit-transform: scale(1);
    //box-shadow: 1px 3px 6px 1px rgba(0, 0, 0, 0.19);
    height: 100%;
    //overflow-y: scroll;
    overflow: hidden;
}

.readprotocol {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    text-align: center;
}