<import name="style" content="./main" module="S" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="Kemu23Content" content=":application/kemu23/component/kemu23Content/main" />
<div class="page-container">
    <div class=":header">
        <com:header title="{{self.title}}" theme="black" qaKey="{{self.qaKey}}" endTheme="black"
            back="{{self.onBackClick}}" />
    </div>
    <div class="{{S.main}} {{S.content}}" ref="scroller" sp-on:scroll="pageScroll">
        <com:Kemu23Content startTime="{{state.startTime}}" endTime="{{state.endTime}}" videos="{{state.videos}}"
            goodsInfo="{{self.currentGoods}}" payBtnCall="{{self.onPayBtnClick}}"></com:Kemu23Content>
    </div>

    <div class="{{S.footer}} {{state.showFooter ? '': S.hide}}">
        <com:buyButton />
    </div>

    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
</div>