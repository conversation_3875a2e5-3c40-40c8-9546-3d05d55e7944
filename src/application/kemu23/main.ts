/*
 * ------------------------------------------------------------------
 * 科目23落地页
 * ------------------------------------------------------------------
 */
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import ExpiredDialog from ':component/expiredDialog/main';
import PayDialog from ':component/payDialog/main';
import { ensureSiriusBound, getDefaultPayType, PayBoundType, startSiriusPay } from ':common/features/pay';
import { BUYED_URL } from ':common/navigate';
import { PayType, persuadeDialogAllow, Platform, setPageName } from ':common/env';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import Header from ':component/header/main';
import PersuadeDialogCom from ':component/persuadeDialog/main';
import { trackExit, trackGoPay, trackPageLoad } from ':common/stat';
import { webClose, getSystemInfo } from ':common/core';
import { onWebBack } from ':common/features/persuade';
import { getKemu23VideoList } from ':store/chores';
import jump from ':common/features/jump';
const groupKey = GroupKey.ChannelKe3;
let timer;
// 标记是否展示过挽留弹窗
let flag = false;
interface State {
    goodsList: GoodsInfo[];
    tabIndex: number;
    scrolled: boolean,
    showFooter: boolean,
    cityCode: string,
    videos: any[],
    startTime: number | string,
    endTime: number | string,
    prevScrollTop: number
}

export default class extends Application<State> {
    swiper: any
    declare children: {
        buyButton: BuyButton;
        expiredDialog: ExpiredDialog;
        persuadeDialog: PersuadeDialogCom;
        payDialog: PayDialog;
        header: Header;
    }
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            goodsList: [],
            tabIndex: 0,
            scrolled: false,
            cityCode: '',
            showFooter: false,
            videos: [
            ],
            startTime: null,
            endTime: null,
            prevScrollTop: 0
        };
    }
    get pageName() {
        return '灯光模拟考试落地页';
    }
    get currentGoods() {
        return this.state.goodsList[this.state.tabIndex];
    }
    get title() {
        return '科三灯光模拟';
    }
    get qaKey() {
        return 'qaKey5';
    }
    async didMount() {
        setPageName(this.pageName);
        
        ensureSiriusBound({ groupKey, type: PayBoundType.GoStatusPage });
        this.fetchSystemInfo();
        await this.fetchGoodsInfo();

        trackPageLoad();
        // 挽留弹窗
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }
    pageScroll(e) {
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget?.scrollTop;
            const targetTop = 500;
            this.children.header.setScrollBg(prevScrollTop, '#000000');
            if (prevScrollTop <= targetTop && this.state.showFooter) {
                this.setState({
                    showFooter: false
                });
            } else if (prevScrollTop > targetTop && !this.state.showFooter) {
                this.setState({
                    showFooter: true
                });
            }
        }, 100);
    }
    private async fetchGoodsInfo() {
        const [goodsInfo] = await getGroupSessionInfo({
            groupKeys: [groupKey]
        });
        if (goodsInfo.expired) {
            this.children.expiredDialog.show({ time: goodsInfo.expiredTime });
        }
        // 主商品已购买，直接跳走
        if (goodsInfo.bought) {
            jump.replace(BUYED_URL);
            return;
        }
        const goodsList = [goodsInfo];
        this.setState({ goodsList });
        this.setPayment();
    }
    private async fetchSystemInfo() {
        const { _userCity, _cityCode } = await getSystemInfo();
        const cityCode = _userCity || _cityCode;
        this.setState({
            cityCode
        }, () => {
            this.getRouteVideo();
        });
    }
    private setPayment() {
        this.children.buyButton.setPay({
            androidPay: this.pay.bind(this),
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => iosBuySuccess({ groupKey: this.currentGoods.groupKey })
        });
        this.children.buyButton.setButtonConfig({
            groupKey: this.currentGoods.groupKey,
            type: 3,
            title: `¥ ${this.currentGoods.payPrice} 确认协议并支付`,
            subtitle: '',
            tag: {
                text: '限时赠送科三3D练车'
            },
            fragmentName1: '底部吸底按钮'
        });
    }
    async getRouteVideo() {
        if (!this.state.cityCode) {
            return;
        }
        const data = await getKemu23VideoList(this.state.cityCode);
        if (data) {
            const videos = data.itemList || [];
            this.setState({
                videos: videos
            });
        }
    }
    /** 发起支付 */
    async pay(stat: PayStatProps) {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.currentGoods.groupKey,
            sessionIds: this.currentGoods.sessionIds,
            activityType: this.currentGoods.activityType,
            couponCode: '',
            ...stat
        }).catch(async (err) => {
            console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: this.currentGoods.groupKey,
                payPrice: this.currentGoods.payPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
          
        });
    }
    // 退出页面的回调
    goBackPage() {
        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: this.currentGoods,
                groupKey: this.currentGoods.groupKey,
                payPrice: this.currentGoods.payPrice,
                title: '',
                txt1: '',
                txt2: '',
                txt3: '',
                txt4: '',
                tag: {
                    text: '每人仅限1次'
                },
                type: 'kemu23'
            }).then(payType => {
                if (payType === false) {
                    trackExit();
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            trackExit();
            webClose();
        }
    }
    onBackClick = () => {
        this.goBackPage();
    }
    onPayBtnClick = (e) => {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');

        // 点击支付按钮打点
        trackGoPay({
            groupKey: this.currentGoods.groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(this.currentGoods.groupKey, {
                fragmentName1
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: this.currentGoods.groupKey,
                payPrice: this.currentGoods.payPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });
        }
    }
}
