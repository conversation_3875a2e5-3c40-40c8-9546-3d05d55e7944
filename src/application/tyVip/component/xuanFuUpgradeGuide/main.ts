import { Platform, setPageName } from ':common/env';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import Texts from ':common/features/texts';
import { trackEvent, trackGoPay } from ':common/stat';
import { openWeb } from ':common/core';
import Loading from ':application/buyed/components/Loading/main';
import { getGroupSessionInfo, GoodsInfo } from ':store/goods';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import { newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { reload } from ':common/features/jump';
import BuyButton, { PayStatProps } from ':component/buyButton/main';

interface State {
    showXuanfu: boolean
    goodsInfoPool: GoodsInfo
}
interface Props {
    swallConfig: any;
    
    nowGoodInfo: any;

    onPayBtnCall: (parmas:object) => void;
}
export default class extends Component<State, Props> {
    declare children: {
        loading: Loading;
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            showXuanfu: false,
            goodsInfoPool: null
        };
    }
    didMount() {
        // setPageName(Texts.BUYED_PAGE);

        // 注册底部支付方法
        // this.children.buyButton.setPay({
        //     androidPay: this.pay,
        //     intercepter: async () => {
        //         return false;
        //     },
        //     iosPaySuccess: () => {
        //         iosBuySuccess({ groupKey: this.state.goodsInfoPool.groupKey });
        //     }
        // });
    }
    closeXuanfu(e) {
        e && e.stopPropagation();
        this.setState({ showXuanfu: false });
    }
    show() {
        this.setState({ showXuanfu: true });
    }
    gotoBuyXunafu() {
        this.setState({
            goodsInfoPool: this.props.nowGoodInfo
        }, () => {
            this.props.onPayBtnCall({
                stat: {
                    fragmentName1: '底部升级浮窗'
                }
            });
        });
    }

    willReceiveProps() {
        return true;
    }
}
