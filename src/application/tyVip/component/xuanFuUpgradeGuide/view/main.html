<import name="style" content="./main" module="S" />
<import name="loading" content=":application/buyed/components/loading/main" />

<div>
    <sp:if value='{{state.showXuanfu}}'>
        <div class="{{S.xuanfuDialog}}" sp-on:click="gotoBuyXunafu">
            <div class="{{S.xunfuContent}}">
                <div class="{{S.left}}">
                    <p class="{{S.title}}"><img class="{{S.iconSpan}}"
                            src="{{props.swallConfig.icon}}" />亲爱的会员{{props.userData.nickname}}
                    </p>
                    <p class="{{S.desc}}">升级<span>{{props.swallConfig.name}}</span>解锁全部VIP权益</p>
                </div>
                <div class="{{S.right}}">
                    <div class="{{S.button}}">{{props.nowGoodInfo.payPrice}}元立即升级</div>
                </div>
                <div class="{{S.closeContainer}}" sp-on:click="closeXuanfu">
                    <div class="{{S.close}}"></div>
                </div>
            </div>
        </div>
    </sp:if>

    <com:loading />
</div>