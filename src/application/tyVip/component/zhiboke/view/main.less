.zhi<PERSON><PERSON> {
    margin-bottom: 20/2px;
    margin-top: 12px;

    .zhiboke-div {
        width: 690/2px;
        min-height: 164/2px;

        padding-left: 16/2px;
        padding-right: 22/2px;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center center;
        background-image: url(../image/bg.png);
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 6/2px;
        .img-avter {
            width: 91/2px;
            height: 91/2px;
            border: 1px solid #ffffff;
            padding: 6/2px;
            border-radius: 50%;
            margin-right: 24/2px;
            position: relative;

            .img-gif {
                width: 36/2px;
                height: 36/2px;
                position: absolute;
                right: -13/2px;
                top: -10/2px;
                width: 36/2px;
                height: 36/2px;
                background: linear-gradient(
                    90deg,
                    #ff7810 0%,
                    #fe3c29 55%,
                    #fe6164 100%
                );
                border-radius: 50%;

                .img-gif-bg {
                    width: 36/2px;
                    height: 36/2px;
                    background: url(../image/qitaPlay.png) no-repeat center;
                    background-size: 36/2px 36/2px;
                }
            }

            .img-avter-bg {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center center;
            }
        }

        .title-container {
            flex: 1;

            .title {
                font-size: 36/2px;
                font-family: PingFangSC-Semibold, PingFang SC;
                color: #ffffff;
                font-weight: 600;
                line-height: 50/2px;
                text-shadow: 0px 2/2px 6/2px rgba(0, 71, 199, 0.3);
                margin-bottom: 4/2px;
            }

            .desc {
                font-size: 26/2px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 30/2px;
            }
        }

        .button-div {
            width: 206/2px;
            height: 84/2px;
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center center;
            position: relative;
            background-image: url(../image/button.png);
            margin-top: 3px;
        }
    }
}
