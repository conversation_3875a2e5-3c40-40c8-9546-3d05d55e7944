import { openWeb } from ':common/core';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { KemuType } from ':common/env';

interface State {
}

interface props{
    nowKemu:KemuType
}

export default class Count extends Component<State, props> {
  
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
        };
    }

    async didMount() {
        console.log('ty');
    }

    goStudy(e) {
        const url = e.target.getAttribute('data-url');

        openWeb({
            url,
            params: {
                kemuStyle: this.props.nowKemu
            }
        });
    }

    willReceiveProps() {
        return true;
    }
}
