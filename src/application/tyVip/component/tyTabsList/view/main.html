<import name="style" content="./main" module="S" />

<div class=":ty-list">
    <sp:each for="{{props.list}}">
        <div class=":item {{S['num-' + ($index+1)]}}">
            <div class=":title">0{{$index + 1}} {{$value.name}}</div>
            <div class=":content">{{$value.desc}}</div>
            <div class=":btn" data-url="{{$value.action[URLParams.carStyle]}}" sp-on:click="goStudy">去学习</div>
        </div>
    </sp:each>
</div>