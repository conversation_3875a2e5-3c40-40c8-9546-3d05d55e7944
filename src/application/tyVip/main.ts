import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import Header from ':component/header/main';
import PackageRecommend from ':application/buyed/components/packageRecommend/main';
import Zhiboke from ':application/buyed/components/zhiboke/main';
import XuanFuUpgradeGuide from ':application/buyed/components/xuanFuUpgradeGuide/main';
import PayDialog from ':component/payDialog/main';

import { MCProtocol } from '@simplex/simple-base';
import { getSwallowConfig } from ':store/chores';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { KemuType, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import { JING_JIAN, MI_JUAN, REAL_ROOM, STEP1_CAR, STEP1_K3_CAR, STEP1_K3_TRUCK, STEP1_TRUCK, STEP2_CAR, STEP2_K3_CAR, STEP2_K3_TRUCK, STEP2_TRUCK, STEP3_K3_TRUCK } from ':common/navigate';
import { getUserIdentity } from ':store/buyed';
import { trackGoPay, trackPageLoad } from ':common/stat';
import { openWeb } from ':common/core';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { Dialog } from ':component/dialog/main';
let timer;
const swallowKey = 'jk_vip_upgrade';

interface State {
    prevScrollTop: number,
    userData: {
        nickname: string,
        avatar: string,
    },
    goodsInfoPool: GoodsInfo[],
    tabIndex: number,
    userAuthList: Array<any>
    tylist14: {
        name: string,
        desc: string,
        action: {
            car: string,
            truck: string
        }
    }[],
    tylist2: {
        name: string,
        desc: string,
        action: {
            car: string,
            truck: string
        }
    }[],
    tylist3: {
        name: string,
        desc: string,
        action: {
            car: string,
            truck: string
        }
    }[],
    // 远程配置
    swallConfig: any,
    passRate: number
}
export default class extends Application<State> {
    declare children: {
        header: Header;
        // 会员专属优惠套餐推荐
        packageRecommend: PackageRecommend;
        zhiboke: Zhiboke;
        // 升级引导悬浮框
        xuanFuUpgradeGuide: XuanFuUpgradeGuide;

        payDialog: PayDialog;

        buyButton: BuyButton;

        errDialog: Dialog
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            prevScrollTop: 0,
            userData: {
                nickname: '***',
                avatar: 'https://jiakao-audit.image.mucang.cn/jiakao-audit/2022/01/24/11/8a66545b8d3a49f5af0b3dc1c5fd87f5.png'
            },
            tabIndex: URLCommon.kemu - 1,
            tylist14: [{
                name: '精简题库',
                desc: '可试练前50题',
                action: {
                    car: JING_JIAN,
                    truck: JING_JIAN
                }
            }, {
                name: '真实考场模拟',
                desc: '真实考场模拟可试练1次',
                action: {
                    car: REAL_ROOM,
                    truck: REAL_ROOM
                }
            }, {
                name: '考前冲刺检查',
                desc: '考前秘卷可试练前30题',
                action: {
                    car: MI_JUAN,
                    truck: MI_JUAN
                }
            }],
            tylist2: [{
                name: '观看必考项目视频讲解',
                desc: '必考项目视频全部可学习',
                action: {
                    car: STEP1_CAR,
                    truck: STEP1_TRUCK
                }
            }, {
                name: '科二单项练习',
                desc: '有效期内可无限练习',
                action: {
                    car: STEP2_CAR,
                    truck: STEP2_TRUCK
                }
            }, {
                name: '科二模拟考场',
                desc: '有效期内可无限练习',
                action: {
                    car: STEP2_CAR,
                    truck: STEP2_TRUCK
                }
            }],
            tylist3: [{
                name: '观看必考项目视频讲解',
                desc: '必考项目视频全部可学习',
                action: {
                    car: STEP1_K3_CAR,
                    truck: STEP1_K3_TRUCK
                }
            }, {
                name: '科三单项练习',
                desc: '有效期内可无限练习',
                action: {
                    car: STEP2_K3_CAR,
                    truck: STEP2_K3_TRUCK
                }
            }, {
                name: '科三模拟考场',
                desc: '有效期内可无限练习',
                action: {
                    car: STEP2_K3_CAR,
                    truck: STEP3_K3_TRUCK
                }
            }],
            swallConfig: {},
            goodsInfoPool: [],
            userAuthList: [],
            passRate: 0
        };
    }
    get nowGoodInfo() {
        const { goodsInfoPool } = this.state;
        return goodsInfoPool[0];
    }
    get nowKemu() {
        const { userAuthList, tabIndex } = this.state;

        return userAuthList[tabIndex]?.kemu || 1;
    }

    didMount() {
        setPageName('我的体验VIP页');
        // 页面进出时长打点
        trackPageLoad();

        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: (groupKey) => {
                iosBuySuccess({ groupKey });
            }
        });
        this.init();
        this.children.packageRecommend.getRecommendGoodsData(1);
    }

    async init() {
        this.getUserData();
        this.getUserAuthList();
        this.swallowConfigRequest();
        this.children.zhiboke?.vipExclusiveLiveRequest(this.nowKemu);

        const passRate = await this.getKe14PassRate(1);
        this.setState({
            passRate
        });
    }
    async getUserAuthList() {
        const itemList = await getUserIdentity();
 
        this.setState({
            userAuthList: itemList
        });
    }

    async getUserData() {

        MCProtocol.Core.User.get((data: any) => {
            this.setState({
                userData: data.data
            });
        });
    }

    onConfirm() {
        openWeb({
            url: 'http://jiakao.nav.mucang.cn/change-tiku?backHome=1'
        });
    }

    // 获取科一科四通过率
    getKe14PassRate(kemu: number): Promise<number> {
        return new Promise((resolve) => {
            MCProtocol.Vip.getPassRate({
                car: URLCommon.tiku,
                kemu: kemu,
                kemuStyle: kemu,
                carStyle: URLCommon.tiku,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                callback: function (ret) {
                    let data = ret.data * 100;
                    data = data >= 100 ? 100 : parseInt(data + '');
                    resolve(data);
                }
            });
        });
    }

    pageScroll(e) {
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            this.children.header.setScrollBg(prevScrollTop, '#000000');
            this.setState({
                prevScrollTop
            });
        }, 30);
    }
    async onTab(e) {
        const tabIndex = +e.target.getAttribute('data-index');
        const { userAuthList } = this.state;

        if (tabIndex === this.state.tabIndex) {
            return;
        }
        this.setState({
            tabIndex
        });

        openWeb({
            url: 'http://jiakao.nav.mucang.cn/switchKemu?kemu=' + this.nowKemu
        });

        if (!userAuthList[tabIndex]?.expireTime) {
            this.children.errDialog.show();
        }

        if (this.nowKemu === KemuType.Ke1 || this.nowKemu === KemuType.Ke4) {
            this.children.zhiboke?.vipExclusiveLiveRequest(this.nowKemu);
            const passRate = await this.getKe14PassRate(this.nowKemu);
            this.setState({
                passRate
            });
        }
    }

    async swallowConfigRequest() {
        const swallConfig = await getSwallowConfig({
            key: swallowKey,
            kemu: 1
        });
        if (!swallConfig || !swallConfig.show) {
            return;
        }
        const goodsInfoPool: GoodsInfo[] = [];
        goodsInfoPool.push({
            groupKey: swallConfig.groupKey
        } as GoodsInfo);
        this.setState({ swallConfig, goodsInfoPool }, () => {
            this.getGoodInfo();
        });
    }

    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });
        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo) => {
                newGoodsPool.push(goodInfo);
            });
            this.setState({
                goodsInfoPool: newGoodsPool
            });

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                if (this.nowGoodInfo?.upgrade || !this.nowGoodInfo.bought) {
                    this.children.xuanFuUpgradeGuide?.show();
                }
            }, 60);
        });
    }

    onPayBtnCall = (config) => {
        const goodsInfoPool = this.nowGoodInfo;
        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool.groupKey,
            ...config?.stat
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool.groupKey, {
                ...config?.stat
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: goodsInfoPool.groupKey,
                payPrice: goodsInfoPool.payPrice,
                onPay: () => {
                    this.onPay({ stat: config?.stat });
                },
                ...config.stat
            });

        }
    }
    pay = async (stat: PayStatProps) => {
        this.onPay({
            stat: {
                ...stat
            }
        });
    }
    onPay = async (config: { stat: PayStatProps }) => {
        const goodsInfoPool = this.nowGoodInfo;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool.groupKey,
            sessionIds: goodsInfoPool.sessionIds,
            activityType: goodsInfoPool.activityType,
            couponCode: '',
            ...config.stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool.groupKey });
        }).catch(async () => {
            this.children.payDialog.show({
                groupKey: goodsInfoPool.groupKey,
                payPrice: goodsInfoPool.payPrice,
                onPay: () => {
                    this.pay(config.stat);
                },
                ...config.stat
            });
        });
    }
}
