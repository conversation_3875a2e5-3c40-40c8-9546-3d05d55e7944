.ty-vip-page {
    background: #fff;
    .page-header {
        position: absolute;
        z-index: 1000;
        top: 0;
        left: 0;
        width: 100%;
    }
    .body-panel {
        flex: 1;
        overflow-y: scroll;
        .top{
            width: 375px;
            height: 170px;
            background:url(../images/1.png) no-repeat;
            background-size:cover;
            padding-top: 90px;
            margin-bottom: -15px;
            .user-avatar{
                width: 100%;
                display: flex;
                align-items: center;
                position: relative;
                padding-left: 15px;
                padding-right: 15px;
                .avatar{
                    height: 48px;
                    width: 48px;
                    border-radius:50%;
                    background-size: 100% 100%;
                }
                .avatar-title {
                    flex: 1;
                    padding-left: 20/2px;
                    position: relative;
        
                    .username {
                        font-size: 20px;
                        font-weight: 600;
                        color: #50472f;
                        line-height: 28px;
                    }
                    
                    .ty-time{
                        color: #50472f;
                        font-size: 13px;
                        line-height:  18px;
                    }
                }
                .ty-vip-icon{
                    position: absolute;
                    width: 61px;
                    height: 22px;
                    background: rgba(255,255,255,0.7);
                    border-radius: 11px;
                    bottom: 0px;
                    right: 21px;
                    font-size: 11px;
                    text-align: center;
                    color: #d77700;
                    line-height: 22px;
                }
            }
        }
        .content{
            padding: 0 15px 0px;
            border-bottom:10px solid #f4f7f7;
        }
        .tabs{
            display: flex;
            align-items: center;
            .tab{
                flex: 1;
                height: 40px;
                font-size: 15px;
                text-align: center;
                line-height: 40px;
                color: #464646;
            }
            .active{
                font-size: 18px;
                font-weight: 600;
                text-align: center;
                color: #333333;
                position: relative;
                &:before {
                    content: "";
                    width: 20px;
                    height: 3px;
                    background: #333333;
                    border-radius: 2px;
                    position: absolute;
                    left: 50%;
                    transform: translateX(-50%);
                    bottom: 0;
                }
            }
        }
        .tabs-content{
            padding: 15px;
            .title{
                font-size: 18px;
                font-weight: bold;
                color: #333333;
                margin-bottom: 10px;
            }
            .ty-ke1-4{
                .passRate{
                    width: 345px;
                    height: 37px;
                    background: linear-gradient(270deg,#fff7f8, #feeeef 2%);
                    border-radius: 4px;
                    font-size: 13px;
                    text-align: left;
                    color: #ff4a40;
                    line-height: 37px;
                    padding-left: 30px;
                    position: relative;
                    margin-bottom: 10px;
                    &::after{
                        content:"";
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        left: 10px;
                        width: 15px;
                        height: 15px;
                        background: url(../images/<EMAIL>) no-repeat;
                        background-size:100% 100%;
                    }
                }
            }
        }
    }
    .dialog-panel {
        position: fixed;
        left: 0;
        right: 0;
        .content {
            border-radius: 10px 10px 0px 0px;
            position: absolute;
            left:50%;
            transform: translateX(-50%);
            bottom: 0;
            width: 375px;
            height: 210px;
            background: white;
            padding: 24px 15px;
        }
        .header {
            font-size: 18px;
            text-align: center;
            color: #333333;
            line-height: 25px;
            font-weight: bold;
        }
        .text {
            font-size: 15px;
            text-align: center;
            color: #333333;
            line-height: 21px;
            margin: 16px 0 31px 0;
        }
    
        .btns {
            display: flex;
            justify-content: space-around;
            .confirm {
                width: 200px;
                height: 44px;
                background: #ff822d;
                border-radius: 220px;
                font-size: 16px;
                text-align: center;
                color: #ffffff;
                line-height: 44px;
            }
        }
    }
}