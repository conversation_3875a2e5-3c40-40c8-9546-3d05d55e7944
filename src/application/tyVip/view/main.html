<import name="style" content="./main" />
<import name="header" content=":component/header/main" />
<import name="PackageRecommend" content=":application/buyed/components/packageRecommend/main" />
<import name="tyTabsList" content="../component/tyTabsList/main" />
<import name="Zhiboke" content="../component/zhiboke/main" />
<import name="XuanFuUpgradeGuide" content="../component/xuanFuUpgradeGuide/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="dialog" content=":component/dialog/main" />

<div class="page-container  ty-vip-page">
    <div class="page-header">
        <com:header title="VIP课程" theme="white" endTheme="white" scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}">
        </com:header>
    </div>

    <div class="body-panel" sp-on:scroll="pageScroll">
        <div class="top">
            <div class="user-avatar">
                <div class="avatar" style="background-image: url({{state.userData.avatar}})"></div>
                <div class="avatar-title">
                    <div class="username">体验VIP课程</div>
                    <div class="ty-time">
                        {{Tools.dateFormat(state.userAuthList[state.tabIndex].expireTime, 'yyyy-MM-dd')}}到期
                    </div>
                </div>

                <div class="ty-vip-icon {{state.userAuthList[state.tabIndex].type !== 3 ? 'hide' : ''}}">体验VIP</div>
            </div>
        </div>
        <div class="content">
            <com:PackageRecommend   name="packageRecommend" theme="ty-vip" propPageType="tyVipPage">
            </com:PackageRecommend>
        </div>

        <div class="tabs" sp:if="{{state.userAuthList.length > 1}}">
            <sp:each for="{{state.userAuthList}}">
                <div class="tab {{self.nowKemu == $value.kemu ? 'active' : ''}}" sp-on:click="onTab"
                    data-index="{{$index}}">科{{Tools.numZh($value.kemu)}}</div>
            </sp:each>
        </div>

        <div class="tabs-content">
            <div class="title">
                科{{Tools.numZh(self.nowKemu)}}体验VIP权益
            </div>

            <sp:if value="{{self.nowKemu == 1 || self.nowKemu == 4}}">
                <div class="ty-ke1-4">
                    <sp:if value="{{state.passRate}}">
                        <div class="passRate">
                            当前通过率 {{state.passRate}}%，可以去参加真实考试了
                        </div>
                    </sp:if>

                    <com:Zhiboke name="zhiboke" fragmentName1="体验权益列表" actionName="进入直播间"></com:Zhiboke>
                    <com:tyTabsList list="{{state.tylist14}}" nowKemu="{{self.nowKemu}}" />
                </div>
            </sp:if>

            <sp:if value="{{self.nowKemu == 2 || self.nowKemu == 3}}">
                <com:tyTabsList nowKemu="{{self.nowKemu}}"
                    list="{{self.nowKemu == 2 ? state.tylist2 : state.tylist3}}" />
            </sp:if>

        </div>
    </div>
    <div class="footer">
        <!-- 升级引导悬浮框 -->
        <com:XuanFuUpgradeGuide userData="{{state.userData}}" nowGoodInfo="{{self.nowGoodInfo}}"
            prevScrollTop="{{state.prevScrollTop}}" swallConfig="{{state.swallConfig}}" name="xuanFuUpgradeGuide"
            onPayBtnCall="{{self.onPayBtnCall}}">

        </com:XuanFuUpgradeGuide>

        <com:payDialog />
        <com:buyButton></com:buyButton>

        <com:dialog position="bottom" name="errDialog">
            <div class="dialog-panel">
                <div class="content">
                    <div class="header">遇到问题</div>
                    <div class="text">
                        抱歉，当前车型、科目下没有体验VIP
                    </div>
                    <div class="btns">
                        <div class="confirm" sp-on:click="onConfirm">
                            去切换
                        </div>
                    </div>
                </div>
            </div>
        </com:dialog>
    </div>
</div>