<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="moveGoods" content=":component/moveGoods/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="ElderCouponDialog" content=":component/elderCoupon/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />

<import
    name="Zigezheng"
    content=":application/special/component/zigezheng/main"
/>
<import name="Score12" content=":application/special/component/score12/main" />
<import
    name="CarElder"
    content=":application/special/component/carElder/main"
/>
<import
    name="CarKemu1and4"
    content=":application/car/component/CarKemu1and4/main"
/>
<import
    name="Score12Short"
    content=":application/score12buy/component/score12Short/main"
/>
<import
    name="CarKemuAll"
    content=":application/car/component/CarKemuAll/main"
/>
<div class="page-container page-special">
    <div class="page-header">
        <com:header
            title="{{state.prevScrollTop > 200?self.nowGoodInfo.name: ' '}}"
            theme="black"
            endTheme="black"
            qaKey="{{self.qaKey}}"
            scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}"
        />
    </div>

    <div class="body-panel" sp-on:scroll="pageScroll">
        <div class="panel-box {{state.tabIndex == 0?'':'hide'}}">
            <sp:if value="URLCommon.isElder">
                <com:CarElder
                    goodsList="{{state.goodsInfoPool}}"
                    labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    kemu="{{state.kemu}}"
                    tiku="{{state.tiku}}"
                    isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
                <sp:elseif value="URLCommon.isZigezheng" />
                <com:Zigezheng
                    goodsList="{{state.goodsInfoPool}}"
                    labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    kemu="{{state.kemu}}"
                    tiku="{{state.tiku}}"
                    isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
                <sp:elseif value="URLCommon.isScore12" />
                <com:Score12
                    goodsList="{{state.goodsInfoPool}}"
                    labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{state.goodsInfoPool[0].groupKey}}"
                    kemu="{{state.kemu}}"
                    tiku="{{state.tiku}}"
                    isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </sp:if>
        </div>
        <!-- 扣满12分短时提分 -->
        <sp:if
            value="(self.getGroupKeyInfo(GroupKey.ChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKou12Short).showPage) || (self.getGroupKeyInfo(GroupKey.HcChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.HcChannelKou12Short).showPage) || (self.getGroupKeyInfo(GroupKey.KcChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.KcChannelKou12Short).showPage) || (self.getGroupKeyInfo(GroupKey.MotoChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.MotoChannelKou12Short).showPage)"
        >
            <div
                class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.ChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.HcChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.KcChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.MotoChannelKou12Short?'':'hide'}}"
            >
                <com:Score12Short
                    goodsInfo="{{self.nowGoodInfo}}"
                    labelPool="{{state.labelPool}}"
                    isHubei="{{state.isHubei}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>

        <!-- 科目14 -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.ElderChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.ElderChannelKe1Ke4Group).showPage"
        >
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.ElderChannelKe1Ke4Group?'':'hide'}}"
            >
                <com:CarKemu1and4
                    currentIndex="{{state.tabIndex}}"
                    goodsList="{{state.goodsInfoPool}}"
                    goodsInfo="{{self.getGroupKeyInfo(GroupKey.ElderChannelKe1Ke4Group)}}"
                    labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>

        <!-- 小车全科 -->
        <sp:if value="self.getGroupKeyInfo(GroupKey.ChannelKemuAll).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKemuAll).showPage">
            <div
                class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.ChannelKemuAll?'':'hide'}}"
            >
                <com:CarKemuAll
                    showActiveBtn="{{true}}"
                    tiku="{{state.tiku}}"
                    goodsInfo="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll)}}"
                    goodsList="{{state.goodsInfoPool}}"
                    labelPool="{{state.labelPool}}"
                    couponPool="{{state.couponPool}}"
                    currentIndex="{{state.tabIndex}}"
                    comparePricePool="{{state.comparePricePool}}"
                    groupKey="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll).groupKey}}"
                    payPrice="{{self.showPrice}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>
    </div>

    <div class="footer-box">
        <div class="footer {{state.goodsInfoPool.length > 1?'':'hide'}}">
            <com:bottomTabs
                tabIndex="{{state.tabIndex}}"
                labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}"
                goodsList="{{state.goodsInfoPool}}"
                tabChange="{{self.tabChangeCall}}"
            />
        </div>
        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
            </div>
        </com:buyButton>
    </div>

    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
    <com:ElderCouponDialog/>
    <!-- <com:moveGoods
        info="{{(self.moveGoodsVideo[self.nowGoodInfo.groupKey] || {})}}" groupKey="{{self.nowGoodInfo.groupKey}}"/> -->
</div>
