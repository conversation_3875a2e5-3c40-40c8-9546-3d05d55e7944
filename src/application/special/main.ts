/*
 * ------------------------------------------------------------------
 * 特殊的场景
 * * 1.资格证 2.扣满12分 3.小车老年版
 * ------------------------------------------------------------------
 */

import { ABTestKey, ABTestType, CarType, KemuType, PayType, persuadeDialogAllow, Platform, setPageName, URLCommon } from ':common/env';
import { formatPrice } from ':common/utils';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import ElderCouponDialog from ':component/elderCoupon/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import { getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupComparePrice, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { typeCode } from ':common/features/bottom';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import { getCache, openVipWebView, saveCache, webClose } from ':common/core';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackExit, trackGoPay, trackPageLoad, trackPageShow } from ':common/stat';
import { onWebBack } from ':common/features/persuade';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { BUYED_URL, CAR_KE1_VIDEO, COUPON_DETAIL_URL, openAuth } from ':common/navigate';
import { pauseAllVideos, scrollTop } from ':common/features/dom';
import { onPageShow } from ':common/features/page_status_switch';
import { zigezhengGroupKeyObj } from ':common/features/zigezheng';
import { isHubei } from ':common/features/locate';
import jump from ':common/features/jump';
import { getTabIndex } from ':common/features/cache';
import isNumber from 'lodash/isNumber';
import { getAbtest } from ':store/chores';

interface State {
    kemu: KemuType,
    tiku: CarType,
    isHubei: boolean,
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    prevScrollTop: number
}

// 标记是否展示过挽留弹窗
let flag = false;
let timer;
const pageNameMap = {
    [GroupKey.ChannelKemuAll]: '全科VIP页',
    [GroupKey.ElderChannelKe1Ke4Group]: '科1科4组合包页'
};

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog,
        ElderCouponDialog: ElderCouponDialog
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    get pageName() {
        return pageNameMap[this.nowGoodInfo.groupKey] || 'VIP速成版页';
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    get qaKey() {
        let qaKey = '';

        if (URLCommon.isElder) {
            qaKey = this.nowGoodInfo.groupKey === GroupKey.ChannelKemuAll ? 'qaKey6' : 'qaKey1';
        }

        if (URLCommon.isScore12) {
            qaKey = 'qaKey8';
        }

        return qaKey;
    }
    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    moveGoodsVideo = {
        [GroupKey.ElderChannelKe1]: {
            videoUrl: CAR_KE1_VIDEO,
            entrance: 'vip-sc',
            stat: {
                fromPageCode: '172',
                fromPathCode: '003155'
            }
        },
        [GroupKey.ElderChannelKe4]: {
            videoUrl: CAR_KE1_VIDEO,
            entrance: 'vip-sc',
            stat: {
                fromPageCode: '172',
                fromPathCode: '003155'
            }
        },
        [GroupKey.ElderChannelKe1Ke4Group]: {
            videoUrl: CAR_KE1_VIDEO,
            entrance: 'vip-sc',
            stat: {
                fromPageCode: '172',
                fromPathCode: '003155'
            }
        },
        [GroupKey.ChannelKemuAll]: {
            videoUrl: CAR_KE1_VIDEO,
            entrance: 'vip-sc',
            stat: {
                fromPageCode: '172',
                fromPathCode: '003155'
            }
        }
    }
    $constructor() {
        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;
        const goodsInfoPool: GoodsInfo[] = [];
        let tabIndex;

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        if (URLCommon.isElder) {
            if (kemu === 1) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ElderChannelKe1
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.ElderChannelKe1Ke4Group
                } as GoodsInfo);
            } else {
                goodsInfoPool.push({
                    groupKey: GroupKey.ElderChannelKe4
                } as GoodsInfo);
            }
            goodsInfoPool.push({
                groupKey: GroupKey.ChannelKemuAll
            } as GoodsInfo);

        } else if (URLCommon.isZigezheng) {
            goodsInfoPool.push({
                groupKey: zigezhengGroupKeyObj[tiku]
            } as GoodsInfo);
        } else if (URLCommon.isScore12) {
            switch (tiku) {
                case CarType.CAR:
                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKou12
                    } as GoodsInfo);

                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKou12Short
                    } as GoodsInfo);
                    break;
                case CarType.TRUCK:
                    goodsInfoPool.push({
                        groupKey: GroupKey.HcChannelKou12
                    } as GoodsInfo);

                    goodsInfoPool.push({
                        groupKey: GroupKey.HcChannelKou12Short
                    } as GoodsInfo);
                    break;
                case CarType.BUS:
                    goodsInfoPool.push({
                        groupKey: GroupKey.KcChannelKou12
                    } as GoodsInfo);

                    goodsInfoPool.push({
                        groupKey: GroupKey.KcChannelKou12Short
                    } as GoodsInfo);
                    break;
                case CarType.MOTO:
                    goodsInfoPool.push({
                        groupKey: GroupKey.MotoChannelKou12
                    } as GoodsInfo);
                    goodsInfoPool.push({
                        groupKey: GroupKey.MotoChannelKou12Short
                    } as GoodsInfo);
                    break;
                default: break;
            }
        }

        this.state = {
            kemu,
            isHubei: true,
            tiku,
            tabIndex: tabIndex || 0,
            goodsInfoPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            prevScrollTop: 0
        };

    }
    async didMount() {

        if (URLCommon.isScore12) {
            const { strategy } = await getAbtest(URLCommon.tiku);

            this.state.tabIndex = strategy[ABTestKey.key7] === ABTestType.B ? 1 : 0;
        }

        // 优先展示底部按钮再加载数据；
        this.setPageInfo();

        await this.getGoodInfo();

        setPageName(this.pageName);
        // 页面进出时长打点
        trackPageLoad();

        onPageShow(() => {
            this.setPageInfo();
        });

        // 判断是否是湖北
        isHubei().then(isHubei => {
            this.setState({
                isHubei
            });
        });

        // app代理方法
        this.appEventProxy();

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey
        });
    }
    appEventProxy() {
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        // 退出当前tab的打点
        this.leavePageCall();

        // 回到滚动的顶部
        scrollTop(document.querySelector('.page-special .body-panel'));

        // 暂停所有视频
        pauseAllVideos();

        this.setState({
            tabIndex
        }, () => {
            setPageName(this.pageName);

            trackPageShow();

            this.setPageInfo();

        });

    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const fragmentName1 = '底部吸底按钮';
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        let bottomType: typeCode = typeCode.type4;

        if (nowGoodInfo.inActivity) {
            bottomType = typeCode.type5;
        }

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1
                });
                break;
            case typeCode.type5:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${nowGoodInfo.inActivity.discountedPrice}元`,
                    price: this.showPrice,
                    originalPrice: '日常价￥' + nowGoodInfo.inActivity.preDiscountPrice,
                    fragmentName1
                });
                break;
            default:
                break;
        }
    }
    async getGoodInfo() {
        const { goodsInfoPool, tabIndex } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    jump.replace(BUYED_URL);
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });
            newGoodsPool[tabIndex].showPage = true;
            this.setState({
                goodsInfoPool: newGoodsPool
            });
            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getLabel();
                await this.getComparePrice();

                this.setPageInfo();
            }, 60);

            // 500ms后再渲染其他tabPage，
            setTimeout(() => {
                newGoodsPool.forEach(item => {
                    item.showPage = true;
                });

                this.setState({
                    goodsInfoPool: newGoodsPool
                });
            }, 500);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;

        GroupComparePrice({ groupKeys: goodsInfoPool.map(item => item.groupKey).join(',') }).then(comparePricePool => {
            this.setState({ comparePricePool });
        });
    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;

            this.setState({
                prevScrollTop
            });
        }, 10);
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
        }).catch(async () => {
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                coupon: this.nowCouponInfo,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    payBtnCall = (e) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');

        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1
            });

        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });
        }
    }
    goAuth = async (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
    // 退出页面的回调
    goBackPage() {
        const { tabIndex, goodsInfoPool, kemu, labelPool } = this.state;
        const nowGoodInfo = goodsInfoPool[tabIndex];

        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: nowGoodInfo,
                groupKey: nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                title: `真的要放弃${nowGoodInfo.name}吗？`,
                txt1: '懒人必备',
                txt2: '省不少时间',
                txt3: '后悔开晚了',
                txt4: '简单好记',
                tag: {
                    text: labelPool[nowGoodInfo.groupKey]?.label
                },
                kemu
            }).then(payType => {
                if (payType === false) {
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            webClose();
        }
    }
    backCall = () => {
        this.goBackPage();
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    // 离开当前页面
    leavePageCall = () => {
        // 退出当前页面的打点
        setPageName(this.pageName);
        trackExit();
    }
}
