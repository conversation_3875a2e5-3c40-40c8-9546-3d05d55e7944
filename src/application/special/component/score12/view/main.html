<import name="style" content="./main" />

<import name="commonQuestion" content=":component/commonQuestion/main" />
<import
    name="pageHeader"
    content=":application/car/component/page-header/main"
/>
<import name="news" content=":component/news/main" />

<div class="page-score12">
    <com:news top="2.67rem" name="扣满12分VIP" groupKey="{{props.groupKey}}"/>

    <com:pageHeader
        uiConfig="{{self.headUiConfig}}"
        iconList="{{state.iconList}}"
        goAuth="{{props.goAuth}}"
    />

    <div class="auth-more" sp-on:click="goAuth" data-uniqkey="">
        <span>查看更多权益</span>
        <span class="auth-more-arrow" />
    </div>

    <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
        <div class="buy-btn">
            确认协议并支付 ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
            '--'}}
        </div>
        <sp:if value="props.labelPool[props.groupKey].label">
            <i class="label">{{props.labelPool[props.groupKey].label}}</i>
        </sp:if>
    </div>

    <div class="score12_sec4"></div>

    <div class="score12_sec5 {{URLCommon.tiku}}"></div>

    <com:commonQuestion type="3" />
</div>
