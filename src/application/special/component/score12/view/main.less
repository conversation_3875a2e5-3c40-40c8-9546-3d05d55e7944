.page-score12 {
    background-color: #221D1D;
    position: relative;

    .auth-more {
        font-size: 13px;
        color: #ffffff;
        line-height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 0.4rem;
        margin: -0.1rem 0;
        position: relative;

        &-arrow {
            margin-left: 6px;
            width: 8px;
            height: 13px;
            background: url('../images/arrow.png');
            background-size: cover;
        }
    }

    .buy-btn-box {
        position: relative;
        width: 343px;
        height: 49px;
        margin: 24px auto 15px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/btn1.png) no-repeat center center/100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .buy-btn {
            color: white;
        }

        .label {
            position: absolute;
            right: 0px;
            top: -7px;
            background: linear-gradient(360deg, #F9C39F 0%, #FEDEC7 100%);
            border-radius: 0 10px 0 8px;
            font-size: 12px;
            font-weight: 500;
            color: #622604;
            transform: scale3d(0.9, 0.9, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2px 9px;
            font-weight: 500;
        }

    }

    .score12_sec2 {
        height: 385px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/score12/1.png) no-repeat 0 10px;
        background-size: 100% 100%;
        position: relative;
        box-sizing: border-box;

        .icon-w {
            display: flex;
            align-items: center;
            justify-content: space-around;
            margin-top: 20px;
            width: 100%;
            position: absolute;
            bottom: 25px;

            div {
                position: relative;
                display: flex;
                align-items: center;
                flex-direction: column;
            }

            img {
                width: 37px;
                height: 36px;
            }

            span {
                color: #ffffff;
                font-size: 13px;
                line-height: 18px;
                padding-top: 6px;
                display: block;
            }

            i {
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
                line-height: 18px;
                padding-top: 2px;
                display: block;
            }
        }
    }

    .score12_sec3 {
        width: 345px;
        height: 89px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/score12/12.png) no-repeat;
        background-size: 100% 100%;
        margin: -22px auto 0 auto;
        position: relative;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        padding-bottom: 42px;
        box-sizing: border-box;

        span {
            color: #ffffff;
            font-size: 18px;
            line-height: 18px;
            font-weight: bold;
            padding-right: 10px;
        }

        i {
            text-decoration: line-through;
            color: rgba(255, 255, 255, 0.7);
            font-size: 15px;
        }
    }

    .score12_sec4 {
        width: 269px;
        height: 55px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/score12/11.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 auto;
    }

    .score12_sec5 {
        width: 345px;
        height: 596px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/score12/car1.png) no-repeat center center/cover;
        margin: 20px auto 0 auto;
        padding-top: 176px;
        box-sizing: border-box;

        &.moto {
            background: url(https://web-resource.mc-cdn.cn/web/vip/score12/moto1.png) no-repeat center center/cover;
        }
    }
}
