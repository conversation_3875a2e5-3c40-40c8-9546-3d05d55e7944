import { CarType, URLCommon } from ':common/env';
import { promiseIconList, promiseList } from ':common/features/promise';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
    iconList: any[];
}
interface Props {
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    get headUiConfig() {
        const config: any = {
            img: URLCommon.tiku === CarType.MOTO ? 'https://web-resource.mc-cdn.cn/web/vip/hd2.png' : 'https://web-resource.mc-cdn.cn/web/vip/hd1.png'
        };

        return config;
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            iconList: [
                {
                    icon: promiseIconList.dtjq,
                    uniqkey: promiseList.dtjq,
                    dec: '答题技巧',
                    dec1: '省时省力'
                },
                {
                    icon: promiseIconList.jj500t,
                    uniqkey: promiseList.jj500t,
                    dec: URLCommon.tiku === CarType.MOTO ? '精简200题' : '精简600题',
                    dec1: '记得住',
                    tipWidth: 37,
                    tip: 'http://exam-room.mc-cdn.cn/exam-room/2022/04/25/16/cb19ff278044447d9c9554925121e946.png'
                },
                {
                    icon: promiseIconList.zskcmn,
                    uniqkey: promiseList.zskcmn,
                    dec: '真实考场模拟',
                    dec1: '高仿真'
                },
                {
                    icon: URLCommon.tiku === CarType.MOTO ? 'http://exam-room.mc-cdn.cn/exam-room/2022/07/01/16/d07386a8ef98426186c60d780c315d82.png' : promiseIconList.kq3tj,
                    uniqkey: promiseList.kq3tj,
                    dec: URLCommon.tiku === CarType.MOTO ? '考前2套卷' : '考前3套卷',
                    dec1: '高效冲刺'
                }
            ]
        };
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
