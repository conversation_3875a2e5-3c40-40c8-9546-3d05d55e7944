import { promiseIconList, promiseList } from ':common/features/promise';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
    iconList: any[];
}

interface Props {
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    get headUiConfig() {
        const config: any = {
            img: 'http://exam-room.mc-cdn.cn/exam-room/2024/07/30/18/a4cd8ef135424ad19a936cd9275277c7.png',
            transparent: true
        };

        return config;
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            iconList: [
                {
                    icon: promiseIconList.jhk,
                    uniqkey: promiseList.jhk,
                    dec: '精华课'
                },
                {
                    icon: promiseIconList.jj500t,
                    uniqkey: promiseList.jj500t,
                    dec: '精简5套卷'
                },
                {
                    icon: promiseIconList.zskcmn,
                    uniqkey: promiseList.zskcmn,
                    dec: '真实考场模拟'
                },
                {
                    icon: promiseIconList.kqmj,
                    uniqkey: promiseList.kqmj,
                    dec: '考前秘卷'
                },
                {
                    icon: promiseIconList.bgbc,
                    uniqkey: promiseList.bgbc,
                    dec: '考不过补偿'
                }
            ]
        };
    }
  
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
