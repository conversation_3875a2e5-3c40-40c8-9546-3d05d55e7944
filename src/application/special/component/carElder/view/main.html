<import name="style" content="./main" />

<import name="commonQuestion" content=":component/commonQuestion/main" />
<import
    name="pageHeader"
    content=":application/car/component/page-header/main"
/>

<div data-panel="panel1" class="elder_panel">
    <com:pageHeader
        uiConfig="{{self.headUiConfig}}"
        iconList="{{state.iconList}}"
        goAuth="{{props.goAuth}}"
    />
    <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
        <div class="buy-btn">
            确认协议并支付 ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
            '--'}}
        </div>
        <i class="label">{{props.labelPool[props.groupKey].label}}</i>
    </div>
    <div class="sec_1"></div>
    <div class="sec_2"></div>
    <div class="sec_3"></div>
    <div class="sec_4" sp-on:click="goAuth" data-uniqkey="bgbc"></div>
    <com:commonQuestion type="1" />
</div>
