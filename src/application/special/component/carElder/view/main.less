.elder_panel {
    background: #420719;
    position: relative;
    .buchang1 {
        width: 60px;
        height: 80px;
        position: absolute;
        top: 310px;
        right: 10px;
    }

     .buy-btn-box {
         position: relative;
         width: 343px;
         height: 49px;
         margin: -14px auto 15px;
         background: url(https://web-resource.mc-cdn.cn/web/vip/btn1.png) no-repeat center center/100% 100%;
         display: flex;
         flex-direction: column;
         justify-content: center;
         align-items: center;

         .buy-btn {
             color: white;
         }

         .label {
             position: absolute;
             right: 0px;
             top: -7px;
             background: linear-gradient(360deg, #F9C39F 0%, #FEDEC7 100%);
             border-radius: 0 10px 0 8px;
             font-size: 12px;
             font-weight: 500;
             color: #622604;
             transform: scale3d(0.9, 0.9, 1);
             display: flex;
             justify-content: center;
             align-items: center;
             padding: 2px 9px;
             font-weight: 500;
         }

     }

    .sec_1 {
        width: 331px;
        height: 64px;
        background: url(../images/2.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 auto;
    }
    .sec_2 {
        width: 375px;
        height: 954px;
        background: url(../images/3.png) no-repeat;
        background-size: 100% 100%;
    }
    .sec_3{
         width: 375px;
         height: 213px;
         background: url(../images/4.png) no-repeat;
         background-size: 100% 100%;
    }
    .sec_4 {
        width: 375px;
        height: 213px;
        background: url(../images/5.png) no-repeat;
        background-size: 100% 100%;
        position: relative;
    }
}
