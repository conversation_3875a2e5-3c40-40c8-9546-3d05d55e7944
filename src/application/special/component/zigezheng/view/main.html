<import name="style" content="./main" />

<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="news" content=":component/news/main" />

<div class="zgz_panel1">
    <div class="sec1">
         <com:news top="2.2rem" name="{{self.buyName + 'VIP'}}" bg1="{{true}}" groupKey="{{props.groupKey}}"/>
    </div>
    <div class="sec2_w">
        <div class="title">- 尊享{{state.iconList.length}}大权益 -</div>
        <ul class="ic_list">
            <sp:each for="state.iconList">
                <li sp-on:click="goAuth"  data-uniqkey="{{$value.uniqkey}}">
                    <span class="icon" style="background-image: url({{$value.icon}});"></span>
                    <span class="desc">{{$value.dec}}</span>
                </li>
            </sp:each>
        </ul>
        <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
            <div class="buy-btn">
                ¥{{props.payPrice || props.payPrice ===
                0?props.payPrice : '--'}} 立即开通
            </div>
            <sp:if value="props.labelPool[props.groupKey].label">
                <i class="label">{{props.labelPool[props.groupKey].label}}</i>
            </sp:if>
        </div>
    </div>
    <div class="sec3"></div>
    <div class="sec4"></div>
    <com:commonQuestion type="6"></com:commonQuestion>
</div>
