import { URLCommon } from ':common/env';
import { promiseIconList, promiseList } from ':common/features/promise';
import { zigezhengTextMap } from ':common/features/zigezheng';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
    iconList: any[];
}

interface Props {
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    get buyName() {
        return zigezhengTextMap[URLCommon.tiku];
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            iconList: [
                {
                    icon: promiseIconList.jjtk,
                    uniqkey: promiseList.jjtk,
                    dec: '精简题库'
                },
                {
                    icon: promiseIconList.zskcmn,
                    uniqkey: promiseList.zskcmn,
                    dec: '真实考场模拟'
                },
                {
                    icon: promiseIconList.kqmj,
                    uniqkey: promiseList.kqmj,
                    dec: '考前秘卷'
                }
            ]
        };
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
