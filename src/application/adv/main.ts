import { CarType, KemuType, setPageName, URLCommon, URLParams } from ':common/env';

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { trackEvent, trackPageLoad } from ':common/stat';
import { typeCode } from ':common/features/bottom';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import PayDialog from ':component/payDialog/main';
import { getPermission, getSwallowConfig } from ':store/chores';
import { openWeb, setStatusBarTheme, webClose } from ':common/core';
import { showSetting } from ':common/features/embeded';
import { dateFormat } from ':common/utils';
import { ADV_URL } from ':common/navigate';
import { makeToast } from ':common/features/dom';
import { onPageShow } from ':common/features/page_status_switch';

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    remoteConfig: object,
    isDialogPage: boolean,
    expireTimeString: string,
    hasPermission: boolean,
    showGoods: boolean,
    showReward: boolean
}

let pageName = '去除广告页';
const isDialogPage = !!URLParams.fromPage;
let fragmentName1;
let fragmentName2;
if (isDialogPage) {
    pageName = URLParams.fromPage || pageName;
    fragmentName1 = URLParams.fragmentName1 || '去除广告';
    fragmentName2 = '支付弹窗';
}
const GGParams = {
    spaceId: 498,
    adCustomerId: 'csj',
    otherParam: 'rewardFreeAd=true'
};
const rewardFragment = '一天免广告';
export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
    };

    get showPrice() {
        const { tabIndex, goodsInfoPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }

    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }

    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            goodsInfoPool: [
                {
                    groupKey: GroupKey.ChannelFilterAdvert
                } as GoodsInfo,
                {
                    groupKey: GroupKey.ChannelFilterAdvert2
                } as GoodsInfo,
                {
                    groupKey: GroupKey.ChannelFilterAdvert3
                } as GoodsInfo

            ],
            showGoods: false,
            remoteConfig: {
                'moreURL': '',
                'q&a': []
            },
            isDialogPage: isDialogPage,
            expireTimeString: '',
            hasPermission: false,
            showReward: false
        };

    }

    async didMount() {
        setPageName(pageName);
        if (isDialogPage) {
            showSetting({
                iosH: 460,
                androidH: 570
            });
            trackEvent({
                fragmentName1: fragmentName1,
                actionType: '点击',
                actionName: '去支付'
            });
        }
        this.getRemoteConfig();
        setStatusBarTheme('dark');

        const pDetail = await this.getPermissionDetail();

        if (!this.setPermissionStatus(pDetail)) {
            trackPageLoad({
                payStatus: 2
            });
            this.getGoodInfo().then(
                (res) => {
                    return res;
                },
                (err) => {
                    throw err;
                });
        } else {
            trackPageLoad({
                payStatus: 1
            });
            this.children.buyButton.hideButton();
        }

    }

    loadRewardVideo() {
        console.log('window.mcAdSdk.loadReward invoked!!!!!!!!!!!!!');
        window.mcAdSdk.loadReward(JSON.stringify(GGParams));

        window.mc_onLoadRewardSuccess = (config) => {
            console.log('window.mc_onLoadRewardSuccess invoked!!!!!!!!!!!!!');
            if (!window.mcAdSdk || !window.mcAdSdk.showReward) {
                makeToast('mcAdSdk fn not found');
            } else {
                this.setState({
                    showReward: true
                }, () => {
                    trackEvent({
                        fragmentName1: isDialogPage ? fragmentName1 : rewardFragment,
                        fragmentName2: isDialogPage ? rewardFragment : '',
                        payStatus: 0,
                        actionType: '出现'
                    });
                });
            }
        };

        window.mc_onRewardArrived = async (config) => {
            console.log('window.mc_onRewardArrived invoked!!!!!!!!!!!!!');
            await new Promise<void>(resolve => {
                onPageShow(resolve);
            });

            this.close();
        };

        window.mc_onRewardAbandon = () => {
            console.log('window.mc_onRewardAbandons invoked!!!!!!!!!!!!!');
            window.mcAdSdk.loadReward(JSON.stringify(GGParams));
            console.log('window.mcAdSdk.loadReward invoked agaiin!!!!!!!!!!!!!');
        };
    }

    showVideo() {
        trackEvent({
            fragmentName1: isDialogPage ? fragmentName1 : rewardFragment,
            fragmentName2: isDialogPage ? rewardFragment : '',
            actionType: '点击',
            actionName: '跳转'
        });
        window.mcAdSdk.showReward(JSON.stringify(GGParams));
    }

    close() {
        webClose();
    }

    async getPermissionDetail() {
        const permissions = ['antiAd', 'antiAdKc', 'antiAdHc', 'antiAdMt'];
        const permissionStore = [];
        const result = [];
        permissions.forEach(async (item) => {
            permissionStore.push(getPermission(item));
        });
        return Promise.all(permissionStore).then(pList => {
            pList.forEach(item => {
                result.push({
                    status: item.status,
                    validEndTime: item.validEndTime
                });
            });
            return result;
        });
    }

    setPermissionStatus(pdetails) {
        const hasPermissionArr = pdetails.filter((item) => {
            return item.status === 1;
        });
        if (hasPermissionArr.length > 0) {
            const sortPermission = hasPermissionArr.sort((next, prev) => {
                return next.validEndTime - prev.validEndTime;
            });
            const exactPermission = sortPermission[0];
            const expireTimeString = dateFormat(exactPermission.validEndTime, 'yyyy.MM.dd');
            this.setState({
                hasPermission: true,
                expireTimeString
            });
            return true;
        }
        return false;
    }

    async getRemoteConfig() {
        const remoteConfig = await getSwallowConfig({ key: 'jk_anti_ad_qa' });
        this.setState({
            remoteConfig
        });
        this.loadRewardVideo();
        console.log(remoteConfig);
    }

    switchGood(e) {
        const index = e.refTarget.getAttribute('data-index');
        this.setState({
            tabIndex: index
        }, () => {
            this.setBuyBottom();
        });
    }
    goMoreUrl(e) {
        const url = e.refTarget.getAttribute('data-url');
        openWeb({
            url,
            title: '去除广告'
        });
    }

    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {

            goodsListInfo.forEach((goodInfo) => {
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            this.setState({
                goodsInfoPool: newGoodsPool,
                showGoods: true
            }, () => {
                this.setBuyBottom();
            });

        });
    }

    setBuyBottom() {
        if (!isDialogPage) {
            fragmentName1 = '底部吸底按钮';
        }

        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const bottomType: typeCode = typeCode.type4;

        // 注册底部支付方法
        this.children.buyButton.setPay({
            isInDialog: isDialogPage,
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey, goUse: true, goUseTxt: '去查看', useUrl: ADV_URL });
            }
        });

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 立即开通',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1,
                    fragmentName2
                });
                break;
            default:
                break;
        }
    }

    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            ...stat
        }, false).then(() => {
            this.children.buyButton.hideButton();
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey, goUse: true, goUseTxt: '去查看', useUrl: ADV_URL });
        }).catch(async () => {
            // console.log('支付错误', err);
            if (!isDialogPage) {
                this.children.payDialog.show({
                    groupKey: goodsInfoPool[tabIndex].groupKey,
                    payPrice: this.showPrice,
                    onPay: () => {
                        this.pay(stat);
                    },
                    ...stat
                });
            }
        });
    }
}
