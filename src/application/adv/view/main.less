body {
    max-width: none;
}

.page-adv {
    background: #fff;
    height: 100%;
    display: flex;
    flex-direction: column;

    .page-header {
        position: relative;
        background: #fff;
        padding: 0 15px;


        .dhd {
            height: 30px;
            display: flex;
            justify-content: flex-end;
            padding-bottom: 5px;
            box-sizing: content-box;

            span {
                height: 30px;
                width: 30px;
                margin-right: -15px;
                background: url(../images/2.png) no-repeat left bottom;
                background-size: 20px 20px;
            }
        }
    }

    .top-header {
        .header {
            background-color: #fff !important;
        }
    }

    .help {
        background: none !important;
    }

    .page-content {
        flex: 1;
        overflow-y: scroll;
        -webkit-overflow-scrolling: touch;
        padding: 0 15px 20px;
    }

    .intro {
        background-color: #FFF9EE;
        font-size: 13px;
        text-align: left;
        color: #6e6e6e;
        line-height: 23px;
        padding: 15px;
        border-radius: 8px;

        .p1 {
            font-size: 17px;
            font-weight: bold;
            text-align: left;
            color: #333333;
            line-height: 24px;
            padding-left: 25px;
            background: url(../images/1.png) no-repeat left center;
            background-size: 20px 20px;
        }

        .p2 {
            font-size: 13px;
            text-align: left;
            color: #6e6e6e;
            line-height: 23px;
            margin-top: 8px;
        }

        .p4{
            color: #f59a23;
            font-size: 13px;
            font-weight: bold;
            padding-top: 3px;
        }
    }

    .goods {
        margin-top: 20px;

        .title {
            font-size: 17px;
            text-align: left;
            color: #333333;
            line-height: 24px;
        }

        .good-wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 24px;
        }

        .good {
            width: 104px;
            height: 118px;
            background: #ffffff;
            border: 1px solid #ffebc7;
            border-radius: 6px;
            padding-top: 14px;
            position: relative;
            display: flex;
            flex-direction: column;
            .tip {
                position: absolute;
                left: 0;
                top: -10px;
                background-color: #FE5A12;
                border-radius: 33px 33px 33px 2px;
                font-size: 12px;
                line-height: 17px;
                color: #FFFFFF;
                padding: 4px 8px 3px 6px;
                transform: scale(0.9);
                transform-origin: left;
            }

            p {
                text-align: center;

                &.p1 {
                    font-size: 11px;
                    text-align: center;
                    color: #6e6e6e;
                    line-height: 16px;
                }

                &.p2 {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 14px 0 19px 0;

                    span {
                        font-size: 12px;
                        font-weight: bold;
                        text-align: center;
                        color: #333333;
                        line-height: 17px;
                        padding-right: 4px;
                        padding-top: 10px;
                    }

                    b {
                        font-size: 30px;
                    }
                }

                &.p3 {
                    height: 25px;
                    font-size: 12px;
                    text-align: center;
                    color: #a05700;
                    line-height: 16px;
                    background: #fef1d7;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 0 0 5px 5px;
                }
            }

            &.active {
                background: linear-gradient(183deg, #fee8ae 2%, #fef5dc 98%);

                .p1 {
                    color: #A05700;
                }

                .p2 {
                    span{
                        color: #804E13;
                    }
                    color: #804E13;
                }

                .p3 {
                    color: #692204;
                    font-weight: bold;
                    background-color: #FDD37B;
                }
            }
        }
    }

    .ad-video {
        margin-top: 10px;
        height: 46px;
        background: linear-gradient(270deg, #ff4f58, #ff6f7b);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 0 10px;
        position: relative;
        label {
            width: 20px;
            height: 20px;
            background: url(../images/4.png) no-repeat;
            background-size: 100% 100%;
        }

        span {
            flex: 1;
            font-size: 14px;
            font-weight: bold;
            color: #ffffff;
            line-height: 20px;
            padding-left: 10px;
        }

        i {
            width: 14px;
            height: 14px;
            background: url(../images/3.png) no-repeat;
            background-size: 100% 100%;
        }

        b {
            width: 66px;
            height: 40px;
            background: url(../images/5.png) no-repeat;
            background-size: 100% 100%;
            position: absolute;
            bottom: 0;
            right: 6px;
        }
    }

    .qa {
        padding-top: 18px;

        .hd {
            display: flex;
            align-items: center;
            justify-content: space-between;

            span {
                font-size: 14px;
                font-weight: bold;
                text-align: left;
                color: #333333;
                line-height: 20px;
            }

            label {
                font-size: 12px;
                text-align: left;
                color: #a0a0a0;
                line-height: 23px;
            }

        }

        .txts {
            font-size: 13px;
            text-align: left;
            color: #6e6e6e;
            line-height: 24px;
            margin-top: 5px;
        }
    }

    .footer {
        padding-bottom: calc(~"10px + constant(safe-area-inset-bottom)/2"
            ) !important;
        /* 兼容 iOS < 11.2 */
        padding-bottom: calc(~"10px + env(safe-area-inset-bottom)/2");
        background: #fff;
    }
}
