<import name="style" content="./main" />
<import name="header" content=":component/header/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payDialog" content=":component/payDialog/main" />
<div class="page-container page-adv">
    <div class="page-header">
        <sp:if value="state.isDialogPage">
            <div class="dhd"><span sp-on:click="close"></span></div>
            <sp:else />
            <com:header title="去除广告" theme="white" endTheme="white" qaKey="{{self.qaKey}}"
                scrollTop="{{state.prevScrollTop}}">
                <div sp:slot="help"></div>
            </com:header>
        </sp:if>
    </div>
    <div class="page-content">
        <sp:if value="state.hasPermission">
            <div class="intro">
                <p class="p1">去除广告</p>
                <p class="p2">感谢您开通「去除广告」权益。祝您考试顺利，早日拿本！</p>
                <p class="p3">有效期截止：{{state.expireTimeString}}</p>
            </div>
            <sp:else />
            <div class="intro">
                <p class="p1">去除广告</p>
                <p class="p2">
                    APP中会出现少量广告推荐。广告收入可以帮助我们开发更优秀的功能并提供更好的服务。为了感谢您的支持，我们亦为您提供了「去除广告」权益。您只需支付很少的费用，即可在相应时长里不再观看广告。
                </p>
                <p class="p4">请注意：去除范围不含App启屏广告</p>
            </div>
        </sp:if>
        <sp:if value="state.showGoods">
            <div class="goods">
                <h3 class="title">请选择需要去除广告的时长</h3>

                <div class="good-wrap">
                    <sp:each for="{{state.goodsInfoPool}}">
                        <div sp-on:click="switchGood" class="good {{$index == state.tabIndex ? 'active': ''}}"
                            data-index="{{$index}}">
                            <sp:if value="$index == 2">
                                <div class="tip">限时特惠</div>
                            </sp:if>
                            <p class="p1">{{$value.validDays}}天</p>
                            <p class="p2"><span>¥</span><b>{{$value.payPrice}}</b></p>
                            <p class="p3">{{$value.tips.label}}</p>
                        </div>
                    </sp:each>
                </div>
            </div>
        </sp:if>
        <sp:if value="state.showReward">
            <div sp-on:click="showVideo" class="ad-video">
                <label></label>
                <span>{{state.remoteConfig['buttonText']}}</span>
                <b></b>
                <i></i>
            </div>
        </sp:if>
        <div class="qa" sp-on:click="goMoreUrl" data-url="{{state.remoteConfig.moreURL}}">
            <div class="hd"><span>常见问题：</span> <label>查看 ></label></div>
            <div class="txts">
                <sp:each for="{{state.remoteConfig['q&a']}}">
                    <p>{{$value.no}}. {{$value.text}}</p>
                </sp:each>
            </div>
        </div>
    </div>

    <sp:if value="!state.hasPermission">
        <div class="footer">
            <com:buyButton> </com:buyButton>
        </div>
    </sp:if>
    <com:payDialog />
</div>
