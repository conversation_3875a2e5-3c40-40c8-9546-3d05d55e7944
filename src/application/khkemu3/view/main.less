.khkemu3-page {
    position: relative;
    padding: 15px 15px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    max-width: 375px;
    margin: 0 auto;

    .top-title{
        font-size: 16px;
        color: #333;
        font-weight: bold;
        line-height: 22px;
    }

    .close {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 17px;
        height: 17px;
        background: url("../images/close.png");
        background-size: cover;
    }

    .tabs {
        margin-top: 18px;
        display: flex;
        justify-content: space-between;

        .tab {
            width: 167px;
            border-radius: 8px;
            text-align: center;
            position: relative;

            .top {
                line-height: 51px;
                font-size: 18px;
                font-family: PingFangSC-Semibold, PingFang SC, sans-serif;
                font-weight: 600;
                white-space: nowrap;
                border-radius: 8px 8px 0 0;

                &.smaller {
                    font-size: 15px;
                }
            }

            .bottom {
                line-height: 25px;
                font-size: 12px;
                font-family: PingFangSC-Medium, PingFang SC, sans-serif;
                font-weight: 500;
                border-radius: 0 0 8px 8px;
            }


            &.tabNormal {
                box-shadow: 0 0 0 1px #f4c2a2;

                .top {
                    color: #171717;
                    background: #ffffff;
                }

                .bottom {
                    color: #aa4120;
                    background: #fef0e7;
                }
            }

            &.tabActive {
                .top {
                    color: #ffffff;
                    background: linear-gradient(113deg, #353b4e 0%, #1d222b 100%);
                }

                .bottom {
                    color: #aa4120;
                    background: linear-gradient(118deg, #f9dbc0 0%, #efaf8b 100%);
                }
            }
        }
    }

    .alone-box {
        .sec1 {
            margin-top: 23px;
            height: 75px;
            background: url(../images/20.png) no-repeat;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            padding: 4px 15px 0 15px;

            .desc1 {
                font-size: 18px;
                font-weight: bold;
                color: #681309;
                line-height: 18px;
            }

            .desc2 {
                font-size: 14px;
                font-weight: 400;
                color: #9f5217;
                line-height: 20px;
                padding-top: 10px;
            }

            .price {
                flex: 1;
                text-align: right;
                padding-right: 10px;

                i {
                    font-size: 18px;
                    font-weight: 400;
                    color: #681309;
                    line-height: 25px;
                    padding-right: 4px;
                }

                b {
                    font-size: 30px;
                    font-weight: bold;
                    color: #681309;
                    line-height: 36px;
                }
            }
        }
    }

    .s-title {
        font-size: 15px;
        text-align: center;
        margin: 15px 0;
        color: #692204;
        font-weight: bold;
    }

    .tab-content {
        position: relative;
        height: 100px;
        border-radius: 6px;
        box-sizing: border-box;
    }

    .tab-content1 {
        .add-buy {
            display: flex;
            justify-content: space-around;

            .step {
                width: 78px;
                height: 88px;
                background: url(../images/13.png) no-repeat center center/cover;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;

                &.moto-step{
                    .c{
                      width:100%;
                      padding: 0px 6px; 
                      margin-bottom: 18px; 
                      line-height: 16px;
                    }
                   .b{
                    line-height: 17px;
                    height: auto;
                    bottom: 3px;
                    text-align: center;
                   } 
                }
                .icon-multi {
                    width: 51px;
                    height: 19px;
                    background: url(http://exam-room.mc-cdn.cn/exam-room/2022/07/12/11/baa34cadd2524f2088f3bad93e562116.png) no-repeat center center/cover;
                    position: absolute;
                    right: -20px;
                    top: -10px;
                }

                .c {
                    width: 60px;
                    font-size: 13px;
                    color: #8c3418;
                    text-align: center;
                    font-weight: bold;
                    margin-bottom: 12px;
                    line-height: 18px;
                }

                .b {
                    font-size: 12px;
                    position: absolute;
                    bottom: 0;
                    height: 22px;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #a03c1c;
                }

                &::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 32px;
                    height: 14px;
                }

                // &:nth-of-type(1)::before {
                //     background: url(../images/<EMAIL>) no-repeat center center/cover;
                // }

                // &:nth-of-type(2)::before {
                //     background: url(../images/<EMAIL>) no-repeat center center/cover;
                // }

                // &:nth-of-type(3)::before {
                //     background: url(../images/<EMAIL>) no-repeat center center/cover;
                // }

                // &:nth-of-type(4) {
                //     width: 78px;
                //     height: 88px;
                //     background: url(../images/14.png) no-repeat center center/cover;
                //     display: flex;
                //     align-items: center;
                //     justify-content: center;

                //     .c {
                //         width: 70px;
                //         font-size: 14px;
                //         color: #aa4120;
                //         text-align: center;
                //         font-weight: bold;
                //         margin-bottom: 0;
                //     }
                // }
            }
        }
    }

    .tab-content2 {
        padding: 7px;
        padding-left: 64px;
        position: relative;
        background: url(../images/19.png) no-repeat center center/cover;

        .diff-box {
            position: absolute;
            left: 0;
            height: 100%;
            width: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 7px;
            box-sizing: border-box;

            .diff {
                line-height: 17px;
                color: #aa4120;
                font-size: 12px;
                text-align: center;

                .unit {
                    color: #f73b31;
                }

                .price {
                    font-size: 16px;
                    color: #f73b31;
                }
            }
        }

        .compare-box {
            display: flex;
            justify-content: space-between;

            .item {
                width: 65px;
                height: 86px;
                background: #fffbf6;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                border-radius: 5px;
                border: 1px solid #fddec8;
                color: #a03c1c;
                position: relative;
                flex: 1;

                .name {
                    font-weight: bold;
                    font-size: 13px;
                    text-align: center;
                }

                .price-box {
                    margin-top: 8px;
                    font-weight: bold;

                    .unit {
                        font-size: 12px;
                    }

                    .price {
                        font-size: 16px;
                    }
                }

                &:not(:last-child)::after {
                    content: "";
                    width: 17px;
                    height: 17px;
                    background: url(../images/<EMAIL>) no-repeat center center/cover;
                    position: absolute;
                    z-index: 1;
                    top: 50%;
                    right: 0;
                    transform: translate(10px, -50%);
                }
            }
        }
    }

    .pay-type {
        margin-bottom: -10px;

    }

    .buy-btn {
        .buy-footer {
            padding: 10px 0 0;
        }
    }

}

body {
    max-width: none;
    background: linear-gradient(180deg, #ffeddc 1%, #fffaf0 39%, #ffffff 78%, #ffffff);
    border-radius: 6px 6px 0px 0px;
}