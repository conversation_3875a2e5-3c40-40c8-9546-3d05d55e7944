/*
 * ------------------------------------------------------------------
 * 新人专享促销页
 * ------------------------------------------------------------------
 */

import { getPromitionExtra, getPromotionSessions, PromotionExtra, PromotionGoodsInfo } from ':store/promotion';
import { Application } from '@simplex/simple-core';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import AnimDialog from './component/animDialog/main';
import Ticker from './component/ticker/main';
import View from './view/main.html';
import { iosBuySuccess } from ':common/features/ios_pay';
import { getDefaultPayType, PayBoundType, startSiriusPay } from ':common/features/pay';
import { PayType, URLParams, URLCommon, setPageName, Platform, Version } from ':common/env';
import { trackDialogShow } from ':common/stat';
import { getCache, openVipWebView, webClose } from ':common/core';
import { dateDiffFormat, numZh, objToParams, ticker, timeout } from ':common/utils';
import { scrollIntoView } from ':common/features/dom';
import { BUYED_URL } from ':common/navigate';
import { zigezhengTextObj } from ':common/features/zigezheng';
import { comparePrice, ComparePriceInfo } from ':store/goods';
import pagFile from './images/many_red_packets.pag';
import { loadAnimation } from ':common/features/pag';
const Swiper = import('./swiper').then(module => module.default);

const fragmentName1 = '新人专享弹窗';
const animationKey = `newbee-${URLCommon.tiku}-${URLCommon.kemu}-${URLParams.sceneCode || 101}-${URLParams.patternCode || 101}`;

interface State {
    tabIndex: number;
    goodsList: PromotionGoodsInfo[];
    extraList: PromotionExtra[];
    timerTips: string[];
    ended: boolean;
}

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        animDialog: AnimDialog;
        ticker: Ticker;
    };

    private swiper: Promise<any>;

    private comparePrice: Promise<ComparePriceInfo>[] = [];

    private startTimer: () => void;

    get currentGoods() {
        const { goodsList, tabIndex } = this.state;
        return goodsList[tabIndex];
    }

    get kemuTitle() {
        if (this.currentGoods?.sceneCode === '102') {
            return '满分学习';
        }

        if (URLCommon.isZigezheng) {
            return zigezhengTextObj[URLParams.carStyle];
        }

        return '科目' + numZh(this.currentGoods?.kemu || URLCommon.kemu);
    }

    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: -1,
            goodsList: [],
            extraList: [],
            timerTips: [],
            ended: false
        };
    }

    async didMount() {
        setPageName(URLParams.pageName || '科目首页');
        trackDialogShow({ fragmentName1 });
        this.fetchGoodsInfo();
    }

    onBackClick() {
        webClose();
    }

    onTabClick(e) {
        const idx = +e.refTarget.getAttribute('data-idx');
        this.switchTab(idx);
    }

    async setTimer() {
        const endReturn = () => {
            this.setState({ ended: true });
            throw new Error('活动已结束');
        };

        let data = await getCache('jkFirstSettingTime');
        console.log('jkFirstSettingTime', data);
        if (!data) {
            endReturn();
        }
        // 用户首次安装时间
        const { time: installTime = new Date().getTime() } = JSON.parse(data);

        data = await getCache('jkNewVipConfig');
        console.log('jkNewVipConfig', data);
        if (!data) {
            endReturn();
        }
        // 活动有效期，一般为24小时
        const { expireTime = 0 } = JSON.parse(data);

        const endTime = +installTime + (expireTime * 1000);

        if (endTime - new Date().getTime() <= 0) {
            endReturn();
        }

        return function startTimer() {
            let diffBefore: string;
            const cancel = ticker(() => {
                const due = endTime - new Date().getTime();
                if (due <= 0) {
                    cancel();
                    this.setState({ ended: true });
                    this.children.buyButton.hideButton();
                    return;
                }

                const diff = dateDiffFormat(due, 'HHmmss');

                if (diff === diffBefore) {
                    return;
                }
                diffBefore = diff;

                this.children.buyButton.setTagText(dateDiffFormat(due, 'HH:mm:ss') + '后活动结束');
                this.children.ticker.setTips(diff.split(''));
            });
        };
    }

    setSwiper() {
        this.swiper = Swiper.then(Swiper => {
            const swiper = new Swiper('.swiper-container', {
                slidesPerView: 1,
                spaceBetween: 20
            });
            swiper.on('slideChange', (swiper) => {
                this.switchTab(swiper.activeIndex);
            });
            return swiper;
        });
    }

    async fetchGoodsInfo() {
        const goodsList = await getPromotionSessions({ promotionType: 2 });

        if (!goodsList.length) {
            this.setState({ ended: true });
            return;
        }

        this.startTimer = await this.setTimer();

        this.setState({ goodsList });
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            getIOSGoods: () => {
                // const { groupKey, sessionIdList, activityType, appleId } = this.currentGoods.iOSGoodsInfo;
                // return {
                //     groupKey, sessionIds: sessionIdList, activityType, appleId
                // };
                return this.currentGoods;
            },
            iosPaySuccess: () => iosBuySuccess({ groupKey: this.currentGoods.groupKey })
        });

        const extraList = await Promise.all(goodsList.map(goods =>
            getPromitionExtra({ groupKey: goods.groupKey, promotionType: 2 })
        ));
        this.setState({ extraList }, () => {
            this.setSwiper();
        });

        goodsList.forEach((goods, i) => {
            if (goods.bought && goods.upgrade) {
                this.comparePrice[i] = comparePrice({
                    groupKey: goods.groupKey,
                    upgradeStrategyCode: goods.upgradeStrategyCode
                });
            }
        });

        this.switchTab(this.getInitialTabIndex(goodsList), true);
    }

    getInitialTabIndex(goodsList: PromotionGoodsInfo[]) {
        const kemu = URLCommon.kemu;
        const sceneCode = URLParams.sceneCode;
        const channelCode = URLParams.channelCode;

        const index = goodsList.findIndex(goods => {
            if (channelCode) {
                return goods.groupKey === channelCode;
            }
            if (URLCommon.isScore12) {
                return goods.sceneCode === sceneCode;
            }
            return goods.kemu === kemu;
        });
        if (index === -1) {
            return 0;
        }
        return index;
    }

    async switchTab(tabIndex: number, initial?: boolean) {
        if (tabIndex === this.state.tabIndex) {
            return;
        }

        this.setState({
            tabIndex
        }, () => {
            this.swiper.then(swiper => swiper.slideTo(tabIndex));
            scrollIntoView(document.querySelector('#tab' + tabIndex), { rightThreshold: 10, center: initial });
        });

        if (this.currentGoods.bought) {
            if (this.currentGoods.upgrade) {
                const comparePrice = await this.comparePrice[tabIndex];
                this.children.buyButton.setButtonConfig({
                    ...comparePrice.savePrice === '0' ? {
                        type: 1
                    } : {
                        type: 6,
                        originalPrice: '日常价￥' + comparePrice.allPrice,
                        discount: '比分开买节省' + comparePrice.savePrice + '元',
                        subtitle: '有效期' + this.currentGoods.validDays + '天'
                    },
                    groupKey: this.currentGoods.groupKey,
                    title: '确认协议并支付',
                    validDays: this.currentGoods.validDays,
                    price: this.currentGoods.payPrice,
                    fragmentName1,
                    goodsInfo: this.currentGoods.iOSGoodsInfo
                });
            } else {
                this.children.buyButton.hideButton();
            }
        } else {
            const savedPrice = String(((+this.currentGoods.originalPrice * 100) - (+this.currentGoods.payPrice * 100)) / 100);
            const enableAnimation = false;
            const needAnimate = initial
                && enableAnimation
                && typeof window.WebAssembly !== 'undefined'
                && (!Platform.isIOS || Version.bizVersion >= 13)
                && !localStorage.getItem(animationKey);

            const buttonConfig = {
                ...savedPrice === '0' ? {
                    type: 1
                } : {
                    type: 6,
                    originalPrice: '日常价￥' + this.currentGoods.originalPrice,
                    discount: `已省${savedPrice}元`,
                    subtitle: '有效期' + this.currentGoods.validDays + '天'
                },
                groupKey: this.currentGoods.groupKey,
                title: '立即开通',
                validDays: this.currentGoods.validDays,
                price: this.currentGoods.payPrice,
                fragmentName1,
                goodsInfo: this.currentGoods.iOSGoodsInfo,
                animate: needAnimate && {
                    price: +this.currentGoods.originalPrice,
                    toPrice: +this.currentGoods.payPrice
                }
            };

            if (!needAnimate) {
                this.children.buyButton.setButtonConfig(buttonConfig);
            }

            if (needAnimate) {
                await this.children.animDialog.show({ savedPrice });
                await this.children.buyButton.setButtonConfig(buttonConfig);

                // TODO: 这段可以放到BuyButton里去
                if (!Platform.isIOS) {
                    const animation = await loadAnimation(pagFile);
                    const manyRedPackets = this.getDOMNode().manyRedPackets as HTMLCanvasElement;
                    manyRedPackets.style.visibility = 'visible';
                    animation.start(manyRedPackets, () => {
                        manyRedPackets.style.visibility = 'hidden';
                    });
                    await timeout(500);
                }

                this.children.buyButton.startAnimation();
                localStorage.setItem(animationKey, 'animated');
            }
        }
        this.startTimer();
    }

    pay = async (stat: PayStatProps) => {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.currentGoods.groupKey,
            sessionIds: this.currentGoods.sessionIds,
            activityType: this.currentGoods.activityType,
            ...stat
        }).catch(async (err) => {
            console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: this.currentGoods.groupKey,
                payPrice: this.currentGoods.payPrice,
                onPay: () => {
                    this.pay(stat);
                },
                fragmentName1
            });
        });
    }

    async goBought() {
        openVipWebView({ url: BUYED_URL + '?' + objToParams({ ...URLParams, kemuStyle: this.currentGoods.kemu }) });
    }
}
