<import name="style" content="./main" module="S" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="animDialog" content="../component/animDialog/main" />
<import name="ticker" content="../component/ticker/main" />

<div class=":page">
    <div class="hotArea :back" sp-on:click="onBackClick"></div>
    <div class=":main">
        <div class=":title">感谢来到{{self.kemuTitle}}</div>
        <div class=":top" />
        <sp:if value="{{!state.ended}}">
            <com:ticker />
            <div class=":carousel {{state.ended && 'hide'}}">
                <div class=":tabsWrapper">
                    <div class=":tabs">
                        <div class=":tabsHole2" />
                        <sp:each for="state.goodsList">
                            <div id="{{'tab'+$index}}" class=":tab {{state.tabIndex===$index && S.tabActive}}"
                                sp-on:click="onTabClick" data-idx="{{$index}}">
                                <div class=":name">{{$value.name}}</div>
                                <sp:if value="{{$value.label}}">
                                    <sp:if value="{{Tools.zhCount($value.label) <= 4}}">
                                        <div class=":tips :tips-normal">{{$value.label}}</div>
                                        <sp:else />
                                        <div class=":tips :tips-marquee">
                                            <div>{{$value.label}}</div>
                                            <span style="animation-duration: {{Tools.zhCount($value.label) / 3 * 2}}s">{{$value.label}}&nbsp;{{$value.label}}&nbsp;</span>
                                        </div>
                                    </sp:if>
                                </sp:if>
                            </div>
                        </sp:each>
                        <div class=":tabsHole" />
                    </div>
                </div>
                <div class="swiper-container" skip="{{state.extraList.length ? 'true' : 'false'}}">
                    <div class="swiper-wrapper">
                        <sp:each for="state.goodsList">
                            <div class="swiper-slide">
                                <img class=":img" src="{{state.extraList[$index].img}}" />
                            </div>
                        </sp:each>
                    </div>
                </div>
            </div>

            <sp:else>
                <div class=":carousel :endWrapper">
                    <div class=":end" />
                </div>
            </sp:else>
        </sp:if>
    </div>
    <sp:if value="{{self.currentGoods.bought && !self.currentGoods.upgrade}}">
        <div class=":use">
            <div class=":btn" sp-on:click="goBought">去使用</div>
        </div>
    </sp:if>
    <div class=":many-red-packets">
        <canvas ref="manyRedPackets" />
    </div>
    <com:buyButton />
    <com:payDialog />
    <com:animDialog />
</div>
<div class=":page-bg" />
