.page {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.page-bg {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -1;
    background: linear-gradient(to bottom, #F1E4D3, white);
}

.back {
    position: absolute;
    left: 19px;
    top: 55px;
    width: 16px;
    height: 14px;
    background: url("../images/back.png");
    background-size: cover;
}

.main {
    height: 0;
    flex: 1;
    background: white url("../images/bg.png") no-repeat;
    background-size: 100% auto;
    display: flex;
    flex-direction: column;
}

.title {
    margin-top: 48px;
    margin-left: 47px;
    font-size: 20px;
    font-family: PingFangSC-Medium, PingFang SC, sans-serif;
    font-weight: bold;
    color: #8d460a;
    line-height: 28px;
}

.top {
    margin-left: 40px;
    margin-top: 4px;
    width: 221px;
    height: 90px;
    background: url("../images/top.png");
    background-size: cover;
}

.carousel {
    height: 0;
    flex: 1;
    background: #ffffff;
    border-radius: 22px 0px 0px 0px;
    margin-left: 11px;
    margin-top: 23px;
    display: flex;
    flex-direction: column;
}

.tabs {
    position: relative;
    display: flex;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    white-space: nowrap;

    &::-webkit-scrollbar {
        display: none;
    }
}

.tabsHole2 {
    width: 30px;
    flex-shrink: 0;
}

.tabsHole {
    flex: 1 0 18px;
}

.tab {
    position: relative;
    padding: 20px 0;
    line-height: 24px;
    font-size: 17px;
    font-family: PingFangSC-Medium, PingFang SC, sans-serif;
    font-weight: bold;
    color: #a0a0a0;
    flex-shrink: 0;

    &+& {
        margin-left: 25px;
    }

    &--active {

        .name {
            color: #272b30;
            transform: scale(1.08);
        }

        &:after {
            content: "";
            position: absolute;
            left: 50%;
            bottom: 0;
            transform: translate3d(-50%, -9px, 0);
            width: 30px;
            height: 5px;
            background: #d29b80;
            border-radius: 3px;
        }
    }

    .tips {
        position: absolute;
        right: 3px;
        top: 2px;
        width: 60px;
        height: 20px;
        background: linear-gradient(90deg, #ffd97b 0%, #ffc400 100%);
        border-radius: 33px 33px 33px 2px;
        font-size: 11px;
        font-family: PingFangSC-Medium, PingFang SC, sans-serif;
        font-weight: 500;
        color: #6F2117;
        line-height: 20px;
    }

    .tips-normal {
        text-align: center;
    }

    @keyframes marquee {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(-50%);
        }
    }

    .tips-marquee {
        overflow: hidden;
        white-space: nowrap;
        width: auto;
        max-width: 100%;
        padding-left: 4px;
        padding-right: 4px;
    
        div {
            color: transparent;
        }
    
        span {
            position: absolute;
            left: 0;
            top: 0;
            display: inline-block;
            animation: marquee 2s linear infinite;
        }
    }
}

:global(.swiper-container) {
    position: relative;
    z-index: 0;
    height: 0;
    flex: 1;
    width: 330px;
    margin-left: 12px;
    overflow: hidden;
    border-radius: 15px;
}

:global(.swiper-wrapper) {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}

:global(.swiper-slide) {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 15px;
}

.img {
    display: block;
    width: 100%;
    border-radius: 15px;
}

.end-wrapper {
    margin-top: 55px;
    justify-content: center;
}

.end {
    width: 214px;
    height: 235px;
    margin-left: 69px;
    background: url("../images/end.png");
    background-size: cover;
}

.use {
    background: white;
    padding: 10px 0 20px;
}

.btn {
    margin: auto;
    width: 356px;
    line-height: 44px;
    background: linear-gradient(103deg, #f9dbc0 0%, #f5cbad 36%, #efaf8b 100%);
    border-radius: 24px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC, sans-serif;
    font-weight: 500;
    color: #5e2e06;
    text-align: center;
}

.many-red-packets {
    position: relative;

    canvas {
        position: absolute;
        z-index: 1;
        left: 100px;
        bottom: -30px;
        width: 60px;
        height: 60px;
        visibility: hidden;
    }
}