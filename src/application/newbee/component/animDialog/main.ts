/*
 * ------------------------------------------------------------------
 * VIP过期弹窗
 * ------------------------------------------------------------------
 */

import View from './view/main.html';
import { Dialog } from ':component/dialog/main';
import { loadAnimation } from ':common/features/pag';
import pagFile from './assets/red_packet.pag';

interface ShowInfo {
    savedPrice: string;
}

export default class AnimDialog extends Dialog<ShowInfo> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }
    public show(showInfo: ShowInfo) {
        return super.show(showInfo, async () => {
            const animation = await loadAnimation(pagFile);
            const doc = animation.file.getTextData(3);
            doc.text = showInfo.savedPrice.padStart(2, ' ');
            animation.file.replaceText(3, doc);
            const canvas = this.getDOMNode().anim as HTMLCanvasElement;
            animation.start(canvas, () => {
                const doms = this.getDOMNode().close as HTMLElement[];
                doms.forEach(dom => {
                    dom.style.visibility = 'visible';
                });
            });
        });
    }
    didMount() {
        this.event.on('close', 'click', () => {
            this.hide();
        });
    }
}
