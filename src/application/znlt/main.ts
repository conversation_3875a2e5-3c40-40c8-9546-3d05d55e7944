/*
 * ------------------------------------------------------------------
 * 智能练题购买弹窗
 * ------------------------------------------------------------------
 */

import { ABTestKey, ABTestType, setPageName, URLParams } from ':common/env';
import { getAbtest } from ':store/chores';

import { Application } from '@simplex/simple-core';
import View from './view/main.html';

export default class extends Application {
    $constructor() {
        const fromPage = URLParams.fromPage || '智能练题弹窗页';
        const fragmentName1 = URLParams.fragmentName1 || '未知片段';

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        setPageName(fromPage);

        this.state = {
            fragmentName1,
            title1: '智能练题',
            subTitle1: '针对性练习，查漏补缺'
        };

    }
    async didMount() {
        const { strategy } = await getAbtest();
        strategy[ABTestKey.key29] = strategy[ABTestKey.key29] || ABTestType.A;
        this.setState({
            strategy
        });
    }
}
