<import name="style" content="./main" />

<import name="BuyDialog" content=":component/buyDialog/main" />
<import name="BuyDialogB" content=":component/buyDialogB/main" />

<div class="page-container page-dtjq">
    <sp:if
        value="URLCommon.tiku === CarType.CAR && URLCommon.isNormal && (URLCommon.kemu ===
        KemuType.Ke1 || state.strategy[ABTestKey.key29] === ABTestType.B)">
        <com:BuyDialogB fragmentName1="{{state.fragmentName1}}" title1="{{state.title1}}"
            subTitle1="{{state.subTitle1}}" />
        <sp:elseif value="state.strategy" />
        <com:BuyDialog fragmentName1="{{state.fragmentName1}}" title1="{{state.title1}}"
            subTitle1="{{state.subTitle1}}" />
    </sp:if>
</div>
