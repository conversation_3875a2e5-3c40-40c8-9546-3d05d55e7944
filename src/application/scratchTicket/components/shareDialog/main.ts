/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */
import { Dialog } from ':component/dialog/main';
import View from './view/main.html';
import { getSwallowConfig } from ':store/chores';
import { shareDouyin, shareRedbook } from ':common/features/share';
import { URLParams, getPageName } from ':common/env';
import { queryUniversalTemplateData, useMaterial } from ':store/activity';
import { makeToast } from ':common/features/dom';

let txtArr1 = [];
let txtArr2 = [];
let topic = '#学车 #科目一 #科目四 #驾考宝典原题 #学车一把过';
// 素材耗尽标志
let isRunout = false;
let imageArr = [];

const materialObj = {
    activityId: 0,
    templateId: 0,
    textTagRelId: '',
    videoTagRelIds: '',
    imgTagRelIds: '',
    shareChannel: 0,
    sourceKey: null
};
const fragmentName1 = 'vip兑换活动';

export default class extends Dialog<{ activityId: number }, void, any, any> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
        };
    }
    public show(showInfo: { activityId: number, sourceKey: number }) {
        setTimeout(() => {
            this.getImageText();
        }, 100);
        materialObj.sourceKey = showInfo.sourceKey;
        return super.show(showInfo);
    }

    async didMount() {
        const remoteConfig = await getSwallowConfig({
            key: 'jk_ke4score_adv'
        });

        txtArr1 = remoteConfig.descList;
    }

    // 从备用的文案池里梅茨随机取一个新的文案出来，知道文案池遍历一边之后重复循环操作
    getText() {
        if (txtArr1.length === txtArr2.length) {
            txtArr2 = [];
        }
        const diffarr = txtArr1.filter(v => !txtArr2.some((item) => item === v));
        const selIndex = Math.floor(Math.random() * diffarr.length);
        const selItem = diffarr.slice(selIndex, selIndex + 1);
        txtArr2.push(selItem[0]);
        return selItem[0];
    }
    getRandomElements(arr: [], n: number) {
        const result = [];
        const list = [].concat(arr);
        while (n--) {
            const x = Math.floor(Math.random() * list.length);
            result.push(list[x]);
            list.splice(x, 1);
        }
        return result;
    }
    getImageText(refetch?: boolean) {
        const { showInfo } = this.state;
        let descText = '';

        queryUniversalTemplateData(showInfo.activityId, refetch).then(retData => {
            const materialData = retData;

            materialObj.activityId = materialData.activityId;
            materialObj.templateId = materialData.templateId;
            materialObj.imgTagRelIds = materialData.img.imgTagRelIds;

            topic = retData.topic;

            const imgList = materialData.img.imgList;
            const imgs = this.getRandomElements(imgList, 2);

            this.setState({
                src1: imgs[0] && imgs[0].imgUrl,
                src2: imgs[1] && imgs[1].imgUrl
            });

            imageArr = [];

            for (let i = 0; i < imgList.length; i++) {
                imageArr.push(imgList[i].imgUrl);
            }

            try {
                descText = materialData.text.content;
                isRunout = false;
                materialObj.textTagRelId = materialData.text.textTagRelId;

            } catch (error) {
                isRunout = true;
                descText = '';
            }

            this.setState({
                descText
            });
        });
    }
    switchDesc() {
        this.getImageText(true);
    }
    onShare(e) {
        const shareChannel = parseInt(e.target.getAttribute('data-channel'));

        this.share(shareChannel);
    }
    async share(shareChannel: number) {
        const { descText } = this.state;
        const images = imageArr;
        const imageUrls = imageArr;

        const content = (isRunout ? '' : descText);
        let confirm;

        if (shareChannel === 1) {
            if (+URLParams.bizVersion > 18) {
                confirm = await shareRedbook({
                    imageUrls,
                    videoPath: '',
                    content: content + topic,
                    shareName: getPageName() + '_' + fragmentName1 + '_分享入口_点击一键分享小红书'
                });
            } else {
                confirm = await shareRedbook({
                    images: JSON.stringify(images),
                    content: content + topic,
                    videoPath: '',
                    shareName: getPageName() + '_' + fragmentName1 + '_分享入口_点击一键分享小红书'
                });
            }

            if (confirm && +URLParams.bizVersion >= 20) {
                this.useMaterial(shareChannel);
            }
        } else {
            confirm = await shareDouyin({
                images,
                imageUrls: images,
                videoPath: '',
                title: content,
                content: topic,
                shareName: getPageName() + '_' + fragmentName1 + '_分享入口_点击一键分享抖音'
            });
        }

        if (confirm && +URLParams.bizVersion >= 20) {
            this.useMaterial(shareChannel);
        }

        if (!confirm) {
            makeToast('分享失败，无法开启');
        }
    }
    useMaterial(shareChannel) {
        const { openGift } = this.props;
        materialObj.shareChannel = shareChannel;
        useMaterial(materialObj).then(() => {
            openGift && openGift();

            this.hide();
        });
    }
    onClose() {
        this.hide();
    }

}
