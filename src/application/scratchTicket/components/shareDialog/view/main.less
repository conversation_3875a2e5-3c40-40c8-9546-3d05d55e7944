.congrats-page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    overflow-y: scroll;
    padding: 30/2px;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;

    .header {
        display: flex;

        .hd {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            padding-top: 10/2px;

            h3 {
                font-size: 36/2px;
                font-weight: bold;
                color: #333333;
                line-height: 52/2px;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                white-space: nowrap;
            }

            p {
                font-size: 26/2px;
                color: #959595;
                line-height: 36/2px;
                padding-top: 20/2px;
            }
        }

        .close {
            width: 80/2px;
            height: 80/2px;
            background: url(../images/close.png) no-repeat right top;
            background-size: 50/2px 50/2px;

        }
    }

    .desc {
        margin-top: 20/2px;
        min-height: 140/2px;
        background: #f3f3f3;
        border-radius: 4/2px;
        padding: 30/2px 30/2px 20/2px 30/2px;

        .text {
            font-size: 28/2px;
            color: #333333;
            line-height: 40/2px;
            max-height: 120/2px;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 3;
            display: -webkit-box;
            -webkit-box-orient: vertical;
        }

        .switch {
            text-align: right;
            font-size: 28/2px;
            color: #999999;
            line-height: 40/2px;
            margin-top: 10/2px;

            span {
                padding-left: 44/2px;
                background: url(../images/switch.png) no-repeat left center;
                background-size: 36/2px 36/2px;
                padding: 10/2px 0 10/2px 44/2px;
            }
        }
    }

    .material {
        margin-top: 15px;
        .divs {
            width: 100%;
            display: flex;
            justify-content: space-between;

            div {
                width: 332/2px;
                height: 444/2px;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }
        }

        .video-wrap {
            // height: 356/2px;
            height: 444/2px;
            position: relative;

            img {
                width: 100%;
                height: 100%;
                border-radius: 16/2px;
                object-fit: contain;
            }

            .play {
                position: absolute;
                top: 50%;
                left: 50%;
                margin-left: -28/2px;
                margin-top: -28/2px;
                width: 56/2px;
                height: 56/2px;
                background: url(../images/play.png) no-repeat;
                background-size: 100% 100%;
            }
        }

        .btns {
            display: flex;
            justify-content: space-between;
            margin-top: 30/2px;
            padding-bottom: calc(~"10/2px + constant(safe-area-inset-bottom)/2");
            /* 兼容 iOS < 11.2 */
            padding-bottom: calc(~"10/2px + env(safe-area-inset-bottom)/2");

            .btn {
                width: 332/2px;
                height: 88/2px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 32/2px;
                font-weight: Regular;
                text-align: center;
                color: #ffffff;
                border-radius: 88/2px;

                &.btn1 {
                    background: linear-gradient(135deg, #524c4c, #000000);
                }

                &.btn2 {
                    background: linear-gradient(135deg, #ff2d4f, #ff2442);
                }
            }
        }
    }

    .generate {
        position: absolute;
        left: -10000/2px;
        top: 0;
        z-index: 100;
    }
}

.skeleton {
    .skeleton-page {
        z-index: 9;
        position: fixed;
        top: 0;
        height: 100vh;
        width: 100vw;
        background: url(data:image/png;base64,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);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .skeleton-leave {
        animation: leave .5s ease-in-out forwards;
    }
}