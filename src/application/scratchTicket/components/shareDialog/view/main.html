<import name="style" content="./main" module="S" />
<import name="dialog" content=":component/dialog/main" />

<div>
    <com:dialog position="bottom">
        <div class=":congrats-page" ref="congratsPage" id="congratsPage">
            <div class=":header">
                <div class=":hd">
                    <h3>分享学车经验即可开启刮刮乐</h3>
                    <p>推荐文案（点击更换可以换一条哦～）</p>
                </div>
                <div class=":close" sp-on:click="onClose">
                </div>
            </div>
    
            <div class=":desc">
                <div class=":text">{{state.descText}}</div>
                <div class=":switch"><span sp-on:click="switchDesc">换一个文案</span></div>
            </div>
    
            <div style="flex: 1;"></div>
    
            <div class=":material">
                <div class=":divs">
                    <div class=":div1">
                        <img src="{{state.src1}}" alt="">
                    </div>
                    <div class=":div2">
                        <img src="{{state.src2}}" alt="">
                    </div>
                </div>
                <div class=":btns">
                    <p class=":btn :btn1" sp-on:click="onShare" data-action="一键分享抖音" data-channel="2">一键分享抖音</p>
                    <p class=":btn :btn2" sp-on:click="onShare" data-action="一键分享小红书" data-channel="1">一键分享小红书</p>
                </div>
            </div>
        </div>
    </com:dialog>
</div>
