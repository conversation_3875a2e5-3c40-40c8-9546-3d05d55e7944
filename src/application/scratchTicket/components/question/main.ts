/*
 * main
 *
 * name: xia<PERSON><PERSON>a
 * date: 16/3/24
 */
import { Dialog } from ':component/dialog/main';
import View from './view/main.html';
import { checkUserOption, getExamQuestion } from ':store/activity';
import { makeToast } from ':common/features/dom';
import { dateFormat } from ':common/utils';
import { getCache, getUserInfo, saveCache } from ':common/core';
import { URLParams } from ':common/env';

const BLANK = 0;
const CORRECT = 1;
const WRONG = 2;

export const answerStatus = {
    BLANK,
    CORRECT,
    WRONG
};

export default class extends Dialog<{ activityId: number }, void, any, any> {
    optionList = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            questionInfo: {
                isCorrent: answerStatus.BLANK
            },
            wrongCount: 0,
            userSelect: {}
        };
    }
    public show(showInfo: { activityId: number, index: number, sourceKey: number }) {
        setTimeout(() => {
            this.onChangeQuestion();
        }, 100);

        return super.show(showInfo);
    }
    // 获取题目
    async getQuestion() {
        const { showInfo } = this.state;
        const question = await getExamQuestion({
            questionCategory: 2,
            activityId: showInfo.activityId
        });

        this.setState({
            questionInfo: question
        });
    }
    // 对答案
    async onCompareAnswer() {
        const { questionInfo, userSelect, showInfo } = this.state;
        let { wrongCount } = this.state;
        const arr = [];
        const userinfo = await getUserInfo();
        this.optionList.forEach(item => {
            if (userSelect[item]) {
                arr.push(item);
            }
        });

        if (!arr.length) {
            makeToast('请先选择答案');
            return;
        }

        if (questionInfo.questionType === 2 && arr.length < 2) {
            makeToast('请选择多个选项');
            return;
        }
        const data = await checkUserOption({
            activityId: showInfo.activityId,
            questionId: questionInfo.questionId,
            userOption: arr.join(','),
            sourceKey: showInfo.sourceKey
        });

        questionInfo.isCorrent = data.result ? answerStatus.CORRECT : answerStatus.WRONG;

        // 给每一个选项设置对错
        questionInfo.options.forEach(item => {
            if (data.rightChoice.indexOf(item.option) > -1) {
                item.correct = true;
            } else {
                item.correct = false;
            }
        });

        if (!data.result) {
            wrongCount++;
            saveCache({
                key: 'send-vip-wrongCount' + URLParams.activityId + showInfo.index,
                value: JSON.stringify({
                    [dateFormat(new Date(), 'yyyy/MM/dd') + userinfo.mucangId]: {
                        wrongCount,
                        questionInfo,
                        userSelect
                    }
                })
            });
        }

        this.setState({
            wrongCount,
            questionInfo
        });

    }
    // 选择答案
    onSelectAnswer(e) {
        const { questionInfo } = this.state;
        let { userSelect } = this.state;
        const index = +e.refTarget.getAttribute('data-index');
        const option = questionInfo.options[index].option;

        if (questionInfo.questionType !== 2) {
            userSelect = {
                [option]: true
            };
        } else if (userSelect[option]) {
            delete userSelect[option];
        } else {
            userSelect[option] = true;
        }

        this.setState({
            userSelect
        });
    }
    // 换一题
    async onChangeQuestion() {
        const { showInfo } = this.state;
        const userinfo = await getUserInfo();

        const saveData = JSON.parse((await getCache('send-vip-wrongCount' + URLParams.activityId + showInfo.index) || '{}'))[dateFormat(new Date(), 'yyyy/MM/dd') + userinfo.mucangId];

        const wrongCount = saveData?.wrongCount;

        if (wrongCount >= 3) {
            console.log(saveData, 11111111111);
            this.setState({
                ...saveData
            });
            return;
        }

        this.setState({
            wrongCount: wrongCount || 0,
            userSelect: {}
        });
        this.getQuestion();
    }
    // 开奖
    onOpenGift() {
        const { openGift } = this.props;

        openGift && openGift();

        this.hide();
    }
    onClose() {
        this.hide();
    }

}
