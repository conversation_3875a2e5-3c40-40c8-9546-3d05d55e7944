<import name="style" content="./main" module="S" />
<import name="dialog" content=":component/dialog/main" />

<div class=":question-dialog">
    <com:dialog>
        <div class=":content">
            <div class=":close" sp-on:click="onClose"></div>
            <div class=":question-box">
                <div class=":question">
                    <div class=":title"> 
                        <sp:if value="{{state.questionInfo.questionType == 1}}">
                            <span class=":sign :single">单选</span>
                        <sp:elseif value="{{state.questionInfo.questionType == 2}}"/>
                            <span class=":sign :mul">多选</span>
                        <sp:else/>
                            <span class=":sign :jud">判断</span>
                        </sp:if>
                        {{state.questionInfo.question}}
                    </div>
                    <sp:each for="{{state.questionInfo.options}}">
                        <div class=":answer-item {{state.userSelect[$value.option] ? S.selected : ''}} {{$value.correct && state.questionInfo.isCorrent && !state.userSelect[$value.option]?S.rightSelect:''}} {{!$value.correct && state.questionInfo.isCorrent && state.userSelect[$value.option]?S.showWrong:''}} {{$value.correct && state.questionInfo.isCorrent && state.userSelect[$value.option]?S.showRight:''}}"
                            data-index="{{$index}}" sp-on:click="onSelectAnswer">
                            <span class=":item-icon">{{$value.option}}</span>
                            <span class=":text">{{$value.optionContent}}</span>
                        </div>
                    </sp:each>
                    <sp:if value="{{!state.questionInfo.isCorrent}}">
                        <div class=":btn :submit" sp-on:click="onCompareAnswer"></div>
                    </sp:if>

                    <sp:if value="{{state.questionInfo.isCorrent === 1}}">
                        <div class=":btn :right" sp-on:click="onOpenGift"></div>
                    </sp:if>

                    <sp:if value="{{state.questionInfo.isCorrent === 2 && state.wrongCount < 3}}">
                        <div class=":btn :wrong" sp-on:click="onChangeQuestion"></div>
                    </sp:if>

                    <sp:if value="{{state.questionInfo.isCorrent === 2 && state.wrongCount >= 3}}">
                        <div class=":btn :wrong-count" sp-on:click="onClose"></div>
                    </sp:if>
                </div>
            </div>
        </div>
    </com:dialog>
</div>
