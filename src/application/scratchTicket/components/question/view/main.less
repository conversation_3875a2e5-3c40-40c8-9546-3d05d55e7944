.question-dialog {
    .content {
        width: 330px;
        padding: 100px 8px 0 5px;
        border-radius: 20px;
        position: relative;
        background-image: url(http://exam-room.mc-cdn.cn/exam-room/2024/06/25/14/cdca353b49844329be49edbc7a8fcdc9.png);
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 318px 338px;

        .close {
            position: absolute;
            top: -20px;
            right: 0;
            width:40px;
            height: 40px;
            background: url(../images/close.png) no-repeat right top;
            background-size: 25px 25px;
        }

        .question-box {
            background: linear-gradient(180deg, #fe5482, #ffbac3);
            padding: 0 18px 18px;
            border-radius: 20px;

            .question {
                background-color: #fff;
                border-radius: 12px;
                padding: 18px 18px 38px;
                position: relative;
                z-index: 1;

                .title {
                    font-size: 16px;
                    font-weight: bold;
                    color: #333;
                    line-height: 22px;
                    margin-bottom: 4px;

                    .sign {
                        padding: 2px 6px;
                        font-size: 13px;
                        font-weight: bold;
                        border-radius: 3px;
                        margin-right: 10px;
                        vertical-align: middle;

                        &.single {
                            background-color: #CFFEFF;
                            color: #00D1FF;
                        }

                        &.mul {
                            background-color: #FFF4CB;
                            color: #FF8400;
                        }

                        &.jud {
                            background-color: #FFE7EA;
                            color: #FE3754;
                        }
                    }
                }

                .answer-item {
                    display: flex;
                    align-items: center;
                    background: #f9f9f9;
                    border-radius: 10px;
                    padding: 12px;
                    margin-top: 12px;

                    .item-icon {
                        width: 28px;
                        height: 28px;
                        box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.15);
                        border: 1px solid rgba(0, 0, 0, 0.05);
                        border-radius: 50%;
                        font-size: 16px;
                        text-align: center;
                        line-height: 28px;
                        margin-right: 15px;
                        flex-shrink: 0;
                    }

                    .text {
                        font-size: 15px;
                        line-height: 21px;
                    }

                    // 多选选中的选项
                    &.selected {
                        .item-icon {
                            border: none;
                            background-color: #B0B7C6;
                            color: white;
                        }
                    }

                    // 正确的选项
                    &.rightSelect:not(.selected) {
                        color: #1DACF9;
                        background-color: #E8FAFF;

                        .item-icon {
                            background-color: #1DACF9;
                            color: white;
                        }
                    }

                    // 答案选择错误
                    &.showWrong {
                        background-color: #FFF0F0;

                        .item-icon {
                            position: relative;

                            &:after {
                                content: '';
                                position: absolute;
                                width: 38px;
                                height: 38px;
                                background: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/jkbd-ic-dati-wrong.png) no-repeat center center/cover;
                                top: -5px;
                                left: -5px;
                            }
                        }

                        .text {
                            color: #FF4A40;
                        }
                    }

                    // 答案选择正确
                    &.showRight {

                        .item-icon {
                            position: relative;

                            &:after {
                                content: '';
                                position: absolute;
                                width: 38px;
                                height: 38px;
                                background: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/jkbd-ic-dati-right.png) no-repeat center center/cover;
                                top: -5px;
                                left: -5px;
                            }
                        }

                        .text {
                            color: #1DACF9 !important;
                        }
                    }

                }

                .btn {
                    width: 232px;
                    height: 54px;

                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translate(-50%, 50%);

                    &.submit {
                        background: url(../images/btn.png) no-repeat center center/cover;
                    }

                    &.right {
                        background: url(../images/btn_right.png) no-repeat center center/cover;
                    }

                    &.wrong {
                        background: url(../images/btn_wrong.png) no-repeat center center/cover;
                    }
                     &.wrong-count {
                         background: url(../images/btn_wrong1.png) no-repeat center center/cover;
                     }
                }
            }
        }

    }
}
