/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */
import { Dialog } from ':component/dialog/main';
import View from './view/main.html';

export default class extends Dialog<void, void> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }
    public show() {
        return super.show();
    }
    onRule() {
        this.show();
    }

    onClose() {
        this.hide();
    }
    willReceiveProps() {
        return true;
    }
}
