<import name="style" content="./main" module="S" />
<import name="dialog" content=":component/dialog/main" />

<div class=":active-vipbuchang-rule">
    <div class=":rule" ref="rule" skip="true" sp-on:click="onRule" id="drag">
        规则
    </div>

    <com:dialog>
        <div class=":rule-panel">
            <div class=":close" sp-on:click="onClose"></div>
            <div class=":rule-wraper">
                {{#props.rule}}
            </div>
        </div>
    </com:dialog>
</div>
