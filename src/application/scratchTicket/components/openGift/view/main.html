<import name="style" content="./main" module="S" />
<import name="dialog" content=":component/dialog/main" />

<div>
    <com:dialog>
        <div class=":content">
            <sp:if value="{{state.status === 1}}">
                <div class=":gift-gif"></div>
            </sp:if>
            <sp:if value="{{state.status === 2}}">
                <div class=":gift">
                    <div class=":center-color"></div>
                    <div class=":center-bg"
                        style="background-image:url({{props.gift.presentImgUrl ? props.gift.presentImgUrl : 'http://exam-room.mc-cdn.cn/exam-room/2024/06/28/18/0c1df91eea824905baa3e3fe35cf5171.png'}});">
                    </div>
                    <div class=":title">{{props.gift.presentName}}</div>
                    <div class=":time">有效期至：{{props.gift.showExpiredTime}}</div>
                    <div class=":btn" sp-on:click="onClose"></div>
                </div>
            </sp:if>
        </div>
    </com:dialog>
</div>
