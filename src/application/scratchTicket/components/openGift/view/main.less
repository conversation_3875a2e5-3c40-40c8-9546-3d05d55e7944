.content {

    .gift-gif {
        width: 325px;
        height: 400px;
        background: url(../images/gif.gif) no-repeat center center/cover;

    }

    .gift {
        width: 325px;
        height: 400px;
        background: url(../images/gift.png) no-repeat center center/cover;
        padding-top: 117px;
        position: relative;

        .center-color {
            width: 130px;
            height: 108px;
            background: linear-gradient(180deg, #e4ffb2, #d7ff8e 100%);
            border: 1px solid #ffffff;
            border-radius: 16px;
            margin: 0 auto;
        }

        .center-bg {
            position: absolute;
            width: 77px;
            height: 74px;
            top: 136px;
            left: 122px;
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
        }

        .title {
            color: #591B34;
            text-align: center;
            font-weight: bold;
            font-size: 24px;
            line-height: 24px;
            margin-top: 15px;
        }

        .time {
            font-size: 13px;
            color: #FF1D5F;
            line-height: 18px;
            text-align: center;
            margin-top: 6px;
        }

        .btn {
            width: 190px;
            height: 54px;
            margin: 10px auto;
        }
    }

}
