/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */
import { Dialog } from ':component/dialog/main';
import View from './view/main.html';

export default class extends Dialog<void, void, any> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            status: 1
        };
    }
    public show() {
        this.setState({
            status: 1
        });
        setTimeout(() => {
            this.setState({
                status: 2
            });
        }, 2000);
        return super.show();
    }

    onClose() {
        this.hide();
    }
    willReceiveProps() {
        return true;
    }
}
