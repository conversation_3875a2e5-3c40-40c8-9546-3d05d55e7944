<import name="style" content="./main" module="S" />
<import name="header" content=":component/header/main" />
<import name="Rule" content="../components/scratchTicketRule/main" />
<import name="ShareDialog" content="../components/shareDialog/main" />
<import name="Question" content="../components/question/main" />
<import name="OpenGift" content="../components/openGift/main" />

<div class="page-container :scratch-ticket" sp-on:scroll="pageScroll">
    <div class=":page-header">
        <com:header title=" " theme="black" endTheme="black" finalBgColor="#ff566f" scrollTop="{{state.prevScrollTop}}">
            <div class=":rule-text" sp:slot="right">
                <com:Rule rule="{{state.activityInfo.shareIndexDTO.introduction}}" />
            </div>
        </com:header>
        <div class=":card-list">
            <div class=":head-img-sign"></div>
            <div class=":head-img-sign-time">活动时间：{{state.activityInfo.showStartTime}} -
                {{state.activityInfo.showEndTime}}</div>
            <sp:each for="{{state.taskList}}">
                <sp:if value="{{$value.status === 3}}">
                    <div class=":card-item :open" data-index="{{$index}}" sp-on:click="onGoUse">
                        <div class=":card-title">{{$value.presentName}}</div>
                        <div class=":card-time">有效期至：{{$value.showExpiredTime}}</div>
                        <div class=":card-sign"
                            style="background-image:
                            url({{$value.presentImgUrl || 'http://exam-room.mc-cdn.cn/exam-room/2024/06/28/18/0c1df91eea824905baa3e3fe35cf5171.png'}});">
                        </div>
                        <sp:if value="{{$value.presentCategory === 5}}">
                            <div class=":car-btn">去领取</div>
                            <sp:else/>
                            <div class=":car-btn">去使用</div>
                        </sp:if>
                    </div>
                    <sp:else />
                    <div class=":card-item" data-index="{{$index}}" sp-on:click="onOpenTask"></div>
                </sp:if>
            </sp:each>
        </div>
    </div>
    <div class=":content-box">
        <div class=":has-times">还有 <span style="color: #3F6CFF;">{{state.taskList.length - self.openGiftCount}}</span>
            次刮卡机会 ↑</div>

        <div class=":other-box">
            <div class=":task-list">
                <sp:each for="{{state.recommendTaskList}}">
                    <div class=":task-item">
                        <div class=":img" style="background-image: url({{$value.extraInfoDO.imgUrl}});"></div>
                        <div class=":info-box">
                            <div class=":info-title">{{$value.taskName}}</div>
                            <div class=":info-dec">{{$value.taskDescription}}</div>
                        </div>
                        <div class=":btn" data-index="{{$index}}" sp-on:click="onGoActive">去领取</div>
                    </div>
                </sp:each>
            </div>
        </div>
    </div>

    <com:Question openGift="{{self.openGift}}" />
    <com:ShareDialog openGift="{{self.openGift}}" />
    <com:OpenGift gift="{{self.openGiftLast}}" />
</div>
