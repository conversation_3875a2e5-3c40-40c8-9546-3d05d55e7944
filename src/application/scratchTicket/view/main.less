.scratch-ticket {
    background-color: #FFD1DC !important;
    overflow-y: auto;

    .page-header {
        flex-shrink: 0;
        height: 599px;
        background: url(../images/<EMAIL>) no-repeat center center/cover;
        padding: 0 30px;
        position: relative;

        .card-list {
            position: absolute;
            width: 341px;
            height: 400px;
            left: 50%;
            bottom: 4px;
            transform: translateX(-50%);
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;

            .head-img-sign {
                position: absolute;
                background: url(../images/<EMAIL>) no-repeat center center/cover;
                width: 306px;
                height: 120px;
                left: 50%;
                top: -5px;
                transform: translate(-50%, -100%);
            }

            // .head-img-sign-dec{
            //     width: 268px;
            //     height: 31px;
            //     background: linear-gradient(270deg, rgba(255, 255, 255, 0.00) 0%, rgba(255, 255, 255, 0.39) 12%, rgba(255, 255, 255, 0.80) 30%, rgba(255, 255, 255, 0.95) 55%, rgba(255, 255, 255, 0.43) 86%, rgba(255, 255, 255, 0.00));
            //     border-radius: 6px;
            //     margin: 0 auto 4px;
            //     color: #F00852;
            //     font-size: 15px;
            //     display: flex;
            //     justify-content: center;
            //     align-items: center;
            //     margin-top: -12px;
            // }

            .head-img-sign-time {
                color: white;
                font-size: 12px;
                transform: scale(0.8);
                margin: 0 auto 10px;
            }


            .card-item {
                width: 168px;
                height: 186px;
                background: url(../images/<EMAIL>) no-repeat center center/cover;

                &.open {
                    background: url(../images/<EMAIL>) no-repeat center center/cover;
                    position: relative;
                    padding: 36px 15px 0;
                    text-align: center;

                    .card-title {
                        font-size: 16px;
                        color: #000;
                        font-weight: bold;
                    }

                    .card-time {
                        margin-top: 4px;
                        font-size: 12px;
                        transform: scale(0.75);
                        color: #4C6C16;
                    }

                    .card-sign {
                        position: absolute;
                        top: 70px;
                        left: 45px;
                        width: 60px;
                        height: 58px;
                        background-size: cover;
                        background-position: center center;
                        background-repeat: no-repeat;
                    }

                    .car-btn {
                        width: 93px;
                        height: 31px;
                        position: absolute;
                        bottom: 12px;
                        left: 50%;
                        transform: translateX(-58px);
                        background: url(../images/btn.png) no-repeat center center/93px 31px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 12px;
                        font-weight: bold;
                        color: #583B32;
                    }
                }

                &:nth-of-type(n+5) {
                    margin-top: 15px;
                }
            }

        }

    }

    .content-box {
        .has-times {
            margin: 0 auto;
            display: flex;
            font-size: 13px;
            justify-content: center;
            align-items: center;
            color: #583B32;
            width: 186px;
            height: 34px;
            background: #FFE4E8;
            border-radius: 18px;
            margin-top: 15px;
        }

        .other-box {
            background-image: url(../images/<EMAIL>);
            background-repeat: no-repeat;
            background-position: top left;
            background-size: 375px 105px;
            padding: 67px 9px 30px;
            margin-top: 25px;
            border-radius: 16px 16px 0 0;
            background-color: #F8F8F8;

            .task-list {
                .task-item {
                    height: 94px;
                    background: #ffffff;
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    padding: 12px 15px;


                    &:nth-of-type(n+2) {
                        margin-top: 12px;
                    }

                    .img {
                        width: 71px;
                        height: 71px;
                        background-repeat: no-repeat;
                        background-position: center center;
                        background-size: cover;
                        border-radius: 8px;
                    }

                    .info-box {
                        margin-left: 13px;
                        width: 150px;

                        .info-title {
                            font-size: 15px;
                            line-height: 21px;
                            font-weight: bold;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 2;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .info-dec {
                            margin-top: 2px;
                            font-size: 13px;
                            color: #8D8889;
                            line-height: 18px;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 2;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }

                    .btn {
                        width: 72px;
                        height: 28px;
                        border-radius: 14px;
                        color: white;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background: linear-gradient(275deg, #ff4bb4 3%, #fa3c80 96%);
                        margin-left: 20px;
                        font-size: 14px;

                        &.no-event {
                            background: #D4D1D1;
                            pointer-events: none;
                        }
                    }
                }
            }
        }
    }
}
