import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { trackPageLoad } from ':common/stat';
import { getActivityInfo, openNextTask, queryRecommendTask, queryTaskSummary } from ':store/activity';
import { URLParams } from ':common/env';
import { getAuthToken, getUserInfo, openWeb } from ':common/core';
import { login } from ':common/features/login';
import Question from './components/question/main';
import OpenGift from './components/openGift/main';
import ShareDialog from './components/shareDialog/main';
import { dateFormat } from ':common/utils';

let timer = null;
let taskIndex;
interface State {
    prevScrollTop: number,
    taskList: any[],
    recommendTaskList: any[],
    activityInfo: any
}
// 1: '答题',
// 2: '分享',
// 3: '邀请拉新'
// 4: 直接获取礼物
enum TaskType {
    QUESTION = 1,
    SHARE = 2,
    JOIN = 3,
    GIFT = 4
}

export default class extends Application<State> {
    declare children: {
        Question: Question,
        OpenGift: OpenGift,
        ShareDialog: ShareDialog
    }
    get openGiftCount() {
        const { taskList } = this.state;
        return taskList.filter(item => item.status === 3)?.length || 0;
    }
    get openGiftLast() {
        const { taskList } = this.state;
        return taskList[taskIndex];
    }
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            prevScrollTop: 0,
            taskList: [],
            recommendTaskList: [],
            activityInfo: {}
        };
        this.props = {};
    }
    async didMount() {
        trackPageLoad();

        const authToken = await getAuthToken();
        if (!authToken) {
            login();
        } else {

            this.queryActivityInfo();

            this.queryTaskSummary();

            this.queryRecommendTask();
        }

    }
    queryActivityInfo() {
        getActivityInfo(URLParams.activityId).then(data => {
            this.setState({
                activityInfo: {
                    ...data,
                    showStartTime: dateFormat(data.startTime, 'yyyy/MM/dd'),
                    showEndTime: dateFormat(data.endTime, 'yyyy/MM/dd')
                }
            });
        });
    }
    queryTaskSummary() {
        queryTaskSummary({
            activityId: URLParams.activityId
        }).then(data => {
            const list = new Array(data.totalRound).fill({
                status: 1
            });

            data.openedTaskList?.forEach(item => {
                item.showExpiredTime = dateFormat(item.expiredTime, 'yyyy.MM.dd');
                list[item.index - 1] = item;
            });

            this.setState({
                taskList: list
            });
        });
    }
    queryRecommendTask() {
        queryRecommendTask().then(data => {
            this.setState({
                recommendTaskList: data
            });
        });
    }
    async onOpenTask(e) {
        e.stopPropagation();
        const { taskList } = this.state;
        const index = +e.refTarget.getAttribute('data-index');

        taskIndex = index;

        let task;

        // 可能已经有正在做的任务
        if (taskList[index].status === 2) {
            task = taskList[index];
        } else {
            task = await openNextTask({
                activityId: URLParams.activityId,
                index: index + 1
            });
        }

        if (task.taskType === TaskType.GIFT) {
            this.openGift();
        }

        if (task.taskType === TaskType.SHARE) {
            this.children.ShareDialog.show({ activityId: task.taskRelateId, sourceKey: task.id });
        }

        if (task.taskType === TaskType.QUESTION) {
            this.children.Question.show({ activityId: task.taskRelateId, index: index + 1, sourceKey: task.id });
        }
    }
    onGoUse(e) {
        const { taskList } = this.state;
        const index = +e.refTarget.getAttribute('data-index');
        const url = taskList[index].actionUrl;

        openWeb({
            url
        });
    }
    onGoActive(e) {
        const { recommendTaskList } = this.state;
        const index = +e.refTarget.getAttribute('data-index');
        const url = recommendTaskList[index].extraInfoDO.actionUrl;

        openWeb({
            url
        });
    }
    openGift = () => {
        setTimeout(() => {
            this.queryTaskSummary();
        }, 1000);
        this.children.OpenGift.show();
    }
    pageScroll(e) {
        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = +e.refTarget.scrollTop;

            this.setState({
                prevScrollTop
            });
        }, 100);
    }
}