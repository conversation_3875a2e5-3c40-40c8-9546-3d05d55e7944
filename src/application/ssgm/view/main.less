.page-ssgm {
    position: relative;
    background: #fff;
    box-shadow: 0px 1px 0px 0px #ffffff;
    border-radius: 6px 6px 0px 0px;
    padding: 15px 15px 0;
    height: 100%;
    display: flex;
    flex-direction: column;

    .tab-container {
        flex: 1;
        position: relative;

        .title {
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
        }

        .close {
            position: absolute;
            top: 0px;
            right: -10px;
            width: 20px;
            height: 20px;
            background: url("../images/close.png");
            background-size: cover;
        }

        .goods {
            height: 76px;
            border-radius: 4px;
            border: 1px solid #DDDDDD;
            display: flex;
            align-items: center;
            margin-top: 17px;
            padding: 0px 15px;
            position: relative;

            .tag{
                position: absolute;
                top: 0;
                right: 0;
                width: 56px;
                height: 20px;
                background: url(../images/<EMAIL>) no-repeat center center/cover;
                transform: translateY(-50%);
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 12px;
                color: #fff;
                white-space: nowrap;
            }

            .goods-left {
                flex: 1;

                .goods-left-title {
                    font-size: 18px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #333333;
                    line-height: 25px;
                }

                .goods-left-desc {
                    font-size: 13px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    margin-top: 2px;
                    line-height: 18px;
                }
            }

            .goods-right {
                .goods-right-title {
                    font-size: 28px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #333333;
                    line-height: 25px;

                    .price {

                        font-size: 18px;

                    }
                }

                .goods-right-desc {
                    font-size: 10px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #666666;
                    line-height: 14px;
                }
            }

            &.click-goods {
                background: rgba(255, 214, 189, 0.2);
                border: 1px solid #FFBB8A;

                .goods-right-title {
                    color: #E1813C;
                }
            }

            .click-icon {
                width: 30px;
                height: 30px;
                background: url(../images/ic-xuanzhong.png) no-repeat center;
                background-size: 100% 100%;
                position: absolute;
                right: -1px;
                bottom: 0px;

            }
        }
    }

    .pay-type {
        margin-bottom: -10px;
        margin-left: 10px;
    }

    .buy-btn {
        margin: 0 -10px;
    }
}