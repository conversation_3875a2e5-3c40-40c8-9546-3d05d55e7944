<import name="style" content="./main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payType" content=":component/payType/main" />

<div class="page-container page-ssgm">
    <div class="tab-container">
        <div class="title">获取拍照搜题次数</div>
        <div class="close" sp-on:click="onCloseClick"></div>
        <sp:each for='{{state.goodsInfoPool}}'>
            <div data-idx="{{$index}}" sp-on:click="tabChange"
                class="goods {{self.nowGoodInfo&&self.nowGoodInfo.groupKey==$value.groupKey?'click-goods':''}}">
                <div class="goods-left">
                    <div class="goods-left-title">{{$value.name}}</div>
                    <div class="goods-left-desc">购买后{{$value.validDays}}天内可使用{{$value.times}}次</div>
                </div>
                <div class="goods-right">
                    <div class="goods-right-title"><span class="price">￥</span>{{$value.payPrice}}</div>
                    <div class="goods-right-desc">低至{{($value.payPrice/$value.times).toFixed(2)}}元／次</div>
                </div>
                <sp:if value="$index === 1">
                    <div class="tag">更划算</div>
                </sp:if>
                <sp:if value='{{self.nowGoodInfo&&self.nowGoodInfo.groupKey==$value.groupKey}}'>
                    <div class="click-icon"></div>
                </sp:if>

            </div>
        </sp:each>



    </div>
    <div class="buy-btn">
        <com:buyButton />
    </div>
</div>