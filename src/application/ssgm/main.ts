/*
 * ------------------------------------------------------------------
 * 搜索购买半截弹窗
 * ------------------------------------------------------------------
 */

import { PayType, setPageName } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import PayTypeCom from ':component/payType/main';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import View from './view/main.html';
import { iosDialogBuySuccess } from ':common/features/ios_pay';
import { getAuthToken, webClose } from ':common/core';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackEvent } from ':common/stat';
import BaseVip from ':common/features/baseVip';
import { showSetting } from ':common/features/embeded';
import { typeCode } from ':common/features/bottom';
import { login } from ':common/features/login';
import { getPermission } from ':store/chores';

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
}
export default class extends BaseVip {
    declare State: State
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        payType: PayTypeCom
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog
    };
    getPageName() {
        return '拍照搜题页';
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            goodsInfoPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {}
        };

    }
    async didMount() {
        showSetting({
            iosH: 220,
            androidH: 366
        });
        const { tabIndex } = this.state;
        const goodsInfoPool: GoodsInfo[] = [];
        goodsInfoPool.push({
            groupKey: GroupKey.ChannelSearch1
        } as GoodsInfo);
        goodsInfoPool.push({
            groupKey: GroupKey.ChannelSearch2
        } as GoodsInfo);
        this.state.goodsInfoPool = goodsInfoPool;

        // 注册底部支付方法
        const authToken = await getAuthToken();
        if (authToken) {
            this.checkPermission();
            this.children.buyButton.setPay({
                androidPay: this.pay,
                intercepter: async () => {
                    return false;
                },
                iosPaySuccess: () => {
                    iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey, goUse: true });
                },
                isInDialog: true
            });
        } else {
            this.children.buyButton.setPay({
                androidPay: this.pay,
                intercepter: () => {
                    login();
                    return true;
                },
                iosPaySuccess: () => {
                    iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey, goUse: true });
                },
                isInDialog: true
            });
        }

        await this.getGoodInfo();
        // 先展示页面，再去请求无关的信息
        setTimeout(async () => {
            await this.getLabel();
            await this.getComparePrice();

            this.setPageInfo();
        }, 60);

        setPageName(this.getPageName());
        // 页面进出时长打点
        trackEvent({
            fragmentName1: '拍照搜题购买弹窗',
            actionType: '去支付'
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: goodsInfoPool[tabIndex].groupKey
        });

    }
    // 如果有权益就会关闭
    async checkPermission() {
        const { hasPromission } = await getPermission('pzst');
        if (hasPromission) {
            webClose();
        }
    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                newGoodsPool.push(goodInfo);
            });

            this.setState({
                goodsInfoPool: newGoodsPool
            });
            this.setPageInfo();
        });

    }

    onCloseClick() {
        webClose();
    }
    pay = async (stat: PayStatProps) => {
        this.onPay({
            stat: {
                ...stat
            }
        });
    }
    onPay = async (config: { stat: PayStatProps }) => {
        const { tabIndex, goodsInfoPool } = this.state;

        // 记住之前选的支付方式
 
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo?.couponCode,
            ...config.stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey, goUse: true }, 2);
        });
    }
    tabChange(e) {
        const tabIndex = +e.refTarget.getAttribute('data-idx');
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        this.setState({
            tabIndex
        }, () => {
            this.setBuyBottom();
        });

    }
    async setBuyBottom() {
        const fragmentName1 = '拍照搜题购买弹窗';
        const fragmentName2 = '点击购买按钮';
        const nowGoodInfo: GoodsInfo = this.nowGoodInfo;
        const bottomType: typeCode = typeCode.type2;

        switch (bottomType) {
            case typeCode.type2:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '',
                    price: this.showPrice,
                    fragmentName1,
                    fragmentName2
                });
                break;
            default:
                break;
        }
    }
}
