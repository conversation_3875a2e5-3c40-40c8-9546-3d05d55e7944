/*
 * ------------------------------------------------------------------
 * 小车科一科四
 * ------------------------------------------------------------------
 */

import { ABTestKey, ABTestType, CarType, KemuType, persuadeDialogAllow, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import { formatPrice } from ':common/utils';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import BuyDialogOrderSign from ':component/buyDialogOrderSign/main';
import PayDialog from ':component/payDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import { getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupComparePrice, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { typeCode } from ':common/features/bottom';
import { isHubei } from ':common/features/locate';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import { webClose } from ':common/core';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackEvent, trackExit, trackGoPay, trackPageLoad, trackPageShow } from ':common/stat';
import { onWebBack } from ':common/features/persuade';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { BUYED_URL, CAR_KE1AND4_VIDEO, CAR_KE1_VIDEO, CAR_KE4_VIDEO, CAR_SESSION500_VIDEO, openAuth } from ':common/navigate';
import { couponAnimate, pauseAllVideos, scrollTop } from ':common/features/dom';
import { setEmbeddedHeight } from ':common/features/embeded';

import { onPageShow } from ':common/features/page_status_switch';
import jump from ':common/features/jump';
import { getTabIndex } from ':common/features/cache';
import isNumber from 'lodash/isNumber';
import { hesitateUserPersuade } from ':common/features/hesitate';
import { getAbtest } from ':store/chores';

interface State {
    isHubei: boolean,
    kemu: KemuType,
    tiku: CarType,
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    standbyPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    showFooter: boolean,
    prevScrollTop: number,
    showSignModal: boolean
}
let timer;
// 标记是否展示过挽留弹窗
let flag = false;

const pageNameMap = {
    [GroupKey.ChannelKe1]: 'VIP速成版页',
    [GroupKey.ChannelKe4]: 'VIP速成版页',
    [GroupKey.ChannelKe1Month]: 'VIP速成版页',
    [GroupKey.ChannelKe4Month]: 'VIP速成版页',
    [GroupKey.ChannelKe1D3]: 'VIP速成版页',
    [GroupKey.ChannelKe4D3]: 'VIP速成版页',
    [GroupKey.ChannelKe1Ke4Group]: '科1科4组合包页',
    [GroupKey.ChannelKemuAll]: '全科VIP页',
    [GroupKey.ChannelKe4Short]: '短时提分页',
    [GroupKey.ChannelKe34]: ''
};
const qaKeyMap = {
    [GroupKey.ChannelKemuAll]: 'qaKey6'
};

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog,
        buyDialogOrderSign: BuyDialogOrderSign
    };
    moveGoodsVideo = {
        [GroupKey.ChannelKe1]: {
            videoUrl: CAR_KE1_VIDEO,
            entrance: 'vip-sc',
            stat: {
                fromPageCode: '172',
                fromPathCode: '003155'
            }
        },
        [GroupKey.ChannelKe4]: {
            videoUrl: CAR_KE4_VIDEO,
            entrance: 'vip-sc',
            stat: {
                fromPageCode: '172',
                fromPathCode: '003155'
            }
        },
        [GroupKey.ChannelKe1Ke4Group]: {
            videoUrl: CAR_KE1AND4_VIDEO,
            entrance: 'kemu1-4',
            stat: {
                fromPageCode: '173',
                fromPathCode: '003156'
            }
        },
        [GroupKey.ChannelKemuAll]: {
            videoUrl: CAR_SESSION500_VIDEO,
            entrance: 'kemu-all',
            stat: {
                fromPageCode: '174',
                fromPathCode: '003157'
            }
        }
    }
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;

            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    get qaKey() {
        return qaKeyMap[this.nowGoodInfo.groupKey] || 'qaKey1';
    }

    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;

        this.state = {
            isHubei: true,
            kemu,
            tiku,
            tabIndex: -1,
            goodsInfoPool: [],
            standbyPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            // 是否展示下方的购买部分(科目4的时候下方的按钮固定，不用关系滚动)
            showFooter: kemu === KemuType.Ke4,
            // 滚动距离
            prevScrollTop: 0,
            showSignModal: false
        };
    }

    async getInitialState() {
        let kemu = +URLCommon.kemu;
        const urlactiveGroupKey = URLParams.activeGroupKey;
        let tabIndex = 0;
        const goodsInfoPool: GoodsInfo[] = [];
        const standbyPool: GoodsInfo[] = [];
        const { strategy } = await getAbtest(URLCommon.tiku);

        // 兼容客户端从科二科三的页面进入时无法购买
        kemu = (kemu === 0 || kemu === 2 || kemu === 3) ? 1 : kemu;

        if (URLCommon.is3DSingle) {
            if (kemu === 1) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe1D3
                } as GoodsInfo);
            } else {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe4D3
                } as GoodsInfo);
            }

        } else if (kemu === 1) {
            if (strategy[ABTestKey.key29] === ABTestType.B) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe1Month 
                } as GoodsInfo);
            } else {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe1
                } as GoodsInfo);
            }

            goodsInfoPool.push({
                groupKey: GroupKey.ChannelKe1Ke4Group
            } as GoodsInfo);

            goodsInfoPool.push({
                groupKey: GroupKey.ChannelKemuAll
            } as GoodsInfo);

            if (strategy[ABTestKey.key29] === ABTestType.B) {
                standbyPool.push({
                    groupKey: GroupKey.ChannelKe1
                } as GoodsInfo);
            }

        } else if (kemu === 4) {
            if (strategy[ABTestKey.key29] === ABTestType.B) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe4Month
                } as GoodsInfo);
            } else {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe4
                } as GoodsInfo);
            }

            goodsInfoPool.push({
                groupKey: GroupKey.ChannelKe4Short
            } as GoodsInfo);

            if (strategy[ABTestKey.key29] === ABTestType.B) {
                standbyPool.push({
                    groupKey: GroupKey.ChannelKe4
                } as GoodsInfo);
            }
        }

        // 根据地址栏中的activeGroupKey获取对应的tabIndex，此tabIndex优先级最高
        if (urlactiveGroupKey) {
            goodsInfoPool.forEach((item, index) => {
                if (item.groupKey === urlactiveGroupKey) {
                    tabIndex = index;
                }
            });
        }

        this.state.tabIndex = tabIndex;
        this.state.goodsInfoPool = goodsInfoPool;
        this.state.standbyPool = standbyPool;
    }
    async didMount() {
        await this.getInitialState();

        setPageName(pageNameMap[this.nowGoodInfo.groupKey]);
        trackEvent({
            eventId: 'debug',
            actionType: '触发',
            actionName: '加载'
        });

        await this.getGoodInfo();

        setPageName(pageNameMap[this.nowGoodInfo.groupKey]);
        // 页面进出时长打点
        trackPageLoad();

        // 判断是否是湖北
        isHubei().then(isHubei => {
            this.setState({
                isHubei
            });
        });

        // app代理方法
        this.appEventProxy();

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: this.state.goodsInfoPool[this.state.tabIndex].groupKey
        });
    }
    appEventProxy() {

        // 可能是半截页面打开，所以设置一下全屏
        setEmbeddedHeight(0);

        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        // 退出当前tab的打点
        this.leavePageCall();

        // 回到滚动的顶部
        scrollTop(document.querySelector('.page-car .body-panel'));

        // 暂停所有视频
        pauseAllVideos();

        this.setState({
            tabIndex
        }, async () => {
            this.children.sendKe2Dialog.show({ type: 'autoClose' });

            setPageName(pageNameMap[this.nowGoodInfo.groupKey]);

            trackPageShow();

            this.setPageInfo();

        });

    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const { kemu } = this.state;
            const prevScrollTop = e.refTarget.scrollTop;
            // 科四没有中间的切换，不做隐藏
            if (kemu !== 4) {
                if (prevScrollTop >= 200) {
                    this.setState({
                        showFooter: true
                    });
                } else {
                    this.setState({
                        showFooter: false
                    });
                }
                this.setBuyBottom();
            }
            this.setState({
                prevScrollTop
            });
        }, 100);
    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const fragmentName1 = '底部吸底按钮';
        const { showFooter, tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        let bottomType: typeCode = typeCode.type4;

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });

        if (!showFooter) {
            this.children.buyButton.hideButton();
            return;
        }
        // 全科并且有活动的时候按钮不同
        if (nowGoodInfo.groupKey === GroupKey.ChannelKemuAll && nowGoodInfo.inActivity) {
            bottomType = typeCode.type5;
        }

        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1
                });
                break;
            case typeCode.type5:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${nowGoodInfo.inActivity.discountedPrice}元`,
                    price: this.showPrice,
                    originalPrice: '日常价￥' + nowGoodInfo.inActivity.preDiscountPrice,
                    fragmentName1
                });
                break;
            default:
                break;
        }
    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const newGoodsPool = [];
        const groupKeys = this.state.standbyPool.concat(this.state.goodsInfoPool).map(item => item.groupKey);
        const standbyGoodsCount = this.state.standbyPool.length;

        console.log('000', performance.now());
        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            const [standbyGoodsListInfo, normalGoodsListInfo] = [goodsListInfo.slice(0, standbyGoodsCount), goodsListInfo.slice(standbyGoodsCount)];

            normalGoodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    jump.replace(BUYED_URL);
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            if (Platform.isZhiHui && URLCommon.kemu === KemuType.Ke1) {
                tabIndex = 1;
            }

            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }
            // showPage用来控制模块的展示， 先渲染需要渲染的tabPage，加快首次渲染速度
            newGoodsPool[tabIndex].showPage = true;
            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool,
                standbyPool: standbyGoodsCount ? (() => {
                    return [
                        {
                            ...newGoodsPool[0],
                            tempName: '连续包月'
                        },
                        ...standbyGoodsListInfo.map(goodInfo => ({
                            ...goodInfo,
                            tempName: '半年卡'
                        }))
                    ];

                })() : []
            }, async () => {
                this.children.sendKe2Dialog.show({ type: 'autoClose' });
            });

            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await Promise.all([
                    this.getCoupon(goodsListInfo),
                    this.getLabel(goodsListInfo),
                    this.getComparePrice(goodsListInfo)
                ]);

                couponAnimate({
                    couponTargetDomSelect: '.body-panel > .show .coupon-position-middle',
                    compareTargetDomSelect: `.body-panel > .show .tab-cards .${newGoodsPool[2]?.groupKey}`,
                    couponData: this.nowCouponInfo,
                    compareData: this.state.comparePricePool[newGoodsPool[2]?.groupKey],
                    compareGoodsData: newGoodsPool[2],
                    goodsData: this.nowGoodInfo
                });
                this.setPageInfo();

                setTimeout(() => {
                    this.setState({ showSignModal: true });
                }, 500);
            }, 60);

            // 500ms后再渲染其他tabPage，
            setTimeout(() => {
                newGoodsPool.forEach(item => {
                    item.showPage = true;
                });

                this.setState({
                    goodsInfoPool: newGoodsPool
                });

            }, 500);
        });
    }
    getCoupon = async (goodsInfoPool: GoodsInfo[]) => {
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel(goodsInfoPool: GoodsInfo[]) {
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice(goodsInfoPool: GoodsInfo[]) {
        await GroupComparePrice({ groupKeys: goodsInfoPool.map(item => item.groupKey).join(',') }).then(comparePricePool => {
            for (const k in comparePricePool) {
                const item = comparePricePool[k];
                comparePricePool[k] = {
                    diffPrice: item.savePrice,
                    allPrice: item.allPrice,
                    groupItems: item.groupItems
                };
            }
            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
        }).catch(async (err) => {
            console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    goAuth = async (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
    payBtnCall = (e) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');

        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });
        }
    }
    // 退出页面的回调
    async goBackPage() {
        const { tabIndex, goodsInfoPool, kemu, labelPool } = this.state;
        const nowGoodInfo = goodsInfoPool[tabIndex];

        if (this.children.buyDialogOrderSign.getStatus()) {
            this.children.buyDialogOrderSign.close();
            return;
        }

        if (await hesitateUserPersuade()) {
            return;
        }

        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: nowGoodInfo,
                groupKey: nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                title: `真的要放弃${nowGoodInfo.name}吗？`,
                txt1: '懒人必备',
                txt2: '省不少时间',
                txt3: '后悔开晚了',
                txt4: '简单好记',
                tag: {
                    text: labelPool[nowGoodInfo.groupKey]?.label
                },
                kemu
            }).then(payType => {
                if (payType === false) {
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            webClose();
        }
    }
    backCall = () => {
        this.goBackPage();
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    // 离开当前页面
    leavePageCall = () => {
        // 退出当前页面的打点
        setPageName(pageNameMap[this.nowGoodInfo.groupKey]);
        trackExit();
    }
    closeSignModal = () => {
        this.setPageInfo();
    }
    // 科四切换商品
    changeGoods = (goodsInfo) => {
        const { goodsInfoPool } = this.state;
        const tabIndex = 0;
        goodsInfoPool[tabIndex] = goodsInfo;

        this.setState({
            goodsInfoPool
        });
        if (!this.children.buyDialogOrderSign?.state.show) {
            this.setPageInfo();
        }
        this.children.sendKe2Dialog.show({ type: 'autoClose' });
    }
}
