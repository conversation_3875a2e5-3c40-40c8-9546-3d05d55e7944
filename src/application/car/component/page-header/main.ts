import { URLCommon } from ':common/env';
import { GoodsInfo, GroupKey } from ':store/goods';
import { getPromitionExtra } from ':store/promotion';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { getSwallowConfig } from ':store/chores';

interface Props {
    type?: string
    uiConfig: {
        bgc?:string
        img?: string
        video?: string 
        hasConfigImg?: boolean  
        // 是否把iconlist变成透明
        transparent?: boolean
    }
    goodsInfo: GoodsInfo;
    // upgrade: boolean,
    iconList?: any[],
    goAuth?(any),
}
export default class PageHeader extends Component<any, Props> {
    get isKemuall() {
        return this.props.type?.includes('kemuall');
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.props = {
            type: '',
            uiConfig: {},
            goodsInfo: {} as GoodsInfo
        };

        this.state = {
        };

    }

    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    willReceiveProps() {
        return true;
    }
}
