<import name="style" content="./main" />

<import name="myVideo" content=":component/myVideo/main" />

<div class="ipad-box" style="background-color: {{props.uiConfig.bgc || '#231d1d'}};">
    <div class="phone-box">
        <div
            class="page-head-banner"
            style="background-image: url({{props.uiConfig.img}})"
        >
            <div class="video-box">
                <sp:if value="props.uiConfig.video">
                    <com:myVideo src="{{props.uiConfig.video}}"></com:myVideo>
                </sp:if>
            </div>

            <!-- 全科 并且不是升级并且没有活动并且没有配置图-->
            <sp:if
                value="self.isKemuall && !props.goodsInfo.upgrade && !props.goodsInfo.inActivity && !props.uiConfig.hasConfigImg"
            >
                <div class="kemuall-icon-box">
                    <div class="alone-box">
                        <div class="tip-price">
                            单独购买价{{props.buyPrice}}元
                        </div>
                        <sp:each for="props.iconList">
                            <div
                                class="alone-item"
                                sp-on:click="goAuth"
                                data-uniqkey="{{$value.uniqkey}}"
                            >
                                <div
                                    class="image-box alone-img"
                                    style="background-image: url({{$value.icon || 'https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/icon_01.png'}});"
                                ></div>
                                <div class="alone-txt">{{$value.name}}</div>
                            </div>
                        </sp:each>
                    </div>
                    <div
                        class="give-box"
                        sp-on:click="goAuth"
                        data-uniqkey="{{props.give.uniqkey}}"
                    >
                        <div class="tip-price">赠送</div>
                        <div
                            class="give-img image-box"
                            style="background-image: url({{props.give.icon}});"
                        ></div>
                        <div class="give-txt">{{props.give.name}}</div>
                    </div>
                </div>
                <div class="sign-dec">
                    温馨提示：点击VIP权益图标可查看详细的会员特权！
                </div>
                <sp:else />
                <!-- 有活动图片(不能判断是否有活动，因为可能有活动但是没有活动图)并且不能升级的时候或者手动设置为透明的时候需要变成透明的 -->
                <div
                    class="icon-list {{URLCommon.isElder?'elder':''}}
                    {{((props.uiConfig.hasConfigImg && !props.goodsInfo.upgrade) || props.uiConfig.transparent)?'transparent':''}}
                    {{props.isElderKe1ke4?'elder-ke1ke4':''}}"
                >
                    <sp:each for="props.iconList">
                        <div
                            class="icon"
                            sp-on:click="goAuth"
                            data-uniqkey="{{$value.uniqkey}}"
                        >
                            <img src="{{$value.icon}}" />
                            <div class="dec-box">
                                <span>{{#$value.dec}}</span>
                                <sp:if value="$value.dec">
                                    <i>{{$value.dec1}}</i>
                                </sp:if>
                            </div>
                            <sp:if value="$value.tip">
                                <span
                                    class="icon-tip"
                                    style="width:{{$value.tipWidth}}px;background-image:url({{$value.tip}}); {{$value.tipHeight?('height:' + $value.tipHeight + 'px'):''}}"
                                ></span>
                            </sp:if>
                        </div>
                    </sp:each>
                </div>
            </sp:if>
        </div>
    </div>
</div>
