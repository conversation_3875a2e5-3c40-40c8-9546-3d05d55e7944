.page-head-banner {
    padding-top: 95px;
    height: 431px;
    position: relative;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #000;

    .image-box {
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center center;
    }

    .video-box {
        width: 345px;
        height: 194px;
        border-radius: 6px;
        overflow: auto;
        margin: 0 auto;
    }

    .top-img {
        width: 100%;
        height: 211px;
        background: url(https://web-resource.mc-cdn.cn/minprogram/jkbd-minprogram/<EMAIL>)
            no-repeat center center/cover;
    }

    // 非全科
    .icon-list {
        display: flex;
        justify-content: space-around;
        position: absolute;
        width: 100%;
        top: 330px;
        min-height: 80px;

        &.transparent {
            opacity: 0;
        }

        .icon {
            position: relative;
            display: flex;
            align-items: center;
            flex-direction: column;
            img {
                width: 37px;
                height: 36px;
                display: block;
                margin: 0 auto;
            }

            .dec-box {
                text-align: center;

                span {
                    color: #ffffff;
                    font-size: 13px;
                    line-height: 18px;
                    padding-top: 6px;
                    white-space: nowrap;
                    display: block;
                }

                i {
                    color: rgba(255, 255, 255, 0.7);
                    font-size: 12px;
                    line-height: 18px;
                    padding-top: 2px;
                    white-space: nowrap;
                    display: block;
                }
            }

            .icon-tip {
                position: absolute;
                top: 0;
                right: 0;
                transform: translate(50%, -60%);
                height: 19px;
                background-position: center;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                box-sizing: border-box;
            }
        }

        // 老年版
        &.elder {
            &.elder-ke1ke4 {
                .icon {
                    .dec-box {
                        span {
                            font-size: 13px;
                        }
                    }
                    &:last-child {
                        position: relative;
                        left: -5px;
                    }
                }
            }
            .dec-box {
                span {
                    font-size: 15px;
                }

                i {
                    font-size: 14px;
                }
            }
        }
    }

    // 全科
    .kemuall-icon-box {
        box-sizing: border-box;
        width: 100%;
        padding: 0 10px;
        display: flex;
        justify-content: space-between;
        position: absolute;
        bottom: 30px;

        .tip-price {
            position: absolute;
            top: 0;
            left: 0;
            transform: translate(0, -50%);
            height: 20px;
            padding: 0 7px;
            background: #ed391a;
            border-radius: 5px 0px 8px 0px;
            font-size: 12px;
            color: white;
            display: flex;
            align-items: center;
        }

        .alone-box {
            box-sizing: border-box;
            width: 270px;
            height: 92px;
            background: url(../images/2.png) no-repeat;
            background-size: 100% 100%;
            position: relative;
            display: flex;
            justify-content: space-around;
            align-items: center;

            &:after {
                content: "";
                position: absolute;
                z-index: 10;
                width: 21px;
                height: 21px;
                top: 50%;
                right: 0;
                transform: translate(15px, -50%);
                background: url(../images/1.png) no-repeat center center/cover;
            }

            .alone-item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .alone-img {
                    width: 26px;
                    height: 26px;
                }

                .alone-txt {
                    margin-top: 11px;
                    font-size: 12px;
                    color: #edc1a8;
                    text-align: center;
                }
            }
        }

        .give-box {
            box-sizing: border-box;
            width: 78px;
            height: 92px;
            background: url(../images/3.png) no-repeat;
            background-size: 100% 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;

            .give-img {
                width: 26px;
                height: 26px;
            }

            .give-txt {
                margin-top: 11px;
                font-size: 12px;
                color: #edc1a8;
                text-align: center;
            }
        }
    }

    .sign-dec {
        position: absolute;
        width: 100%;
        bottom: 10px;
        font-size: 12px;
        color: #edc1a8;
        text-align: center;
    }
}
