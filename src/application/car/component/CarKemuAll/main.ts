/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */
import { promiseIconList, promiseList } from ':common/features/promise';
import { QK_VIDEO } from ':common/navigate';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { KemuType, URLCommon } from ':common/env';
import { getDivisionInfo, getSwallowConfig } from ':store/chores';
import { mergeObjects } from ':common/utils';

interface State {
    give: any,
    divisionInfo: any
    headInfo: any
}
interface Props {
    goodsList: GoodsInfo[],
    currentIndex: number,
    goodsInfo: GoodsInfo,
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    get iconList() {
        // pageheader的商品列表
        let iconList;

        // 不能升级并且没有活动的权益并且没有配置图片
        if (!this.props.goodsInfo.upgrade && !this.props.goodsInfo.inActivity && !this.headUiConfig.hasConfigImg) {
            iconList = [
                {
                    icon: promiseIconList.k1vip,
                    uniqkey: promiseList.k1vip,
                    name: '科一vip'
                },
                {
                    icon: promiseIconList.k2vip,
                    uniqkey: promiseList.k2vip,
                    name: '科二vip'
                },
                {
                    icon: promiseIconList.k3vip,
                    uniqkey: promiseList.k3vip,
                    name: '科三vip'
                },
                {
                    icon: promiseIconList.k4vip,
                    uniqkey: promiseList.k4vip,
                    name: '科四vip'
                }
            ];
        } else {
            iconList = [
                {
                    icon: promiseIconList.bxbd,
                    uniqkey: promiseList.bxbd,
                    dec: '必学榜单',
                    tip: 'https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/jk-tag-zhiboke.png',
                    tipWidth: 37,
                    tipHeight: 15
                },
                {
                    icon: promiseIconList.k1vip,
                    uniqkey: promiseList.k1vip,
                    dec: '科一VIP'
                },
                {
                    icon: promiseIconList.k2vip,
                    uniqkey: promiseList.k2vip,
                    dec: '科二VIP',
                    tip: 'https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/jk-tag-vipfangzhen.png',
                    tipWidth: 30,
                    tipHeight: 15
                },
                {
                    icon: promiseIconList.k3vip,
                    uniqkey: promiseList.k3vip,
                    dec: '科三VIP'
                },
                {
                    icon: promiseIconList.k4vip,
                    uniqkey: promiseList.k4vip,
                    dec: '科四VIP'
                }
            ];
        }

        return iconList;

    }
    get goodsListBg() {
        return {
            [GroupKey.ChannelKe1]: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/05/12/8ac81d76d2f34e1f8517598f51f8e230.png',
            [GroupKey.ChannelKe2]: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/05/12/63ed7f508dba43ddb2235f2a08deb53e.png',
            [GroupKey.ChannelKe3Group]: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/05/12/106f6329aa2d43a68436de6b70111711.png',
            [GroupKey.ChannelKe4]: 'http://exam-room.mc-cdn.cn/exam-room/2022/07/05/12/a89d30c14ff746f4a50014fc21eace68.png'
        };
    }

    get headUiConfig() {
        const { goodsList, currentIndex, goodsInfo } = this.props;
        const { headInfo } = this.state;
        const info = goodsInfo || goodsList[currentIndex];

        let config: any = {
            img: 'https://web-resource.mc-cdn.cn/web/vip/car/quanke.png',
            video: QK_VIDEO
        };
        // 目前当活动处理，为了方便还原， 所以上面声明config代码没有删除
        config = {
            img: 'http://exam-room.mc-cdn.cn/exam-room/2024/07/30/18/becade192d454e2b9ea501c52c661396.png',
            bgc: '#2AA6FE',
            video: QK_VIDEO,
            hasConfigImg: true,
            transparent: true
        };
    
        if (headInfo) {
            mergeObjects(config, {
                ...headInfo,
                hasConfigImg: true
            });
        }

        if (info.giftPromotion?.promotionStatus) {
            mergeObjects(config, {
                hasConfigImg: true
            });
        }

        if (info.headConfig) {
            mergeObjects(config, {
                img: info.headConfig.img,
                bgc: info.headConfig.bgc,
                video: info.headConfig.video,
                hasConfigImg: !!info.headConfig.img,
                transparent: !!info.headConfig.img
            });
        }

        return config;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            // 赠品
            give: {
                uniqkey: promiseList.qyzbk,
                icon: promiseIconList.qyzbk,
                name: '千元直播课'
            },
            divisionInfo: {},
            headInfo: {}
        };

    }
    didMount(): void {
        this.getDivision();
        this.getHeadInfo();
    }
    async getHeadInfo() {
        const headInfo = await getSwallowConfig({
            key: 'jk_quanke_page'
        });
        
        this.setState({
            headInfo
        });

    }
    getDivision() {
        getDivisionInfo({
            type: 20
        }).then(info => {
            this.setState({
                divisionInfo: info
            });
        });
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
