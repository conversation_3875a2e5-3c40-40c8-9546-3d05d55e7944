.panel-carkemuall {
    background: #f2f2f2;
    position: relative;
    img {
        display: block;
        width: 100%;
        margin: 0 auto;
    }

    .card-box {
        background: linear-gradient(#231d1d 50%, #f2f2f2 51%, #f2f2f2 100%);

        &.activity {
            background: linear-gradient(#222222 50%, #f2f2f2 51%, #f2f2f2 100%);
        }
    }

    .buy-btn-box {
        position: relative;
        width: 343px;
        height: 49px;
        margin: 24px auto 15px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/btn1.png) no-repeat center center/100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .buy-btn {
            font-size: 19px;
            color: white;
        }

        .label {
            position: absolute;
            right: 0px;
            top: -7px;
            background: linear-gradient(360deg, #F9C39F 0%, #FEDEC7 100%);
            border-radius: 0 10px 0 8px;
            font-size: 12px;
            font-weight: 500;
            color: #622604;
            transform: scale3d(0.9, 0.9, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2px 9px;
            font-weight: 500;
        }

    }

    .protocol-box {
        margin-bottom: 20px;
        font-size: 13px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
    }

    // 活动按钮
    .activity-buy-btn {
        position: relative;
        z-index: 10;
        height: 70px;
        background: linear-gradient(90deg, #F95B38 0%, #E83E30 100%);
        display: flex;
        padding: 5px 10px;
        box-sizing: border-box;
        margin-bottom: 20px;
        margin-top: -10px;

        .div1 {
            flex: 1;

            .p1 {
                display: flex;
                align-items: center;
                padding-top: 4px;
            }

            .sp1 {
                width: 78px;
                height: 18px;
                background: url(../images/1.png) no-repeat;
                background-size: 100% 100%;
            }

            .sp2 {
                font-size: 11px;
                color: #FFFFFF;
                line-height: 16px;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.3) 100%);
                border-radius: 8px;
                padding: 1px 5px 0 5px;
                transform: scale3d(0.9, 0.9, 0.9);
                margin-left: 5px;
            }

            .p2 {
                display: flex;
                align-items: center;
                padding-top: 5px;
            }

            .sp3 {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);

                .i1 {
                    font-size: 12px;
                    color: rgba(255, 255, 255, 0.6);
                    transform: scale3d(0.9, 0.9, 0.9);
                }

                .b1 {
                    font-size: 16px;
                    font-weight: bold;
                    color: #ffffff;
                    transform: scale3d(0.9, 0.9, 0.9);
                }
            }

            .sp4 {
                background: linear-gradient(360deg, #FAB78A 0%, #FFDFC3 100%);
                border-radius: 13px;
                font-size: 12px;
                font-weight: bold;
                color: #B4440F;
                line-height: 17px;
                padding: 5px 10px;
                margin-left: 12px;

                .i2 {
                    font-size: 12px;
                    transform: scale3d(0.85, 0.85, 0.85);
                }

                .b2 {
                    font-size: 18px;
                }
            }
        }

        .div2 {
            width: 128px;
            height: 60px;
            background: url(../images/2.png) no-repeat;
            background-size: 100% 100%;
            box-sizing: border-box;
            padding: 10px 0 0 10px;

            .p3 {
                font-size: 15px;
                font-weight: bold;
                color: #6F2117;
                line-height: 21px;
                text-align: center;
            }

            .p4 {
                font-size: 15px;
                font-weight: bold;
                color: #6F2117;
                line-height: 21px;
                text-align: center;
            }

            .count-content {
                text-align: center;

                .count {
                    color: #6F2117;
                    font-size: 15px;
                    font-weight: 600;
                }
            }
        }
    }

    .goods-describe {
        padding: 0 15px;

        .describe-title {
            font-size: 25px;
            line-height: 34px;
            text-align: center;
            font-weight: bold;

            .price {
                color: #ED391A;
            }
        }

        .describe-dec {
            text-align: center;
            margin-top: 2px;
            font-size: 12px;
            color: #6B6870;

            span {
                color: #ED391A;
            }
        }

        .describe-content {
            width: 345px;
            height: 445px;
            margin: 20px auto 40px;
            background: url(../images/<EMAIL>) no-repeat center center/cover;
            padding-top: 40px;

            .goods-list {
                display: flex;
                padding: 0 10.5px;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;

                .goods-item {
                    margin-top: 10px;
                    width: 157px;
                    height: 145px;
                    position: relative;

                    .price {
                        position: absolute;
                        right: 15px;
                        top: 22px;
                        color: #986C59;
                        font-size: 17px;
                        font-weight: bold;

                        &.bought::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 50%;
                            transform: translate(-50%, -100%);
                            width: 41px;
                            height: 18px;
                            background: url(../images/<EMAIL>) no-repeat center center/cover;
                        }
                    }
                }
            }

            .exclusive {
                margin: 10px auto 0;
                width: 325px;
                height: 74px;
                background: url(../images/<EMAIL>) no-repeat center center/cover;
            }
        }
    }

    .kemuall-1 {
        position: relative;
        padding: 0 15px 40px 15px;
    }

    .kemuall-2 {
        position: relative;
        padding: 0 15px;
    }

    .kemuall-3 {
        position: relative;
        padding: 0 15px;
        margin-top: 25px;

        .img {
            width: 300px;
        }

        .wrap {
            background: #ffffff;
            border-radius: 10px;
            padding: 0 20px 30px 20px;
            margin-top: 30px;

            .p0 {
                height: 36px;
                background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/sketchb0455919-2c43-47db-b7d7-ad5bc6838ae8.png) no-repeat center;
                background-size: 136px 36px;
                color: #FFFFFF;
                font-size: 16px;
                text-align: center;
                line-height: 36px;
            }

            .p1 {
                color: #666666;
                font-size: 16px;
                text-align: center;
                line-height: 22px;
                padding-top: 7px;
            }

            .p2 {
                padding: 10px 0;
            }

            .div1 {
                display: flex;
                align-items: baseline;
                justify-content: space-around;

                p {
                    img {
                        width: 46px;
                        height: 46px;
                    }

                    span {
                        width: 60px;
                        display: block;
                        font-size: 13px;
                        color: #333333;
                        text-align: center;
                        padding-top: 8px;
                        line-height: 1.5;
                    }
                }
            }
        }
    }

    .kemuall-4 {
        position: relative;
        padding: 0 15px;
        margin-top: 25px;

        .img {
            width: 300px;
        }

        .wrap {
            background: #ffffff;
            border-radius: 10px;
            padding: 0 20px 30px 20px;
            margin-top: 30px;

            .p0 {
                height: 36px;
                background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/sketchb0455919-2c43-47db-b7d7-ad5bc6838ae8.png) no-repeat center;
                background-size: 136px 36px;
                color: #FFFFFF;
                font-size: 16px;
                text-align: center;
                line-height: 36px;
            }

            .p1 {
                color: #666666;
                font-size: 15px;
                text-align: center;
                line-height: 22px;
                padding-top: 7px;
            }

            .p2 {
                padding: 10px 0;
            }

            .div1 {
                display: flex;
                align-items: baseline;
                justify-content: space-around;

                p {
                    img {
                        width: 46px;
                        height: 46px;
                    }

                    span {
                        width: 100px;
                        display: block;
                        font-size: 13px;
                        color: #333333;
                        text-align: center;
                        padding-top: 8px;
                        line-height: 1.5;
                    }
                }
            }
        }
    }

    .kemuall-5 {
        position: relative;
        padding: 0 15px;
        margin-top: 30px;

        .img {
            width: 325px;
            height: 57px;
        }
    }

    .kemuall-9 {
        padding: 30px 15px 0 15px;
    }

}
