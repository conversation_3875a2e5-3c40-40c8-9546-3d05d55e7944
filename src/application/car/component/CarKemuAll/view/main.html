<import name="style" content="./main" />
<import name="Count" content=":component/count/main" />
<import name="pageHeader" content=":application/car/component/page-header/main" />
<import name="PriceTag" content=":component/priceTag/main" />
<import name="cards" content=":application/car/component/cards/main" />

<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="giftText" content=":component/giftText/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />
<import name="buchangTips" content=":component/buchangTips/main" />
<import name="SendVipEnter" content=":component/sendVipEnter/main" />

<div class="panel-carkemuall">
    <com:pageHeader type="car-kemuall" goAuth="{{props.goAuth}}"
        uiConfig="{{self.headUiConfig}}"
        iconList="{{self.iconList}}" buyPrice="{{props.comparePricePool[props.groupKey].allPrice}}"
        give="{{state.give}}" goodsInfo="{{props.goodsInfo}}" 
        />
    <com:buchangTips />
    <!-- 有切换才有这个 -->
    <sp:if value="{{props.tabChange}}">
        <div class="ipad-box"
            style="background: linear-gradient({{self.headUiConfig.bgc}} 50%, #f2f2f2 51%, #f2f2f2 100%);">
            <div class="card-box phone-box {{props.goodsInfo.inActivity?'activity':''}}"
                style="background: linear-gradient({{self.headUiConfig.bgc}} 50%, #f2f2f2 51%, #f2f2f2 100%);">
                <com:cards comparePricePool="{{props.comparePricePool}}" goodsList="{{props.goodsList}}"
                    tabChange="{{props.tabChange}}" tabIndex="{{props.currentIndex}}" />
            </div>
        </div>
    </sp:if>

    <div class="ipad-box">
        <div class="phone-box">
            <sp:if value="props.goodsInfo.inActivity && !props.goodsInfo.upgrade && props.showActiveBtn">
                <div class="activity-buy-btn" sp-on:click="pay" data-fragment="主图">
                    <div class="div1">
                        <p class="p1">
                            <span class="sp1"></span>
                            <span
                                class="sp2">比分开买节省{{props.comparePricePool[props.goodsInfo.groupKey].diffPrice}}元</span>
                        </p>
                        <p class="p2">
                            <span class="sp3">
                                日常价
                                <i class="i1">&nbsp; ¥ &nbsp;</i>
                                <b class="b1">{{props.goodsInfo.inActivity.preDiscountPrice}}</b>
                            </span>
                            <span class="sp4">
                                特惠价
                                <i class="i2">&nbsp; ¥ &nbsp;</i>
                                <b class="b2">{{props.payPrice || '--'}}</b>
                            </span>
                        </p>
                    </div>
                    <div class="div2">
                        <div class="p3">限时特惠</div>
                        <div class="p4">
                            <com:Count name="Count" startTime="{{props.goodsInfo.inActivity.discountStartTime}}"
                                endTime="{{props.goodsInfo.inActivity.discountEndTime}}" />
                        </div>
                    </div>
                </div>
                <sp:else />
                <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                    <div class="buy-btn">
                        确认协议并支付 ¥{{props.payPrice || props.payPrice ===
                        0?props.payPrice : '--'}}
                    </div>
                    <!-- <com:giftText/> -->
                    <com:PriceTag class="noScale" goodsInfo="{{props.goodsInfo}}"
                        comparePriceMap="{{props.comparePricePool}}" labelMap="{{props.labelPool}}" />
                </div>
                <div class="protocol-box">
                    <com:middleProtocol groupKey="{{props.groupKey}}" couponPool="{{props.couponPool}}" />
                </div>
            </sp:if>
            
            <sp:if value="{{state.divisionInfo.imageUrl}}">
                <img style="width: 284px;height: 2px;display: block;margin: 15px auto;"
                    src="{{state.divisionInfo.imageUrl}}" alt="">
            </sp:if>

            <com:SendVipEnter name="center" entranceCode="kemuall_index_center" position="center" />
            <com:SendVipEnter name="right" entranceCode="kemuall_index_right" position="right" />

            <div class="kemuall-1" key="kemuall-1">
                <img src="http://exam-room.mc-cdn.cn/exam-room/2023/12/19/11/074b108ea98640b796859a0b25347f71.png" />
            </div>
            <div class="kemuall-2" key="kemuall-2">
                <img src="http://exam-room.mc-cdn.cn/exam-room/2023/12/29/10/430c886d75f848098afe0c5c7abe6cd5.png" />
            </div>
            <div class="kemuall-3" key="kemuall-3">
                <img src="http://exam-room.mc-cdn.cn/exam-room/2023/12/29/10/ca6560a26d0142349803a6ca0981b298.png" />
            </div>
             <div class="kemuall-3" key="kemuall-3-1">
                 <img src="http://exam-room.mc-cdn.cn/exam-room/2023/12/29/10/9c293ed2d55f4d36a18f3ff812101aa6.png" />
             </div>
            <div class="kemuall-4" key="kemuall-4">
                <img src="http://exam-room.mc-cdn.cn/exam-room/2023/12/29/10/a25f35d8bb164f6c8d5528c9a33ad3db.png" />
            </div>

            <div class="kemuall-5" key="kemuall-5">
                <img src="http://exam-room.mc-cdn.cn/exam-room/2023/12/29/10/3b0958057a284cd18d15ba65593802cc.png" />
            </div>

           <div sp-on:click="goAuth" class="kemuall-9" key="kemuall-9" data-uniqkey="bgbc">
               <img src="http://exam-room.mc-cdn.cn/exam-room/2023/12/29/10/5134ba509c5b448fa052a3ccc58dbee7.png" />
           </div>
           
            <com:commonQuestion type="2" />
        </div>
    </div>
</div>
