<import name="style" content="./main" />
<import name="Count" content=":component/count/main" />

<div>
    <div class="tab-cards">
        <div class="cards">
            <sp:if value="props.goodsList.length == 2">
                <sp:each for="{{props.goodsList}}">
                    <div
                        class="card {{props.tabIndex===$index?'active':''}} {{$value.groupKey}} {{props.standbyPool.length?'select-goods':''}}"
                        data-idx="{{$index}}"
                        sp-on:click="onTabClick"
                    >
                        <sp:if value="$value.inActivity.discountedPrice">
                            <div class="time-tip">
                                <span class="sp"
                                    >立减{{$value.inActivity.discountedPrice}}元</span
                                >
                                <com:Count
                                    name="Count"
                                    startTime="{{$value.inActivity.discountStartTime}}"
                                    endTime="{{$value.inActivity.discountEndTime}}"
                                ></com:Count>
                            </div>
                        </sp:if>
                        <div class="div1">
                            <p class="p1">{{$value.name}}</p>
                            <p class="p2">
                                <i class="i">¥</i
                                ><span class="sp">{{$value.payPrice}}</span
                                ><b class="b">/{{$value.validDays}}天</b>
                                <sp:if
                                    value="$value.inActivity.preDiscountPrice"
                                >
                                    <label class="lab">
                                        日常价¥{{$value.inActivity.preDiscountPrice}}
                                    </label>
                                </sp:if>
                            </p>
                        </div>
                        <div class="div2">
                            <span class="sp">
                                <sp:if value="$index == 0">
                                    考不过补偿50元
                                </sp:if>

                                <sp:if value="$index != 0">
                                    比分开买节省{{props.comparePricePool[$value.groupKey].diffPrice}}元
                                </sp:if>
                            </span>
                        </div>

                        <div skip="true" class="animate-box"></div>
                    </div>
                </sp:each>
            </sp:if>
            <sp:if value="props.goodsList.length == 3">
                <sp:each for="{{props.goodsList}}">
                    <div
                        class="three-card {{props.tabIndex===$index?'active':''}} {{$value.groupKey}} {{props.standbyPool.length?'select-goods':''}}"
                        data-idx="{{$index}}"
                        sp-on:click="onTabClick"
                    >
                        <sp:if value="$value.inActivity.discountedPrice">
                            <div class="time-tip">
                                <span class="sp"
                                    >立减{{$value.inActivity.discountedPrice}}元</span
                                >
                                <com:Count
                                    startTime="{{$value.inActivity.discountStartTime}}"
                                    endTime="{{$value.inActivity.discountEndTime}}"
                                ></com:Count>
                            </div>
                            <sp:elseif
                                value="props.comparePricePool[$value.groupKey]"
                            />
                            <div class="time-tip">
                                <span class="sp"
                                    >立省{{props.comparePricePool[$value.groupKey].diffPrice}}元</span
                                >
                            </div>
                        </sp:if>
                        <div class="info">
                            <div class="name {{$value.name.length > 7?'min-txt':''}}">{{$value.name}}</div>
                            <div class="price-box">
                                <i class="i">¥</i
                                ><span class="sp">{{$value.payPrice}}</span
                                ><b class="b">/{{$value.validDays}}天</b>
                            </div>
                            <div class="other-price">
                                <sp:if value="$index !== 0 && $value.inActivity.preDiscountPrice">
                                    日常价¥{{$value.inActivity.preDiscountPrice}}
                                </sp:if>

                                <sp:if
                                    value="$index !== 0 && (!$value.inActivity || !$value.inActivity.preDiscountPrice)">
                                    分开买¥{{props.comparePricePool[$value.groupKey].allPrice}}
                                </sp:if>
                            </div>
                        </div>
                        <div class="other-info">
                            <span class="sp">
                                <sp:if value="$index == 0">
                                    考不过补偿50元
                                </sp:if>
                                <sp:if value="$index != 0">
                                    比分开买节省{{props.comparePricePool[$value.groupKey].diffPrice}}元
                                </sp:if>
                            </span>
                        </div>

                         <div skip="true" class="animate-box"></div>
                    </div>
                </sp:each>
            </sp:if>
        </div>
    </div>
</div>
