import { GoodsInfo } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { CarType, KemuType, URLCommon } from ':common/env';

interface Props {
    goodsList: GoodsInfo[]
    comparePricePool: any[]
    tiku: any
    tabChange(number)
}
export default class extends Component<any, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
        };
    }
    onTabClick(e) {
        const idx = +e.refTarget.getAttribute('data-idx');

        this.props.tabChange && this.props.tabChange(idx);
    }
    willReceiveProps() {
        return true;
    }
}
