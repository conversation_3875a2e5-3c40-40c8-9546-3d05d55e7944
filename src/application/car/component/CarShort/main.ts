/*
 * main
 *
 * name: xia<PERSON><PERSON>a
 * date: 16/3/24
 */
import { URLCommon } from ':common/env';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { getFourStep } from ':store/chores';
import { promiseIconList, promiseList } from ':common/features/promise';
import jump from ':common/features/jump';

interface State {
    fourStepData: any[],
    curIndex: number,
    iconList: any[]
}
interface Props {
    goAuth?(id: any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            fourStepData: [],
            curIndex: 0,
            iconList: [
                {
                    icon: promiseIconList.jj500t,
                    uniqkey: promiseList.jj500t,
                    dec: '精简500题',
                    dec1: '省时省力'
                },
                {
                    icon: promiseIconList.zskcmn,
                    uniqkey: promiseList.zskcmn,
                    dec: '真实考场模拟',
                    dec1: '高仿真'
                },
                {
                    icon: promiseIconList.kqmj,
                    uniqkey: promiseList.kqmj,
                    dec: '考前秘卷',
                    dec1: '高效冲刺'
                },
                {
                    icon: promiseIconList.bgbc,
                    uniqkey: promiseList.bgbc,
                    dec: '不过补偿',
                    dec1: '查看补偿说明'
                },
                {
                    icon: promiseIconList.jpzbk,
                    uniqkey: promiseList.jpzbk,
                    dec: '精品直播课',
                    dec1: '讲师带学'
                }
            ]
        };
        this.props = {
        };

    }
    willMount() {
        this.getFourStep();
    }
    getFourStep() {
        getFourStep({
            carType: URLCommon.tiku,
            kemu: URLCommon.kemu
        }).then(data => {
            this.setState({
                fourStepData: data
            });
        });
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    goPlay() {
        const { fourStepData, curIndex } = this.state;
        setTimeout(() => {
            jump.navigateTo('http://jiakao.nav.mucang.cn/topLesson/detail', {
                id: fourStepData[curIndex].lessonId 
            });
        }, 10);
    }
    fourStepChangeTab(e) {
        this.setState({
            curIndex: e.refTarget.getAttribute('data-index')
        });
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    willReceiveProps() {
        return true;
    }
}
