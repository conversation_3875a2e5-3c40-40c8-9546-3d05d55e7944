.panel-carshort {
    background: #EBC7A2;
    padding-bottom: 15px;

    img {
        display: block;
        width: 100%;
        margin: 0 auto;
    }

    .head-banner {
        height: 488px;
        background-image: url(../images/1.png);
        background-repeat: no-repeat;
        background-size: cover;
        padding-top: 60px;
        position: relative;

        .img1 {
            width: 358px;
            height: 81px;
            background-image: url(../images/2_1.png);
            background-repeat: no-repeat;
            background-size: cover;
            margin: 0 auto;
        }

        .img2 {
            width: 289px;
            height: 30px;
            background-image: url(../images/3.png);
            background-repeat: no-repeat;
            background-size: cover;
            margin: 0 auto;
        }


        .auth-list {
            display: flex;
            align-items: center;
            justify-content: space-around;
            width: 100%;
            position: absolute;
            top: 350px;

            .auth-item {
                position: relative;
                display: flex;
                align-items: center;
                flex-direction: column;

                img {
                    width: 36px;
                    height: 36px;
                    display: block;
                    margin: 0 auto;
                }

                .dec-box {
                    position: absolute;
                    left: 50%;
                    bottom: 0;
                    transform: translate(-50%, 100%);
                    text-align: center;

                    .dec {
                        color: #ffffff;
                        font-size: 13px;
                        line-height: 18px;
                        padding-top: 6px;
                        white-space: nowrap;
                        display: block;
                    }

                    .dec1 {
                        color: rgba(255, 255, 255, 0.7);
                        font-size: 12px;
                        line-height: 18px;
                        padding-top: 2px;
                        white-space: nowrap;
                        display: block;
                    }
                }
            }
        }
    }



    .divider {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 36px;
        margin: -18px 0;
        position: relative;

        i {
            width: 12px;
            height: 50px;
            background: url(../images/13.png) no-repeat;
            background-size: 100% 100%;
        }
    }

    .k4-1 {
        background: #FFF5DF;
        border-radius: 10px;
        padding: 10px 15px 30px 15px;
        margin: -40px 15px 0 15px;
        position: relative;
        .img1 {
            width: 287px;
            height: 44px;
            background: url(../images/5.png) no-repeat right center;
            background-size: 100% 100%;
            margin: 0 auto;
        }

        .img2 {
            width: 316px;
            height: 165px;
            background: url(../images/10.png) no-repeat right center;
            background-size: 100% 100%;
            margin: 15px auto 0 auto;
        }

        .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 22px;

            label {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 305px;
                height: 49px;
                background: linear-gradient(90deg, #F95B38 0%, #FF703D 100%);
                color: #ffffff;
                font-size: 19px;
                border-radius: 25px;
                position: relative;
                text-align: center;

                .tip {
                    position: absolute;
                    top: -18px;
                    right: 0;
                    font-size: 12px;
                    line-height: 14px;
                    color: #6F2117;
                    background: linear-gradient(90deg, #FFD878 0%, #FFC400 100%);
                    padding: 5px 10px;
                    border-radius: 30px;

                    i {
                        position: absolute;
                        display: block;
                        width: 10px;
                        height: 7px;
                        background-color: red;
                        right: 28px;
                        bottom: 7px;
                        background: url(../images/11.png) no-repeat right center;
                        background-size: 100% 100%;
                    }
                }
            }
        }
    }

    .k4-2 {
        background: #FFF5DF;
        border-radius: 10px;
        padding: 10px 15px 15px 15px;
        margin: 0 15px;

        .img1 {
            width: 293px;
            height: 42px;
            background: url(../images/6.png) no-repeat right center;
            background-size: 100% 100%;
            margin: 0 auto;
        }

        .img2 {
            width: 307px;
            height: 172px;
            border-radius: 7px;
            margin: 20px auto;
            display: flex;
            font-size: 0;
            position: relative;
            overflow: hidden;

            .play {
                position: absolute;
                width: 52px;
                height: 52px;
                left: 50%;
                top: 50%;
                margin-left: -26px;
                margin-top: -26px;
            }
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: space-between;
            padding: 0 5px;

            label {
                width: 140px;
                height: 34px;
                color: #FF6D3C;
                border: 1px solid #FF6D3C;
                border-radius: 20px;
                font-size: 16px;
                line-height: 34px;
                text-align: center;
                font-weight: bold;
                margin-bottom: 10px;

                &.active {
                    color: #ffffff;
                    background-color: #FF6D3C;
                }
            }
        }
    }

    .k4-3 {
        border-radius: 10px;
        padding: 15px 0 0 0;
        margin: 0 15px;

        .img1 {
            width: 311px;
            height: 43px;
            background: url(../images/7.png) no-repeat right center;
            background-size: 100% 100%;
            margin: 0 auto;
        }

        .imgs {
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-top: 20px;

            img {
                width: 346px;
                height: 190px;
            }


            p {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }

            span {
                color: #7C1707;
                font-size: 15px;
                padding-top: 12px;
            }
        }
    }

    .k4-4 {
        background: #FFF5DF;
        border-radius: 10px;
        padding: 10px 15px 15px 15px;
        margin: 20px 15px 15px 15px;

        .img1 {
            width: 267px;
            height: 43px;
            background: url(../images/12.png) no-repeat right center;
            background-size: 100% 100%;
            margin: 0 auto;
        }

        .imgs {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            justify-content: space-around;
            padding-top: 20px;

            img {
                width: 140px;
                height: 133px;
                margin-bottom: 15px;
            }


        }
    }

    .k4-5 {
        background: #FFF5DF;
        border-radius: 10px;
        padding: 10px 15px 15px 15px;
        margin: 15px;

        .img1 {
            width: 209px;
            height: 44px;
            background: url(../images/8.png) no-repeat right center;
            background-size: 100% 100%;
            margin: 0 auto;
        }

        .sec-wenda-w {
            padding: 15px 0 0 0;

            .sec-wenda {
                background: #ffffff;
                border-radius: 10px;
                padding: 0 15px 15px 15px;
                position: relative;
                overflow: hidden;
            }

            .wenda-li {
                padding-top: 15px;
                padding-bottom: 12px;
                border-top: 1px dashed #EAC5A0;

                &:nth-child(1) {
                    border-top: none;
                }

                .div1 {
                    p {
                        color: #333333;
                        font-weight: bold;
                        background: url(../images/q.png) no-repeat left top;
                        background-size: 22px 22px;
                        font-size: 15px;
                        line-height: 22px;
                        padding-left: 30px;
                    }
                }

                .div2 {
                    padding-top: 10px;

                    img {
                        width: 22px;
                        height: 22px;
                        border-radius: 100%;
                        margin: 0;
                    }

                    b {
                        color: #6E6E6E;
                        font-size: 14px;
                        padding-left: 8px;
                        flex: 1;
                    }

                    span {
                        color: #7C1707;
                        font-size: 13px;
                        line-height: 14px;
                        padding: 3px 10px;
                        background: linear-gradient(90deg, #FFC88B 0%, #FFA669 100%);
                        border-radius: 2px;
                    }
                }

                .div3 {
                    color: #666666;
                    font-size: 14px;
                    padding-left: 30px;
                    padding-top: 5px;

                    p {
                        line-height: 1.5;
                        display: -webkit-box;
                        -webkit-line-clamp: 3;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                    }
                }
            }

            .wenda-f1 {
                position: relative;
                display: flex;
                padding-top: 10px;
                text-align: center;
                align-items: center;
                justify-content: center;

                span {
                    display: block;
                    color: #7C1707;
                    background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/arrow.png) no-repeat right center;
                    height: 21px;
                    line-height: 21px;
                    font-size: 15px;
                    padding-right: 12px;
                    background-size: 7px 11px;
                }
            }
        }
    }

}
