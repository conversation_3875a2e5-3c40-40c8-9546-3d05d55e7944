<import name="style" content="./main" />

<import name="wenda" content=":component/wenda/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />


 <div class="panel-carshort">
    <div class="ipad-box">
        <div class="phone-box">
            <div class="head-banner">
                <div class="img1"></div>
                <div class="img2"></div>

                <div class="auth-list">
                    <sp:each for="state.iconList">
                        <div class="auth-item" sp-on:click="goAuth"  data-uniqkey="{{$value.uniqkey}}">
                            <img src="{{$value.icon}}" alt="">
                            <div class="dec-box">
                                <div class="dec">{{$value.dec}}</div>
                                <div class="dec1">{{$value.dec1}}</div>
                            </div>
                        </div>
                    </sp:each>
                </div>
            </div>
            
            <div class="k4-1">
                <div class="img1"></div>
                <div class="img2"></div>
                <div sp-on:click="pay" class="btn buy-btn" data-fragment="主图">
                    <label
                        >{{props.payPrice || '--'}}元立即开通，节省300元
                        <sp:if value="props.labelPool[props.groupKey].label">
                            <span class="tip">{{props.labelPool[props.groupKey].label}}</span>
                        </sp:if>
                    </label>
                </div>
            </div>
            <div class="divider"><i></i><i></i></div>
            <div class="k4-2">
                <div class="img1"></div>
                <div sp-on:click="goPlay" class="img2">
                    <img src="{{state.fourStepData[state.curIndex].imgUrl}}" />
                    <img class="play" src="http://exam-room.mc-cdn.cn/exam-room/2022/02/07/21/5668605b6e884e43b62b42dc035f2ccd.png" />
                </div>
                <div class="tags">
                    <sp:each for="state.fourStepData" value="item" index="i">
                        <label
                            sp-on:click="fourStepChangeTab"
                            class="{{state.curIndex == i ? 'active': ''}}"
                            data-index="{{i}}"
                            >{{item.title}}</label
                        >
                    </sp:each>
                </div>
            </div>
            <div class="k4-3">
                <div class="img1"></div>
                <div class="imgs">
                        <img src="https://web-resource.mc-cdn.cn/web/vip/20.png" alt="">
                </div>
            </div>
            <div class="k4-4">
                <div class="img1"></div>
                <div class="imgs">
                    <img src="http://exam-room.mc-cdn.cn/exam-room/2022/02/07/21/d4c3f3196c1c4b0cabaef1d4607203f9.png" />
                    <img src="http://exam-room.mc-cdn.cn/exam-room/2022/02/07/21/20430d736b2049df97a88b9708d49505.png" />
                    <img src="http://exam-room.mc-cdn.cn/exam-room/2022/02/07/21/6f66d0633cdf4736a7495cb124c5659a.png" />
                    <img src="http://exam-room.mc-cdn.cn/exam-room/2023/02/28/18/c40892a0cc8b4827902242416a9f62db.png" />
                </div>
            </div>
            <div class="k4-5 {{props.isHubei || Platform.isXueTang ? 'hide': 'show'}}">
                <div class="img1"></div>
                <div class="sec-wenda-w">
                    <com:wenda name="wenda" groupKey="{{props.groupKey}}" type="2" />
                </div>
            </div>
            <com:commonQuestion type="8"/>
        </div>
    </div>
    
</div>

