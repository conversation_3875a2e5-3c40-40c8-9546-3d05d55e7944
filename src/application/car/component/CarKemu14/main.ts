/*
 * main
 *
 * name: xiao<PERSON>a
 * date: 16/3/24
 */
import { CarType, KemuType, URLCommon, URLParams } from ':common/env';
import { CAR_KE1_VIDEO, CAR_KE4_VIDEO } from ':common/navigate';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { promiseIconList, promiseList } from ':common/features/promise';
import { getDivisionInfo } from ':store/chores';
import { GoodsInfo } from ':store/goods';
import { mergeObjects } from ':common/utils';

interface State {
    iconList: any[],
    divisionInfo: any
}

interface Props {
    goodsList?: GoodsInfo[]
    currentIndex?: number
    goodsInfo?: GoodsInfo
    noVideo?: boolean
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    get videoIntroduce() {
        // 挂车也用这个组件，但是不能有视频
        if (this.props.noVideo) {
            return '';
        }
        return URLCommon.kemu === KemuType.Ke1 ? CAR_KE1_VIDEO : CAR_KE4_VIDEO;
    }
    get headUiConfig() {
        const { goodsList, currentIndex, goodsInfo } = this.props;
        const info = goodsInfo || goodsList[currentIndex];
        const config: any = {
            img: 'http://exam-room.mc-cdn.cn/exam-room/2024/07/31/10/4d1b621a1064465a8cb55bc29124f9e1.png',
            video: this.videoIntroduce
        };

        if (info.giftPromotion?.promotionStatus) {
            mergeObjects(config, {
                img: 'http://exam-room.mc-cdn.cn/exam-room/2024/12/05/16/d19688a00cb140978d54d501d83a5014.png'
            });
        }
         
        if (info.headConfig) {
            mergeObjects(config, {
                img: info.headConfig.img,
                bgc: info.headConfig.bgc,
                video: info.headConfig.video,
                transparent: !!info.headConfig.img
            });
        }

        return config;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            iconList: [
                {
                    icon: promiseIconList.jj500t,
                    uniqkey: promiseList.jj500t,
                    dec: '精简500题',
                    dec1: '省时省力'
                },
                {
                    icon: promiseIconList.zskcmn,
                    uniqkey: promiseList.zskcmn,
                    dec: '真实考场模拟',
                    dec1: '高仿真'
                },
                {
                    icon: promiseIconList.kqmj,
                    uniqkey: promiseList.kqmj,
                    dec: '考前秘卷',
                    dec1: '高效冲刺'
                },
                {
                    icon: promiseIconList.bgbc,
                    uniqkey: promiseList.bgbc,
                    dec: '不过补偿',
                    dec1: '查看补偿说明'
                }
            ],
            divisionInfo: {}
        };
    }
    didMount(): void {
        this.getDivision();
    }
    getDivision() {
        getDivisionInfo({
            type: 20
        }).then(info => {
            this.setState({
                divisionInfo: info
            });
        });
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
