<import name="style" content="./main" />
<import name="pageHeader" content=":application/car/component/page-header/main" />
<import name="cards" content=":application/car/component/cards/main" />
<import name="standbyGoods" content=":application/car/component/standbyGoods/main" />
<import name="payType" content=":component/payType/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="studentGuide" content=":component/studentGuide/main" />
<import name="wenda" content=":component/wenda/main" />
<import name="giftText" content=":component/giftText/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />
<import name="SendVipEnter" content=":component/sendVipEnter/main" />
<import name="sendKe2Txt" content=":component/sendKe2Txt/main" />

<div class="panel-carkemu14">
    <com:pageHeader goAuth="{{props.goAuth}}"
        iconList="{{state.iconList}}" 
        uiConfig="{{self.headUiConfig}}"
    />
    <sp:if value="props.kemu == 1 && props.goodsList.length">
        <div class="ipad-box" style="background: linear-gradient(#231d1d 50%, #f2f2f2 51%, #f2f2f2 100%);">
            <div class="card-box phone-box">
                <com:cards comparePricePool="{{props.comparePricePool}}" standbyPool="{{props.standbyPool}}"
                    goodsList="{{props.goodsList}}" tabChange="{{props.tabChange}}" tabIndex="{{props.currentIndex}}" />
            </div>
        </div>
    </sp:if>

    <sp:if value="props.standbyPool.length">
        <div class="ipad-box">
            <div class="phone-box">
                <com:standbyGoods changeGoods="{{props.changeGoods}}" standbyPool="{{props.standbyPool}}" />
            </div>
        </div>
    </sp:if>

    <div class="ipad-box">
        <div class="phone-box">
           <com:sendKe2Txt goodsInfo="{{props.goodsList[props.currentIndex]}}" />
            <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                <div class="buy-btn">
                    确认协议并支付 ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
                    '--'}}
                </div>
                <!-- <com:giftText/> -->
                <i class="label">{{props.labelPool[props.groupKey].label}}</i>
            </div>
            <div class="protocol-box">
                <com:middleProtocol groupKey="{{props.groupKey}}" couponPool="{{props.couponPool}}" />
            </div>

            <sp:if value="{{state.divisionInfo.imageUrl}}">
                <img style="width: 284px;height: 2px;display: block;margin: 15px auto;"
                    src="{{state.divisionInfo.imageUrl}}" alt="">
            </sp:if>

            <com:SendVipEnter name="center" entranceCode="{{props.kemu == 1?'ke1_index_center':'ke4_index_center'}}"
                position="center" />

            <com:SendVipEnter name="right" entranceCode="{{props.kemu == 1?'ke1_index_right':'ke4_index_right'}}"
                position="right" />

            <div class="step-box">

                <sp:if value="!props.isHubei && !Platform.isXueTang">
                    <div class="wenda-box">
                        <com:wenda name="wenda" groupKey="{{props.groupKey}}" type="1" />
                    </div>
                </sp:if>

                <div class="step step1">
                    <img src="{{props.kemu== 1 ? 'https://web-resource.mc-cdn.cn/web/vip/500ti/5.png': 'https://web-resource.mc-cdn.cn/web/vip/500ti/3.png'}}"
                        alt="" />
                </div>

                <div class="step step2" key="step2">
                    <img src="https://web-resource.mc-cdn.cn/web/vip/step.png" alt="" />
                </div>
                <div class="step step3">
                    <img
                        src="https://web-resource.mc-cdn.cn/web/vip/500ti/2.png" />
                </div>
                <div class="step step4" sp-on:click="goAuth" data-uniqkey="bgbc">
                    <img
                        src="https://web-resource.mc-cdn.cn/web/vip/ban2.png" />
                </div>

                <com:studentGuide />
            </div>

            <com:commonQuestion type="1" />
        </div>
    </div>
</div>
