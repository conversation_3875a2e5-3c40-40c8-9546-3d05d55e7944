/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */

import { KemuType, Platform, URLCommon } from ':common/env';
import { promiseIconList, promiseList } from ':common/features/promise';
import { CAR_KE1AND4_VIDEO } from ':common/navigate';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { getDivisionInfo } from ':store/chores';
import { GoodsInfo } from ':store/goods';
import { mergeObjects } from ':common/utils';

interface State {
    iconList: any[],
    divisionInfo: any
}
interface Props {
    goodsList: GoodsInfo[]
    currentIndex: number
    goodsInfo?: GoodsInfo
    goAuth?(any)
    payBtnCall?(e: Event)
}
export default class extends Component<State, Props> {
    get videoIntroduce() {
        return URLCommon.isElder ? '' : CAR_KE1AND4_VIDEO;
    }
    get headUiConfig() {
        const { goodsList, currentIndex, goodsInfo } = this.props;
        const info = goodsInfo || goodsList[currentIndex];
        const config: any = {
            img: URLCommon.isElder ? 'http://exam-room.mc-cdn.cn/exam-room/2024/07/31/10/f6a6a821f5874acdbad33f0cce63356a.png' : 'http://exam-room.mc-cdn.cn/exam-room/2024/07/31/10/4083f5889b0c4d8aa25bccacbc2a5520.png',
            video: this.videoIntroduce
        };

        if (info.giftPromotion?.promotionStatus) {
            mergeObjects(config, {
                img: 'http://exam-room.mc-cdn.cn/exam-room/2024/12/05/16/a353e1c7169c4eefa1a6798c71040dbc.png'
            });
        }
    
        if (info.headConfig) {
            mergeObjects(config, {
                img: info.headConfig.img,
                bgc: info.headConfig.bgc,
                video: info.headConfig.video,
                transparent: !!info.headConfig.img
            });
        }

        return config;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            iconList: (URLCommon.isElder ? [
                {
                    uniqkey: promiseList.jhk,
                    icon: promiseIconList.jhkelder,
                    dec: '精华课'
                }
            ] : []).concat([
                {
                    uniqkey: promiseList.k1vip,
                    icon: promiseIconList.k1vip,
                    dec: '科一VIP'
                },
                {
                    uniqkey: promiseList.bgbc,
                    icon: promiseIconList.k1bgbc,
                    dec: '科一考不过<br/>补偿'
                },
                {
                    uniqkey: promiseList.k4vip,
                    icon: promiseIconList.k4vip,
                    dec: '科四VIP'
                },
                {
                    uniqkey: promiseList.bgbc,
                    icon: promiseIconList.k4bgbc,
                    dec: '科四考不过<br/>补偿'
                }
            ]),
            divisionInfo: {}
        };

    }
    didMount(): void {
        this.getDivision();
    }
    getDivision() {
        getDivisionInfo({
            type: 20
        }).then(info => {
            this.setState({
                divisionInfo: info
            });
        });
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
