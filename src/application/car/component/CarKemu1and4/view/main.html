<import name="style" content="./main" />
<import name="pageHeader" content=":application/car/component/page-header/main" />
<import name="cards" content=":application/car/component/cards/main" />

<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="studentGuide" content=":component/studentGuide/main" />
<import name="wenda" content=":component/wenda/main" />
<import name="giftText" content=":component/giftText/main" />
<import name="PriceTag" content=":component/priceTag/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />
<import name="SendVipEnter" content=":component/sendVipEnter/main" />
<import name="sendKe2Txt" content=":component/sendKe2Txt/main" />

<div class="panel-carkemu1and4">
    <com:pageHeader goAuth="{{props.goAuth}}"
        uiConfig="{{self.headUiConfig}}"
        iconList="{{state.iconList}}" isElderKe1ke4="{{true}}" 
        />
    <!-- 有切换才有这个 -->
    <sp:if value="{{props.tabChange}}">
        <div class="ipad-box" style="background: linear-gradient(#231d1d 50%, #f2f2f2 51%, #f2f2f2 100%);">
            <div class="card-box phone-box {{props.goodsInfo.inActivity?'activity':''}}"
                style="background: linear-gradient({{state.activeBgc}} 50%, #f2f2f2 51%, #f2f2f2 100%);">
                <com:cards comparePricePool="{{props.comparePricePool}}" goodsList="{{props.goodsList}}"
                    tabChange="{{props.tabChange}}" tabIndex="{{props.currentIndex}}" />
            </div>
        </div>
    </sp:if>

    <div class="ipad-box">
        <div class="phone-box">
             <com:sendKe2Txt goodsInfo="{{props.goodsList[props.currentIndex]}}" />
            <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                <div class="buy-btn">
                    确认协议并支付 ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
                    '--'}}
                </div>
                <!-- <com:giftText/> -->
                 <com:PriceTag class="noScale" goodsInfo="{{props.goodsInfo}}"
                     comparePriceMap="{{props.comparePricePool}}" labelMap="{{props.labelPool}}" />
            </div>
            <div class="protocol-box">
                <com:middleProtocol groupKey="{{props.groupKey}}" couponPool="{{props.couponPool}}" />
            </div>

            <sp:if value="{{state.divisionInfo.imageUrl}}">
                <img style="width: 284px;height: 2px;display: block;margin: 15px auto;"
                    src="{{state.divisionInfo.imageUrl}}" alt="">
            </sp:if>

            <com:SendVipEnter name="center"
                entranceCode="ke1ke4_index_center"
                position="center" />

            <com:SendVipEnter name="right"
                entranceCode="ke1ke4_index_right" position="right" />

            <div class="step-box">
                <div class="step step1">
                    <img src="{{URLCommon.isElder?'http://exam-room.mc-cdn.cn/exam-room/2022/07/26/15/758783bbf43f43b8927ae913f794a3a9.png':'https://web-resource.mc-cdn.cn/web/vip/500ti/5.png'}}"
                        alt="" />
                </div>
                <div class="step step2 {{URLCommon.isElder?'elder-step2':''}} ">
                    <img src="{{URLCommon.isElder?'https://web-resource.mc-cdn.cn/web/vip/21.png':'https://web-resource.mc-cdn.cn/web/vip/step.png'}}"
                        alt="" />
                </div>

                <div class="step step5 {{URLCommon.isElder?'elder-step5':''}}">
                    <img src="{{URLCommon.isElder?'http://exam-room.mc-cdn.cn/exam-room/2022/07/26/16/8d9332ad347542578ba56172f6571831.png':'https://web-resource.mc-cdn.cn/web/vip/500ti/3.png'}}"
                        alt="" />
                </div>

                <div class="step step6 {{URLCommon.isElder?'elder-step6':''}}">
                    <img src="{{URLCommon.isElder?'https://web-resource.mc-cdn.cn/web/vip/19.png':'https://web-resource.mc-cdn.cn/web/vip/500ti/steps.png'}}"
                        alt="" />
                </div>

                <div class="step step3">
                    <img
                        src="https://jiakao-web.mc-cdn.cn/jiakao-web/2023/05/11/13/27d85add828340b891de06e3bb9a34b6.png"
                        />
                </div>
                <div class="step step4 {{URLCommon.isElder?'elder-step4':''}}" sp-on:click="goAuth" data-uniqkey="bgbc">
                    <img
                        src="https://jiakao-web.mc-cdn.cn/jiakao-web/2023/05/11/11/1e1e055cbf3c44988a92d22288ed787f.png"
                        />
                </div>

                <com:studentGuide />
            </div>

            <com:commonQuestion type="1" kemuTxt="科一和科四" />
        </div>
    </div>

</div>
