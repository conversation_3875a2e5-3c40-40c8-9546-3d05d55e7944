.panel-carkemu1and4 {
    background-color: #f2f2f2;
    position: relative;

    .card-box {
        background: linear-gradient(#231d1d 50%, #f2f2f2 51%, #f2f2f2 100%);
    }

    .buy-btn-box {
        position: relative;
        width: 343px;
        height: 49px;
        margin: 24px auto 15px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/btn1.png)
            no-repeat center center/100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .buy-btn {
            font-size: 19px;
            color: white;
        }

        .label {
            position: absolute;
            right: 0px;
            top: -7px;
            background: linear-gradient(360deg, #f9c39f 0%, #fedec7 100%);
            border-radius: 0 10px 0 8px;
            font-size: 12px;
            font-weight: 500;
            color: #622604;
            transform: scale3d(0.9, 0.9, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2px 9px;
            font-weight: 500;
        }
    }

    .protocol-box {
        margin-bottom: 20px;
        font-size: 13px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
    }

    .wenda-box {
        margin-bottom: 30px;
    }

    .step-box {
        padding: 0 15px;

        img {
            display: block;
            width: 100%;
            margin: 0 auto;
        }

        .step1 {
            padding: 0 15px;
        }

        .step2 {
            margin-top: 30px;
            &.elder-step2 {
                margin-top: 15px;
            }
        }

        .step5 {
            margin-top: 20px;
            padding: 0 15px;
            &.elder-step5 {
                margin-top: 40px;
            }
        }

        .step6 {
            margin-top: 20px;
            &.elder-step6 {
                margin-top: 15px;
            }
        }

        .step3 {
            padding: 0 28px;
            margin-top: 40px;
        }

        .step4 {
            margin-top: 20px;
            &.elder-step4 {
                margin-bottom: 10px;
            }
        }
    }
}
