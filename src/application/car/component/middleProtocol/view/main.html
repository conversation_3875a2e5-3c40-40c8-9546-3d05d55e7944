<import name="style" content="./main" module="S" />

<div class=":protocol-box">
    <span class=":txt">
        <span sp-on:click="changeReaded"
            >{{props.protocolText1 || '开通前请阅读'}}</span
        >
        <span class=":go-protocol" sp-on:click="goProtocol">
            {{props.protocolText2 || ('《会员协议》')}}
             <sp:if value="{{self.isRenew}}">
                 <span style="color: #666;">(含自动续费条款)</span>
             </sp:if>
        </span>
    </span>
    <span sp:if="{{!Platform.isIOS && props.couponPool && props.couponPool[props.groupKey].couponCode}}"
        class=":coupon coupon-position-middle"
        >{{'已优惠' +
                props.couponPool[props.groupKey].priceCent + '元'}}</span>
</div>
