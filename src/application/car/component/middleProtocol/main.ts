/*
 * ------------------------------------------------------------------
 * 协议勾选组件, 勾选状态全局统一
 * ------------------------------------------------------------------
 */

import { openWeb } from ':common/core';
import { PROTOCOL1_URL } from ':common/navigate';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GroupKey } from ':store/goods';
import { globalGoodsInfo } from ':store/newGoods';

interface Props {
    groupKey?: GroupKey
    protocolUrl?: string
}

export default class ReadProtocol extends Component<any, Props> {
    // 是否是续订,续订才显示文案
    get isRenew() {
        const { groupKey } = this.props;
        return !!globalGoodsInfo[groupKey]?.renewTpl;
    }
    $constructor() {
        this.$super({ name: module.id, view: View });
        this.state = {
        };
    }

    willReceiveProps() {
        return true;
    }

    goProtocol() {
        const { protocolUrl = PROTOCOL1_URL } = this.props;

        openWeb({
            url: protocolUrl
        });
    }
}
