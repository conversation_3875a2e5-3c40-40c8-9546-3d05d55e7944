<import name="style" content="./main" module="S" />

<div class=":standby-goods-box {{props.className && S[props.className]}}">
    <div class=":standby-goods">
        <div class=":icon"></div>
        <div class=":goods-list">
            <sp:each for="{{props.standbyPool}}">
                <div sp-on:click="onTabChange" data-idx="{{$index}}"
                    class=":goods-item {{state.tabIndex === $index?S.active:''}}">
                    <div class=":temp-name">{{$value.tempName}}</div>
                    <div class=":unit-price">
                        <span class=":unit">￥</span>
                        <span class=":price">{{$value.payPrice}}</span>
                        <span class=":day-time">/{{$value.validDays}}天</span>
                    </div>
                </div>
            </sp:each>
        </div>
    </div>
    <div class=":dec-txt ">
        <sp:if value="{{!!props.standbyPool[state.tabIndex].renewTpl}}">
            {{props.standbyPool[state.tabIndex].renewTpl.renewDescription}}
        </sp:if>
    </div>
</div>
