.standby-goods-box {
    width: 344px;
    margin: 8px auto;

    .standby-goods {
        box-sizing: border-box;
        padding: 5px 8px;
        display: flex;
        background-color: #fff;
        border-radius: 6px;

        .icon {
            flex-shrink: 0;
            width: 60px;
            height: 48px;
            background: url(../images/1.png) no-repeat center center/cover;
            margin-right: 13px;
        }

        .goods-list {
            flex: 1;
            display: flex;
            justify-content: space-between;

            .goods-item {
                width: 124px;
                height: 46px;
                border: 1px solid #dbdbdb;
                border-radius: 4px;
                display: flex;
                justify-content: center;
                flex-direction: column;
                text-align: center;

                .temp-name {
                    font-size: 12px;
                }

                .unit-price {
                    .unit {
                        font-size: 12px;
                        font-weight: bold;
                        line-height: 17px;
                        color: #ED391A;
                    }

                    .price {
                        font-size: 16px;
                        line-height: 17px;
                        font-weight: bold;
                        color: #ED391A;
                    }

                    .day-time {
                        font-size: 12px;
                        transform: scale(0.8);
                        color: #AA4120;
                        line-height: 13px;
                    }
                }

                &.active {
                    background: #fff0e2;
                    border: 2px solid #ed9964;
                }
            }
        }
    }

    .dec-txt {
        height: 12px;
        margin-top: 2px;
        font-size: 12px;
        color: #464646;
        transform: scale(0.8);
        transform-origin: left;
    }

    &.type1 {
        margin: 0 auto;
        padding: 10px 0 0;
        border-radius: 0;

        .standby-goods{
            padding: 0;
            .icon {
                display: none;
            }
    
            .goods-list {
                justify-content: flex-start;
    
                .goods-item {
                    background: #ffffff;
                    border: 1px solid #d8d8d8;
                    flex: 1;
                    height: 30px;
                    flex-direction: row;
                    align-items: center;
    
                    .temp-name {
                        color: #AA4120;
                    }
    
                    .unit-price {
                        display: flex;
                        align-items: baseline;
    
                        .unit {
                            font-size: 12px;
                            line-height: 17px;
                            color: #ED391A;
                            font-weight: 400;
                        }
    
                        .price {
                            line-height: 17px;
                            color: #ED391A;
                            font-weight: 400;
                            font-weight: bold;
                        }
    
                        .day-time {
                            font-size: 12px;
                            line-height: 17px;
                            color: #AA4120;
                            font-weight: 400;
                        }
                    }
    
                    &:nth-of-type(1) {
                        border-radius: 4px 0 0 4px;
                        border-right: 0;
                    }
    
                    &:nth-of-type(2) {
                        border-radius: 0 4px 4px 0;
                        border-left: 0;
                    }
    
                    &.active {
                        background: #fff0e2;
                        border: 1px solid #ed9964;
                    }
                }
            }
        }

    }
}
