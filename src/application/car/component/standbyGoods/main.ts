/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import createEventBus from ':common/eventbus';
import { ABTestKey, CarType, KemuType, URLCommon, URLParams } from ':common/env';
import { getAbtest } from ':store/chores';

const EventChange = 'selectGoodsTime';

interface State {
    tabIndex: number
}

interface Props {
    standbyDefaultTab: number
    standbyPool: any[]
    changeGoods(obj: any)
}

const eventbus = createEventBus<{
    /** 选中的商品 */
    tabIndex: number
}>();

export default class extends Component<State, Props> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0
        };
    }
    didMount(): void {
        // 默认值
        this.setState({
            tabIndex: this.props.standbyDefaultTab || this.props.standbyDefaultTab === 0 ? this.props.standbyDefaultTab : 0
        });
        eventbus.on('tabIndex', (tabIndex) => {
            this.setState({
                tabIndex
            });
        });
    }
    onTabChange(e) {
        const { tabIndex } = this.state;
        const { standbyPool, changeGoods } = this.props;
        const index = +e.refTarget.getAttribute('data-idx');

        if (index !== tabIndex) {
            eventbus.emit('tabIndex', index);
            changeGoods && changeGoods(standbyPool[index]);
        }
    }

    willReceiveProps() {
        return true;
    }
}
