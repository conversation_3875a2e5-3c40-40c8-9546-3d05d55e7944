<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="moveGoods" content=":component/moveGoods/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="buyDialogOrderSign" content=":component/buyDialogOrderSign/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="CarKemu14" content=":application/car/component/CarKemu14/main" />
<import
    name="CarKemu1and4"
    content=":application/car/component/CarKemu1and4/main"
/>

<import name="CarShort" content=":application/car/component/CarShort/main" />
<import name="CarKe3Ke4Group" content=":application/kemu3/component/kemu34Content/main" />

<import
    name="CarKemuAll"
    content=":application/car/component/CarKemuAll/main"
/>
<import name="standbyGoods" content=":application/car/component/standbyGoods/main" />
<import name="sendKe2Dialog" content=":component/sendKe2Dialog/main" />
<!-- <import name="sendKe2Right" content=":component/sendKe2Right/main" /> -->

<div class="page-container page-car">
    <div class="page-header">
        <com:header
            title="{{(state.showFooter && state.prevScrollTop > 200)?self.nowGoodInfo.name: ' '}}"
            theme="black"
            endTheme="black"
            qaKey="{{self.qaKey}}"
            scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}"
        />
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <!-- 科目1或4 -->
        <div
            class="{{((self.nowGoodInfo&&self.nowGoodInfo.groupKey) === GroupKey.ChannelKe1 || (self.nowGoodInfo&&self.nowGoodInfo.groupKey) === GroupKey.ChannelKe4 || (self.nowGoodInfo&&self.nowGoodInfo.groupKey) === GroupKey.ChannelKe1D3 || (self.nowGoodInfo&&self.nowGoodInfo.groupKey) === GroupKey.ChannelKe4D3 || (self.nowGoodInfo&&self.nowGoodInfo.groupKey) === GroupKey.ChannelKe4Month || (self.nowGoodInfo&&self.nowGoodInfo.groupKey) === GroupKey.ChannelKe1Month)?'show':'hide'}}"
        >
            <com:CarKemu14
                currentIndex="{{state.tabIndex}}"
                goodsList="{{state.goodsInfoPool}}"
                labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}"
                couponPool="{{state.couponPool}}"
                standbyPool="{{state.standbyPool}}"
                payPrice="{{self.showPrice}}"
                groupKey="{{self.nowGoodInfo.groupKey}}"
                kemu="{{state.kemu}}"
                tiku="{{state.tiku}}"
                isHubei="{{state.isHubei}}"
                goAuth="{{self.goAuth}}"
                payBtnCall="{{self.payBtnCall}}"
                tabChange="{{self.tabChangeCall}}"
                changeGoods="{{self.changeGoods}}"
            />
        </div>

        <!-- 科目14 -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.ChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKe1Ke4Group).showPage"
        >
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.ChannelKe1Ke4Group?'show':'hide'}}"
            >
                <com:CarKemu1and4
                    goodsInfo="{{self.nowGoodInfo}}"
                    currentIndex="{{state.tabIndex}}"
                    goodsList="{{state.goodsInfoPool}}"
                    labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    couponPool="{{state.couponPool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    kemu="{{state.kemu}}"
                    tiku="{{state.tiku}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                    tabChange="{{self.tabChangeCall}}"
                />
            </div>
        </sp:if>

        <!-- 短时提分 -->
        <sp:if value="state.kemu== 4 && self.getGroupKeyInfo(GroupKey.ChannelKe4Short).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKe4Short).showPage">
            <div
                class="{{self.nowGoodInfo&&self.nowGoodInfo.groupKey === GroupKey.ChannelKe4Short?'show':'hide'}}"
            >
                <com:CarShort
                    payPrice="{{self.showPrice}}"
                    labelPool="{{state.labelPool}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>

        <!-- 科三科四组合包 -->
        <sp:if value="state.kemu== 4 && self.getGroupKeyInfo(GroupKey.ChannelKe34).payPrice  && self.getGroupKeyInfo(GroupKey.ChannelKe34).showPage">
            <div
                class="{{self.nowGoodInfo&&self.nowGoodInfo.groupKey === GroupKey.ChannelKe34?'show':'hide'}}"
            >
                <com:CarKe3Ke4Group
                    goodsInfo="{{self.getGroupKeyInfo(GroupKey.ChannelKe34)}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                    comparePricePool="{{state.comparePricePool}}"
                />
            </div>
        </sp:if>

        <!-- 全科 -->
        <sp:if
            value="state.kemu== 1 && self.getGroupKeyInfo(GroupKey.ChannelKemuAll).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKemuAll).showPage"
        >
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.ChannelKemuAll?'show':'hide'}}"
            >
                <com:CarKemuAll
                    tiku="{{state.tiku}}"
                    goodsList="{{state.goodsInfoPool}}"
                    goodsInfo="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll)}}"
                    labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    couponPool="{{state.couponPool}}"
                    currentIndex="{{state.tabIndex}}"
                    groupKey="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll).groupKey}}"
                    upgradeStrategyCode="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll).upgradeStrategyCode}}"
                    payPrice="{{self.showPrice}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                    tabChange="{{self.tabChangeCall}}"
                />
            </div>
        </sp:if>
    </div>
   
    <div class="{{state.showFooter ? '': 'hide'}}">
         <com:standbyGoods changeGoods="{{self.changeGoods}}"
             standbyPool="{{state.tabIndex === 0?state.standbyPool:[]}}" className="type1" />
        <div
            class="footer {{state.goodsInfoPool.length > 1?'':'hide'}}"
        >
            <com:bottomTabs
                tabIndex="{{state.tabIndex}}"
                labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}"
                goodsList="{{state.goodsInfoPool}}"
                tabChange="{{self.tabChangeCall}}"
                standbyPool="{{state.tabIndex === 0?state.standbyPool:[]}}"
            />
        </div>
        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
            </div>
        </com:buyButton>
    </div>
    <!-- 所有商品信息加载完成才能加载这个组件，内部有根据商品判断 -->
    <sp:if value="state.showSignModal">
        <com:buyDialogOrderSign goodsInfoPool="{{state.goodsInfoPool}}" standbyPool="{{state.standbyPool}}"
            labelPool="{{state.labelPool}}"
            comparePricePool="{{state.comparePricePool}}" couponPool="{{state.couponPool}}"
            close="{{self.closeSignModal}}"/>
    </sp:if>
       
    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
    <com:sendKe2Dialog goodsInfo="{{self.nowGoodInfo}}" />
    <!-- <com:sendKe2Right goodsInfo="{{self.nowGoodInfo}}" /> -->
    <!-- <com:moveGoods
        info="{{(self.moveGoodsVideo[self.nowGoodInfo.groupKey] || {})}}" groupKey="{{self.nowGoodInfo.groupKey}}"/> -->
</div>
