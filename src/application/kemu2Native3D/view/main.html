<import name="style" content="./main" module="S" />

<import name="header" content=":component/header/main" />
<import name="moveGoods" content=":component/moveGoods/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buchangDialog" content="../component/buchang/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="CarKemu2" content=":application/kemu2Native3D/component/CarKemu2/main" />
<import name="CarKemu2and3" content=":application/kemu2Native3D/component/CarKemu2And3/main" />
<import name="CarKemu2Place" content=":application/kemu2Native3D/component/CarKemu2Place/main" />
<import name="CarKemu2AndGroup" content=":application/kemu2Native3D/component/CarKemu2AndGroup/main" />
<import name="buyDialogOrderSignK2" content=":component/buyDialogOrderSignK2/main" />
<import name="standbyGoods" content=":application/car/component/standbyGoods/main" />
<div class="page-container">
    <div class=":page-header">
        <com:header title="{{state.prevScrollTop > 200?self.nowGoodInfo.name: ' '}}" scrollTop="{{state.prevScrollTop}}"
            theme="black" qaKey="{{self.qaKey}}" endTheme="black" back="{{self.backCall}}" />
    </div>

    <div class=":main" id="pageScroll" sp-on:scroll="pageScroll">
        <!-- 科二3D -->
        <div class="{{state.tabIndex===0?'':'hide'}}">
            <sp:if value="!self.showKe2AssetGoods">
                <com:CarKemu2 isHubei="{{state.isHubei}}" goodsInfo="{{state.goodsInfoPool[0]}}"
                    payPrice="{{self.showPrice}}" payBtnCall="{{self.payBtnCall}}" goAuth="{{self.goAuth}}"
                    labelMap="{{state.labelPool}}" comparePriceMap="{{state.comparePricePool}}"
                    standbyPool="{{state.standbyPool}}" changeGoods="{{self.changeGoods}}"/>

                <sp:else />

                <com:CarKemu2Place isHubei="{{state.isHubei}}" goodsInfo="{{state.goodsInfoPool[0]}}"
                    payPrice="{{self.showPrice}}" payBtnCall="{{self.payBtnCall}}" goAuth="{{self.goAuth}}"
                    labelMap="{{state.labelPool}}" comparePriceMap="{{state.comparePricePool}}" />
            </sp:if>
        </div>

        <!-- 科二科三3D -->
        <!-- <sp:if value="self.getGroupKeyInfo(self.ChannelKe2Ke33D).payPrice &&
            self.getGroupKeyInfo(self.ChannelKe2Ke33D).showPage">
            <div class="{{self.nowGoodInfo.groupKey === self.ChannelKe2Ke33D?'':'hide'}}">
                <com:CarKemu2and3 isHubei="{{state.isHubei}}" goodsList="{{state.goodsInfoPool}}"
                    tabIndex="{{state.tabIndex}}" payPrice="{{self.showPrice}}"
                    goodsInfo="{{self.getGroupKeyInfo(GroupKey.ChannelKe2Ke33D)}}"
                    comparePriceMap="{{state.comparePricePool}}" payBtnCall="{{self.payBtnCall}}"
                    goAuth="{{self.goAuth}}" />
            </div>
        </sp:if> -->

        <!-- 科二科三组合包 -->
        <sp:if value="self.getGroupKeyInfo(self.ChannelKe2Ke3GroupNew).payPrice &&
            self.getGroupKeyInfo(self.ChannelKe2Ke3GroupNew).showPage">
            <div class="{{self.nowGoodInfo.groupKey === self.ChannelKe2Ke3GroupNew?'':'hide'}}">
                <com:CarKemu2AndGroup isHubei="{{state.isHubei}}" goodsList="{{state.goodsInfoPool}}"
                    tabIndex="{{state.tabIndex}}" payPrice="{{self.showPrice}}" labelMap="{{state.labelPool}}"
                    goodsInfo="{{self.getGroupKeyInfo(GroupKey.ChannelKe2Ke3GroupNew)}}"
                    comparePriceMap="{{state.comparePricePool}}" payBtnCall="{{self.payBtnCall}}"
                    goAuth="{{self.goAuth}}" />
            </div>
        </sp:if>

    </div>

    <div class=":footer">
        <com:standbyGoods changeGoods="{{self.changeGoods}}" standbyPool="{{state.tabIndex === 0?state.standbyPool:[]}}"
            className="type1" />
        <div class=" {{state.goodsInfoPool.length>1?'':'hide'}}">
            <com:bottomTabs comparePricePool="{{state.comparePricePool}}" labelPool="{{state.labelPool}}"
                tabIndex="{{state.tabIndex}}" goodsList="{{state.goodsInfoPool}}" tabChange="{{self.tabChangeCall}}"
                standbyPool="{{state.tabIndex === 0?state.standbyPool:[]}}"/>
        </div>
        <com:buyButton>
            <div sp:slot="couponEntry">
                <div sp-on:click="goCoupon" class="coupon-position-bottom">
                    {{self.nowCouponInfo.couponCode?'已优惠' +
                    self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
                </div>
            </div>
        </com:buyButton>
    </div>

    <!-- 所有商品信息加载完成才能加载这个组件，内部有根据商品判断 -->
    <sp:if value="state.showSignModal">
        <com:buyDialogOrderSignK2 goodsInfoPool="{{state.goodsInfoPool}}"   standbyPool="{{state.standbyPool}}"
            labelPool="{{state.labelPool}}"
            comparePricePool="{{state.comparePricePool}}" couponPool="{{state.couponPool}}"
            close="{{self.closeSignModal}}" />
    </sp:if>

    <com:persuadeDialog>
        <div sp:slot="text-body">
            <div class=":persuade-content"></div>
        </div>
    </com:persuadeDialog>
    <com:payDialog />
    <com:expiredDialog />
    <sp:if value="state.showSignModal">
        <com:moveGoods info="{{(self.moveGoodsVideo[self.nowGoodInfo.groupKey] || {})}}"
            groupKey="{{self.nowGoodInfo.groupKey}}" />
    </sp:if>
</div>
