<import name="style" content="./main" module="S" />
<import name="style" content="./override" />
<import name="PriceTag" content=":component/priceTag/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="testimonials" content="../../testimonials/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />
<import name="standbyGoods" content=":application/car/component/standbyGoods/main" />


<div class=":page carkemu2">
    <div class=":banner">
        <p class=":buchang" ref="buchangIntro" data-uniqkey="bgbc" sp-on:click="goAuth" />
        <div class=":rights">
            <div class=":right" data-uniqkey="k2vip" sp-on:click="goAuth">
                <i />
                <span>科二5项<br />3D模拟</span>
            </div>
            <div class=":right" data-uniqkey="zskc" sp-on:click="goAuth">
                <i />
                <span>
                    <span sp:if="state.cityName">{{state.cityName}}<br /></span>
                    <span>科二考场</span>
                </span>
            </div>
            <div class=":right" data-uniqkey="k2xmsp" sp-on:click="goAuth">
                <i />
                <span>必考项<br />教学视频</span>
            </div>
            <div class=":right" data-uniqkey="bgbc" sp-on:click="goAuth">
                <i />
                <span>考不过<br />现金补偿</span>
            </div>
        </div>
    </div>
    <div class=":main">
        <sp:if value="props.standbyPool.length">
            <com:standbyGoods changeGoods="{{props.changeGoods}}" standbyPool="{{props.standbyPool}}" />
        </sp:if>
        <div sp-on:click="pay" class=":pay" data-fragment="主图">
            <div class=":buy-btn">
                确认协议并支付 ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
                '--'}}
            </div>
            <!-- <com:giftText/> -->
            <sp:if value="props.labelPool[props.groupKey].label">
                <com:PriceTag class="{{S['tips']}}" goodsInfo="{{props.goodsInfo}}"
                    comparePriceMap="{{props.comparePriceMap}}" labelMap="{{props.labelMap}}" />
            </sp:if>
        </div>

        <div class=":protocol-box">
            <com:middleProtocol />
        </div>

        <div class=":title">
            <span>科二学不会怎么办?</span>
            <div class=":subtitle">仅需3步，科二轻松通关</div>
        </div>

        <div class=":sec1" />

        <div class=":title">
            <span>考不过补偿</span>
            <div class=":subtitle">考不过立即补偿40元</div>
        </div>

        <div class=":sec2" class="kemuall-9" data-uniqkey="bgbc" sp-on:click="goAuth" />

        <div class=":title">
            <span>适合人群</span>
            <div class=":subtitle">量身打造针对性练习</div>
        </div>

        <div class=":sec3" />

        <sp:if value="!props.isHubei">
            <div class=":title">
                <span>大家怎么说</span>
                <div class=":subtitle">真实用户 真实反馈</div>
            </div>

            <com:testimonials />
        </sp:if>

        <com:commonQuestion type="9" />

    </div>
</div>
