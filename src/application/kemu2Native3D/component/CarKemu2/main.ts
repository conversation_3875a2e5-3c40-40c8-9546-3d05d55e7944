/*
 * ------------------------------------------------------------------
 * 科目二购买页
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GoodsExtra, GoodsInfo, GroupKey } from ':store/goods';
import { getCurrentCityName } from ':common/features/locate';

interface Props {
    goodsInfo: GoodsInfo;
    payBtnCall: any;
    goAuth: any;
    comparePriceMap: Partial<Record<GroupKey, { diffPrice: string; allPrice: string }>>;
    labelMap: Partial<Record<GroupKey, GoodsExtra>>;
}

export default class extends Component<any, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }

    willReceiveProps() {
        return true;
    }

    didMount() {
        getCurrentCityName().then(cityName => {
            this.setState({
                cityName
            });
        });
    }

    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }

    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
}
