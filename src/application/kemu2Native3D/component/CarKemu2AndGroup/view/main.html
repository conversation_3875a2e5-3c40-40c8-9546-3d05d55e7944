<import name="style" content="./main" />
<import name="PriceTag" content=":component/priceTag/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="testimonials" content="../../testimonials/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />

<div class="panel-carkemu2and3-group">
    <div class="banner">

    </div>
    <div class="main">
        <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
            <div class="buy-btn">
                确认协议并支付 ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
                    '--'}}
            </div>
            <com:PriceTag class="noScale" goodsInfo="{{props.goodsInfo}}" comparePriceMap="{{props.comparePriceMap}}"
                labelMap="{{props.labelMap}}" />
        </div>
       
        <div class="protocol-box">
            <com:middleProtocol />
        </div>

        <div class="image-box">
            <img class="img1"
                src="http://exam-room.mc-cdn.cn/exam-room/2023/12/01/14/7e341d3865554c1ca827796044860a75.png" alt="">
            <img class="img2"
                src="http://exam-room.mc-cdn.cn/exam-room/2023/12/01/14/d172b7fa74b845cd8b16cf3edf8a8ae2.png" alt="">
            <img class="img3"
                src="http://exam-room.mc-cdn.cn/exam-room/2023/12/01/14/e911a8daaa9048178bc8373cf87e3ee4.png" alt=""
                data-uniqkey="bgbc" sp-on:click="goAuth">
        </div>

        <com:commonQuestion type="18" />
    </div>
</div>
