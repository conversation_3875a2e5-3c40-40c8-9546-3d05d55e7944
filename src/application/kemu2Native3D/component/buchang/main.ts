import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';
import { Platform } from ':common/env';

export default class extends Component {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            showAll: false,
            statusBarHeight: 0
        };
    }

    willReceiveProps() {
        return true;
    }

    didMount() {
        MCProtocol.Core.System.env((data) => {
            console.log('env', data.data, window.devicePixelRatio);
            let statusBarHeight = data.data.statusBarHeight;
            
            if (Platform.isAndroid) {
                statusBarHeight /= window.devicePixelRatio;
            }

            if (statusBarHeight) {
                statusBarHeight += 'px';
            } else {
                statusBarHeight = 'env(safe-area-inset-top)';
            }
            this.setState({
                statusBarHeight
            });
        });
    }

    show() {
        this.setState({
            showAll: true
        });
        (this.app.children.buyButton as any).toggleButton(true);
    }

    closeModal() {
        this.setState({
            showAll: false
        });
        (this.app.children.buyButton as any).toggleButton(false);
    }
}
