.hide {
    display: none !important;
}

.page {
    position: fixed;
    z-index: 1000;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    background: #f5f6f8;

    .header {
        background-color: rgb(0, 0, 0);
        display: flex;
        box-sizing: content-box;
        height: 48px;
        align-items: center;
        font-size: 17px;
        color: white;

        i {
            margin-left: 20px;
            margin-right: 15px;
            width: 50px/2;
            height: 50px/2;
            background-image: url('../../../images/back.png');
            background-size: cover;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                left: -80%;
                right: -50%;
                top: -50%;
                bottom: -50%
            }
        }
    }

    .main {
        flex: 1;
        overflow: auto;
        padding-top: 20px;

        .card {
            margin: 0 15px 20px;
            color: #333333;
            font-size: 14px;
            line-height: 21px;
            position: relative;
            padding: 18px;
            background: #ffffff;
            border-radius: 6px;

            p {
                position: relative;
                overflow: hidden;
            }

            p:after {
                content: "";
                position: absolute;
                left: 2px;
                top: 7px;
                width: 17px;
                height: 17px;
            }

            p:first-child {
                padding-left: 30px;
                padding-bottom: 12px;
                border-bottom: 1px dashed rgba(151, 151, 151, 50%);
                font-weight: bold;

                &::after {
                    top: 2px;
                    background: url("../../../images/faq_q.png");
                    background-size: 100% 100%;
                }
            }

            p:last-child {
                padding-left: 30px;
                padding-top: 10px;

                &::after {
                    background: url("../../../images/faq_a.png");
                    background-size: 100% 100%;
                    top: 12px;
                }
            }
        }

        b {
            display: none;
            color: #50e3c2;
        }

        a {
            color: rgba(255,67,27,1);
        }

    }
}