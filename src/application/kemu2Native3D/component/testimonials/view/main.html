<import name="style" content="./main" module="S" />

<div>
    <div class=":users">
        <sp:each for="state.sampleTexts">
            <div class=":user">
                <img class=":avatar" src="{{$value.avatar}}" />
                <div class=":desc">
                    <div>{{#$value.text}}</div>
                    <div class=":from">{{$value.from}}</div>
                </div>
            </div>
        </sp:each>
        <div class=":more" sp-on:click="showMoreClick" style="color: {{props.color}}">查看更多学员心得</div>
    </div>

    <div class=":page {{!state.showAll?S['hide']:''}}">
        <div class=":header" style="padding-top:{{state.statusBarHeight}}">
            <i sp-on:click="closeModal" />
            <span>学员心得</span>
        </div>
        <div class=":main">
            <sp:each for="state.texts">
                <div class=":card">
                    <img class=":avatar" src="{{$value.avatar}}" />
                    <div class=":desc">
                        <div>{{#$value.text}}</div>
                        <div class=":from">{{$value.from}}</div>
                    </div>
                </div>
            </sp:each>
        </div>
    </div>
</div>