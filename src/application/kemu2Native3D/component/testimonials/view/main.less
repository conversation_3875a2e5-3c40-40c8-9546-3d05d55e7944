.users {
    margin-top: 20px;
    width: 345px;
    background: #ffffff;
    border-radius: 10px;
    padding: 0 20px;
    padding-top: 5px;
}

.user {
    display: flex;
    border-bottom: 1px dashed rgba(151, 151, 151, 0.2);
    padding: 15px 0;
}

.avatar {
    margin-right: 14px;
    width: 30px;
    height: 30px;
    box-shadow: 0 0 0 1px #d4ab7e;
    border-radius: 50%;
}

.desc {
    flex: 1;
    font-size: 14px;
    color: #333333;
    line-height: 21px;
}

.from {
    display: inline-block;
    margin: 6px 0 0;
    padding: 0 5px;
    line-height: 17px;
    background: rgba(216, 216, 216, 0.3);
    font-size: 12px;
    color: rgba(51, 51, 51, 0.5);
}

.more {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    text-align: left;
    color: #1556a6;
    line-height: 20px;
}

.hide {
    display: none !important;
}

.page {
    position: fixed;
    z-index: 1000;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    background: #f5f6f8;

    .header {
        background-color: rgb(0, 0, 0);
        display: flex;
        box-sizing: content-box;
        height: 48px;
        align-items: center;
        font-size: 17px;
        color: white;

        i {
            margin-left: 20px;
            margin-right: 15px;
            width: 50px/2;
            height: 50px/2;
            background-image: url('../../../images/back.png');
            background-size: cover;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                left: -80%;
                right: -50%;
                top: -50%;
                bottom: -50%
            }
        }
    }

    .main {
        flex: 1;
        overflow: auto;
        padding-top: 20px;

        .card {
            margin: 0 15px 10px;
            background: #ffffff;
            border-radius: 6px;
            display: flex;
            padding: 18px 15px;
        }
    }
}