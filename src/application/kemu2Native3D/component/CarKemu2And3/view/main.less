.page {}

.banner {
    width: 375px;
    height: 1120/2px;
    background-image: url('../../../images/13.png');
    background-size: cover;
    position: relative;

    .buchang {
        position: absolute;
        left: 36px;
        top: 190px;
        height: 24px;
        width: 86px;
    }

    .rights {
        position: absolute;
        top: 360px;
        left: 40px;
        right: 40px;
        display: flex;
        justify-content: space-between;
        white-space: nowrap;

        .right {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 36px;
            font-size: 12px;
            text-align: center;
            color: #ffffff;
            line-height: 16px;

            i {
                width: 100%;
                height: 36px;
                background-size: cover;
            }

            &:nth-child(1) i {
                background-image: url('../../../images/26.png');
            }

            &:nth-child(2) i {
                background-image: url('../../../images/25.png');
            }

            &:nth-child(3) i {
                background-image: url('../../../images/27.png');
            }

            &:nth-child(4) i {
                background-image: url('../../../images/24.png');
            }
        }
    }

    .rights2 {
        top: 440px;

        .right {
            &:nth-child(1) i {
                background-image: url('../../../images/21.png');
            }

            &:nth-child(2) i {
                background-image: url('../../../images/22.png');
            }

            &:nth-child(3) i {
                background-image: url('../../../images/27.png');
            }

            &:nth-child(4) i {
                background-image: url('../../../images/25.png');
            }
        }
    }
}

.main {
    margin-top: -24px;
    position: relative;
    background: #f2f2f2;
    border-radius: 20px 20px 0px 0px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .pay {
        margin: 10px auto 0;
        width: 345px;
        height: 89px;
        position: relative;
        background-image: url('../../../images/8.png');
        background-size: cover;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        color: #ffffff;
        line-height: 25px;
        padding-top: 27px;

        .tips {
            position: absolute;
            right: 20px;
            top: 0;
            line-height: 17px;
            background: linear-gradient(90deg, #ffd878 5%, #ffc400);
            border-radius: 33px 33px 33px 2px;
            font-size: 12px;
            color: #6f2117;
            padding: 2px 10px 3px 8px;
        }
    }

    .protocol-box {
        margin-bottom: 20px;
    }
}

.title {
    font-size: 25px;
    text-align: center;
    color: #333333;
    line-height: 36px;

    span {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;

        &:before,
        &:after {
            content: '';
            width: 30px;
            height: 14px;
            background-image: url('../../../images/7.png');
            background-size: cover;
        }

        &::before {
            margin-right: 14px;
        }

        &:after {
            margin-left: 14px;
            transform: rotate(180deg);
        }
    }

    .subtitle {
        margin-top: 1px;
        font-size: 15px;
        text-align: center;
        color: #6b6870;
        line-height: 21px;
    }
}

.sec1 {
    margin-top: 20px;
    margin-bottom: 40px;
    width: 690px/2;
    height: 708px/2;
    background-image: url('../../../images/14.png');
    background-size: cover;
}

.sec4 {
    margin-top: 20px;
    margin-bottom: 40px;
    width: 690px/2;
    height: 708px/2;
    background-image: url('../../../images/15.png');
    background-size: cover;
}

.sec2 {
    margin-top: 20px;
    margin-bottom: 40px;
    width: 690px/2;
    height: 232px/2;
    background-image: url('../../../images/16.png');
    background-size: cover;
}

.sec3 {
    margin-top: 20px;
    margin-bottom: 36px;
    width: 690px/2;
    height: 448px/2;
    background-image: url('../../../images/17.png');
    background-size: cover;
}