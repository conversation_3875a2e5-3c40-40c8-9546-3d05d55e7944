import { getPageName, PayType, persuadeDialogAllow, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import BaseVip from ':common/features/baseVip';
import { couponAnimate, pauseAllVideos, scrollTop } from ':common/features/dom';
import { iosDialogBuySuccess } from ':common/features/ios_pay';
import jump from ':common/features/jump';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { BUYED_URL, CAR_KE1_VIDEO, MOVE_GOODS_KEMU2, openAuth } from ':common/navigate';
import { trackExit, trackPageLoad, trackPageShow } from ':common/stat';
import { PayStatProps } from ':component/buyButton/main';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import View from './view/main.html';
import { onWebBack } from ':common/features/persuade';
import { webClose } from ':common/core';
import { onPageShow } from ':common/features/page_status_switch';
import { getTabIndex } from ':common/features/cache';
import isNumber from 'lodash/isNumber';
import { isHubei } from ':common/features/locate';

/** 科二3d */
const ChannelKe23D = GroupKey.ChannelKe2;
/** 科二科三3d */
// const ChannelKe2Ke33D = GroupKey.ChannelKe2Ke3Group;

/** 科二科三组合包 */
const ChannelKe2Ke3GroupNew = GroupKey.ChannelKe2Ke3GroupNew;

/** 科二3d考场包 */
const ChannelKe2Asset = GroupKey.ChannelKe2Asset;

const pageNameMap = {
    [GroupKey.ChannelKe2]: '科二速成VIP购买页',
    [GroupKey.ChannelKe2Month]: '科二速成VIP购买页',
    [GroupKey.ChannelKe2Asset]: '科二3d考场包VIP购买页',
    // [GroupKey.ChannelKe2Ke3Group]: '科二科三3DVIP页',
    [GroupKey.ChannelKe2Ke3GroupNew]: '科二科三组合VIP页'
};

interface State {
    isHubei: boolean
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    standbyPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    prevScrollTop: number,
    showSignModal: boolean
}
let timer;
// 标记是否展示过挽留弹窗
let flag = false;

export default class extends BaseVip {
    ChannelKe23D = ChannelKe23D;
    // ChannelKe2Ke33D = ChannelKe2Ke33D;
    ChannelKe2Asset = ChannelKe2Asset;
    ChannelKe2Ke3GroupNew = ChannelKe2Ke3GroupNew;
    declare state: State
    get pageName() {
        const { goodsInfoPool, tabIndex } = this.state;

        return pageNameMap[goodsInfoPool[tabIndex].groupKey];
    }

    get qaKey() {
        return this.nowGoodInfo.groupKey === GroupKey.ChannelKemuAll || this.nowGoodInfo.groupKey === GroupKey.HcChannelKemuAll ? 'qaKey6' : 'qaKey1';
    }

    get showKe2AssetGoods() {
        return URLParams.k2RealId && URLParams.isPayKe2Car;
    }
    moveGoodsVideo = {
        [GroupKey.ChannelKe2]: {
            videoUrl: MOVE_GOODS_KEMU2,
            videoPoster: 'http://exam-room.mc-cdn.cn/exam-room/2024/03/12/14/5f1abbe8410d484c91e89ea82d1d0e21.png',
            entrance: 'kemu2-sc',
            stat: {
                fromPageCode: '175',
                fromPathCode: '003158'
            }
        }
    }

    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        const goodsInfoPool: GoodsInfo[] = [];
        const standbyPool: GoodsInfo[] = [];
        if (this.showKe2AssetGoods) {
            goodsInfoPool.push({
                groupKey: ChannelKe2Asset
            } as GoodsInfo);
        } else {
            goodsInfoPool.push({
                groupKey: ChannelKe23D
            } as GoodsInfo);

            // standbyPool.push({
            //     groupKey: ChannelKe23D
            // } as GoodsInfo);

            goodsInfoPool.push({
                groupKey: ChannelKe2Ke3GroupNew
            } as GoodsInfo);
        }

        this.state = {
            isHubei: true,
            tabIndex: 0,
            goodsInfoPool: goodsInfoPool,
            standbyPool: standbyPool,
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            // 滚动距离
            prevScrollTop: 0,
            showSignModal: false
        };
    }
    async didMount() {
        this.windowResize();
        this.appEventProxy();

        // 判断是否是湖北
        isHubei().then(isHubei => {
            this.setState({
                isHubei
            });
        });

        await this.getGoodInfo();
        setPageName(this.pageName);

        // 页面进出时长打点
        trackPageLoad();

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: this.nowGoodInfo.groupKey
        });

    }
    appEventProxy() {
        onWebBack(async () => {
            // 如果大家说弹窗全屏展示，则物理键返回时先关闭大家说弹窗
            for (const k in this.children) {
                const testimonials = this.children[k].children.testimonials;
                if (testimonials && testimonials.state.showAll) {
                    testimonials.closeModal();
                    return;
                }
            }
            this.goBackPage();
        });
    }
    backCall = () => {
        this.goBackPage();
    }
    // 退出页面的回调
    goBackPage() {
        const { tabIndex, goodsInfoPool, labelPool } = this.state;
        const nowGoodInfo = goodsInfoPool[tabIndex];

        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: nowGoodInfo,
                groupKey: nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                title: `真的要放弃${this.nowGoodInfo.name}吗？`,
                txt1: '倒车入库看点位\n太重要了',
                txt2: '3D模拟挺有意思\n好用',
                txt3: '居然可以看到\n本地的考场',
                txt4: URLCommon.tiku === 'truck' ? '朋友推荐的说好用' : '第一次没考过\n真的补偿了40块钱',
                tag: {
                    text: labelPool[this.nowGoodInfo.groupKey]?.label
                }
            }).then(payType => {
                if (payType === false) {
                    this.leavePageCall();
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            webClose();
        }
    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys = this.state.standbyPool.concat(goodsInfoPool).map(item => item.groupKey);
        const standbyGoodsCount = this.state.standbyPool.length;

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            const [standbyGoodsListInfo, normalGoodsListInfo] = [goodsListInfo.slice(0, standbyGoodsCount), goodsListInfo.slice(standbyGoodsCount)];

            normalGoodsListInfo.forEach((goodInfo, index) => {

                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    jump.replace(BUYED_URL);
                    return;
                }

                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }

            // showPage用来控制模块的展示， 先渲染需要渲染的tabPage，加快首次渲染速度
            newGoodsPool[tabIndex].showPage = true;

            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool,
                standbyPool: standbyGoodsCount ? [
                    {
                        ...newGoodsPool[0],
                        tempName: '连续包月'
                    },
                    ...standbyGoodsListInfo.map(goodInfo => ({
                        ...goodInfo,
                        tempName: '半年卡'
                    }))
                ] : []
            });

            this.setPageInfo();
            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon(goodsListInfo);
                await this.getLabel(goodsListInfo);
                await this.getComparePrice(goodsListInfo);

                couponAnimate({
                    couponTargetDomSelect: '.coupon-position-bottom',
                    compareTargetDomSelect: `.bottom-tabs .${newGoodsPool[2]?.groupKey}`,
                    couponData: this.nowCouponInfo,
                    compareData: this.state.comparePricePool[newGoodsPool[2]?.groupKey],
                    goodsData: this.nowGoodInfo,
                    compareGoodsData: newGoodsPool[2],
                    compareAnimateType: 3
                });

                this.setPageInfo();

                this.setState({ showSignModal: true });

            }, 60);

            // 500ms后再渲染其他tabPage，
            setTimeout(() => {
                newGoodsPool.forEach(item => {
                    item.showPage = true;
                });

                this.setState({
                    goodsInfoPool: newGoodsPool
                });

            }, 500);
        });

    }
    setPageInfo() {
        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });
        this.setBuyBottom();
    }
    goAuth = async (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        // 退出当前tab的打点
        this.leavePageCall();

        // 回到滚动的顶部
        scrollTop(document.querySelector('#pageScroll'));

        // 暂停所有视频
        pauseAllVideos();

        this.setState({
            tabIndex
        }, () => {
            setPageName(this.pageName);

            trackPageShow();

            this.setPageInfo();

        });

    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            this.setState({
                prevScrollTop
            });
        }, 300);
    }
    payBtnCall = (e) => {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        this.onPayBtnCall({
            stat: {
                fragmentName1
            }
        });
    }
    pay = async (stat: PayStatProps) => {
        this.onPay({
            stat: {
                ...stat
            }
        });
    }
    onPay = async (config: { stat: PayStatProps }) => {
        const { tabIndex, goodsInfoPool } = this.state;

        // 记住之前选的支付方式
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo?.couponCode,
            ...config.stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey }, 2);
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(config.stat);
                },
                ...config.stat
            });
        });
    }
    // 离开当前页面
    leavePageCall = () => {
        // 退出当前页面的打点
        setPageName(pageNameMap[this.nowGoodInfo.groupKey]);
        trackExit();
    }
    windowResize() {
        const reCalc = function () {
            const dimensionWidth = Math.min(document.documentElement.clientWidth, 480);

            const rate = dimensionWidth / Package.build.style.baseWidth;

            const baseFontSize = 100 * rate;
            window.baseFontSize = baseFontSize;

            document.documentElement.style.fontSize = baseFontSize + 'px';
        };
        setTimeout(() => {
            reCalc();
        }, 500);
    }
    closeSignModal = () => {
        this.setPageInfo();
    }
    changeGoods = (goodsInfo) => {
        const { goodsInfoPool } = this.state;
        const tabIndex = 0;
        goodsInfoPool[tabIndex] = goodsInfo;

        this.setState({
            goodsInfoPool
        });
        if (!this.children.buyDialogOrderSignK2?.state.show) {
            this.setPageInfo();
        }

    }
}
