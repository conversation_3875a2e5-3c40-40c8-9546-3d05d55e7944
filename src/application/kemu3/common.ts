import { getSystemInfo } from ':common/core';
import { Platform } from ':common/env';
import { getVideoList } from ':store/chores';

export async function getRouteVideo() {
    const { _userCity, _cityCode } = await getSystemInfo();
    const cityCode = _userCity || _cityCode;
    
    if (!cityCode) {
        return null;
    }

    const data = await getVideoList(cityCode);
    let image;
    if (data) {
        image = data.image;

        if (Platform.isIOS) {
            if (image.indexOf('!') > -1) {
                image = image.slice(0, image.indexOf('!'));
            }
        }

        return {
            routeSrc: data.videoUrl,
            routePoster: image,
            routeCity: data.city
        };
    }

    return null;
}
