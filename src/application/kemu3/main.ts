/*
 * ------------------------------------------------------------------
 * 科目三vip落地页
 * ------------------------------------------------------------------
 */
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { comparePrice, ComparePriceInfo, getGroupSessionInfo, getSessionExtra, GoodsExtra, GoodsInfo, GroupKey } from ':store/goods';
import { batchGetBestCoupon, couponInfo2Coupon, Coupons, couponWithHint, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import ExpiredDialog from ':component/expiredDialog/main';
import PayDialog from ':component/payDialog/main';
import { ensureSiriusBound, getDefaultPayType, PayBoundType, startSiriusPay } from ':common/features/pay';
import { BUYED_URL, CAR_KE1_VIDEO, MOVE_GOODS_KEMU3, openAuth } from ':common/navigate';
import { ABTestKey, ABTestType, CarType, PayType, persuadeDialogAllow, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import Header from ':component/header/main';
import PersuadeDialogCom from ':component/persuadeDialog/main';
import { trackExit, trackGoPay, trackPageLoad, trackPageShow } from ':common/stat';
import { getSystemInfo, webClose } from ':common/core';
import { onWebBack } from ':common/features/persuade';
import { pauseAllVideos, scrollTop } from ':common/features/dom';
import { onPageShow } from ':common/features/page_status_switch';
import { getTabIndex } from ':common/features/cache';
import isNumber from 'lodash/isNumber';
import jump from ':common/features/jump';
import { getAbtest } from ':store/chores';
import { selectBestCoupon3D } from ':store/coupon';
import { isKe3ClaimOpen } from ':store/kemu3';
import { getCityName } from ':common/utils';
const groupKey = GroupKey.ChannelKe3Group;
const ke34GroupKey = GroupKey.ChannelKe34;
let timer;
// 标记是否展示过挽留弹窗
let flag = false;
interface State {
    goodsList: GoodsInfo[];
    tabIndex: number;
    isKemuall: boolean;
    scrolled: boolean,
    coupons: Coupons;
    labelPool?: Record<string, any>,
    comparePricePool?: Record<string, any>,
    prevScrollTop: number,
    diffPrice: ComparePriceInfo;
    tiku: CarType;
    ke3ClaimOpen: boolean,
    userCityName: string
}

const pageNameMap = {
    [GroupKey.ChannelKe3Group]: '科三速成VIP购买页',
    [GroupKey.ChannelKe34]: '科三科四组合VIP页'
};
export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        expiredDialog: ExpiredDialog;
        persuadeDialog: PersuadeDialogCom;
        payDialog: PayDialog;
        header: Header;
    }

    $constructor() {
        const tiku = URLCommon.tiku;

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            goodsList: [],
            tabIndex: 0,
            isKemuall: false,
            scrolled: false,
            coupons: {},
            prevScrollTop: 0,
            diffPrice: null,
            tiku,
            ke3ClaimOpen: false,
            userCityName: ''
        };
    }

    get pageName() {
        return pageNameMap[this.currentGoods.groupKey] || '科三速成VIP购买页';
    }
    get goodsListWithCoupon() {
        return this.state.goodsList.map(goods => goodsInfoWithCoupon(goods, this.state.coupons[goods.groupKey] || { code: '', price: '', name: '' }));
    }

    get currentGoods() {
        return goodsInfoWithCoupon(this.state.goodsList[this.state.tabIndex], this.currentCoupon);
    }

    get currentCoupon() {
        return couponWithHint(this.state.coupons[this.state.goodsList[this.state.tabIndex]?.groupKey], true);
    }
    get title() {
        return this.currentGoods.groupKey === GroupKey.ChannelKe34 ? '科三科四组合VIP' : '科三VIP';
    }
    get qaKey() {
        return 'qaKey1';
    }
    moveGoodsVideo = {
        [GroupKey.ChannelKe3Group]: {
            videoUrl: MOVE_GOODS_KEMU3,
            videoPoster: 'http://exam-room.mc-cdn.cn/exam-room/2024/03/12/14/ba5782bd3b4e42829109f03aeb76dfec.png',
            entrance: 'kemu3-sc',
            stat: {
                fromPageCode: '176',
                fromPathCode: '003159'
            }
        }
    }
    async didMount() {

        setPageName(this.pageName);
        this.getSystemInfo();
        ensureSiriusBound({ groupKey, type: PayBoundType.GoStatusPage });
        await this.fetchGoodsInfo();

        trackPageLoad();

        // 拉取不重要的信息
        setTimeout(() => {
            this.fetchCoupon();
            this.getComparePrice();
            this.getLabel();
        }, 60);
        // 挽留弹窗
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }

    async getSystemInfo() {
        const systemInfo = await getSystemInfo();
        const userCityCode = systemInfo._userCity || systemInfo._cityCode;
        const userCityName = await getCityName(+userCityCode) || '';

        isKe3ClaimOpen({cityCode: userCityCode}).then((res) => {
            this.setState({
                ke3ClaimOpen: res.value
            });
        });

        this.setState({
            userCityName
        });

    }
    
    willReceiveProps() {
        return true;
    }
    getGroupKeyInfo(groupKey) {
        const { goodsList } = this.state;
        const goodInfo = goodsList.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    pageScroll(e) {
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget?.scrollTop;
            const channelKe3Color = '#090F24';
            this.children.header.setScrollBg(prevScrollTop, this.currentGoods.groupKey === GroupKey.ChannelKe34 ? '#1C1E2B' : channelKe3Color);
        }, 100);
    }
    private async fetchGoodsInfo() {
        const [goodsInfo, ke34GoodsInfo] = await getGroupSessionInfo({
            groupKeys: [groupKey, ke34GroupKey]
        });
        const goodsList: (GoodsInfo & { showPage?: boolean })[] = [goodsInfo];

        if (ke34GoodsInfo && !ke34GoodsInfo.bought && URLCommon.isNormal) {
            goodsList.push(ke34GoodsInfo);
        }
        if (goodsInfo.expired) {
            this.children.expiredDialog.show({ time: goodsInfo.expiredTime });
        }
        // 主商品已购买，直接跳走
        if (goodsInfo.bought && !goodsInfo.upgrade) {
            jump.replace(BUYED_URL);
            return;
        }
        // showPage用来控制模块的展示， 先渲染需要渲染的tabPage，加快首次渲染速度
        goodsList[0].showPage = true;

        this.setState({ goodsList });

        // 500ms后再渲染其他tabPage，
        setTimeout(() => {
            goodsList.forEach(item => {
                item.showPage = true;
            });

            this.setState({
                goodsList
            });
        }, 500);

    }

    async getComparePrice() {
        const { goodsList, tiku } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsList.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsList[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice
                    };
                }
            });

            this.setState({ comparePricePool });
        });
    }

    async getLabel() {
        const { goodsList } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsList.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsList[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });

    }

    private async fetchCoupon() {
        // 获取优惠券
        const coupons = await batchGetBestCoupon(this.state.goodsList);
        this.setState({ coupons });
        this.setPageInfo();
    }

    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }

        trackExit();

        // 回到滚动的顶部
        scrollTop(document.querySelector('#car-ke34'));

        // 暂停所有视频
        pauseAllVideos();

        this.setState({
            tabIndex
        }, () => {
            setPageName(this.pageName);

            trackPageShow();

            this.setPageInfo();
        });
    }

    setPageInfo() {
        this.setBuyBottom();
    }

    setBuyBottom() {
        // const fragmentName1 = '底部吸底按钮';
        // const nowGoodInfo: GoodsInfo = this.currentGoods;
        this.children.buyButton.setPay({
            androidPay: this.pay.bind(this),
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => iosBuySuccess({ groupKey: this.currentGoods.groupKey })
        });

        this.children.buyButton.setButtonConfig({
            groupKey: this.currentGoods.groupKey,
            type: 4,
            title: `¥ ${this.currentGoods.payPrice} 确认协议并支付`,
            subtitle: '有效期' + this.currentGoods.validDays + '天',
            fragmentName1: '底部吸底按钮'
        });

    }
    /** 发起支付 */
    async pay(stat: PayStatProps) {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.currentGoods.groupKey,
            sessionIds: this.currentGoods.sessionIds,
            activityType: this.currentGoods.activityType,
            couponCode: this.currentCoupon.code,
            ...stat
        }).catch(async (err) => {
            console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: this.currentGoods.groupKey,
                payPrice: this.currentGoods.payPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    // 退出页面的回调
    goBackPage() {
        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: this.currentGoods,
                groupKey: this.currentGoods.groupKey,
                payPrice: this.currentGoods.payPrice,
                title: '真的要放弃科三VIP吗？',
                txt1: '灯光模拟太好用了 在家也能练',
                txt2: '教练让我回家用手机 多模拟',
                txt3: '幸好买了考场路线 回家反复看了',
                txt4: '朋友推荐的还是靠谱',
                tag: {
                    text: this.state.labelPool[this.currentGoods.groupKey]?.label
                },
                kemu: 3
            }).then(payType => {
                if (payType === false) {
                    trackExit();
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            trackExit();
            webClose();
        }
    }
    async onCouponEntryClick() {
        const couponInfo = await selectUserCoupon(this.currentGoods, this.currentCoupon?.code);

        if (couponInfo) {
            this.setState({
                coupons: {
                    ...this.state.coupons,
                    [this.currentGoods.groupKey]: couponInfo2Coupon(couponInfo)
                }
            });
            this.setPageInfo();
        }
    }
    onBackClick = () => {
        this.goBackPage();
    }

    onPayBtnClick = (e) => {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        console.log(this.currentGoods.groupKey, 'this.currentGoods.groupKey');

        // 点击支付按钮打点
        trackGoPay({
            groupKey: this.currentGoods.groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(this.currentGoods.groupKey, {
                fragmentName1
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: this.currentGoods.groupKey,
                payPrice: this.currentGoods.payPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });
        }
    }

    goAuth = async (id) => {
        const { goodsList } = this.state;
        openAuth({
            groupKeys: goodsList.map(item => item.groupKey).join(','),
            groupKey: this.currentGoods.groupKey,
            authId: id
        });

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
}
