.page-container {
    height: 100%;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    -webkit-flex-direction: column;
    background-size: 100% auto;
    background-color: #280d37;
}

.kemu3-page-content {
    flex: 1;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    background-color: #1c1e2b;

    .sec1 {
        height: 220px;
        background: url(../images/kemu3/1.png) no-repeat;
        background-size: 100% 100%;
    }

    .wraper {
        padding: 0 15px 35px 15px;
        margin-top: -80px;
    }

    .sec2 {
        display: flex;
        margin-top: 50px;
        justify-content: space-between;
        .item-l {
            width: 169px;
            height: 258px;
            background: url(../images/kemu3/bg1.png) no-repeat;
            background-size: 100% 100%;
        }

        .item-r {
            width: 169px;
            height: 258px;
            background: url(../images/kemu3/bg2.png) no-repeat;
            background-size: 100% 100%;
        }
    }
    .buy-button {
        width: 345px;
        height: 89px;
        background: url(../images/btn.png) no-repeat;
        background-size: 100% 100%;
        font-size: 18px;
        font-weight: 500;
        color: #ffffff;
        position: relative;
        text-align: center;
        line-height: 80px;
        margin-top: 14px;
        .tag {
            position: absolute;
            top: 0px;
            right: 20px;
            width: 115px;
            height: 22px;
            background-size: cover;
            background: linear-gradient(90deg, #ffd878 0%, #ffc400 100%);
            border-radius: 33px 33px 33px 2px;
            font-size: 12px;
            color: #6f2117;
            line-height: 22px;
        }
    }

    .sec3 {
        margin-top: 30px;
    }

    .sec4 {
        width: 310px;
        height: 56px;
        background: url(../images/kemu3/9.png) no-repeat;
        background-size: 100% 100%;
        margin: 15px auto 0 auto;
    }

    .sec5 {
        width: 345px;
        padding-top: 20px;
        padding-bottom: 25px;
        margin-top: 20px;
        background: #ffffff;
        box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.3);
        border-radius: 10px;

        .step {
            width: 100%;
            height: 67px;
        }

        .step1 {
            background: url(../images/kemu3/6.png) no-repeat;
            background-size: 100% 100%;
        }

        .step2 {
            background: url(../images/kemu3/7.png) no-repeat;
            background-size: 100% 100%;
        }

        .step3 {
            background: url(../images/kemu3/8.png) no-repeat;
            background-size: 100% 100%;
        }

        .media {
            width: 305px;
            height: 177px;
            border-radius: 7px;
            margin: 20px auto 0 auto;
            position: relative;
            overflow: hidden;
            background: #ffffff;

            &.media1 {
                margin-bottom: 20px;
            }

            video {
                width: 100%;
                height: 100%;
                object-fit: fill;
            }

            .poster {
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                z-index: 5;
                width: 100%;
                height: 100%;
                background: url(../images/kemu3/ke3_zhibo.png) no-repeat;
                background-size: 100% 100%;

                &.poster1 {
                    background: none;
                    overflow: hidden;
                    display: flex;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
            }

            .play-btn {
                position: absolute;
                top: 59px;
                left: 126px;
                z-index: 60;
                width: 52px;
                height: 52px;
                border-radius: 52px;
                background: url(../images/kemu3/playvideo.png) no-repeat center
                    center;
                background-size: 52px 52px;
            }
        }

        .gif {
            width: 305px;
            height: 172.5px;
            border-radius: 7px;
        }

        .gif1 {
            background: url(../images/kemu3/ke3_dgmn.gif) no-repeat;
            background-size: 100% 100%;
            margin: 25px auto;
        }

        .gif2 {
            background: url(../images/kemu3/ke3_shijing.gif) no-repeat;
            background-size: 100% 100%;
            margin: 25px auto;
        }
    }
    .sec6 {
        width: 345px;
        height: 671px;
        background: url(../images/kemu3/k4.png) no-repeat;
        background-size: 100% 100%;
        margin: 30px auto 0;
    }
    .sec7 {
        .title {
            width: 216px;
            height: 58px;
            background: url(../images/kemu3/18.png) no-repeat;
            background-size: 100% 100%;
            margin: 30px auto 0;
        }
        .content {
            width: 345px;
            height: 107px;
            margin: 18px auto 0;
            background: url(../images/kemu3/step3.png) no-repeat;
            background-size: 100% 100%;
        }
        .content1 {
            width: 345px;
            height: 90px;
            margin: 18px auto 0;
            background: url(../images/kemu3/bgbc.png) no-repeat;
            background-size: 100% 100%;
        }
    }

    .sec8 {
        margin: 0 -15px;
    }
}

.footer-wraper {
    .footer {
        position: relative;
        // z-index: 120;
        background: #ffffff;
        padding: 15px 10px 0 10px;
        box-shadow: 1px -2px 12px 2px rgba(0, 0, 0, 0.08);
        border-radius: 10px 10px 0 0;

        .footer-pay {
            height: 43px;
            color: #5e2e06;
            font-size: 17px;
            border-radius: 43px;
            background: linear-gradient(90deg, #f2ab79 0%, #f9be95 100%);
            text-align: center;
            line-height: 44px;
            position: relative;

            .validay {
                padding-left: 5px;
                color: rgba(94, 46, 6, 0.8);
                font-size: 12px;
                transform: scale3d(0.85, 0.85, 1);
                display: inline-block;
            }
        }

        .proto-coupon {
            display: flex;
            justify-content: space-between;

            .coupon-pick {
                display: flex;
                height: 30px;
                background: #ffffff;
                box-sizing: border-box;
                margin-top: 2px;

                .coupon-enter {
                    font-size: 12px;
                    line-height: 30px;
                    color: #741b01;
                    text-align: right;
                }
            }
        }
    }
}

.tip {
    position: absolute;
    top: -28px;
    right: 0;
    font-size: 12px;
    line-height: 14px;
    color: #6f2117;
    background: linear-gradient(90deg, #ffd878 0%, #ffc400 100%);
    padding: 5px 10px;
    border-radius: 30px;

    i {
        position: absolute;
        display: block;
        width: 10px;
        height: 7px;
        background-color: red;
        right: 28px;
        bottom: -7px;
        background: url(../images/kemu3/17.png) no-repeat right center;
        background-size: 100% 100%;
    }
}

.hide {
    display: none !important;
}
