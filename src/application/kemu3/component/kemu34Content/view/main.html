<import name="style" content="./main" module="S" />
<import name="commonQuestion" content=":component/commonQuestion/main" />

<div class=":page">
    <div class=":kemu3-page-content">
        <div class=":sec1">
        </div>
        <div class=":wraper">
            <div class=":sec2">
                <div class=":item-l" />
                <div class=":item-r" />
            </div>
            <div class=":buy-button" ref="pay-btn" data-fragment="主图">确认协议并支付 ¥{{props.goodsInfo.payPrice || '--'}}
                <div class=":tag">比分开买节省{{props.comparePricePool[GroupKey.ChannelKe34].diffPrice}}元</div>
            </div>

            <div class=":sec4">
            </div>

            <div class=":sec5">
                <div class="{{S.step}} {{S.step1}}"></div>
                <div class="{{S.media}} {{S.media1}}">
                    <video id="video" ref="video" class="" webkit-playsinline=""
                        controlslist="nodownload noremoteplayback" playsinline="" x-webkit-airplay="allow"
                        x5-playsinline="" controls=""
                        src="{{Texts.TVHOSTMAP.maiche}}/2021/07/15/53c352ab1fd14f09828b566bd8e5e27d.middle.mp4"
                        preload="" poster="https://web-resource.mc-cdn.cn/web/qiye-jkbd-v3/img-2.png"></video>
                    <div class="{{S.poster}} {{state.played ? S.hide: ''}}">
                        <div class=":play-btn" ref="play"></div>
                    </div>
                </div>
                <div class="{{S.step}} {{S.step2}}"></div>
                <div class="{{S.gif}} {{S.gif1}}"></div>
                <div class="{{S.gif}} {{S.gif2}}"></div>
                <div class="{{S.step}} {{S.step3}}"></div>
                <div class="{{S.media}} {{S.media2}}">
                    <video id="video1" ref="video1" class="" webkit-playsinline=""
                        controlslist="nodownload noremoteplayback" playsinline="" x-webkit-airplay="allow"
                        x5-playsinline="" controls="" src="{{state.routeSrc}}" preload=""
                        poster="{{state.routePoster}}"></video>
                    <div class="{{S.poster}} {{S.poster1}} {{state.played1 ? S.hide: ''}}"><img
                            src="{{state.routePoster}}" alt="">
                        <div class=":play-btn" ref="play1"></div>
                    </div>

                </div>
            </div>

            <div class=":sec6">

            </div>
            <div class=":sec7">
                <sp:if value="props.ke3ClaimOpen">
                    <div class=":content1"></div>
                    <sp:else />
                    <div class=":title"></div>
                </sp:if>

                <div class=":content" sp-on:click="goAuth" data-uniqkey="bgbc"></div>
            </div>
            <div class=":sec8">
                <com:commonQuestion type="1" kemuTxt="科目四" />
            </div>
        </div>
    </div>
</div>
