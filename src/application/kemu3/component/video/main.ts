import { pauseAllVideos } from ':common/features/dom';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface State {
    // 0:初始状态 1: 非wifi情况 2: 播放完成 3: 点击了播放，但是视频在loading
    type: 0 | 1 | 2 | 3
    play: boolean;
    initPlay: boolean
}

interface Props {
    lookAll(any)
    allowNoneWifiPlay: boolean
    netWork: string,
    isPay: boolean,
    isShowControls: boolean,
    isLoop: boolean
}

export default class extends Component<State, Props> {
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            type: 0,
            play: false,
            initPlay: false
        };

    }
    didMount() {
        this.bindEvent();
    }
    bindEvent() {
        const $video = this.getDOMNode().video as HTMLVideoElement;

        $video.addEventListener('ended', () => {
            if (this.props.isPay) {
                this.setState({
                    type: 2
                });
            }

        });

        $video.addEventListener('timeupdate', () => {
            this.setState({
                type: 0
            });
        });

        $video.addEventListener('pause', () => {
            this.setState({
                play: false
            });
            this.pause();
        });

        $video.addEventListener('play', () => {
            this.setState({
                type: 3,
                play: true
            });
        });
    }
    play() {
        const $video = this.getDOMNode().video as HTMLVideoElement;
        pauseAllVideos();
        this.setState({
            type: 0,
            initPlay: true
        });
        $video.play();
    }
    pause() {
        const $video = this.getDOMNode().video as HTMLVideoElement;
        if (!this.props.isShowControls) {
            this.setState({
                type: 0
            });
        }

        $video.pause();
    }
    onPlay(e) {
        e.stopPropagation();
        this.play();
    }
    onPause(e) {
        e.stopPropagation();
        this.pause();
    }
    onReload(e) {
        const $video = this.getDOMNode().video as HTMLVideoElement;

        e.stopPropagation();

        $video.currentTime = 0;

        this.play();
    }
    onLookAll(e) {
        const { lookAll } = this.props;
        e.stopPropagation();
        lookAll && lookAll(e);
    }
    willReceiveProps() {
        return true;
    }
}
