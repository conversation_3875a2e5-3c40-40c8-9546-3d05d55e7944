.ke3-video-item {
    width: 100%;
    height: 100%;
    .hide {
        display: none !important;
    }
    video {
        width: 100%;
        height: 100%;
        object-fit: fill;
        border-radius: 10px;
    }
    .poster-container {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        background: url(../images/playvideo.png) no-repeat;
        background-size: 100% 100%;
        background: none;
        overflow: hidden;
        display: flex;
        img {
            width: 100%;
            height: 100%;
            border-radius: 10px;
        }
        .btn-play-poster {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 52px;
            height: 52px;
            display: block;
            background: url(../images/playvideo.png) no-repeat;
            background-size: 100% 100%;
            z-index: 9;
        }
    }
    .play-icon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 52px;
        height: 52px;
        display: block;
        background: url(../images/playvideo.png) no-repeat;
        background-size: 100% 100%;
        z-index: 9;
    }

    .mask {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        background: rgba(33, 33, 33, 0.8);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 20;
        border-radius: 8px;

        .pause-reason {
            .dec {
                text-align: center;
                font-size: 15px;
                color: #ffffff;
            }

            .active-box {
                display: flex;
                padding: 20px 35px;
                justify-content: space-around;
                width: 100%;
                box-sizing: border-box;
                .reload-play {
                    width: 111px;
                    height: 32px;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 100px;
                    font-size: 15px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    text-align: center;
                    color: #ffffff;
                    line-height: 30px;
                    .icon {
                        width: 16px;
                        height: 15px;
                        background: url(../images/reload.png) no-repeat;
                        background-size: 100% 100%;
                        margin-right: 3px;
                    }
                }
                .look-all {
                    width: 101px;
                    height: 32px;
                    background: rgba(255, 255, 255, 0.25);
                    border-radius: 100px;
                    font-size: 14px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    text-align: center;
                    color: #ffffff;
                    line-height: 32px;
                    margin-left: 26px;
                }
            }
        }
    }
}
