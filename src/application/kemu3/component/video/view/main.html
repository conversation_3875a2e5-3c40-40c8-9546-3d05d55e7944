<import name="style" content="./main" />

<div class="ke3-video-item">
    <sp:if value='{{props.isLoop}}'>
        <video ref="video" loop src="{{props.previewL}}" type="video/mp4" playsinline="true" controlslist="nodownload noremoteplayback" controls="{{props.isShowControls}}"
            poster="{{Tools.calcImg(props.videoImage)}}"></video>
            <sp:else/>
            <video ref="video"  controlslist="nodownload noremoteplayback" src="{{props.previewL}}" type="video/mp4" playsinline="true" controls="{{props.isShowControls}}"
                poster="{{Tools.calcImg(props.videoImage)}}"></video>
    </sp:if>
   
    <sp:if value='{{props.isShowControls}}'>
        <div class="poster-container {{state.play?'hide':''}}"><img src="{{Tools.calcImg(props.videoImage)}}" alt="">
            <div class="btn-play-poster" sp-on:click="onPlay"></div>
        </div>
    </sp:if>
    <sp:if value='{{!props.isShowControls}}'>
        <div sp-on:click="onPlay" class="play-icon {{state.play || state.type?'hide':''}}"></div>
        <div class="mask {{state.type&&!props.isShowControls?'':'hide'}}">
            <div class="pause-reason {{state.play && state.type==3?'':'hide'}}">
                <div class="dec">加载中...</div>
            </div>
            <sp:if value='{{props.isPay}}'>
                <div class="pause-reason play-end {{state.type==2?'':'hide'}}">
                    <div class="dec">试看已结束</div>
                    <div class="active-box">
                        <div sp-on:click="onReload" class="reload-play">
                            <span class="icon"></span>重新试看
                        </div>
                        <div sp-on:click="onLookAll" data-fragment="试看结束" class="look-all">
                            解锁完整视频
                        </div>
                    </div>
                </div>
            </sp:if>

        </div>
    </sp:if>

</div>