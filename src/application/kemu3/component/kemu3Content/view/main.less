.page-container {
    height: 100%;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    -webkit-flex-direction: column;
    background-size: 100% auto;
    background-color: #280D37;
}

.kemu3-page-content {
    flex: 1;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    background-color: #280D37;

    .sec1 {
        height: 343px;
        background: url(../images/kemu3/1.png) no-repeat;
        background-size: 100% 100%;
    }

    .wraper {
        padding: 0 15px 35px 15px;
        margin-top: -80px;
    }

    .sec2 {
        background: linear-gradient(180deg, #FAD5B4 0%, #FFEEE1 100%);
        box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.35);
        border-radius: 10px;
        padding: 10px;

        .box {
            background: #ffffff;
            padding: 5px 0 20px 0;

            .p1 {
                width: 284px;
                height: 59px;
                background: url(../images/kemu3/2.png) no-repeat;
                background-size: 100% 100%;
                margin: 0 auto;
            }

            .p2 {
                width: 295px;
                height: 38px;
                background: url(../images/kemu3/3.png) no-repeat;
                background-size: 100% 100%;
                margin: 5px auto 0 auto;
            }

            .p3 {
                width: 295px;
                height: 38px;
                background: url(../images/kemu3/4.png) no-repeat;
                background-size: 100% 100%;
                margin: 0 auto;
            }

            .p4 {
                width: 295px;
                height: 38px;
                background: url(../images/kemu3/03luxian.png) no-repeat;
                background-size: 100% 100%;
                margin: 0 auto;
            }

            .p5 {
                width: 305px;
                height: 49px;
                background: url(../images/kemu3/button-bg.png) no-repeat;
                background-size: 100% 100%;
                margin: 30px auto 0 auto;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 19px;
                font-weight: 500;
                color: #FFFFFF;
                line-height: 26px;
            }

            .arrow {
                width: 15px;
                height: 11px;
                background: url(../images/kemu3/sanjiao.png) no-repeat;
                background-size: 100% 100%;
                margin: 7px auto;
            }
        }
    }

    .sec3 {
        margin-top: 30px;
    }

    .sec4 {
        width: 310px;
        height: 56px;
        background: url(../images/kemu3/9.png) no-repeat;
        background-size: 100% 100%;
        margin: 30px auto 0 auto;
    }

    .sec5 {
        width: 345px;
        padding-top: 20px;
        padding-bottom: 25px;
        margin-top: 20px;
        background: #FFFFFF;
        box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.3);
        border-radius: 10px;

        .step {
            width: 100%;
            height: 67px;

        }

        .step1 {
            background: url(../images/kemu3/6.png) no-repeat;
            background-size: 100% 100%;
        }

        .step2 {
            background: url(../images/kemu3/7.png) no-repeat;
            background-size: 100% 100%;
        }

        .step3 {
            background: url(../images/kemu3/8.png) no-repeat;
            background-size: 100% 100%;
        }

        .media {
            width: 305px;
            height: 177px;
            border-radius: 7px;
            margin: 20px auto 0 auto;
            position: relative;
            overflow: hidden;
            background: #ffffff;

            &.media1 {
                margin-bottom: 20px;
            }

            video {
                width: 100%;
                height: 100%;
                object-fit: fill;
            }

            .poster {
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                background: url(../images/kemu3/ke3_zhibo.png) no-repeat;
                background-size: 100% 100%;

                &.poster1 {
                    background: none;
                    overflow: hidden;
                    display: flex;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
            }

            .play-btn {
                position: absolute;
                top: 59px;
                left: 126px;
                width: 52px;
                height: 52px;
                border-radius: 52px;
                background: url(../images/kemu3/playvideo.png) no-repeat center center;
                background-size: 52px 52px;
            }
        }

        .gif {
            width: 305px;
            height: 172.5px;
            border-radius: 7px;
        }

        .gif1 {
            background: url(../images/kemu3/ke3_dgmn.gif) no-repeat;
            background-size: 100% 100%;
            margin: 25px auto;
        }

        .gif2 {
            background: url(../images/kemu3/ke3_shijing.gif) no-repeat;
            background-size: 100% 100%;
            margin: 25px auto;
        }

    }

    .sec8 {
        margin-top: 20px;

        .sec8-w {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 26px 20px 20px 20px;
        }

        .title {
            width: 227px;
            height: 45px;
            background: url(../images/kemu3/10.png) no-repeat;
            background-size: 100% 100%;
            margin: 0 auto 13px auto;
        }

        .qa {
            color: #494455;
            font-size: 15px;
            line-height: 21px;
            padding-top: 12px;

            .p-q {
                padding-left: 26px;
                background: url(../images/kemu3/11.png) no-repeat left 2px;
                background-size: 17px 15px;
            }

            .p-a {
                padding-left: 26px;
                padding-top: 8px;
            }
        }
    }
}

.footer-wraper {
    .footer {
        position: relative;
        // z-index: 120;
        background: #ffffff;
        padding: 15px 10px 0 10px;
        box-shadow: 1px -2px 12px 2px rgba(0, 0, 0, 0.08);
        border-radius: 10px 10px 0 0;

        .footer-pay {
            height: 43px;
            color: #5E2E06;
            font-size: 17px;
            border-radius: 43px;
            background: linear-gradient(90deg, #F2AB79 0%, #F9BE95 100%);
            text-align: center;
            line-height: 44px;
            position: relative;

            .validay {
                padding-left: 5px;
                color: rgba(94, 46, 6, .8);
                font-size: 12px;
                transform: scale3d(0.85, 0.85, 1);
                display: inline-block;
            }
        }

        .proto-coupon {
            display: flex;
            justify-content: space-between;

            .coupon-pick {
                display: flex;
                height: 30px;
                background: #ffffff;
                box-sizing: border-box;
                margin-top: 2px;

                .coupon-enter {
                    font-size: 12px;
                    line-height: 30px;
                    color: #741B01;
                    text-align: right;
                }
            }
        }
    }
}

.tip {
    position: absolute;
    top: -28px;
    right: 0;
    font-size: 12px;
    line-height: 14px;
    color: #6F2117;
    background: linear-gradient(90deg, #FFD878 0%, #FFC400 100%);
    padding: 5px 10px;
    border-radius: 30px;

    i {
        position: absolute;
        display: block;
        width: 10px;
        height: 7px;
        background-color: red;
        right: 28px;
        bottom: -7px;
        background: url(../images/kemu3/17.png) no-repeat right center;
        background-size: 100% 100%;
    }
}

.hide {
    display: none !important;
}