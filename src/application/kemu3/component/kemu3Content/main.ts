/*
 * ------------------------------------------------------------------
 * 科目二购买页
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GoodsInfo } from ':store/goods';
import { getRouteVideo } from ':application/kemu3/common';
import Texts from ':common/features/texts';

interface State {
    played: boolean;
    played1: boolean;
    routeSrc: string,
    routePoster: string,
    routeCity: string,
    showVideo: boolean
}

interface Props {
    goodsInfo: GoodsInfo;
    payBtnCall: any;
}

export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            played: false,
            played1: false,
            showVideo: false,
            routeSrc: '',
            routePoster: '',
            routeCity: ''
        };
    }

    willReceiveProps() {
        return true;
    }

    didMount() {
        setTimeout(() => {
            this.setState({
                showVideo: true
            }, () => {
                this.event.on('pay-btn', 'click', (e) => {
                    this.props.payBtnCall?.(e);
                });
                this.event.on('play', 'click', () => {
                    (document.getElementById('video1') as HTMLVideoElement)
                        .pause();
                    (document.getElementById('video') as HTMLVideoElement)
                        .play();
                    this.setState({
                        played: true,
                        played1: false
                    });
                });

                this.event.on('play1', 'click', () => {
                    (document.getElementById('video') as HTMLVideoElement)
                        ?.pause();
                    (document.getElementById('video1') as HTMLVideoElement)
                        .play();
                    this.setState({
                        played: false,
                        played1: true
                    });
                });

                getRouteVideo().then(res => {
                    if (res && res.routeSrc) {
                        this.setState(res);
                    } else {
                        this.setState({
                            routeSrc: `${Texts.TVHOSTMAP.maiche}/lsan/2023/03/13/e9de2556eae04fd8ae57384fe278c752.high.mp4`,
                            routePoster: 'http://exam-room.image.mucang.cn/exam-room/2019/09/18/17/87cfedcfed524d24889e37dcbaf5d56f.jpg!w750*0'
                        });
                    }
                });
            });
        }, 500);
    }
}
