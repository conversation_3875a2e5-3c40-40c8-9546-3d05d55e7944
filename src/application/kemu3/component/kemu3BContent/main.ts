/*
 * ------------------------------------------------------------------
 * 科目三购买页
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GoodsInfo } from ':store/goods';
import { getRouteVideo } from ':application/kemu3/common';
import Swiper from 'swiper';
import 'swiper/swiper-bundle.css';
import { pauseAllVideos, scrollIntoView } from ':common/features/dom';
import { getPractice, getStep1Video, getStep2Video } from ':store/kemu3';
import { getSystemInfo } from ':common/core';
import Texts from ':common/features/texts';
interface State {
    routeSrc: string,
    routePoster: string,
    routeCity: string,
    currentIndex: number,
    currentIndex2: number,
    step1VideoList: any[],
    step2VideoList: any[],
    showVideo: boolean
    showVideoSteps: boolean
    // 是否有互动视频
    interact: boolean
}

interface Props {
    goodsInfo: GoodsInfo;
    payBtnCall: any;
    ke3ClaimOpen: boolean;
    userCityName: string
}

export default class extends Component<State, Props> {
    swiper: Swiper;
    swiper2: Swiper;
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            routeSrc: '',
            routePoster: '',
            routeCity: '',
            currentIndex: 0,
            currentIndex2: 0,
            step1VideoList: [],
            step2VideoList: [],
            showVideo: false,
            showVideoSteps: true,
            interact: false
        };
    }

    willReceiveProps() {
        return true;
    }

    didMount() {
        this.getStep1VideoList();
        this.getStep2VideoList();
        this.getPracticeData();
        this.setSwiper();
        this.setSwiper2();
        setTimeout(() => {
            this.setState({ showVideo: true });
        }, 500);
    }
    async getStep1VideoList() {
        const videoList = await getStep1Video();
        this.setState({
            step1VideoList: videoList
        });
    }
    async getStep2VideoList() {
        const videoList = await getStep2Video({
            step: 1
        });
        this.setState({
            step2VideoList: videoList
        });
    }
    async getPracticeData() {
        const { _userCity, _cityCode } = await getSystemInfo();
        const cityCode = _userCity || _cityCode;

        if (cityCode) {
            const hasPractice = await getPractice({
                cityCode: cityCode
            });
            if (hasPractice.value) {
                this.setState({
                    interact: true,
                    routeSrc: `${Texts.TVHOSTMAP.maiche}/lsan/2023/03/13/da19768d531749dfb59d3b9e6de4165e.high.mp4`,
                    routePoster: 'http://exam-room.mc-cdn.cn/exam-room/2023/03/13/14/28c35f0d41cd4bd3b01ddedc6257007b.jpg'
                });
            } else {
                getRouteVideo().then(res => {
                    if (res && res.routeSrc) {
                        this.setState(res);
                    } else {
                        this.setState({
                            routeSrc: `${Texts.TVHOSTMAP.maiche}/lsan/2023/03/13/e9de2556eae04fd8ae57384fe278c752.high.mp4`,
                            routePoster: 'http://exam-room.image.mucang.cn/exam-room/2019/09/18/17/87cfedcfed524d24889e37dcbaf5d56f.jpg!w750x0'
                        });
                    }
                });
            }
        }

    }
    setSwiper() {
        const $dom = this.getDOMNode().swiper as HTMLElement;
        this.swiper?.destroy();
        this.swiper = new Swiper($dom, {
            loop: false,
            slidesPerView: 1,
            centeredSlides: true,
            initialSlide: 0
        });
        this.swiper.on('slideChange', (swiper) => {
            this.changeSwiper(swiper.activeIndex);
        });
    }
    setSwiper2() {
        const $dom = this.getDOMNode().swiper2 as HTMLElement;
        this.swiper2?.destroy();
        this.swiper2 = new Swiper($dom, {
            loop: false,
            slidesPerView: 1,
            centeredSlides: true,
            initialSlide: 0
        });
        this.swiper2.on('slideChange', (swiper) => {
            this.changeSwiper2(swiper.activeIndex);
        });
    }
    onTabsClick(e) {
        const index = +e.refTarget.getAttribute('data-index');
        this.changeSwiper(index);
    }
    onTabsClick2(e) {
        const index = +e.refTarget.getAttribute('data-index');
        this.changeSwiper2(index);
    }
    changeSwiper(index: number) {
        pauseAllVideos();
        this.setState({
            currentIndex: index
        }, () => {
            this.swiper.slideTo(index);
            const scroller = document.querySelector('#step1Id' + index);
            scroller && scrollIntoView(scroller as HTMLElement, { leftThreshold: 40, rightThreshold: 40, center: true });
        });
    }
    changeSwiper2(index: number) {
        pauseAllVideos();
        this.setState({
            currentIndex2: index
        }, () => {
            this.swiper2.slideTo(index);
            const scroller = document.querySelector('#step2Id' + index);
            scroller && scrollIntoView(scroller as HTMLElement, { leftThreshold: 40, rightThreshold: 40, center: true });
        });
    }
    lookAll = (e) => {
        this.props.payBtnCall?.(e);
    }
    payBtn(e) {
        this.props.payBtnCall?.(e);
    }
    startVideoPlay = () => {
        this.setState({
            showVideoSteps: false
        });
    }
    endVideoPlay = () => {
        this.setState({
            showVideoSteps: true
        });
    }
}
