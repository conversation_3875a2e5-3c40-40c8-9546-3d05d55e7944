<import name="style" content="./main" module="S" />
<import name="videoContainer" content="../../video/main" />
<import name="videoList" content="../../videoList/main" />
<import name="myVideo" content=":component/myVideo/main" />
<div class=":page">
    <div class=":kemu3-page-content">
        <div class=":sec1">
            <sp:if value="!props.ke3ClaimOpen">
                <div class=":sec1-top"></div>
                <sp:else />
                <div class=":sec1-label">「{{props.userCityName}}」学员限时专享</div>
                <div class=":sec1-top1"></div>
            </sp:if>

        </div>

        <div class=":wraper">
            <div class=":sec2 {{state.showVideoSteps?'':S.transparent}}">
                <p class=":p1"></p>
                <div class=":icon-container">
                    <div class=":icon-item {{S.iconItem1}}">
                        <div class=":img"></div>
                        <div class=":title">3D练车</div>
                        <div class=":desc">还原每一个<br />上车要领</div>
                    </div>
                    <div class=":icon-split">

                    </div>
                    <div class=":icon-item {{S.iconItem1}}">
                        <div class=":img {{S.img2}}"></div>
                        <div class=":title">灯光模拟</div>
                        <div class=":desc">不用上车<br />随时练灯光</div>
                    </div>
                    <div class=":icon-split">

                    </div>
                    <div class=":icon-item">
                        <div class=":img {{S.img3}}"></div>
                        <div class=":title">模拟路考</div>
                        <div class=":desc">看路线 模拟考<br />路考没烦恼</div>
                    </div>
                </div>
                <div class=":video-box">
                    <com:myVideo
                        src="{{Texts.TVHOSTMAP.maiche + '/lsan/2024/01/09/461b277f4390481cb6d122a662126f6b.high.mp4'}}"
                        endPlay="{{self.endVideoPlay}}" onPlay="{{self.startVideoPlay}}">
                    </com:myVideo>
                </div>
            </div>
            <div class=":sec3" data-fragment="主图" sp-on:click="payBtn">
                {{props.goodsInfo.payPrice || '--'}}元立即{{props.goodsInfo.upgrade?'升级':'开通'}}
            </div>

            <div class=":sec4">
            </div>
            <div class=":sec5">
                <div class=":step1"></div>
                <div class=":step1-cntainer">
                    <div class=":pagination-container">
                        <sp:each for="{{state.step1VideoList}}">
                            <div id="{{'step1Id'+$index}}" sp-on:click="onTabsClick" data-index="{{$index}}"
                                class=":pagination-bullut {{$index===state.currentIndex?S.on:''}}">
                                {{$value.examProjectName}}</div>
                        </sp:each>
                    </div>
                    <div class=":pagination-mask"></div>
                    <div class=":swiper-main">
                        <div class="swiper {{S.swiperContainer}}" ref="swiper" skip-attribute="class|style">
                            <div class="swiper-wrapper" skip-attribute="class|style">
                                <sp:each for="{{state.step1VideoList}}">
                                    <div class="swiper-slide">
                                        <sp:if value='{{state.showVideo&&$index===state.currentIndex}}'>
                                            <div class=":video-box">
                                                <com:videoList
                                                    previewL="{{$value.pay?$value.previewVideoMiddle:$value.videoMiddle}}"
                                                    videoImage="{{$value.detailImage}}" isPay="{{$value.pay}}"
                                                    isLoop="{{$value.pay?false:true}}" lookAll="{{self.lookAll}}" />

                                                <!-- <com:videoContainer
                                                    previewL="{{$value.pay?$value.previewVideoMiddle:$value.videoMiddle}}"
                                                    videoImage="{{$value.detailImage}}" isPay="{{$value.pay}}"
                                                    isLoop="{{$value.pay?false:true}}" lookAll="{{self.lookAll}}" /> -->

                                            </div>
                                        </sp:if>
                                    </div>
                                </sp:each>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="{{S.step2}} {{props.hasAnyTdRoute?S.hasAnyTdRoute:''}}"></div>
                <div class=":step1-cntainer">
                    <div class=":pagination-container">
                        <sp:each for="{{state.step2VideoList}}">
                            <div id="{{'step2Id'+$index}}" sp-on:click="onTabsClick2" data-index="{{$index}}"
                                class=":pagination-bullut {{$index===state.currentIndex2?S.on:''}}">
                                {{$value.examProjectName}}</div>
                        </sp:each>
                    </div>
                    <div class=":pagination-mask"></div>
                    <div class=":swiper-main">
                        <div class="swiper {{S.swiperContainer}}" ref="swiper2" skip-attribute="class|style">
                            <div class="swiper-wrapper" skip-attribute="class|style">
                                <sp:each for="{{state.step2VideoList}}">
                                    <div class="swiper-slide">
                                        <sp:if value='{{state.showVideo&&$index===state.currentIndex2}}'>
                                            <div class=":video-box">
                                                <com:videoList previewL="{{$value.videoUrl}}"
                                                    videoImage="{{$value.cover}}" isLoop="{{true}}" />
                                                <!-- <com:videoContainer previewL="{{$value.videoUrl}}"
                                                    videoImage="{{$value.cover}}" isLoop="{{true}}" /> -->

                                            </div>
                                        </sp:if>
                                    </div>
                                </sp:each>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="{{S.step3}} {{state.interact?S.interact:''}}"></div>
                <sp:if value='{{state.showVideo}}'>
                    <div class=":video-box" skip="{{state.routeSrc?'true':'false'}}">

                        <com:videoContainer previewL="{{state.routeSrc}}" videoImage="{{state.routePoster}}"
                            isShowControls="{{true}}" isLoop="{{false}}" />

                    </div>
                </sp:if>

            </div>

            <sp:if value="props.ke3ClaimOpen">
                <div class=":sec6">
                    <div class=":div1"></div>
                    <div class=":div2">考不过立即补偿60元</div>
                    <div class=":div3"></div>
                </div>
            </sp:if>

            <div class=":sec8">
                <div class=":title"></div>
                <div class=":sec8-w">
                    <div class=":qa">
                        <p class=":p-q">科三VIP适合哪些学员购买？包含哪些权益？</p>
                        <p class=":p-a"> 适合小车c1和c2的学员购买，可以提前学习上车准备、灯光考试、路考规则和操作要领，提前看考场。<br />

                            科三VIP包含：科三3d练车、灯光模拟、项目讲解视频、考场实拍视频 <span sp:if="{{state.interact}}">，互动视频</span> 。</p>
                    </div>
                    <div class=":qa">
                        <p class=":p-q">有效期从什么时候开始算？</p>
                        <p class=":p-a">有效期从您购买之日算起，有效期180天。</p>
                    </div>
                    <div class=":qa">
                        <p class=":p-q">我购买了科三VIP，为什么不能用？</p>
                        <p class=":p-a">请检查是否已经登录了账号，购买成功后，需要登录您的账号才能享受服务。同时一个账号最多只支持3台设备使用。</p>
                    </div>
                    <div class=":qa">
                        <p class=":p-q">购买后可以退款吗？</p>
                        <p class=":p-a">本商品是虚拟商品，不支持退款哦。</p>
                    </div>
                    <div class=":qa">
                        <p class=":p-q">其他问题及注意事项？</p>
                        <p class=":p-a">如遇问题，可直接联系右上角帮助中的在线客服或者拨打客服电话400-056-1617咨询，我们会及时为您解答。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
