.page-container {
    height: 100%;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    -webkit-flex-direction: column;
    background-size: 100% auto;
    background-color: #280d37;
}

.kemu3-page-content {
    flex: 1;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    background-color: #090f24;

    .sec1 {
        height: 404px;
        background: url(../images/kemu3/1.png) top no-repeat;
        background-size: 100% 100%;
        position: relative;
        z-index: 0;

        .sec1-top {
            position: absolute;
            top: 104px;
            left: 50%;
            transform: translateX(-50%);
            width: 326px;
            height: 147px;
            background: url(../images/kemu3/1_top.png) no-repeat;
            background-size: 100% 100%;
        }

        .sec1-label {
            position: absolute;
            top: 100px;
            left: 30px;
            background: linear-gradient(79deg, #37bbf4 8%, #67e3f4 63%, #fede89 95%);
            border-radius: 17px;
            box-shadow: 0px -1px 5px 0px rgba(255, 255, 255, 0.50) inset, -4px -2px 7px 0px rgba(255, 255, 255, 0.47) inset;
            font-size: 18px;
            font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 2.0-Black;
            font-weight: bold;
            text-align: left;
            color: #ffffff;
            line-height: 25px;
            height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 15px 0 8px;
            text-shadow: 0px 2px 4px 0px rgba(0, 89, 219, 0.25);
        }

        .sec1-top1 {
            position: absolute;
            top: 134px;
            left: 50%;
            transform: translateX(-50%);
            width: 332px;
            height: 119px;
            background: url(../images/kemu3/1_top1.png) no-repeat;
            background-size: 100% 100%;
        }
    }

    .wraper {
        padding: 0 15px 35px 15px;
        margin-top: -138px;
        position: relative;
    }

    .sec2 {
        background: #ffffff;
        padding: 20px 15px 53px 15px;
        border-radius: 20px;
        position: relative;

        &.transparent {
            background-color: transparent;
        }

        .p1 {
            width: 135px;
            height: 33px;
            background: url(../images/kemu3/step.png) no-repeat;
            background-size: 100% 100%;
        }

        .icon-container {
            margin-top: 14px;
            display: flex;
            justify-content: space-between;
            padding-left: 14px;
            padding-right: 6px;


            .icon-split {
                width: 13px;
                height: 16px;
                background: url(../images/kemu3/step_split.png) no-repeat;
                background-size: 100% 100%;
                margin-left: 10px;
                margin-right: 10px;
                position: relative;
                top: 22px;
            }

            .icon-item {
                flex: 1;
                display: flex;
                align-items: center;
                flex-direction: column;

                &.icon-item1 {
                    flex: 0 0 68px;
                }

                .img {
                    width: 60px;
                    height: 60px;
                    background: url(../images/kemu3/step_01.png) no-repeat;
                    background-size: 100% 100%;

                    &.img2 {
                        background: url(../images/kemu3/step_02.png) no-repeat;
                        background-size: 100% 100%;
                    }

                    &.img3 {
                        background: url(../images/kemu3/step_03.png) no-repeat;
                        background-size: 100% 100%;
                    }
                }

                .title {
                    font-size: 15px;
                    font-family: PingFangSC, PingFangSC-Medium;
                    font-weight: 700;
                    color: #00223b;
                    margin-top: 8px;
                    margin-bottom: 1px;
                    line-height: 21px;
                }

                .desc {
                    font-size: 13px;
                    font-family: PingFangSC, PingFangSC-Medium;
                    font-weight: 500;
                    color: #8f97ad;
                    line-height: 17px;
                    text-align: center;
                }
            }
        }

        .video-box {
            width: 345px;
            height: 198px;
            border-radius: 6px;
            margin: 0 auto;
            position: absolute;
            z-index: 5;
            top: 0;
            left: 0;
        }
    }

    .sec3 {
        margin: -36px auto 0px auto;
        width: 325px;
        height: 68px;
        background: url(../images/kemu3/button_bg.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-family: AlibabaPuHuiTi_2_115_Black,
            AlibabaPuHuiTi_2_115_Black-Regular;
        font-weight: 700;
        color: #ffffff;
        text-shadow: 0px 4px 4px #fe9d0c;
        position: relative;

        .button-icon {
            display: flex;
            width: 28px;
            height: 17px;
            background: url(../images/kemu3//button_icon.png) no-repeat;
            background-size: 100% 100%;
            position: relative;
            top: 1px;
        }
    }

    .sec4 {
        width: 330px;
        height: 57px;
        background: url(../images/kemu3/step2_tips.png) no-repeat;
        background-size: 100% 100%;
        margin: 24px auto 0 auto;
    }

    .sec5 {
        width: 345px;
        margin-top: 20px;
        background: #ffffff;
        box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.3);
        border-radius: 20px;
        padding: 20px;

        .step1 {
            width: 208px;
            height: 47px;
            background: url(../images/kemu3/step2_01.png) no-repeat;
            background-size: 100% 100%;
        }

        .step1-cntainer {
            position: relative;
            width: 100%;

            .pagination-mask {
                position: absolute;
                right: -15px;
                top: 0px;
                width: 20px;
                height: 28px;
                background: linear-gradient(270deg,
                        #ffffff 50%,
                        rgba(255, 255, 255, 0));
            }

            .pagination-container {
                margin-top: 10px;
                white-space: nowrap;
                overflow-x: auto;
                overflow-y: hidden;
                display: flex;

                .pagination-bullut {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 14px;
                    padding: 0px 15px;
                    height: 28px;
                    background: #f2f2f2;
                    font-size: 14px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    text-align: center;
                    color: #666666;
                    margin-right: 15px;

                    &.on {
                        background: linear-gradient(90deg, #1173fe, #4f98fe);
                        box-shadow: 0px 1px 5px 0px rgba(255, 255, 255, 0.1) inset;
                        font-size: 14px;
                        font-family: PingFangSC, PingFangSC-Medium;
                        font-weight: 500;
                        color: #ffffff;
                    }

                    &:last-child {
                        margin-right: 0px;
                    }
                }
            }

            .swiper-main {
                .swiper-container {
                    margin-top: 10px;
                    z-index: 0 !important;
                }
            }
        }

        .video-box {
            height: 177px;
            position: relative;
            border-radius: 8px;
            padding-right: 2px;
            padding-left: 2px;

            .video {
                padding-left: -1px;
                width: 100%;
                height: 100%;
            }
        }

        .step2 {
            width: 188px;
            height: 47px;
            background: url(../images/kemu3/step2_02.png) no-repeat;
            background-size: 100% 100%;
            margin-top: 30px;

            &.hasAnyTdRoute {
                width: 245px;
                background: url(../images/kemu3/step2_02_tdRoute.png) no-repeat;
                background-size: 100% 100%;
                margin-top: 30px;
            }
        }

        .step3 {
            width: 245px;
            height: 51px;
            background: url(../images/kemu3/step2_03.png) no-repeat;
            background-size: 100% 100%;
            margin-top: 30px;
            margin-bottom: 10px;

            &.interact {
                background: url(../images/kemu3/step2_03_interact.png) no-repeat center left/209px 51px;
            }
        }
    }

    .sec6 {
        margin-top: 24px;
        .div1 {
            width: 212px;
            height: 40px;
            background: url(../images/kemu3/bgbc2.png) no-repeat;
            background-size: 100% 100%;
            margin: 0 auto;
        }

        .div2 {
            font-size: 15px;
            text-align: center;
            color: #a59793;
            line-height: 21px;
            text-align: center;
        }

        .div3 {
            width: 345px;
            height: 90px;
            background: url(../images/kemu3/bgbc1.png) no-repeat;
            background-size: 100% 100%;
            margin: 15px auto 0 auto;
        }
    }

    .sec8 {
        margin-top: 17px;

        .sec8-w {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 23px 20px 20px 20px;
        }

        .title {
            width: 190px;
            height: 54px;
            background: url(../images/kemu3/10.png) no-repeat;
            background-size: 100% 100%;
            margin: 0 auto 13px auto;
        }

        .qa {
            color: #494455;
            font-size: 15px;
            line-height: 21px;
            padding-top: 12px;

            .p-q {
                padding-left: 26px;
                background: url(../images/kemu3/11.png) no-repeat left 2px;
                background-size: 17px 15px;
            }

            .p-a {
                padding-left: 26px;
                padding-top: 8px;
            }
        }
    }
}

.hide {
    display: none !important;
}
