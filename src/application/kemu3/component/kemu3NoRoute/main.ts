/*
 * ------------------------------------------------------------------
 * 科目二购买页
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GoodsInfo } from ':store/goods';
import Swiper from 'swiper';
import 'swiper/swiper-bundle.css';

interface State {
}

interface Props {
    goodsInfo: GoodsInfo;
    payBtnCall: any;
    routeSrc: string,
    routePoster: string,
    routeCity: string

}

export default class extends Component<State, Props> {
    Swiper: any
    get recommends() {
        return [
            {
                name: '王教练',
                desc: '南宁  艺通驾校',
                avatar: 'https://jiakao-web.image.mucang.cn/jiakao-web/2018/08/03/15/15897d82c7ff479983818d0419959fac',
                text: '以前我有一些学员学科目三很困难，容易紧张，总是挂。现在用了这个好多了，通过率明显提高了很多'
            },
            {
                name: '吕教练',
                desc: '北京  远方驾校',
                avatar: 'https://jiakao-web.image.mucang.cn/jiakao-web/2018/08/03/15/300c5a1d41264390990839e5e81233a1',
                text: '学员反馈这个挺好用的，可以随时熟悉考点，了解真实考场情况，背路线的效率快多了'
            },
            {
                name: '吕教练',
                desc: '石家庄  君道驾校',
                avatar: 'https://jiakao-web.image.mucang.cn/jiakao-web/2018/08/03/15/726491ffe09d4cb898decc784a5a278f',
                text: '灯光模拟挺好的，推荐学员用了这个。以前灯光容易挂，用了这个明显通过率好一些，也能缓解学员的紧张情绪。挺靠谱'
            },
            {
                name: '黄教练',
                desc: '武汉  绅宝驾校',
                avatar: 'https://jiakao-web.image.mucang.cn/jiakao-web/2018/08/03/15/6561af320f554db98ed8137be574fbe2',
                text: '强烈推荐。实地看考场时间很有限。提前用这个熟悉考场视频，在实地看考场的时候效率会更高，学员心态更稳'
            },
            {
                name: '王教练',
                desc: '西安  西高驾校',
                avatar: 'https://jiakao-web.image.mucang.cn/jiakao-web/2018/08/03/15/114fd4411c9c47358466b53f25cab4a4',
                text: '会推荐学员用。看了考场视频的学员，心态好很多，背路线也没那么费劲了。都能顺利过科三'
            }
        ];
    }
    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
        };
    }

    willReceiveProps() {
        return true;
    }

    didMount() {
        this.setSwiper();
    }
    setSwiper() {
        const $dom = this.getDOMNode().swiper as HTMLElement;
        setTimeout(() => {
            // eslint-disable-next-line no-undef
            this.Swiper = new Swiper($dom, {
                loop: false
            });
        }, 300);
    }
}
