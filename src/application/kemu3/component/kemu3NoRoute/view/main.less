.kemu3-no-route {
    display: flex;
    flex-direction: column;
    -webkit-flex-direction: column;
    background-size: 100% auto;
    background-color: #190E35;
    padding-bottom: 40px;


    .sec1 {
        height: 343px;
        width: 375px;
        background: url(../images/1.png) no-repeat;
        background-size: 100% 100%;
        position: relative;
    }

    .icons-w {
        position: absolute;
        bottom: 12px;
        left: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .icon-c {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;

            i {
                color: #FF3913;
                font-size: 12px;
                background-color: #ffffff;
                border-radius: 10px 0 10px 0;
                padding: 3px 6px;
                position: absolute;
                right: -20px;
                top: -8px;
                transform: scale3d(.9, .9, 1);
                transform-origin: left;
            }

            label {
                width: 41px;
                height: 40px;
                background-size: 100% 100%;
                margin-bottom: 5px;
            }

            span {
                font-size: 12px;
                color: #ffffff;
                line-height: 1.5;
            }

            .icon-spxj {
                background-image: url(../images/jkbd__vip_spxj.png);
            }

            .icon-3dlc {
                background-image: url(../images/jkbd__vip_3Dlc.png);
            }

            .icon-zskc {
                background-image: url(../images/jkbd__vip_zskc.png);
            }

            .icon-dgmn {
                background-image: url(../images/jkbd__vip_dgmn.png);
            }
        }
    }

    .sec2 {
        padding: 0 15px;
    }

    .sec2-w {
        background: #ffffff;
        border-radius: 10px;
        padding: 15px 25px 20px 25px;

        .div {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 33px;

            i {
                width: 19px;
                height: 11px;
                background: url(../images/2.png) no-repeat center;
                background-size: 19px 11px;
            }

            h3 {
                font-size: 18px;
                font-weight: bold;
                color: #F84D30;
                padding: 0 10px;
            }
        }

        .p {
            //display: flex;
            //align-items: center;
            font-size: 14px;
            color: #393543;
            margin-top: 10px;
            padding-left: 25px;
            background: url(../images/3.png) no-repeat left center;
            background-size: 17px 15px;
            line-height: 21px;

            b {
                font-size: 14px;
                color: #F84D30;
            }
        }
    }

    .sec3 {
        height: 389px;
        width: 345px;
        background: url(../images/4.png) no-repeat;
        background-size: 100% 100%;
        margin: 20px auto 0px auto;
    }

    .sec-com {
        padding: 0 15px;

        .sec-com-w {
            background-color: #ffffff;
            border-radius: 10px;
            padding-bottom: 40px;
        }

        .title {
            display: flex;
            align-items: center;
            justify-content: center;

            p {
                color: #ffffff;
                font-weight: bold;
                font-size: 20px;
                line-height: 28px;
                padding: 8px 12px;
                border-radius: 0 0 10px 10px;
                background: linear-gradient(180deg, #FC845E 0%, #F84D30 100%);
            }
        }

        .desc {
            color: #363A3E;
            font-size: 16px;
            line-height: 22px;
            margin-top: 10px;
            text-align: center;
        }

        .media {
            width: 302px;
            height: 172.5px;
            position: relative;
            overflow: hidden;
            background: #ffffff;
            border-radius: 7px;
            background: url(../images/16.png) no-repeat;
            background-size: 100% 100%;
            margin: 20px auto 0 auto;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .items {
            display: flex;
            //align-items: center;
            justify-content: space-around;
            margin-top: 30px;

            p {
                display: flex;
                flex-direction: column;
                align-items: center;

                b {
                    width: 56px;
                    height: 56px;
                    margin-bottom: 8px;
                }

                span {
                    color: #333333;
                    font-size: 13px;
                    line-height: 1.5;
                }
            }
        }
    }

    .media {

        video {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }

        .play-poster {
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            bottom: 0;
            z-index: 50;
            width: 100%;
            height: 100%;
        }

        .play-btn {
            position: absolute;
            top: 59px;
            left: 108px;
            z-index: 60;
            width: 52px;
            height: 52px;
            border-radius: 52px;
            background: url(../images/10.png) no-repeat center center;
            background-size: 52px 52px;
        }

        .play-btn1 {
            left: 123px;
        }
    }

    .sec4-w {
        .media {
            background: url(../images/ke3_zhibo.png) no-repeat;
            background-size: 100% 100%;
        }
    }

    .sec5-w {
        .media {
            margin-top: 0;
            background: url(../images/ke3_shijing.gif) no-repeat;
            background-size: 100% 100%;
        }

        .flag1 {
            display: block;
            color: #fff;
            font-size: 14px;
            font-weight: bold;
            line-height: 30px;
            padding-left: 10px;
            width: 145px;
            height: 29px;
            background: url(../images/13.png) no-repeat;
            background-size: 100% 100%;
            margin: 30px 0 0 40px;
        }

        .media1 {
            margin-top: 0;
            background: url(../images/ke3_dgmn.gif) no-repeat;
            background-size: 100% 100%;
        }

        .flag2 {
            display: block;
            width: 120px;
            height: 29px;
            background: url(../images/14.png) no-repeat;
            background-size: 100% 100%;
            color: #fff;
            font-size: 14px;
            font-weight: bold;
            line-height: 30px;
            padding-left: 10px;
            margin: 30px 0 0 40px;
        }
    }

    .sec7 {
        padding: 0 15px;

        .sec7-w {
            background-color: #ffffff;
            border-radius: 10px;
            margin-top: 20px;
            padding: 25px 0 30px 20px;
            position: relative;
            overflow: hidden;
        }

        .flag {
            position: absolute;
            left: 0;
            top: 0;
            width: 96px;
            height: 26px;
            z-index: 70;
            background: url(../images/9.png) no-repeat;
            background-size: 100% 100%;
        }

        .title {
            width: 267px;
            height: 39px;
            background: url(../images/7.png) no-repeat;
            background-size: 100% 100%;
            margin: 0 auto;
        }

        .slider-wrap {
            height: 159px;
            margin-top: 25px;
            padding-right: 42px;
        }

        .swiper-container {
            flex: 1;
            -webkit-flex: 1;
            width: 100%;
            height: 100%;
        }

        .swiper-pagination {
            width: 100%;
            text-align: center;
        }

        .swiper-pagination-bullet {
            background: #B9BFCB;
            width: 4px;
            height: 4px;
            border-radius: 4px;
            margin: 0 2px;
        }

        .swiper-pagination-bullet-active {
            background: #3D5AFF;
        }

        .swiper-slide {
            width: 280px;
            height: 159px;

            padding-right: 20px;
            box-sizing: border-box;
            transition: 300ms;
            -webkit-transition: 300ms;
            transform: scale3d(1, 1, 1) translate3d(0, 0, 0);
            -webkit-transform: scale3d(1, 1, 1) translate3d(0, 0, 0);
            //box-shadow: 1px 3px 6px 1px rgba(0, 0, 0, 1);
            //border-top-left-radius: 6px;
            //border-top-right-radius: 6px;
        }

        .swiper-slide-active,
        .swiper-slide-duplicate-active {
            transform: scale(1);
            -webkit-transform: scale(1);
            //box-shadow: 1px 3px 6px 1px rgba(0, 0, 0, 0.19);
            height: 100%;
            //overflow-y: scroll;
            overflow: hidden;
        }

        .jl-w {
            position: relative;
            display: flex;
            padding: 23px 0;
            height: 100%;
            background-color: #FFF4F1;
            border-radius: 7px;

            .icon {
                position: absolute;
                left: 0;
                top: 0;
                width: 86px;
                height: 26px;
                background: url(../images/8.png) no-repeat;
                background-size: 100% 100%;
            }

            .jl-l {
                width: 90px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 0 20px;
                box-sizing: content-box;
            }

            .avatar {
                width: 56px;
                height: 56px;
                border-radius: 56px;
            }

            .desc {
                color: #333333;
                font-size: 14px;
                font-weight: bold;
                text-align: center;
                line-height: 1.5;
                padding-top: 8px;
            }

            .jl-r {
                display: flex;
                flex-direction: column;
                flex: 1;
                padding-right: 10px;
            }

            .name {
                font-size: 16px;
                color: #333333;
            }

            .text {
                font-size: 12px;
                color: #333333;
                line-height: 1.5;
                padding-top: 8px;
            }
        }
    }

    .sec8 {
        padding: 0 15px;
        margin-top: 20px;

        .sec8-w {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 26px 20px 20px 20px;
        }

        .title {
            width: 267px;
            height: 39px;
            background: url(../images/6.png) no-repeat;
            background-size: 100% 100%;
            margin: 0 auto 13px auto;
        }

        .qa {
            color: #494455;
            font-size: 15px;
            line-height: 21px;
            padding-top: 12px;

            .p-q {
                padding-left: 26px;
                background: url(../images/11.png) no-repeat left 2px;
                background-size: 17px 15px;
            }

            .p-a {
                padding-left: 26px;
                padding-top: 8px;
            }
        }
    }

    .ic1 {
        background: url(../images/ic_1.png) no-repeat;
        background-size: 100%;
    }

    .ic2 {
        background: url(../images/ic_2.png) no-repeat;
        background-size: 100%;
    }

    .ic3 {
        background: url(../images/ic_3.png) no-repeat;
        background-size: 100%;
    }

    .ic4 {
        background: url(../images/ic_4.png) no-repeat;
        background-size: 100%;
    }

    .ic5 {
        background: url(../images/ic_5.png) no-repeat;
        background-size: 100%;
    }

    .ic6 {
        background: url(../images/ic_6.png) no-repeat;
        background-size: 100%;
    }

    .proto-coupon {
        display: flex;
        justify-content: space-between;

        .coupon-pick {
            display: flex;
            height: 30px;
            box-sizing: border-box;
            margin-top: 4px;
            padding-right: 5px;

            .coupon-enter {
                font-size: 12px;
                line-height: 30px;
                color: #FFDC93;
                text-align: right;
            }
        }
    }

    .footer {
        height: 60px;
        background: url(../images/12.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;

        .fl {
            .p1 {
                color: #FFF2AD;
                font-size: 18px;
                font-weight: bold;
                line-height: 27px;
            }

            .p2 {
                color: #FFF2AD;
                font-size: 12px;
                line-height: 18px;
                transform: scale3d(0.9, 0.9, 1);
                transform-origin: left;
            }
        }

        .fr {
            padding-top: 5px;

            b {
                font-weight: bold;
                color: #ffffff;
                font-size: 18px;
            }

            span {
                color: #ffffff;
                font-size: 14px;
            }

            i {
                padding-left: 12px;
                color: rgba(255, 255, 255, .6);
                font-size: 14px;
                text-decoration: line-through;
            }

        }
    }

    .divider {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 56px;
        margin: -18px 0;

        i {
            width: 15px;
            height: 56px;
            background: url(../images/5.png) no-repeat;
            background-size: 100% 100%;
        }
    }

    .hide {
        display: none !important;
    }

}
