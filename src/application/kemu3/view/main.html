<import name="style" content="./main" module="S" />

<import name="header" content=":component/header/main" />
<import name="moveGoods" content=":component/moveGoods/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="Kemu3BContent" content=":application/kemu3/component/kemu3BContent/main" />
<import name="Kemu34Content" content=":application/kemu3/component/kemu34Content/main" />

<div class="page-container :car-ke34">
    <div class=":header">
        <com:header title="{{self.title}}" theme="black" qaKey="{{self.qaKey}}" endTheme="black"
            back="{{self.onBackClick}}" />
    </div>

    <div class="{{S.main}} {{S.content}}" id="car-ke34" ref="scroller" sp-on:scroll="pageScroll">
        <div class="{{self.currentGoods.groupKey ===  GroupKey.ChannelKe3Group  ? '' : S.hide}}">
            <com:Kemu3BContent goodsInfo="{{self.currentGoods}}" payBtnCall="{{self.onPayBtnClick}}" ke3ClaimOpen="{{state.ke3ClaimOpen}}" userCityName="{{state.userCityName}}" />
        </div>
        <sp:if value="self.getGroupKeyInfo(GroupKey.ChannelKe34).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKe34).showPage">
            <div class="{{self.currentGoods.groupKey === GroupKey.ChannelKe34 ? '' : S.hide}}">
                <com:Kemu34Content goodsInfo="{{self.currentGoods}}" payBtnCall="{{self.onPayBtnClick}}"
                    comparePricePool="{{state.comparePricePool}}" goAuth="{{self.goAuth}}" ke3ClaimOpen="{{state.ke3ClaimOpen}}"/>
            </div>
        </sp:if>
    </div>
    <div class="{{S.footer}}">
        <sp:if value="state.goodsList.length > 1">
            <com:bottomTabs tabIndex="{{state.tabIndex}}" labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}" goodsList="{{state.goodsList}}"
                tabChange="{{self.tabChangeCall}}" />
        </sp:if>
    </div>

    <div style="position: relative;z-index: 10;">
        <com:buyButton>
            <div sp:slot="couponEntry">
                <div sp-on:click="onCouponEntryClick">
                    {{self.currentCoupon.code?'已优惠' +
                    self.currentCoupon.price + '元>':'领取优惠券'}}
                </div>
            </div>
        </com:buyButton>
    </div>

    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
    <com:moveGoods info="{{(self.moveGoodsVideo[self.currentGoods.groupKey] || {})}}"
         groupKey="{{self.currentGoods.groupKey}}" />
</div>
