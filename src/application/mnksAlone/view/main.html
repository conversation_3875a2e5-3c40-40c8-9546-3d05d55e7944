<import name="style" content="./main" />
<import name="payType" content=":component/payType/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="PriceTag" content=":component/priceTag/main" />
<import name="com" content=":application/mnksAlone/component/com/main" />

<div class="container mnks-alone {{Platform.isIOS ? 'ios-container': ''}}">
    <div class="close" sp-on:click="onCloseClick"></div>

    <com:com
        tabIndex="{{state.tabIndex}}"
        goodsList="{{state.goodsInfoPool}}"
    >
        <div class="hd-tabs" sp:slot="bottomTabs">
            <div
                class="hd-tab {{state.tabIndex===0?'active':''}}"
                data-idx="0"
                sp-on:click="onTabClick"
            >
                <p class="btn-box">
                    {{state.goodsInfoPool[0].name}}&nbsp;<i class="price1"
                        >{{state.goodsInfoPool[0].payPrice || '--'}}</i
                    >元
                </p>
                <div class="dec">只能使用1次</div>
            </div>
            <div
                class="hd-tab {{state.tabIndex===1?'active':''}}"
                data-idx="1"
                sp-on:click="onTabClick"
            >
                <p class="btn-box">
                    {{state.goodsInfoPool[1].name}}&nbsp;<i class="price1"
                        >{{state.goodsInfoPool[1].payPrice || '--'}}</i
                    >元
                </p>
                <div class="dec">不限次数</div>
                <com:PriceTag
                    goodsInfo="{{state.goodsInfoPool[1]}}"
                    comparePriceMap="{{state.comparePricePool}}"
                    labelMap="{{state.labelPool}}"
                />
            </div>
        </div>
    </com:com>

    <div class="footer">
        <div class="pay-type {{Platform.isIOS && 'hide'}}">
            <com:payType theme="mnks" />
        </div>
        <div class="buy-btn">
            <com:buyButton noPayType="true">
                <div sp:slot="couponEntry" class="go_coupon">
                    {{self.nowCouponInfo.couponCode?'已优惠' +
                    self.nowCouponInfo.priceCent + '元':''}}
                </div>
            </com:buyButton>
        </div>
    </div>
</div>
