html,
body {
    height: 100vh;
    overflow: hidden;
    max-width: 100% !important;
}

.mnks-alone {
    &.container {
        position: relative;
        height: 100vh;
        background: url(../images/mnks_3.png) no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
    }

    &.ios-container {
        background: url(../images/mnks_3_ios.png) no-repeat;
        background-size: 100% 100%;
        padding-bottom: 110px;
    }

    .close {
        position: absolute;
        right: 0;
        top: 0;
        padding: 10px;
        width: 24px;
        height: 24px;
        background: url(../images/close.png) no-repeat center;
        background-size: 24px 24px;
        z-index: 1;
        box-sizing: content-box;
    }

    .footer {
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        width: 100%;

        .buy-footer {
            padding: 0 20px;
            background-color: transparent;
            .other-info {
                height: 32px;
            }
        }

        .tag {
            color: #6F2117 !important;
            background: linear-gradient(90deg, #FFD878 0%, #FFC400 100%) !important;
            border-radius: 33px 33px 33px 2px !important;
            top: -22px !important;
            transform: scale3d(0.85, 0.85, 0.85);
        }
    }

    .pay-type {
        margin-bottom: -5px;
    }

    .buy-btn {
        width: 100%;
    }

    .coupon-pick {
        background: transparent !important;
        color: #AE8A79 !important;
    }

    .hd-tabs {
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        -webkit-justify-content: space-between;
        width: 100%;
        padding: 10px 15px 0 15px;

        .hd-tab {
            position: relative;
            font-weight: bold;
            color: #843405;
            width: 154px;
            height: 70px;
            border-radius: 8px;
            background-color: #FFFFFF;

            .btn-box {
                height: 42px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 14px;
            }

            .dec {
                border-radius: 0 0 8px 8px;
                height: 28px;
                display: flex;
                justify-content: center;
                align-items: center;
                background: #FEF0E7;
                font-size: 12px;
                color: #AA4120;
            }

            &.active {
                color: #ffffff;
                background: linear-gradient(113deg, #353B4E 0%, #1D222B 100%);
                border: none;

                .dec {
                    background: linear-gradient(99deg, #FFEDDF 0%, #FFE1C8 100%);
                    color: #692204;
                }

                &::after {
                    content: '';
                    position: absolute;
                    left: 0px;
                    top: 0;
                    width: 30px;
                    height: 15px;
                    background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/2-icon.png) no-repeat center center/cover;
                }
            }
        }

    }

    .sure-buy-mask {
        background-color: rgba(0, 0, 0, 0.8);
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        .content {
            background-color: #fff;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 8px;
            padding: 15px;
            width: 280px;
            .title{
                font-weight: bold;
                font-size: 16px;
            }
            .info {
                font-size: 18px;
                line-height: 28px;
                margin-top: 15px;
            }

            .active-box {
                margin-top: 15px;
                display: flex;
                .sure {
                    margin-left: auto;
                    font-size: 14px;
                    padding: 10px 15px;
                    color: white;
                    background: green;
                    border-radius: 4px;

                }
            }
        }
    }

}
