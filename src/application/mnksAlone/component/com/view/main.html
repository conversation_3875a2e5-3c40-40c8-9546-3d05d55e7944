<import name="style" content="./main" />
<div class="c-alone-container">
    <div class="content">
        <!-- 资格证不能显示图片 -->
        <div
            class="title {{URLCommon.isZigezheng?'zigezheng':''}} {{ state.kemu == 1 ? 'title1': 'title4'}}"
        ></div>

        <sp:slot name="bottomTabs"></sp:slot>
        <div class="flex-content">
            <div class="icons-w {{props.tabIndex == 1 ? '': 'hide'}}">
                <div class="icon-c">
                    <label class="i1_1"></label>
                    <span>精简题库</span>
                </div>
                <div class="icon-c">
                    <label class="i1_2"></label>
                    <span>答题技巧</span>
                </div>
                <div class="icon-c">
                    <label class="i1_3"></label>
                    <span>考前秘卷</span>
                </div>
                <!-- 扣满12分和资格证没有考不过补偿 -->
                <sp:if value="!URLCommon.isScore12 && !URLCommon.isZigezheng">
                    <div class="icon-c">
                        <label class="i1_4"></label>
                        <span>考不过补偿</span>
                    </div>
                </sp:if>
            </div>

            <div class="alone-dec-box {{props.tabIndex == 0 ? '': 'hide'}}">
                <div class="icon"></div>
                <div class="text">深度还原考场电脑界面考试不紧张</div>
            </div>
        </div>
    </div>
</div>
