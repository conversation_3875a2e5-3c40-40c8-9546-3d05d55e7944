.c-alone-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
    .content {
        width: 100%;
        height: 100%;
        margin: 0 auto;
        box-sizing: border-box;
        position: relative;
        display: flex;
        flex-direction: column;
        .flex-content{
            display: flex;
            flex-direction: column;
            justify-content: center;
            flex: 1;
            .alone-dec-box {
                width: 315px;
                margin: 0 auto;
                height: 46px;
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid #FFE3CC;
                display: flex;
                justify-content: center;
                align-items: center;
                .icon{
                    width: 32px;
                    height: 32px;
                    background: url(../images/<EMAIL>) no-repeat center center/cover;
                }
                .text{
                    margin-left: 5px;
                    font-size: 16px;
                    color: #8F3112;
                }
            }
        }
    }

    .title {
        width: 281px;
        height: 23px;
        margin: 15px 0 0 15px;
        transform-origin: left top;
        flex-shrink: 0;
        &.title1 {
            background: url(../images/t1.png) no-repeat;
            background-size: 100% 100%;
        }

        &.title4 {
            background: url(../images/t4.png) no-repeat;
            background-size: 100% 100%;
        }

        &.zigezheng {
            width: 243px;
            height: 22px;
            background: url(../images/mnks_5.png) no-repeat;
            background-size: 100% 100%;
        }
    }

    .ht {
        font-size: 13px;
        color: #8F3112;
        line-height: 15px;
        text-align: center;
        padding-top: 8px;
    }

    .icons-w {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 5px 10px 0 10px;

        .icon-c {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;

            label {
                width: 32px;
                height: 32px;
                background-size: 100% 100%;
                margin-bottom: 5px;
            }

            span {
                font-size: 12px;
                color: #8F3112;
                line-height: 1;
            }

            .i1_1 {
                background-image: url(../images/i1_1.png);
            }

            .i1_2 {
                background-image: url(../images/i1_2.png);
            }

            .i1_3 {
                background-image: url(../images/i1_3.png);
            }

            .i1_4 {
                background-image: url(../images/i1_4.png);
            }

            .i2_1 {
                background-image: url(../images/i2_1.png);
            }

            .i2_2 {
                background-image: url(../images/i2_2.png);
            }

            .i2_3 {
                background-image: url(../images/i2_3.png);
            }

            .i2_4 {
                background-image: url(../images/i2_4.png);
            }

            .i2_5 {
                background-image: url(../images/i2_5.png);
            }

            .i3_1 {
                background-image: url(../images/i1_3.png);
            }

            .i3_2 {
                background-image: url(../images/i1_4.png);
            }

            .i3_3 {
                background-image: url(../images/i2_5.png);
            }

            .i3_4 {
                background-image: url(../images/i1_4.png);
            }
        }
    }

}


