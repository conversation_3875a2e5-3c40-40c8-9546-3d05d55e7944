import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import {
    URLCommon
} from ':common/env';

interface Props {
   
}
export default class extends Component<any, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
        this.state = {
            tiku: URLCommon.tiku,
            kemu: URLCommon.kemu,
            isScore12: URLCommon.isScore12
        };
    }
    willReceiveProps() {
        return true;
    }
}
