.panel-motoshort {
    background: #6B6373;
    flex: 1;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;

    .buy-btn-box {
        position: relative;
        width: 345px;
        height: 89px;
        margin: 0 auto;
        background: url(https://web-resource.mc-cdn.cn/web/jiakaobaodian-h5/buy.png) no-repeat center center/cover;

        .buy-btn {
            padding-top: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .preDiscountPrice {
            position: relative;
            font-size: 12px;
            transform: scale(0.8);
            color: white;
            margin-left: 4px;
            margin-top: 4px;

            &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 0;
                width: 100%;
                height: 1px;
                background-color: white;
            }
        }

        .time-tip {
            position: absolute;
            top: 15px;
            right: 18px;
            transform: translateY(-50%);
            height: 22px;
            font-size: 12px;
            display: flex;
            align-items: center;
            padding: 0 6px;
            background: linear-gradient(90deg, #FFD878 0%, #FFC400 100%);
            border-radius: 33px 33px 33px 2px;
            color: #6F2117;
            overflow: hidden;
            z-index: 1;

            .sp {
                transform: scaleX(0.9) scaleY(0.9);
                transform-origin: bottom;
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                -webkit-line-clamp: 1;
            }

            .count-content {
                margin-left: 1px;
                transform: scaleX(0.9) scaleY(0.9);
                transform-origin: bottom;
            }
        }

        .tip {
            position: absolute;
            top: 15px;
            right: 18px;
            transform: translateY(-50%);
            font-size: 12px;
            line-height: 14px;
            color: #6F2117;
            background: linear-gradient(90deg, #FFD878 0%, #FFC400 100%);
            padding: 5px 10px;
            border-radius: 30px;
        }
    }

    .sec1 {
        height: 400px;
        background: url(../images/1.png) no-repeat;
        background-size: 100% auto;
    }

    .sec2_w {
        width: 345px;
        background: linear-gradient(180deg, #FFE5D9 0%, #FFFDFB 28%, #FFFFFF 100%);
        border-radius: 8px;
        border: 1px solid hsl(240, 6%, 41%);
        margin: -137px auto 0 auto;
        padding: 15px 5px 0 5px;

        .title {
            font-size: 24px;
            color: #333333;
            text-align: center;
            font-weight: bold;
            line-height: 33px;
        }

        .ic_list {
            display: flex;
            padding-top: 14px;

            li {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .icon {
                width: 38px;
                height: 38px;
                border-radius: 100%;

                &.icon1 {
                    background: url(../images/ic_1.png) no-repeat;
                    background-size: 100% 100%;
                }

                &.icon2 {
                    background: url(../images/ic_2.png) no-repeat;
                    background-size: 100% 100%;
                }

                &.icon3 {
                    background: url(../images/ic_3.png) no-repeat;
                    background-size: 100% 100%;
                }

                &.icon4 {
                    background: url(../images/ic_4.png) no-repeat;
                    background-size: 100% 100%;
                }

                &.icon5 {
                    background: url(../images/ic_5.png) no-repeat;
                    background-size: 100% 100%;
                }
            }

            .desc {
                font-size: 13px;
                color: #666666;
                line-height: 18px;
                width: 52px;
                text-align: center;
            }
        }
    }

    .sec3 {
        height: 82px;
        width: 322px;
        background: url(../images/2.png) no-repeat;
        background-size: 100% 100%;
        margin: 15px auto 0 auto;
    }

    .sec4 {
        height: 676px;
        width: 345px;
        background: url(../images/3.png) no-repeat;
        background-size: 100% 100%;
        margin: 20px auto 0 auto;
    }

    .sec5 {
        height: 62px;
        width: 326px;
        background: url(../images/4.png) no-repeat;
        background-size: 100% 100%;
        margin: 20px auto 0 auto;
    }

    .sec6 {
        height: 216px;
        width: 345px;
        background: url(../images/5.png) no-repeat;
        background-size: 100% 100%;
        margin: 20px auto 0 auto;
    }

    .sec7 {
        height: 67px;
        width: 320px;
        background: url(../images/6.png) no-repeat;
        background-size: 100% 100%;
        margin: 20px auto 0 auto;
    }

    .sec8 {
        height: 102px;
        width: 345px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/ban2.png) no-repeat;
        background-size: 100% 100%;
        margin: 20px auto 0 auto;
    }
}
