<import name="style" content="./main" />

<import name="Count" content=":component/count/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />

<div class="panel-motoshort">
    <div class="sec1"></div>
    <div class="sec2_w">
        <div class="title">- 尊享5大权益 -</div>
        <ul class="ic_list">
            <li>
                <span class="icon icon1"></span>
                <span class="desc">精华课</span>
            </li>
            <li>
                <span class="icon icon2"></span>
                <span class="desc">精简题库</span>
            </li>
            <li>
                <span class="icon icon3"></span>
                <span class="desc">真实考场模拟</span>
            </li>
            <li>
                <span class="icon icon4"></span>
                <span class="desc">考前秘卷</span>
            </li>
            <li>
                <span class="icon icon5"></span>
                <span class="desc">考不过补偿</span>
            </li>
        </ul>
        <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
            <div class="buy-btn" key="buy-btn">
                确认协议并支付 ¥{{props.payPrice}}
                <sp:if value="props.goodsInfo.inActivity">
                    <span class="preDiscountPrice"
                        >日常价{{props.goodsInfo.inActivity.preDiscountPrice}}元</span
                    >
                </sp:if>
            </div>

            <sp:if value="props.goodsInfo.inActivity">
                <div class="time-tip">
                    <span class="sp"
                        >立减{{props.goodsInfo.inActivity.discountedPrice}}元</span
                    >
                    <com:Count
                        name="Count"
                        startTime="{{props.goodsInfo.inActivity.discountStartTime}}"
                        endTime="{{props.goodsInfo.inActivity.discountEndTime}}"
                    />
                </div>
                <sp:elseif
                    value="props.labelPool[props.goodsInfo.groupKey].label"
                />
                <span class="tip"
                    >{{props.labelPool[props.goodsInfo.groupKey].label}}<i></i
                ></span>
            </sp:if>
        </div>
    </div>
    <div class="sec3"></div>
    <div class="sec4"></div>
    <div class="sec7"></div>
    <div class="sec8" sp-on:click="goAuth" data-uniqkey="bgbc"></div>
    <com:commonQuestion type="1"></com:commonQuestion>
</div>
