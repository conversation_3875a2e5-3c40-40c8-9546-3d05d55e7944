/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */
import { Component } from '@simplex/simple-core';
import View from './view/main.html';

interface Props {
    payBtnCall?(e: Event)
    goAuth?(any)
}

export default class extends Component<any, Props> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
        };
        this.props = {
        };

    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
