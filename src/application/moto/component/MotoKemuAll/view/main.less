.panel-motokemuall {
    background-color: #f2f2f2;

    img {
        display: block;
        width: 100%;
        margin: 0 auto;
    }
    .card-box {
        margin-top: -8px;
    }
    .buy-btn-box {
        position: relative;
        width: 343px;
        height: 49px;
        margin: 24px auto 15px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/btn1.png)
            no-repeat center center/100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .buy-btn {
            color: white;
        }

        .label {
            position: absolute;
            right: 0px;
            top: -7px;
            background: linear-gradient(360deg, #f9c39f 0%, #fedec7 100%);
            border-radius: 0 10px 0 8px;
            font-size: 12px;
            font-weight: 500;
            color: #622604;
            transform: scale3d(0.9, 0.9, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2px 9px;
            font-weight: 500;
        }
    }

    // 活动按钮
    .activity-buy-btn {
        position: relative;
        z-index: 10;
        height: 70px;
        background: linear-gradient(90deg, #f95b38 0%, #e83e30 100%);
        display: flex;
        padding: 5px 10px;
        box-sizing: border-box;
        margin-bottom: 20px;
        margin-top: -10px;

        .div1 {
            flex: 1;

            .p1 {
                display: flex;
                align-items: center;
                padding-top: 4px;
            }

            .sp1 {
                width: 78px;
                height: 18px;
                background: url(../images/1.png) no-repeat;
                background-size: 100% 100%;
            }

            .sp2 {
                font-size: 11px;
                color: #ffffff;
                line-height: 16px;
                background: linear-gradient(
                    180deg,
                    rgba(0, 0, 0, 0.3) 0%,
                    rgba(0, 0, 0, 0.3) 100%
                );
                border-radius: 8px;
                padding: 1px 5px 0 5px;
                transform: scale3d(0.9, 0.9, 0.9);
                margin-left: 5px;
            }

            .p2 {
                display: flex;
                align-items: center;
                padding-top: 5px;
            }

            .sp3 {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);

                .i1 {
                    font-size: 12px;
                    color: rgba(255, 255, 255, 0.6);
                    transform: scale3d(0.9, 0.9, 0.9);
                }

                .b1 {
                    font-size: 16px;
                    font-weight: bold;
                    color: #ffffff;
                    transform: scale3d(0.9, 0.9, 0.9);
                }
            }

            .sp4 {
                background: linear-gradient(360deg, #fab78a 0%, #ffdfc3 100%);
                border-radius: 13px;
                font-size: 12px;
                font-weight: bold;
                color: #b4440f;
                line-height: 17px;
                padding: 5px 10px;
                margin-left: 12px;

                .i2 {
                    font-size: 12px;
                    transform: scale3d(0.85, 0.85, 0.85);
                }

                .b2 {
                    font-size: 18px;
                }
            }
        }

        .div2 {
            width: 128px;
            height: 60px;
            background: url(../images/2.png) no-repeat;
            background-size: 100% 100%;
            box-sizing: border-box;
            padding: 10px 0 0 10px;

            .p3 {
                font-size: 15px;
                font-weight: bold;
                color: #6f2117;
                line-height: 21px;
                text-align: center;
            }

            .p4 {
                font-size: 15px;
                font-weight: bold;
                color: #6f2117;
                line-height: 21px;
                text-align: center;
            }

            .count-content {
                text-align: center;

                .count {
                    color: #6f2117;
                    font-size: 15px;
                    font-weight: 600;
                }
            }
        }
    }

    .protocol-box {
        margin-bottom: 30px;
        font-size: 13px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
    }

    .kemuall-step {
        position: relative;
        padding: 0 15px;
        margin-top: 40px;
    }
}
