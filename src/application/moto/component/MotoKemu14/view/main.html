<import name="style" content="./main" />
<import name="pageHeader" content=":application/car/component/page-header/main" />
<import name="Count" content=":component/count/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="giftText" content=":component/giftText/main" />
<import name="PriceTag" content=":component/priceTag/main" />
<import name="cards" content=":application/car/component/cards/main" />
<import name="middleProtocol" content=":application/car/component/middleProtocol/main" />

<div class="panel-motokemu14">
    <com:pageHeader iconList="{{state.iconList}}" goAuth="{{props.goAuth}}"
       uiConfig="{{self.headUiConfig}}" />
    <div>
        <sp:if value="props.kemu == 1 && props.goodsList.length">
            <div class="card-box">
                <com:cards comparePricePool="{{props.comparePricePool}}" goodsList="{{props.goodsList}}"
                    tabChange="{{props.tabChange}}" tabIndex="{{props.currentIndex}}" />
            </div>
        </sp:if>
    </div>
    <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
        <div class="buy-btn" key="buy-btn">
            确认协议并支付 ¥{{props.payPrice}}

            <sp:if value="props.goodsInfo.inActivity">
                <span class="preDiscountPrice">日常价{{props.goodsInfo.inActivity.preDiscountPrice}}元</span>
            </sp:if>
        </div>
        <!-- <com:giftText /> -->
        <com:PriceTag goodsInfo="{{props.goodsInfo}}" comparePriceMap="{{props.comparePricePool}}"
            labelMap="{{props.labelPool}}" />
    </div>
    <div class="protocol-box">
        <com:middleProtocol groupKey="{{props.groupKey}}" couponPool="{{props.couponPool}}" />
    </div>

    <div class="moto-sec-1">
        <img
            src="{{URLCommon.kemu== 1 ? 'https://web-resource.mc-cdn.cn/web/vip/t2.png': 'https://web-resource.mc-cdn.cn/web/vip/t2-1.png'}}" />
    </div>
    <div class="moto-sec-2-qita">
        <img src="https://jiakao-web.mc-cdn.cn/jiakao-web/2022/12/12/14/cf2b64546df740139901155c405c6feb.png" />
    </div>
    <div class="moto-sec-5-qita" data-type="1" data-uniqkey="goods-baozhang">
        <img src="https://web-resource.mc-cdn.cn/web/vip/t1.png" />
    </div>
    <div class="moto-sec-6" sp-on:click="goAuth" data-uniqkey="bgbc">
        <img src="https://web-resource.mc-cdn.cn/web/vip/ban2.png" />
    </div>
    <sp:slot />
    <com:commonQuestion type="1"></com:commonQuestion>
</div>
