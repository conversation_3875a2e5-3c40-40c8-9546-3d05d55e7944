.panel-motokemu14 {
    background: #221d1d;
    flex: 1;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;

    img {
        display: block;
        width: 100%;
        margin: 0 auto;
    }

    .card-box {
        margin-bottom: 24px;
        &.card-box-b {
            margin-top: 10px;
        }
    }

    .buy-btn-box {
        position: relative;
        width: 343px;
        height: 49px;
        margin: 0px auto 15px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/btn1.png)
            no-repeat center center/100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .buy-btn {
            font-size: 19px;
            color: white;
        }

        .preDiscountPrice {
            position: relative;
            font-size: 12px;
            transform: scale(0.8);
            color: white;
            margin-left: 4px;
            margin-top: 4px;

            &::after {
                content: "";
                position: absolute;
                top: 50%;
                left: 0;
                width: 100%;
                height: 1px;
                background-color: white;
            }
        }
    }

    .protocol-box{
        margin-bottom: 30px;
        font-size: 13px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
    }

    .moto-sec-1 {
        padding: 0 30px;
    }

    .moto-sec-2-qita {
        margin-top: 30px;
        padding: 0 15px;
    }

    .moto-sec-3 {
        margin-top: 40px;
        padding: 0 40px;
    }

    .moto-sec-4 {
        margin-top: 20px;
        padding: 0 15px;
    }

    .moto-sec-5-qita {
        margin-top: 40px;
        padding: 0 66px;
    }

    .moto-sec-6 {
        margin-top: 20px;
        padding: 0 15px;
    }
}
