/*
 * main
 *
 * name: xia<PERSON><PERSON>a
 * date: 16/3/24
 */

import { promiseIconList, promiseList } from ':common/features/promise';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GoodsInfo } from ':store/goods';
import { mergeObjects } from ':common/utils';

interface State {
    iconList: any[]
}
interface Props {
    goodsList: GoodsInfo[]
    currentIndex: number
    goodsInfo?: GoodsInfo
    goAuth?(any)
    payBtnCall?(e: Event)
}
export default class extends Component<State, Props> {

    get headUiConfig() {
        const { goodsList, currentIndex, goodsInfo } = this.props;
        const info = goodsInfo || goodsList[currentIndex];
        const config: any = {
            img: 'https://web-resource.mc-cdn.cn/web/jkbd-vip/<EMAIL>'
        };

        if (info.giftPromotion?.promotionStatus) {
            mergeObjects(config, {

            });
        }

        if (info.headConfig) {
            mergeObjects(config, {
                img: info.headConfig.img,
                bgc: info.headConfig.bgc,
                video: info.headConfig.video,
                transparent: !!info.headConfig.img
            });
        }

        return config;
    }
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            iconList: [
                {
                    uniqkey: promiseList.k1vip,
                    icon: promiseIconList.k1vip,
                    dec: '科一VIP'
                },
                {
                    uniqkey: promiseList.jpzbk,
                    icon: promiseIconList.motoKe1Ke4ShortJpzbk,
                    dec: '科一直播课'
                },
                {
                    uniqkey: promiseList.k4vip,
                    icon: promiseIconList.k4vip,
                    dec: '科四VIP'
                },
                {
                    uniqkey: promiseList.jpzbk,
                    icon: promiseIconList.motoKe1Ke4ShortJpzbk,
                    dec: '科四直播课'
                },
                {
                    uniqkey: promiseList.bgbc,
                    icon: promiseIconList.k4bgbc,
                    dec: '考不过<br/>补偿'
                }
            ]
        };
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
        return true;
    }
}
