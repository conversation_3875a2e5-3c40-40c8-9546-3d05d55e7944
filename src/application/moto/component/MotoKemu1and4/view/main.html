<import name="style" content="./main" />
<import
    name="pageHeader"
    content=":application/car/component/page-header/main"
/>
<import name="cards" content=":application/car/component/cards/main" />

<import name="commonQuestion" content=":component/commonQuestion/main" />
<import
    name="middleProtocol"
    content=":application/car/component/middleProtocol/main"
/>

<import name="PriceTag" content=":component/priceTag/main" />
<import name="Count" content=":component/count/main" />
<import name="giftText" content=":component/giftText/main" />

<div class="panel-motokemu1and4">
    <com:pageHeader
        uiConfig="{{self.headUiConfig}}"
        goodsInfo="{{props.goodsInfo}}"
        groupKey="{{props.groupKey}}"
        goAuth="{{props.goAuth}}"
        iconList="{{state.iconList}}"
    />
    <div class="ipad-box">
        <div class="phone-box">
            <sp:if value="{{props.tabChange}}">
                <div class="card-box">
                    <com:cards
                        comparePricePool="{{props.comparePricePool}}"
                        goodsList="{{props.goodsList}}"
                        tabChange="{{props.tabChange}}"
                        tabIndex="{{props.currentIndex}}"
                    />
                </div>
            </sp:if>
            <sp:if
                value="props.goodsInfo.inActivity && !props.goodsInfo.upgrade"
            >
                <div
                    class="activity-buy-btn"
                    sp-on:click="pay"
                    data-fragment="主图"
                >
                    <div class="div1">
                        <p class="p1">
                            <span class="sp1"></span>
                            <span class="sp2"
                                >比分开买节省{{props.comparePricePool[props.goodsInfo.groupKey].diffPrice}}元</span
                            >
                        </p>
                        <p class="p2">
                            <span class="sp3">
                                日常价
                                <i class="i1">&nbsp; ¥ &nbsp;</i>
                                <b class="b1"
                                    >{{props.goodsInfo.inActivity.preDiscountPrice}}</b
                                >
                            </span>
                            <span class="sp4">
                                限时折后
                                <i class="i2">&nbsp; ¥ &nbsp;</i>
                                <b class="b2">{{props.payPrice || '--'}}</b>
                            </span>
                        </p>
                    </div>
                    <div class="div2">
                        <div class="p3">限时折扣</div>
                        <div class="p4">
                            <com:Count
                                name="Count"
                                startTime="{{props.goodsInfo.inActivity.discountStartTime}}"
                                endTime="{{props.goodsInfo.inActivity.discountEndTime}}"
                            ></com:Count>
                        </div>
                    </div>
                </div>
                <sp:else />
                <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                    <div class="buy-btn">
                        确认协议并支付 ¥{{props.payPrice || props.payPrice ===
                        0?props.payPrice : '--'}}
                    </div>
                    <!-- <com:giftText /> -->
                    <com:PriceTag
                        goodsInfo="{{props.goodsInfo}}"
                        comparePriceMap="{{props.comparePricePool}}"
                        labelMap="{{props.labelPool}}"
                    />
                </div>
            </sp:if>

            <div class="protocol-box">
                <com:middleProtocol groupKey="{{props.groupKey}}" couponPool="{{props.couponPool}}" />
            </div>

            <div class="step-box">
                <div class="step step1">
                    <img
                        src="https://web-resource.mc-cdn.cn/web/vip/5.png"
                        alt=""
                    />
                </div>
                <div class="step step2">
                    <img
                        src="https://web-resource.mc-cdn.cn/web/vip/4.png"
                        alt=""
                    />
                    <img
                        src="https://web-resource.mc-cdn.cn/web/vip/8.png"
                        alt=""
                    />
                    <img
                        src="https://jiakao-web.mc-cdn.cn/jiakao-web/2022/12/12/15/246572345a5242d8af6bb92b5ac3f2f3.png"
                        alt=""
                    />
                </div>

                <div class="step step3">
                    <img
                        src="https://web-resource.mc-cdn.cn/web/vip/2.png"
                    />
                </div>
                <div
                    class="step step4"
                    sp-on:click="goAuth"
                    data-uniqkey="bgbc"
                >
                    <img
                        src="https://web-resource.mc-cdn.cn/web/vip/7.png"
                    />
                </div>
            </div>
            <sp:slot/>
            <com:commonQuestion type="1" kemuTxt="科一和科四" />
        </div>
    </div>
</div>
