<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="compareGood" content=":component/compareGood/main" />
<import name="compareGood1" content=":component/compareGood/main" />
<import name="compareGood2" content=":component/compareGood/main" />

<import name="MotoShort" content=":application/moto/component/MotoShort/main" />
<import name="MotoKemu14" content=":application/moto/component/MotoKemu14/main" />
<import name="MotoKemu1and4" content=":application/moto/component/MotoKemu1and4/main" />
<import name="MotoKe14Short" content=":application/moto/component/MotoKe14Short/main" />

<import name="MotoKemuAll" content=":application/moto/component/MotoKemuAll/main" />
<import name="buyDialogOrderSign" content=":component/buyDialogOrderSign/main" />

<div class="page-container page-moto">
    <div class="page-header">
        <com:header title="{{state.prevScrollTop > 200?self.nowGoodInfo.name:' '}}" theme="black" endTheme="black"
            scrollTop="{{state.prevScrollTop}}" back="{{self.backCall}}" />
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <!-- 科目1或4 -->
        <div
            class="{{self.nowGoodInfo&&self.nowGoodInfo.groupKey === GroupKey.MotoChannelKe1 || self.nowGoodInfo&&self.nowGoodInfo.groupKey === GroupKey.MotoChannelKe4?'show':'hide'}}">
            <com:MotoKemu14 kemu="{{URLCommon.kemu}}" goodsList="{{state.goodsInfoPool}}"
                currentIndex="{{state.tabIndex}}" tabChange="{{self.tabChangeCall}}"
                goodsInfo="{{self.nowGoodInfo}}"
                labelPool="{{state.labelPool}}" comparePricePool="{{state.comparePricePool}}"
                payPrice="{{self.showPrice}}" groupKey="{{self.nowGoodInfo.groupKey}}" goAuth="{{self.goAuth}}"
                payBtnCall="{{self.payBtnCall}}">
                <com:compareGood key="compareGood" name="compareGood"
                    img="http://exam-room.mc-cdn.cn/exam-room/2022/10/17/14/2acf560caa5e4039aceb58a33410c214.png">
                    <div class="buy-box">
                        <sp:each for="state.goodsInfoPool">
                            <div class="buy-btn" sp-on:click="payBtnCall" data-fragment="VIP对比模块"
                                data-payindex="{{$index}}">
                                {{#self.buyBtnTxt[$value.groupKey] ||
                                '立即购买'}}
                            </div>
                        </sp:each>
                    </div>
                </com:compareGood>
            </com:MotoKemu14>
        </div>
        <!-- 摩托全科VIP -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.MotoChannelKemuAll).payPrice && self.getGroupKeyInfo(GroupKey.MotoChannelKemuAll).showPage">
            <div class="{{self.nowGoodInfo&&self.nowGoodInfo.groupKey === GroupKey.MotoChannelKemuAll?'show':'hide'}}">
                <com:MotoKemuAll goodsList="{{state.goodsInfoPool}}" currentIndex="{{state.tabIndex}}"
                    tabChange="{{self.tabChangeCall}}" goodsInfo="{{self.nowGoodInfo}}" labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}" payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}" goAuth="{{self.goAuth}}" payBtnCall="{{self.payBtnCall}}">
                    <com:compareGood1 key="compareGood" name="compareGood"
                        img="http://exam-room.mc-cdn.cn/exam-room/2022/10/17/14/2acf560caa5e4039aceb58a33410c214.png">
                        <div class="buy-box">
                            <sp:each for="state.goodsInfoPool">
                                <div class="buy-btn" sp-on:click="payBtnCall" data-fragment="VIP对比模块"
                                    data-payindex="{{$index}}">
                                    {{#self.buyBtnTxt[$value.groupKey] ||
                                    '立即购买'}}
                                </div>
                            </sp:each>
                        </div>
                    </com:compareGood1>
                </com:MotoKemuAll>
            </div>
        </sp:if>

        <!-- 短时提分 -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.MotoChannelKe1Short).payPrice && self.getGroupKeyInfo(GroupKey.MotoChannelKe1Short).showPage">
            <div class="{{self.nowGoodInfo.groupKey === GroupKey.MotoChannelKe1Short?'show':'hide'}}">
                <com:MotoShort goodsInfo="{{self.nowGoodInfo}}" payPrice="{{self.showPrice}}"
                    labelPool="{{state.labelPool}}" groupKey="{{self.nowGoodInfo.groupKey}}" isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}" payBtnCall="{{self.payBtnCall}}" />
            </div>
        </sp:if>


        <!-- 科一科四组合包 -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.MotoChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.MotoChannelKe1Ke4Group).showPage">
            <div class="{{self.nowGoodInfo.groupKey === GroupKey.MotoChannelKe1Ke4Group?'show':'hide'}}">
                <com:MotoKemu1and4 goodsList="{{state.goodsInfoPool}}" currentIndex="{{state.tabIndex}}"
                    tabChange="{{URLCommon.kemu === KemuType.Ke1 && self.tabChangeCall}}"
                    goodsInfo="{{self.nowGoodInfo}}" payPrice="{{self.showPrice}}"
                    comparePricePool="{{state.comparePricePool}}" labelPool="{{state.labelPool}}"
                    couponPool="{{state.couponPool}}" groupKey="{{self.nowGoodInfo.groupKey}}"
                    isHubei="{{state.isHubei}}" goAuth="{{self.goAuth}}" payBtnCall="{{self.payBtnCall}}">
                    <com:compareGood2 key="compareGood" name="compareGood"
                        img="http://exam-room.mc-cdn.cn/exam-room/2022/10/17/14/2acf560caa5e4039aceb58a33410c214.png">
                        <div class="buy-box">
                            <sp:each for="state.goodsInfoPool">
                                <div class="buy-btn" sp-on:click="payBtnCall" data-fragment="VIP对比模块"
                                    data-payindex="{{$index}}">
                                    {{#self.buyBtnTxt[$value.groupKey] ||
                                    '立即购买'}}
                                </div>
                            </sp:each>
                        </div>
                    </com:compareGood2>
                </com:MotoKemu1and4>
            </div>
        </sp:if>

        <!-- 科一科四短时提分组合包 -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.MotoChannelKe1Ke4Short).payPrice &&
            self.getGroupKeyInfo(GroupKey.MotoChannelKe1Ke4Short).showPage">
            <div class="{{self.nowGoodInfo.groupKey === GroupKey.MotoChannelKe1Ke4Short?'show':'hide'}}">
                <com:MotoKe14Short goodsList="{{state.goodsInfoPool}}" currentIndex="{{state.tabIndex}}"
                    tabChange="{{URLCommon.kemu === KemuType.Ke1 && self.tabChangeCall}}"
                    goodsInfo="{{self.nowGoodInfo}}" payPrice="{{self.showPrice}}"
                    comparePricePool="{{state.comparePricePool}}" labelPool="{{state.labelPool}}"
                    couponPool="{{state.couponPool}}" groupKey="{{self.nowGoodInfo.groupKey}}"
                    isHubei="{{state.isHubei}}" goAuth="{{self.goAuth}}" payBtnCall="{{self.payBtnCall}}">
                </com:MotoKe14Short>
            </div>
        </sp:if>

        
    </div>
    <div class="{{state.showFooter ? '': 'hide'}}">
        <div class="footer {{state.goodsInfoPool.length > 1?'':'hide'}}">
            <com:bottomTabs tabIndex="{{state.tabIndex}}" labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}" goodsList="{{state.goodsInfoPool}}"
                tabChange="{{self.tabChangeCall}}" />
        </div>
        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
            </div>
        </com:buyButton>
    </div>
    <!-- 所有商品信息加载完成才能加载这个组件，内部有根据商品判断 -->
    <sp:if value="state.showSignModal">
        <com:buyDialogOrderSign goodsInfoPool="{{state.goodsInfoPool}}" labelPool="{{state.labelPool}}"
            comparePricePool="{{state.comparePricePool}}" couponPool="{{state.couponPool}}"
            close="{{self.closeSignModal}}" />
    </sp:if>
    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
</div>
