/*
 * ------------------------------------------------------------------
 * 摩托车vip
 * ------------------------------------------------------------------
 */

import { ABTestKey, ABTestType, CarType, KemuType, PayType, persuadeDialogAllow, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import { formatPrice } from ':common/utils';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import BuyDialogOrderSign from ':component/buyDialogOrderSign/main';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { typeCode } from ':common/features/bottom';
import { isHubei } from ':common/features/locate';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import { openVipWebView, webClose } from ':common/core';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackEvent, trackExit, trackGoPay, trackPageLoad, trackPageShow } from ':common/stat';
import { onWebBack } from ':common/features/persuade';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { BUYED_URL, COUPON_DETAIL_URL, openAuth } from ':common/navigate';
import { couponAnimate, pauseAllVideos, scrollTop } from ':common/features/dom';
import { setEmbeddedHeight } from ':common/features/embeded';
import { onPageShow } from ':common/features/page_status_switch';
import jump from ':common/features/jump';
import { getTabIndex } from ':common/features/cache';
import isNumber from 'lodash/isNumber';
import { getAbtest, StrategyType } from ':store/chores';

interface State {
    isHubei: boolean,
    kemu: KemuType,
    tiku: CarType,
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    prevScrollTop: number,
    strategy?: StrategyType,
    showFooter: boolean
    showSignModal: boolean
}
let timer;
// 标记是否展示过挽留弹窗
let flag = false;

const pageNameMap = {
    [GroupKey.MotoChannelKe1]: 'VIP速成版页',
    [GroupKey.MotoChannelKe4]: 'VIP速成版页',
    [GroupKey.MotoChannelKe1Short]: '短时提分页',
    [GroupKey.MotoChannelKe1Ke4Group]: '科1科4组合包页',
    [GroupKey.MotoChannelKe1Ke4Short]: '科1科4短时提分页',
    [GroupKey.MotoChannelKemuAll]: '全科VIP页'
};

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog,
        buyDialogOrderSign: BuyDialogOrderSign
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    get buyBtnTxt() {
        return {
            [GroupKey.MotoChannelKe1]: '解锁科一<br/>高效学',
            [GroupKey.MotoChannelKe1Ke4Group]: '解锁科一<br/>科四VIP',
            [GroupKey.MotoChannelKemuAll]: '解锁全科<br/>VIP'
        };
    }
    getPayPrice(showIndex) {
        const { goodsInfoPool, couponPool } = this.state;
        const groupKey = goodsInfoPool[showIndex].groupKey;
        const nowPayPrice = goodsInfoPool[showIndex].payPrice;

        if (couponPool[groupKey]?.couponCode) {
            const showPrice = +nowPayPrice - (+couponPool[groupKey].priceCent);
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;

        this.state = {
            isHubei: true,
            kemu,
            tiku,
            tabIndex: 0,
            goodsInfoPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            // 滚动距离
            prevScrollTop: 0,
            // 是否展示下方的购买部分(科目4的时候下方的按钮固定，不用关系滚动)
            showFooter: kemu === 4,
            showSignModal: false
        };

    }
    async didMount() {

        let kemu = +URLCommon.kemu;
        const urlactivePanel = URLParams.activePanel;
        const urlactiveGroupKey = URLParams.activeGroupKey;
        let tabIndex;
        const goodsInfoPool: GoodsInfo[] = [];

        // 兼容客户端从科二科三的页面进入时无法购买
        kemu = (kemu === 0 || kemu === 2 || kemu === 3) ? 1 : kemu;

        const { strategy } = await getAbtest(CarType.MOTO);

        if (kemu === 1) {
            goodsInfoPool.push({
                groupKey: GroupKey.MotoChannelKe1
            } as GoodsInfo);
            if (strategy[ABTestKey.key24] === ABTestType.B) {
                goodsInfoPool.push({
                    groupKey: GroupKey.MotoChannelKe1Ke4Short 
                } as GoodsInfo);
            } else {
                goodsInfoPool.push({
                    groupKey: GroupKey.MotoChannelKe1Ke4Group
                } as GoodsInfo);
            }
           
            goodsInfoPool.push({
                groupKey: GroupKey.MotoChannelKemuAll
            } as GoodsInfo);

        } else if (kemu === 4) {
            goodsInfoPool.push({
                groupKey: GroupKey.MotoChannelKe4
            } as GoodsInfo);
            goodsInfoPool.push({
                groupKey: GroupKey.MotoChannelKe1Ke4Group
            } as GoodsInfo);
        }

        // 历史原因，判断第几个去控制tabIndex,后续这段代码需要删除
        if (urlactivePanel) {
            tabIndex = urlactivePanel === 'panel2' ? 1 : 0;
            // eslint-disable-next-line brace-style
        }
        else if (kemu === 1) {
            // 没有加科一科四组合前，默认是1，加科一科四后，默认也是1
            tabIndex = 1;
        } else {
            tabIndex = +sessionStorage.getItem('actviePanel') || 0;
        }

        // 根据地址栏中的activeGroupKey获取对应的tabIndex
        if (urlactiveGroupKey) {
            goodsInfoPool.forEach((item, index) => {
                if (item.groupKey === urlactiveGroupKey) {
                    tabIndex = index;
                }
            });
        }

        this.state.tabIndex = tabIndex;
        this.state.goodsInfoPool = goodsInfoPool;

        await this.getGoodInfo();

        setPageName(pageNameMap[this.nowGoodInfo.groupKey]);
        // 页面进出时长打点
        trackPageLoad();

        // 比较曝光打点
        trackEvent({
            actionType: '曝光',
            fragmentName1: 'VIP对比模块'
        });

        onPageShow(() => {
            this.setPageInfo();
        });

        // 判断是否是湖北
        isHubei().then(isHubei => {
            this.setState({
                isHubei
            });
        });

        // app代理方法
        this.appEventProxy();

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: goodsInfoPool[tabIndex].groupKey
        });

    }

    appEventProxy() {

        // 可能是半截页面打开，所以设置一下全屏
        setEmbeddedHeight(0);

        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        // 退出当前tab的打点
        this.leavePageCall();

        // 回到滚动的顶部
        scrollTop(document.querySelector('.page-moto .body-panel'));

        // 暂停所有视频
        pauseAllVideos();

        this.setState({
            tabIndex
        }, () => {
            setPageName(pageNameMap[this.nowGoodInfo.groupKey]);

            trackEvent({
                actionType: '曝光',
                fragmentName1: 'VIP对比模块'
            });

            trackPageShow();

            this.setPageInfo();

        });

    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            // 科四没有中间的切换，不做隐藏
            if (URLCommon.kemu !== 4) {
                if (prevScrollTop >= 200) {
                    this.setState({
                        showFooter: true
                    });
                } else {
                    this.setState({
                        showFooter: false
                    });
                }
                this.setBuyBottom();
            }
            this.setState({
                prevScrollTop
            });
        }, 60);
    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const fragmentName1 = '底部吸底按钮';
        const { showFooter, tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        let bottomType: typeCode = typeCode.type4;

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: (groupKey) => {
                iosBuySuccess({ groupKey: groupKey || this.nowGoodInfo.groupKey });
            }
        });

        // 全科并且有活动的时候按钮不同
        if (nowGoodInfo.inActivity) {
            bottomType = typeCode.type5;
        }
        if (!showFooter) {
            this.children.buyButton.hideButton();
            return;
        }
        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1
                });
                break;
            case typeCode.type5:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${nowGoodInfo.inActivity.discountedPrice}元`,
                    price: this.showPrice,
                    originalPrice: '日常价￥' + nowGoodInfo.inActivity.preDiscountPrice,
                    fragmentName1
                });
                break;
            default:
                break;
        }
    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {

            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    jump.replace(BUYED_URL);
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });
            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }

            // showPage用来控制模块的展示， 先渲染需要渲染的tabPage，加快首次渲染速度
            newGoodsPool[tabIndex].showPage = true;

            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool
            });
            this.setPageInfo();
            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getLabel();
                await this.getComparePrice();

                couponAnimate({
                    couponTargetDomSelect: '.body-panel > .show .coupon-position-middle',
                    compareTargetDomSelect: `.body-panel > .show .tab-cards .${newGoodsPool[2]?.groupKey}`,
                    couponData: this.nowCouponInfo,
                    compareData: this.state.comparePricePool[newGoodsPool[2]?.groupKey],
                    goodsData: this.nowGoodInfo,
                    compareGoodsData: newGoodsPool[2]
                });

                this.setPageInfo();
                setTimeout(() => {
                    this.setState({ showSignModal: true });
                }, 500);
            }, 60);

            // 500ms后再渲染其他tabPage，
            setTimeout(() => {
                newGoodsPool.forEach(item => {
                    item.showPage = true;
                });

                this.setState({
                    goodsInfoPool: newGoodsPool
                });
            }, 500);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool, tiku } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice,
                        groupItems: item.groupItems
                    };
                }
            });

            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps, payIndex?: number) => {
        const { goodsInfoPool, couponPool, tabIndex } = this.state;
        payIndex = payIndex && +payIndex;
        payIndex = typeof payIndex !== 'number' ? tabIndex : payIndex;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[payIndex].groupKey,
            sessionIds: goodsInfoPool[payIndex].sessionIds,
            activityType: goodsInfoPool[payIndex].activityType,
            couponCode: couponPool[goodsInfoPool[payIndex].groupKey]?.couponCode,
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[payIndex].groupKey });
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[payIndex].groupKey,
                payPrice: this.getPayPrice(payIndex),
                onPay: () => {
                    this.pay(stat, payIndex);
                },
                ...stat
            });
        });
    }
    goAuth = async (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
    payBtnCall = (e) => {
        const { goodsInfoPool, tabIndex } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        const fragmentName2 = e.refTarget.getAttribute('data-fragment2');
        let payIndex = e.refTarget.getAttribute('data-payindex');
        payIndex = payIndex && +payIndex;
        payIndex = typeof payIndex !== 'number' ? tabIndex : payIndex;
        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[payIndex].groupKey,
            fragmentName1,
            fragmentName2: fragmentName2 || ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[payIndex].groupKey, {
                fragmentName1,
                fragmentName2: fragmentName2 || ''
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay({ fragmentName1, fragmentName2: fragmentName2 || '' }, payIndex);
                },
                fragmentName1
            });

        }
    }
    // 退出页面的回调
    goBackPage() {
        const { tabIndex, goodsInfoPool, kemu, labelPool } = this.state;
        const nowGoodInfo = goodsInfoPool[tabIndex];

        if (this.children.buyDialogOrderSign.getStatus()) {
            this.children.buyDialogOrderSign.close();
            return;
        }

        if (persuadeDialogAllow && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: nowGoodInfo,
                groupKey: nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                title: `真的要放弃${nowGoodInfo.name}吗？`,
                txt1: '懒人必备',
                txt2: '省不少时间',
                txt3: '后悔开晚了',
                txt4: '简单好记',
                tag: {
                    text: labelPool[nowGoodInfo.groupKey]?.label
                },
                kemu
            }).then(payType => {
                if (payType === false) {
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            webClose();
        }
    }
    backCall = () => {
        this.goBackPage();
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    // 离开当前页面
    leavePageCall = () => {
        // 退出当前页面的打点
        setPageName(pageNameMap[this.nowGoodInfo.groupKey]);
        trackExit();
    }
    closeSignModal = () => {
        this.setPageInfo();
    }
}
