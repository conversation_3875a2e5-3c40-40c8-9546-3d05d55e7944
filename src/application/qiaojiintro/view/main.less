.page-qiaojiintro {
    .body-panel {
        flex: 1;
        overflow-y: scroll;
        background-color: #fff;
        .part1 {
            height: 190px;
            background: url(../images/img-koujue-bg.png) no-repeat;
            background-size: cover;
            display: flex;
            align-items: center;
            justify-content: center;

            p {
                font-size: 30px;
                color: #333;
                font-weight: bold;
                line-height: 32px;
            }
        }

        .part2 {
            width: 180px;
            height: 97px;
            margin: 0 auto;
            background: url(../images/title-koujue-01.png) no-repeat;
            background-size: cover;
            display: flex;
            align-items: center;
            justify-content: center;

            p {
                font-size: 23px;
                color: #333;
                font-weight: bold;
                line-height: 24px;
            }
        }

        .part3 {
            padding: 15px 28px;

            h3 {
                text-align: center;
                font-weight: bold;
                font-size: 17px;
                color: #333;
                padding: 12px;
            }

            h4 {
                text-align: center;
                font-size: 13px;
                color: #333;
                padding-bottom: 10px;
            }

            .div1 {
                width: 320px;
                height: 190px;
                margin: 0 auto;
                background: url(../images/img-koujue-shouyeshunxu.png) no-repeat;
                background-size: cover;
            }

            .div2 {
                width: 320px;
                height: 225px;
                margin: 0 auto;
                background: url(../images/img-koujue-lianxixiangjie.png) no-repeat;
                background-size: cover;
            }
        }


    }

    .part4 {
        flex-shrink: 0;
        height: 40px;
        padding: 0px 43px 35px 43px;
        background: #ffffff;
        box-sizing: content-box;

        p {
            color: #fff;
            height: 40px;
            background: linear-gradient(90deg, #E5D07B 0, #D4B145 100%);
            border-radius: 40px;
            line-height: 40px;
            text-align: center;
            font-size: 16px;
        }
    }
}
