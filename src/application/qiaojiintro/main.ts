/*
 * ------------------------------------------------------------------
 * 巧记引导页
 * ------------------------------------------------------------------
 */

import { setStatusBarTheme, webClose } from ':common/core';
import { Platform, URLParams, Version } from ':common/env';
import { navigateTo, replace } from ':common/features/jump';
import { BUYED_URL } from ':common/navigate';
import { onWebBack } from ':common/features/persuade';
import { Application } from '@simplex/simple-core';
import { MCBaseUtils } from '@simplex/simple-base';
import View from './view/main.html';

export default class extends Application {
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        if (Version.bizVersion < 3) {
            replace(BUYED_URL);
            return;
        }

        this.state = {
        };

    }
    didMount() {
        this.appProxy();
    }
    appProxy() {
        setStatusBarTheme('dark');

        onWebBack(() => {
            webClose();
            return Promise.resolve();
        });
    }
    goQiaoji() {
        const actionUrl = 'http://jiakao.nav.mucang.cn/main/orderPrac';

        if (Platform.isAndroid && Platform.isXueTang && !MCBaseUtils.compareVersion(URLParams._version, '1.1.7')) {
            navigateTo('http://jiakao.nav.mucang.cn/tab/orderPrac');
        } else {
            navigateTo(actionUrl);
        }
    }
}
