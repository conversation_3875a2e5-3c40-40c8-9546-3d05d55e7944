/*
 * ------------------------------------------------------------------
 * 设备超限提醒
 * ------------------------------------------------------------------
 */

import { setPageName, URLParams } from ':common/env';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { MCProtocol } from '@simplex/simple-base';

export default class extends Application {
    $constructor() {
        const fromPage = URLParams.fromPage || '设备超限提醒';
        const fragmentName1 = URLParams.fragmentName1 || '未知片段';

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        setPageName(fromPage);

        this.state = {
            fragmentName1
        };
    }
    didMount() {
        MCProtocol.Core.Web.setting({
            titleBar: false,
            toolbar: false,
            menu: false,
            button: false
        });
    }
}
