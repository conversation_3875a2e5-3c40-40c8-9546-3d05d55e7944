body {
    max-width: none;
}

.page-clean {
    background: linear-gradient(180deg,#fff0d7, #fffdfa 25%);
    padding: 0 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .page-header {
        position: relative;

        .dhd {
            display: flex;
            justify-content: space-between;
            padding-bottom: 12px;
            box-sizing: content-box;

            .title {
                font-size: 18px;
                padding-top: 20px;
                font-weight: bold;
            }

            span {
                height: 30px;
                width: 30px;
                margin-right: -15px;
                &.close {
                    background: url(../images/2.png) no-repeat left bottom;
                    background-size: 20px 20px;
                }
            }
        }
    }

    .top-header {
        .header {
            background-color: #fff !important;
            padding: 30px 20px 0;
        }
    }

    .help {
        background: none !important;
    }

    .page-content {
        flex: 1;
        overflow-y: scroll;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 20px;
    }

    .intro {
        background: linear-gradient(90deg,#ffe6b3, #fff6e3);
        padding: 8px 0 7px;
        border-radius: 8px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        p {
            font-size: 14px;
            color: #654423;
            font-weight: 600;
            line-height: 24px;
            padding-left: 30px;
            min-width: 120px;
            &.s1 {
                background: url(../images/1.png) no-repeat left center;
                background-size: 24px 24px;
            }
            &.s2 {
                background: url(../images/3.png) no-repeat left center;
                background-size: 24px 24px;
            }
        }
    }
    .text {
        font-size: 13px;
        color: #6e6e6e;
        text-align: left;
        line-height: 23px;
        margin-top: 12px;

        .p2 {
            margin-top: 8px;
            span {
                color: #D08D1E;
            }
        }
    }

    .goods {
        .good-wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 24px;
        }

        .good {
            width: 104px;
            height: 118px;
            background: #ffffff;
            border: 1px solid #eae6e1;
            border-radius: 8px;
            padding: 18px 1px 1px 1px;
            position: relative;
            display: flex;
            flex-direction: column;
            .tip {
                position: absolute;
                left: -4px;
                top: -12px;
                width: 75px;
                height: 24px;
                background: url(../images/4.png) no-repeat left center;
                background-size: 75px 24px;
                padding-top: 6px;
                font-size: 12px;
                color: #FFFFFF;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            p {
                text-align: center;

                &.p1 {
                    font-size: 14px;
                    text-align: center;
                    color: #333333;
                    line-height: 16px;
                }

                &.p2 {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 6px 0 5px 0;

                    span {
                        font-size: 20px;
                        font-weight: bold;
                        text-align: center;
                        color: #333333;
                        line-height: 17px;
                        padding-right: 4px;
                        padding-top: 10px;
                    }

                    b {
                        font-size: 38px;
                    }
                }

                &.p3 {
                    height: 25px;
                    font-size: 11px;
                    text-align: center;
                    color: #6e6e6e;
                    line-height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            &.active {
                padding: 17px 0 0 0;
                margin: 0;
                background: #fff2dd;
                border: 2px solid #eb9300;
                .tip {
                    left: -5px;
                    top: -13px;
                }

                .p3 {
                    color: #a86900;
                }
            }
        }
    }

    .footer {
        padding-bottom: calc(~"10px + constant(safe-area-inset-bottom)/2"
            ) !important;
        /* 兼容 iOS < 11.2 */
        padding-bottom: calc(~"10px + env(safe-area-inset-bottom)/2");
        background: #fff;
    }
}
