<import name="style" content="./main" />
<import name="header" content=":component/header/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payDialog" content=":component/payDialog/main" />
<div class="page-container page-clean">
    <div class="page-header">
        <sp:if value="state.isDialogPage">
            <div class="dhd">
                <span></span>
                <div class="title">清爽答题</div>
                <span class="close" sp-on:click="close"></span>
            </div>
            <sp:else />
            <com:header title="清爽答题" theme="white" endTheme="white"
                scrollTop="{{state.prevScrollTop}}">
                <div sp:slot="help"></div>
            </com:header>
        </sp:if>
    </div>
    <div class="page-content">
        <div class="intro">
            <p class="s1">自选答题背景</p>
            <p class="s2">做题免广告</p>
        </div>
        <sp:if value="state.hasPermission">
            <div class="text">
                <p class="p2">感谢您开通「清爽答题」权益。祝您考试顺利，早日拿本！</p>
                <p class="p3">有效期截止：{{state.expireTimeString}}</p>
            </div>
            <sp:else />
            <div class="text">
                <p class="p2">
                    开通清爽答题权益后，您可以<span>自选做题界面背景</span>。同时，在指定时间内，做题界面将<span>不再展示广告</span>。这将给您更沉浸、清爽的做题体验！
                </p>
            </div>
        </sp:if>
        <sp:if value="state.showGoods">
            <div class="goods">
                <div class="good-wrap">
                    <sp:each for="{{state.goodsInfoPool}}">
                        <div sp-on:click="switchGood" class="good {{$index == state.tabIndex ? 'active': ''}}"
                            data-index="{{$index}}">
                            <sp:if value="$index == 2">
                                <div class="tip">限时特惠</div>
                            </sp:if>
                            <p class="p1">{{$value.validDays}}天</p>
                            <p class="p2"><span>¥</span><b>{{$value.payPrice}}</b></p>
                            <p class="p3">{{$value.tips.label}}</p>
                        </div>
                    </sp:each>
                </div>
            </div>
        </sp:if>
    </div>

    <sp:if value="!state.hasPermission">
        <div class="footer">
            <com:buyButton> </com:buyButton>
        </div>
    </sp:if>
    <com:payDialog />
</div>
