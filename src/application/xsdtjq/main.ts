/*
 * ------------------------------------------------------------------
 * 答题技巧
 * ------------------------------------------------------------------
 */

import { setPageName, URLParams } from ':common/env';
import texts from ':common/features/texts';

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
export default class extends Application {
    $constructor() {
        const fromPage = URLParams.fromPage || '答题技巧弹窗页';
        const fragmentName1 = URLParams.fragmentName1 || '未知片段';

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        setPageName(fromPage);

        this.state = {
            fragmentName1,
            title1: '精简题库',
            subTitle1: '快速掌握重难点，省时省力'
        };        
    }
}
