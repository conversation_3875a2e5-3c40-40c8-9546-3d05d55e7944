<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="buyDialogOrderSign" content=":component/buyDialogOrderSign/main" />
<!-- <import
    name="BusTruck14"
    content=":application/khche/component/BusTruck14/main"
/> -->
<import name="CarKemu14" content=":application/car/component/CarKemu14/main" />

<import name="CarShort" content=":application/car/component/CarShort/main" />

<div class="page-container page-guache">
    <div class="page-header">
        <com:header
            title="{{state.prevScrollTop > 200?self.nowGoodInfo.name: ' '}}"
            theme="black"
            endTheme="black"
            qaKey="{{self.qaKey}}"
            scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}"
        />
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <!-- 科目1或4 -->
        <div
            class="{{self.nowGoodInfo.groupKey === GroupKey.GcChannelKe4?'show':'hide'}}"
        >
            <com:CarKemu14
                noVideo="true"
                goodsInfo="{{self.nowGoodInfo}}"
                labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}"
                couponPool="{{state.couponPool}}"
                payPrice="{{self.showPrice}}"
                groupKey="{{self.nowGoodInfo.groupKey}}"
                isHubei="{{state.isHubei}}"
                goAuth="{{self.goAuth}}"
                payBtnCall="{{self.payBtnCall}}"
            />
        </div>

        <!-- 短时提分 -->
        <sp:if value="state.kemu== 4">
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.GcChannelKe4Short?'show':'hide'}}"
            >
                <com:CarShort
                    payPrice="{{self.showPrice}}"
                    labelPool="{{state.labelPool}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>
    </div>
    <div class="footer {{state.goodsInfoPool.length > 1?'':'hide'}}">
        <com:bottomTabs
            tabIndex="{{state.tabIndex}}"
            labelPool="{{state.labelPool}}"
            comparePricePool="{{state.comparePricePool}}"
            goodsList="{{state.goodsInfoPool}}"
            tabChange="{{self.tabChangeCall}}"
        />
    </div>
    <com:buyButton>
        <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
            {{self.nowCouponInfo.couponCode?'已优惠' +
            self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
        </div>
    </com:buyButton>
    <!-- 所有商品信息加载完成才能加载这个组件，内部有根据商品判断 -->
    <sp:if value="state.showSignModal">
        <com:buyDialogOrderSign goodsInfoPool="{{state.goodsInfoPool}}" labelPool="{{state.labelPool}}"
            comparePricePool="{{state.comparePricePool}}" couponPool="{{state.couponPool}}"
            close="{{self.closeSignModal}}" />
    </sp:if>
    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
</div>
