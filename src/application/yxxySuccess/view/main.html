<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="K1K4" content=":application/yxxy/component/k1k4/main" />

<div class="page-container page-yxxySuccess">
    <div class="page-header">
        <com:header
            title="{{state.prevScrollTop > 200?'购买成功': ' '}}"
            theme="black"
            endTheme="black"
            scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}"
        />
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <com:K1K4
            isDefaluse="{{false}}"
            isCheckPermission="{{true}}"
            fromRouetrPage={{URLParams.fromRouetrPage}}
        />
    </div>
    <div class="footer goview-exam-container" sp-on:click="gotoExam">
        <div class="goview-exam">
            <sp:if value='{{URLParams.fromRouetrPage==="mnksPage"}}'>
                <p class="goview-exam-title">去使用</p>
                <sp:else />
                <p class="goview-exam-title">去使用真实考场</p>
                <sp:if value="state.checkPermission.residualTimes > 0">
                    <p class="goview-exam-desc">
                        还剩{{state.checkPermission.residualTimes}}次
                    </p>
                </sp:if>
            </sp:if>
           
            <div class="exam-tips">
                {{state.checkPermission.expireTimeString}}到期
            </div>
        </div>
    </div>
</div>
