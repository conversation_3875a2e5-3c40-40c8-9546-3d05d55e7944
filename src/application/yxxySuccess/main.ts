/*
 * ------------------------------------------------------------------
 * 优秀学员落地页
 * ------------------------------------------------------------------
 */

import { Application } from '@simplex/simple-core';
import { ABTestKey, ABTestType, CarType, URLCommon, URLParams, Version } from ':common/env';
import View from './view/main.html';
import { openWeb, webClose } from ':common/core';
import { checkRestriction, getAbtest, getPermission } from ':store/chores';
import { hiddenIOSPayButton } from ':common/features/ios_pay';
import { onPageShow } from ':common/features/page_status_switch';
import jump, { replace } from ':common/features/jump';
import { OUTLIMIT, REAL_ROOM } from ':common/navigate';
import { dateFormat } from ':common/utils';

interface State {
    prevScrollTop: number
    checkPermission: any
    isDefaluse: boolean
}

let timer;

export default class extends Application<State> {
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            prevScrollTop: 0,
            checkPermission: {},
            isDefaluse: false
        };

    }
    get getAbtestKeyByTiku() {
        if (URLCommon.tiku === CarType.TRUCK) {
            return ABTestKey.key15;
        } else if (URLCommon.tiku === CarType.CAR) {
            return ABTestKey.key13;
        }
        return '';
    }
    async didMount() {
        this.appProxy();
        await this.getPermission();
    }
    appProxy() {
        // ios的情况下，上个页面的底部按钮要关闭（当前webview跳转的）
        hiddenIOSPayButton();

        onPageShow(() => {
            this.getPermission();
        });
    }
    async getPermission() {
        let permisstionKey;
        if (URLParams.fromRouetrPage === 'mnksPage') {
            switch (URLCommon.tiku) {
                case CarType.CAR:
                    permisstionKey = URLCommon.kemu === 1 ? 'kcmnK1' : 'kcmnK4';
                    break;
                case CarType.TRUCK:
                    permisstionKey = URLCommon.kemu === 1 ? 'kcmnhcK1' : 'kcmnhcK4';
                    break;
                case CarType.MOTO:
                    permisstionKey = URLCommon.kemu === 1 ? 'kcmnmtK1' : 'kcmnmtK4';
                    break;
                default:
                    break;
            }
        } else {
            switch (URLCommon.tiku) {
                case CarType.CAR:
                    permisstionKey = URLCommon.kemu === 1 ? 'kcmnnumK1' : 'kcmnnumK4';
                    break;
                case CarType.TRUCK:
                    permisstionKey = URLCommon.kemu === 1 ? 'kcmnnumhcK1' : 'kcmnnumhcK4';
                    break;
                default:
                    break;
            }
        }

        let checkPermission: any = {};
        checkPermission = await getPermission(permisstionKey);
        checkPermission.expireTimeString = dateFormat(checkPermission.validEndTime, 'yyyy.MM.dd');
        if (checkPermission.status !== 1) {
            if (URLParams.fromRouetrPage === 'mnksPage') {
                replace('./mnksPage.html');
            } else {
                replace('./yxxy.html');
            }

            return;
        }

        await this.checkRestriction();

        this.setState({ checkPermission });
    }
    gotoExam() {
        openWeb({
            url: REAL_ROOM
        });
    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            this.setState({
                prevScrollTop
            });
        }, 100);
    }

    backCall = () => {
        webClose();
    }

    /** 进入页面先判断是否设备超限 */
    async checkRestriction() {
        const { pass } = await checkRestriction();
        if (!pass) {
            jump.replace(OUTLIMIT);
        }
    }
}
