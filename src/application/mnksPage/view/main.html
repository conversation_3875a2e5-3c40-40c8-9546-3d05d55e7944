<import name="style" content="./main" />
<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="K1K4" content=":application/yxxy/component/k1k4/main" />
<import name="CarKemu14" content=":application/car/component/CarKemu14/main" />
<import name="BusTruck14" content=":application/khche/component/BusTruck14/main" />
<import name="MotoKemu14" content=":application/moto/component/MotoKemu14/main" />
<div class="page-container page-mnks-page">
    <div class="page-header">
        <com:header title="{{state.prevScrollTop > 200?self.getTitle: ' '}}" theme="black" endTheme="black"
            qaKey="{{self.qaKey}}" scrollTop="{{state.prevScrollTop}}" back="{{self.backCall}}" />
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <div class="{{state.tabIndex === 0?'':'hide'}}">
            <com:K1K4 isDefaluse="{{false}}" labelPool="{{state.labelPool}}" goodsList="{{self.nowGoodInfo}}"
                groupKey="{{self.nowGoodInfo.groupKey}}" payPrice="{{self.showPrice}}" goAuth="{{self.goAuth}}"
                payBtnCall="{{self.payBtnCall}}" fromRouetrPage="mnksPage" />
        </div>
        <!-- 小车科目1或4 -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.ChannelKe1).payPrice || self.getGroupKeyInfo(GroupKey.ChannelKe4).payPrice">
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.ChannelKe1 ||self.nowGoodInfo.groupKey === GroupKey.ChannelKe4?'':'hide'}}">
                <com:CarKemu14 currentIndex="{{state.tabIndex}}" labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}" payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}" kemu="{{URLCommon.kemu}}" tiku="{{URLCommon.tiku}}"
                    isHubei="{{state.isHubei}}" goAuth="{{self.goAuth}}" payBtnCall="{{self.payBtnCall}}" />
            </div>
        </sp:if>
        <!-- 货车科目1或4 -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.HcChannelKe1).payPrice || self.getGroupKeyInfo(GroupKey.HcChannelKe4).payPrice">
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.HcChannelKe1 ||self.nowGoodInfo.groupKey === GroupKey.HcChannelKe4?'':'hide'}}">
                <com:BusTruck14 goodsInfo="{{self.nowGoodInfo}}" labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}" payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}" isHubei="{{state.isHubei}}" goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}" />
            </div>
        </sp:if>
        <!-- 摩托车科一科四 -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.MotoChannelKe1).payPrice || self.getGroupKeyInfo(GroupKey.MotoChannelKe4).payPrice">
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.MotoChannelKe1 ||self.nowGoodInfo.groupKey === GroupKey.MotoChannelKe4?'':'hide'}}">
                <com:MotoKemu14 kemu="{{URLCommon.kemu}}" goodsList="{{self.nowGoodInfo}}"
                    currentIndex="{{state.tabIndex}}" tabChange="{{self.tabChangeCall}}"
                    goodsInfo="{{self.nowGoodInfo}}" labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}" payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}" goAuth="{{self.goAuth}}" payBtnCall="{{self.payBtnCall}}">
                </com:MotoKemu14>
            </div>
        </sp:if>
    </div>
    <div class="footer {{state.goodsInfoPool.length > 1?'':'hide'}}">
        <com:bottomTabs tabIndex="{{state.tabIndex}}" labelPool="{{state.labelPool}}"
            comparePricePool="{{state.comparePricePool}}" goodsList="{{state.goodsInfoPool}}"
            tabChange="{{self.tabChangeCall}}" />
    </div>
    <com:buyButton>
        <div sp:slot="couponEntry" class="go_coupon" sp-on:click="goCoupon">
            {{self.nowCouponInfo.couponCode?'已优惠' +
            self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
        </div>
    </com:buyButton>
    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />

</div>