/*
 * ------------------------------------------------------------------
 * 模拟考试落地页
 * ------------------------------------------------------------------
 */

import { CarType, KemuType, PayType, Platform, setPageName, URLCommon } from ':common/env';
import { formatPrice } from ':common/utils';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import { comparePrice, getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupKey, TimesList } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { typeCode } from ':common/features/bottom';
import { isHubei } from ':common/features/locate';
import { iosBuySuccess, iosPay } from ':common/features/ios_pay';
import { webClose } from ':common/core';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackExit, trackGoPay, trackPageLoad, trackPageShow } from ':common/stat';
import { onWebBack } from ':common/features/persuade';
import { Coupon, getBestCoupon, goodsInfoWithCoupon, selectUserCoupon } from ':common/features/coupon';
import { BUYED_URL, openAuth } from ':common/navigate';
import { pauseAllVideos, scrollTop } from ':common/features/dom';
import { onPageShow } from ':common/features/page_status_switch';
import jump, { replace } from ':common/features/jump';
import { getTabIndex } from ':common/features/cache';
import isNumber from 'lodash/isNumber';
import { getPermission } from ':store/chores';

interface State {
    isHubei: boolean,
    kemu: KemuType,
    tiku: CarType,
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    prevScrollTop: number,
}
let timer;
const pageNameMap = {
    [GroupKey.ChannelKe1]: 'VIP速成版页',
    [GroupKey.ChannelKe4]: 'VIP速成版页',
    [GroupKey.HcChannelKe1]: 'VIP速成版页',
    [GroupKey.HcChannelKe4]: 'VIP速成版页',
    [GroupKey.MotoChannelKe1]: 'VIP速成版页',
    [GroupKey.MotoChannelKe4]: 'VIP速成版页'
};
const qaKeyMap = {
    [GroupKey.ChannelKe1]: 'qaKey1',
    [GroupKey.ChannelKe4]: 'qaKey4',
    [GroupKey.HcChannelKe1]: 'qaKey1',
    [GroupKey.HcChannelKe4]: 'qaKey4',
    [GroupKey.MotoChannelKe1]: 'qaKey1',
    [GroupKey.MotoChannelKe4]: 'qaKey4'
};

export default class extends Application<State> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;

            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    get qaKey() {
        return qaKeyMap[this.nowGoodInfo.groupKey] || 'qaKey1';
    }
    get getTitle() {
        return this.state.tabIndex === 0 ? '真实考场模拟' : this.nowGoodInfo.name;
    }
    get pageName() {
        return this.state.tabIndex === 0 ? '真实考场单商品页' : pageNameMap[this.nowGoodInfo.groupKey];
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        const tiku = URLCommon.tiku;
        const kemu = +URLCommon.kemu;

        this.state = {
            isHubei: true,
            kemu,
            tiku,
            tabIndex: 0,
            goodsInfoPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            prevScrollTop: 0
        };

    }
    async didMount() {
        const kemu = +URLCommon.kemu;
        const goodsInfoPool: GoodsInfo[] = [];
        // 有模拟考试的权限就跳到模拟考试成功页
        console.log('permisstionKey');
        if (await this.checkPermission()) {

            replace('./yxxySuccess.html', {
                fromRouetrPage: 'mnksPage'
            });
            return;
        }
        switch (URLCommon.tiku) {
            case CarType.CAR:
                switch (kemu) {
                    case 1:
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1Kcmn
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1
                        } as GoodsInfo);
                        break;
                    case 4:
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe4Kcmn
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe4
                        } as GoodsInfo);
                        break;
                    default:
                        break;
                }
                break;
            case CarType.TRUCK:
                switch (kemu) {
                    case 1:
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1Kcmnhc
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe1
                        } as GoodsInfo);
                        break;
                    case 4:
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe4Kcmnhc
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe4
                        } as GoodsInfo);
                        break;
                    default:
                        break;
                }
                break;
            case CarType.MOTO:
                switch (kemu) {
                    case 1:
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1Kcmnmt
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: GroupKey.MotoChannelKe1
                        } as GoodsInfo);
                        break;
                    case 4:
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe4Kcmnmt
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: GroupKey.MotoChannelKe4
                        } as GoodsInfo);
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        this.state.goodsInfoPool = goodsInfoPool;
        await this.getGoodInfo();

        setPageName(this.pageName);
        // 页面进出时长打点
        trackPageLoad();

        onPageShow(() => {
            // 这里重新设置的主要原因是因为优惠券
            this.setPageInfo();
        });

        // 判断是否是湖北
        isHubei().then(isHubei => {
            this.setState({
                isHubei
            });
        });

        // app代理方法
        this.appEventProxy();

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                if (TimesList.includes(this.nowGoodInfo.groupKey)) {
                    replace('./yxxySuccess.html', {
                        fromRouetrPage: 'mnksPage'
                    });
                } else {
                    iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
                }

            }
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: goodsInfoPool[this.state.tabIndex].groupKey
        });
    }
    async checkPermission() {
        let permisstionKey;
        switch (URLCommon.tiku) {
            case CarType.CAR:
                permisstionKey = URLCommon.kemu === 1 ? 'kcmnK1' : 'kcmnK4';
                break;
            case CarType.TRUCK:
                permisstionKey = URLCommon.kemu === 1 ? 'kcmnhcK1' : 'kcmnhcK4';
                break;
            case CarType.MOTO:
                console.log('permisstionKey');
                permisstionKey = URLCommon.kemu === 1 ? 'kcmnmtK1' : 'kcmnmtK4';
                break;
            default:
                break;
        }

        const { hasPromission } = await getPermission(permisstionKey);
        if (hasPromission) {
            return true;
        }
        return false;
    }
    getGroupKeyInfo(groupKey) {
        const { goodsInfoPool } = this.state;
        const goodInfo = goodsInfoPool.find(item => {
            return item.groupKey === groupKey;
        });
        return goodInfo || {};
    }
    appEventProxy() {
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        // 退出当前tab的打点
        this.leavePageCall();

        // 回到滚动的顶部
        scrollTop(document.querySelector('.page-mnks-page .body-panel'));

        // 暂停所有视频
        pauseAllVideos();

        this.setState({
            tabIndex
        }, () => {
            setPageName(this.pageName);

            trackPageShow();

            this.setPageInfo();

        });

    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            this.setState({
                prevScrollTop
            });
        }, 100);
    }
    setPageInfo() {
        this.setBuyBottom();
    }
    setBuyBottom() {
        const fragmentName1 = '底部吸底按钮';
        const { tabIndex, goodsInfoPool } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const bottomType: typeCode = typeCode.type4;
        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1
                });
                break;
            default:
                break;
        }
    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });
        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    jump.replace(BUYED_URL);
                    return;
                }

                // 商品未购买才push
                if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }
            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool
            });

            this.setPageInfo();

            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getLabel();
                await this.getComparePrice();

                this.setPageInfo();
            }, 60);
        });
    }
    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }
    async getComparePrice() {
        const { goodsInfoPool, tiku } = this.state;
        const promiseList = [];
        const comparePricePool = {};

        goodsInfoPool.forEach((item) => {
            promiseList.push(comparePrice({
                groupKey: item.groupKey,
                upgradeStrategyCode: item.upgradeStrategyCode,
                tiku
            }));
        });

        await Promise.all(promiseList).then(comparePriceList => {
            comparePriceList.forEach((item, index) => {
                if (item) {
                    comparePricePool[goodsInfoPool[index].groupKey] = {
                        diffPrice: item.savePrice,
                        allPrice: item.allPrice
                    };
                }
            });
            console.log(comparePricePool, 'comparePricePool');

            this.setState({ comparePricePool });
        });
    }
    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: this.nowCouponInfo.couponCode,
            ...stat
        }, false).then(() => {
            if (TimesList.includes(this.nowGoodInfo.groupKey)) {
                replace('./yxxySuccess.html', {
                    fromRouetrPage: 'mnksPage'
                });
            } else {
                newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
            }
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    goAuth = async (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });

        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
    payBtnCall = (e) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');

        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });
        }
    }
    // 退出页面的回调
    goBackPage() {
        webClose();
    }
    backCall = () => {
        this.goBackPage();
    }
    async goCoupon() {
        const { couponPool } = this.state;
        const couponInfo = await selectUserCoupon(this.nowGoodInfo, this.nowCouponInfo?.couponCode);

        if (couponInfo) {
            couponPool[this.nowGoodInfo.groupKey] = {
                ...couponInfo,
                priceCent: formatPrice(couponInfo.priceCent)
            };
            this.setState({
                couponPool
            });
            this.forceUpdate(true);
        }
        this.setPageInfo();
    }
    // 离开当前页面
    leavePageCall = () => {
        // 退出当前页面的打点
        setPageName(this.pageName);
        trackExit();
    }
}
