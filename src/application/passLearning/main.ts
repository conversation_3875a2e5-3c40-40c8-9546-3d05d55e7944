/* eslint-disable no-nested-ternary */
/*
 * 高效学习法
 */

import { openVipWebView, openWeb, webClose } from ':common/core';
import { CarType, KemuType, Platform, URLCommon, URLParams, setPageName, getPageName } from ':common/env';
import { checkReaded } from ':common/features/agreement';
import { getBestCoupon } from ':common/features/coupon';
import { makeToast } from ':common/features/dom';
import { iosDialogBuySuccess, iosPay } from ':common/features/ios_pay';
import { PayBoundType, ensureSiriusBound, newBuySuccess, startSiriusPay } from ':common/features/pay';
import { zigezhengGroupKeyObj, zigezhengTextMap } from ':common/features/zigezheng';
import { AGED_500, EXAM_JUAN, JING_JIANG, MI_JIANG, PAY_GUIDE_URL, SCORE12 } from ':common/navigate';
import { trackExit, trackGoPay, trackPageLoad } from ':common/stat';
import { dateFormat, dayDiff, handleIosZigezhen, promisify } from ':common/utils';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import Header from ':component/header/main';
import PayDialog from ':component/payDialog/main';
import { GoodsInfo, GroupComparePrice, GroupKey, getGroupSessionInfo, getSessionExtra } from ':store/goods';
import { getIsVipInfo } from ':store/passAnalysis';
import { MCProtocol } from '@simplex/simple-base';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import jump, { reload } from ':common/features/jump';
import { onPageShow } from ':common/features/page_status_switch';

// 从非科一科四进来要取科一的数据
if (URLCommon.kemu !== KemuType.Ke1 && URLCommon.kemu !== KemuType.Ke4) {
    URLCommon.kemu = KemuType.Ke1;
    URLParams.kemuStyle = KemuType.Ke1 + '';
    // 需要切科目，不然跳转到做题页会有问题
    openWeb({
        url: 'http://jiakao.nav.mucang.cn/switchKemu?kemu=1'
    });
}

interface State {
    userInfo: any;
    step: number;
    headerTitle: string;

    examTime: {
        date: number;
        notOrder: boolean;
        type: 'none' | 'notOrder' | 'date';
        labelInfo: {
            time?: string;
            dueLabel?: string;
            leftOverLabel?: string;
            todayLabel?: string;
        }
    };

    isVip: boolean;
    isQuanke: boolean;

    normalReport: {
        totalCount: number;
        doneCount: number;
        rightCount: number;
    };
    vipReport: {
        totalCount: number;
        doneCount: number;
    };
    errorReport: {
        errorCount: number;
        knowledgePoint: Array<{
            id: number;
            name: string;
            errorCount: number;
        }>
    };
    knowledgeReport: {
        totalCount: number;
        unskilledCount: number;
        undoneCount: number;
    };
    examReport: {
        examCount: number;
        successCount: number;
        doubleSuccess: boolean;
        topScore: number;
        records: Array<{
            score: number;
            success: boolean;
            startTime: number;
            duration: number;
            title: string;
        }>
    };
    passScore: number;

    tabIndex: number;
    goodsInfoPool: GoodsInfo[];
    couponPool: object;
    labelPool: object;
    comparePricePool: object;
}

export default class extends Application<State> {
    declare children: {
        payDialog: PayDialog;
        buyButton: BuyButton;
        header: Header
    }

    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }

    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey] || {};
    }

    get showPrice() {
        const { tabIndex, goodsInfoPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;
        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }

    get pageTitle() {
        let prefix: string;
        if (URLCommon.isZigezheng) {
            prefix = zigezhengTextMap[URLCommon.tiku];
        } else if (URLCommon.isScore12) {
            prefix = '满分学习';
        } else {
            prefix = URLCommon.kemu === KemuType.Ke4 ? '科目四' : '科目一';
        }
        return prefix + '高效学习法';
    }

    get mijuanText() {
        return `考前${!URLCommon.isScore12 ? '秘' : URLCommon.tiku === CarType.MOTO ? '两套' : '三套'}卷`;
    }

    get progressInfo() {
        const { knowledgeReport, isVip, vipReport, normalReport } = this.state;

        return {
            progress1: {
                doneCount: isVip ? vipReport.doneCount : normalReport.doneCount,
                totalCount: isVip ? vipReport.totalCount : normalReport.totalCount,
                get percent() {
                    return this.doneCount / this.totalCount;
                },
                get success() {
                    return (this.percent > 0.9);
                },
                get level() {
                    if (this.percent < 0.8) {
                        return 1;
                    } else if (this.percent < 0.9) {
                        return 2;
                    }
                    return 3;
                },
                get text() {
                    if (this.percent < 0.8) {
                        return '较慢';
                    } else if (this.percent < 0.9) {
                        return '一般';
                    }
                    return '较快';
                }
            },
            progress2: {
                get percent() {
                    if (!normalReport.doneCount) {
                        return 0;
                    }
                    return Math.round(normalReport.rightCount / normalReport.doneCount * 100) / 100;
                },
                get percentNum() {
                    if (!normalReport.doneCount) {
                        return 0;
                    }
                    return Math.round(normalReport.rightCount / normalReport.doneCount * 100);
                },
                get success() {
                    return (this.percent > 0.9);
                },
                get level() {
                    if (this.percent < 0.8) {
                        return 1;
                    } else if (this.percent < 0.9) {
                        return 2;
                    }
                    return 3;
                },
                get text() {
                    if (this.percent < 0.8) {
                        return '较低';
                    } else if (this.percent < 0.9) {
                        return '一般';
                    }
                    return '较好';
                }
            },
            progress3: {
                doneCount: knowledgeReport.totalCount - knowledgeReport.unskilledCount - knowledgeReport.undoneCount,
                totalCount: knowledgeReport.totalCount,
                get percent() {
                    return this.doneCount / this.totalCount;
                },
                get success() {
                    return (this.percent > 0.9);
                },
                get level() {
                    if (this.percent < 0.8) {
                        return 1;
                    } else if (this.percent < 0.9) {
                        return 2;
                    }
                    return 3;
                },
                get text() {
                    if (this.percent < 0.8) {
                        return '较低';
                    } else if (this.percent < 0.9) {
                        return '一般';
                    }
                    return '较好';
                }
            }
        };
    }

    $constructor(params) {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {
            // 已经制定过学习计划，则直接展示详情
            step: localStorage.getItem(this.getPlanKey(params.userInfo)) === 'true' ? 3 : 1,
            userInfo: params.userInfo,
            headerTitle: '',

            examTime: {
                date: 0,
                notOrder: false,
                type: 'none',
                labelInfo: {}
            },

            isVip: false,
            isQuanke: false,

            normalReport: {
                doneCount: 0,
                totalCount: 1,
                rightCount: 0
            },
            vipReport: {
                totalCount: 1,
                doneCount: 0
            },
            knowledgeReport: {
                totalCount: 1,
                undoneCount: 0,
                unskilledCount: 0
            },
            errorReport: {
                errorCount: 0,
                knowledgePoint: []
            },
            examReport: {
                examCount: 0,
                successCount: 0,
                doubleSuccess: false,
                topScore: 0,
                records: []
            },
            passScore: 90,

            tabIndex: 0,
            goodsInfoPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {}
        };
    }

    async didMount() {
        // 设置主题色
        MCProtocol.Core.Web.setStatusBarTheme({ theme: 'dark' });

        if (this.state.step === 1) {
            this.goStep1();
        } else {
            this.goStep3();
        }
    }

    private getPlanKey(userInfo) {
        // 根据mucangId-车型-[科目]缓存
        return `passLearning-${userInfo.mucangId}-${URLCommon.tiku}${URLCommon.isZigezheng ? '' : '-' + (URLCommon.kemu === KemuType.Ke4 ? KemuType.Ke4 : KemuType.Ke1)}`;
    }

    onBackClick() {
        webClose();
    }

    // ------- 以下为step1的逻辑 -------

    goStep1() {
        setPageName('VIP高效学习法制定页');
        trackPageLoad({
            payStatus: 0
        });
        this.getExamTime();
    }

    async getExamTime() {
        const res = await promisify(MCProtocol['jiakao-global'].web.getExamDate)({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode
        });
        console.log('web.getExamDate', res);
        const examTime = res.data;
        const labelInfo: any = {};
        const type = examTime.notOrder ? 'notOrder' : examTime.date ? 'date' : 'none';
        if (type === 'date') {
            labelInfo.time = dateFormat(examTime.date, 'M月dd日');
            const diff = dayDiff(examTime.date);

            if (diff === 0) {
                labelInfo.todayLabel = '祝您今天考试顺利';
            } else if (diff < 0) {
                labelInfo.dueLabel = `已过期${-diff}天`;
            } else if (diff > 0) {
                labelInfo.leftOverLabel = `还剩${diff}天`;
            }
        }
        this.setState({
            examTime: {
                ...examTime,
                type,
                labelInfo
            }
        });
    }

    async selectExamTime() {
        await promisify(MCProtocol['jiakao-global'].web.showExamDateAlert)({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            from: URLParams.from,
            fromPageCode: URLParams.from,
            pageName: getPageName(),
            autoPassAnalysis: false
        });
        await this.getExamTime();
    }

    async onGenPlanClick() {
        if (!this.state.userInfo.authToken) {
            // 登录完立即刷新
            promisify(MCProtocol.Core.User.login)({
                from: 'jiakaobaodian',
                skipAuthRealName: true,
                pageType: 'quicklogin'
            }).then(() => {
                reload();
            });
            return;
        }

        // 未填写过约考时间
        if (this.state.examTime.type === 'none') {
            makeToast('请您现在选择约考时间');
            return;
        }

        // 约考时间已过期
        if (this.state.examTime.labelInfo.dueLabel) {
            await this.selectExamTime();
        }

        this.setState({
            step: 2
        }, () => {
            trackExit();
            localStorage.setItem(this.getPlanKey(this.state.userInfo), 'true');
            this.bindAfterGenPlan();
        });
    }

    bindAfterGenPlan() {
        const dom = this.getDOMNode().anim as HTMLElement;
        dom.addEventListener('animationend', () => {
            // 不能无感跳转，打点有问题
            // this.setState({
            //     step: 3
            // }, () => {
            //     this.goStep3();
            // });
            // location.reload();
            jump.reload();
        });
    }

    // ------- 以下为step3的逻辑 -------

    async goStep3() {
        setPageName('VIP高效学习法页');

        this.getDataReport();
        await this.getVipInfo();
        // 页面进出时长打点
        trackPageLoad({
            payStatus: this.state.isVip ? '1' : '2'
        });
        this.renderVipPage();
    }

    async getVipInfo() {
        const authToken = this.state.userInfo.authToken;
        if (authToken) {
            const vipInfo = await getIsVipInfo();
            console.log('getIsVipInfo', vipInfo);
            this.setState({ isVip: vipInfo.isVip, isQuanke: vipInfo.isQuanke });
        }
    }

    getSceneCode() {
        let score12Type: number;
        // 判断以前版本传了score12为1代表扣满12分
        if (+URLParams.score12 === 1) {
            score12Type = 102;
        } else {
            score12Type = 101;
        }
        return +URLParams.sceneCode || score12Type;
    }

    getDataReport() {
        MCProtocol.data.practice.normalReport({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            callback: (res) => {
                console.log('normalReport', res);
                // 对题数不能大于已做题数
                res.data.rightCount = Math.min(res.data.rightCount, res.data.doneCount);
                this.setState({ normalReport: res.data || {} });
            }
        });
        MCProtocol.data.practice.vipReport({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            callback: (res) => {
                console.log('vipReport', res);
                this.setState({ vipReport: res.data || {} });
            }
        });
        MCProtocol.data.exam.report({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            recentTimes: 0,
            callback: (res) => {
                console.log('examReport', res);
                this.setState({ examReport: res.data || {} });
            }
        });
        MCProtocol.Vip.getExamRule({
            car: URLCommon.tiku,
            kemu: URLCommon.isZigezheng ? 8 : URLCommon.kemu,
            kemuStyle: URLCommon.isZigezheng ? 8 : URLCommon.kemu,
            carStyle: URLCommon.tiku,
            patternCode: URLParams.patternCode,
            sceneCode: this.getSceneCode(),
            callback: (res) => {
                console.log('getExamRule', res);
                const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
                this.setState({ passScore: +(data.passScore || 90) });
            }
        });
        MCProtocol.data.practice.knowledgePointReport({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            recentTimes: 0,
            callback: (res) => {
                console.log('knowledgePointReport', res);
                this.setState({
                    knowledgeReport: res.data || {}
                });
            }
        });
        MCProtocol.data.practice.errorReport({
            kemu: URLCommon.kemu,
            kemuStyle: URLCommon.kemu,
            carStyle: URLCommon.tiku,
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            knowledgePointCount: 0,
            callback: (res) => {
                console.log('errorReport', res);
                this.setState({ errorReport: res.data || {} });
            }
        });
    }

    onPageScroll(e) {
        const prevScrollTop = e.currentTarget.scrollTop;
        if (prevScrollTop > 100) {
            !this.state.headerTitle && this.setState({
                headerTitle: this.pageTitle
            });
        } else {
            this.state.headerTitle && this.setState({
                headerTitle: ''
            });
        }
    }

    async goPractice(e) {
        const type = e.refTarget.getAttribute('data-type');

        let action: string;
        if (type === 'jingjian') {
            if (URLCommon.tiku === CarType.CAR && URLCommon.isElder) {
                action = AGED_500;
            } else {
                action = JING_JIANG;
            }
        } else if (type === 'exam') {
            action = EXAM_JUAN;
        } else if (type === 'mijuan') {
            if (URLCommon.isScore12) {
                action = SCORE12;
            } else {
                action = MI_JIANG;
            }
        }

        if (!handleIosZigezhen(action)) {
            openWeb({
                url: action
            });
        }

        // 返回后需要刷新做题数据
        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });
        this.getDataReport();
    }

    // ------------ 以下为vip购买逻辑 -------------

    async renderVipPage() {
        const kemu = +URLCommon.kemu;
        const goodsInfoPool: GoodsInfo[] = [];
        if (URLCommon.isScore12) {
            switch (URLCommon.tiku) {
                case CarType.CAR:
                    goodsInfoPool.push({
                        groupKey: GroupKey.ChannelKou12
                    } as GoodsInfo);
                    break;
                case CarType.TRUCK:
                    goodsInfoPool.push({
                        groupKey: GroupKey.HcChannelKou12
                    } as GoodsInfo);
                    break;
                case CarType.BUS:
                    goodsInfoPool.push({
                        groupKey: GroupKey.KcChannelKou12
                    } as GoodsInfo);
                    break;
                case CarType.MOTO:
                    goodsInfoPool.push({
                        groupKey: GroupKey.MotoChannelKou12
                    } as GoodsInfo);
                    break;
                default: break;
            }
        } else if (URLCommon.isElder) {
            goodsInfoPool.push({
                groupKey: kemu === 1 ? GroupKey.ElderChannelKe1
                    : GroupKey.ElderChannelKe4
            } as GoodsInfo);
            if (kemu === 1) {
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKemuAll
                } as GoodsInfo);
            }
        } else {
            switch (URLCommon.tiku) {
                case CarType.CAR:
                    goodsInfoPool.push({
                        groupKey: kemu === 1
                            ? GroupKey.ChannelKe1
                            : GroupKey.ChannelKe4
                    } as GoodsInfo);
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.ChannelKe1Ke4Group
                        } as GoodsInfo);
                    }
                    break;
                case CarType.TRUCK:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.HcChannelKe1 : GroupKey.HcChannelKe4
                    } as GoodsInfo);
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.HcChannelKe1Ke4Group
                        } as GoodsInfo);
                    }
                    break;
                case CarType.BUS:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.KcChannelKe1 : GroupKey.KcChannelKe4
                    } as GoodsInfo);
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.KcChannelKe1Ke4Group
                        } as GoodsInfo);
                    }
                    break;
                case CarType.MOTO:
                    goodsInfoPool.push({
                        groupKey: kemu === 1 ? GroupKey.MotoChannelKe1 : GroupKey.MotoChannelKe4
                    } as GoodsInfo);
                    if (kemu === 1) {
                        goodsInfoPool.push({
                            groupKey: GroupKey.MotoChannelKe1Ke4Group
                        } as GoodsInfo);
                    }
                    break;
                case CarType.GUACHE:
                    goodsInfoPool.push({
                        groupKey: GroupKey.GcChannelKe4
                    } as GoodsInfo);
                    break;
                default:
                    if (URLCommon.isZigezheng) {
                        goodsInfoPool.push({
                            groupKey: zigezhengGroupKeyObj[URLCommon.tiku]
                        } as GoodsInfo);
                    }
                    break;
            }
        }
        this.state.goodsInfoPool = goodsInfoPool;
        await this.getGoodInfo();
        setTimeout(async () => {
            await this.getCoupon();
            await this.getLabel();
            await this.getComparePrice();
        }, 60);
        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: async () => {
                iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey, noClose: true });
                this.afterBuyed();
            }
        });
        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: goodsInfoPool[this.state.tabIndex].groupKey
        });
    }

    getCoupon = async () => {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const couponPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getBestCoupon(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                couponPool[goodsInfoPool[index].groupKey] = {
                    couponCode: item.code,
                    priceCent: item.price
                };
            });

            this.setState({ couponPool });
        });

    }

    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });
            this.setState({ labelPool });
        });
    }

    // 比价必须在商品详情之后
    async getComparePrice() {
        const { goodsInfoPool } = this.state;

        GroupComparePrice({ groupKeys: goodsInfoPool.map(item => item.groupKey).join(',') }).then(comparePricePool => {
            this.setState({ comparePricePool });
        });

    }

    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach((item) => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async (goodsListInfo) => {

            goodsListInfo.forEach((goodInfo) => {
                // 商品未购买才push,或是可升级
                if (!goodInfo.bought || goodInfo.upgrade) {
                    goodInfo.name = goodInfo.upgrade ? '升级' + goodInfo.name : goodInfo.name;
                    newGoodsPool.push(goodInfo);
                }
            });
            this.setState({
                goodsInfoPool: newGoodsPool
            });
        });
    }

    pay = async (stat: PayStatProps) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: goodsInfoPool[tabIndex].groupKey,
            sessionIds: goodsInfoPool[tabIndex].sessionIds,
            activityType: goodsInfoPool[tabIndex].activityType,
            couponCode: '',
            ...stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey, noClose: true });
            this.afterBuyed();
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }

    async afterBuyed() {
        // 返回后需要刷新做题数据
        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });
        reload();
    }

    buttonBuy = (e) => {
        checkReaded(() => {
            const tabIndex = e.refTarget.getAttribute('data-tabIndex');
            this.setState({ tabIndex }, () => {
                this.payBtnCall(e);
            });

        });
    }

    payBtnCall = (e) => {
        const { tabIndex, goodsInfoPool } = this.state;
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        // 点击支付按钮打点
        trackGoPay({
            groupKey: goodsInfoPool[tabIndex].groupKey,
            fragmentName1,
            fragmentName2: ''
        });

        if (Platform.isIOS) {
            iosPay(goodsInfoPool[tabIndex].groupKey, {
                fragmentName1
            });
        } else {
            // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
            // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay({ fragmentName1 });
                },
                fragmentName1
            });
        }
    }

    gotoPayGuide() {
        openVipWebView({
            url: PAY_GUIDE_URL
        });
    }
}