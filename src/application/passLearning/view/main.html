<import name="style" content="./main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="PriceTag" content=":component/priceTag/main" />

<div class="page-step1 {{state.step!==1&&'hide'}}">
    <div class="back hotArea" sp-on:click="onBackClick" />
    <div class="panel">
        <div class="title">请确认您的约考时间</div>
        <div class="desc">以便推荐更合适的学习方案</div>

        <div class="label">{{state.examTime.type==='date'?'我的约考时间':'您预约的考试时间是？'}}</div>
        <div class="select" sp-on:click="selectExamTime">
            <div class="date {{state.examTime.type!=='date'&&'leftover'}}">
                {{state.examTime.type==='notOrder'?'未约考>':(state.examTime.labelInfo.time||'请选择考试时间>')}}
            </div>
            <div class="tips {{state.examTime.labelInfo.leftOverLabel&&'leftover'}} {{state.examTime.labelInfo.dueLabel&&'due'}} {{state.examTime.labelInfo.todayLabel&&'today'}}"
                sp:if="state.examTime.labelInfo.leftOverLabel||state.examTime.labelInfo.dueLabel||state.examTime.labelInfo.todayLabel">
                {{state.examTime.labelInfo.leftOverLabel||state.examTime.labelInfo.dueLabel||state.examTime.labelInfo.todayLabel}}
            </div>
        </div>
        <div class="flex1"></div>
        <div class="btn {{state.examTime.type==='none'&&'disable'}}" sp-on:click="onGenPlanClick">
            {{state.examTime.labelInfo.dueLabel?'调整时间，生成计划':state.examTime.type==='none'?'生成计划':'无需调整，生成计划'}}
        </div>
    </div>
</div>

<div class="page-step2 {{state.step!==2&&'hide'}}">
    <div class="back hotArea" sp-on:click="onBackClick" />
    <div class="title">HI，<span>{{state.userInfo.nickname}}</span></div>
    <div class="subtitle">正在为你生成目标</div>
    <div class="anim">
        <div class="scanner"></div>
    </div>
    <div class="progress-tips">练习情况评估中…</div>
    <div class="progress">
        <div class="inner" ref="anim"></div>
    </div>
</div>

<div class="page-step3 {{state.step!==3&&'hide'}}">
    <!-- 标题栏 -->
    <div class="header {{state.headerTitle ? 'header-white' : ''}}">
        <div class="back hotArea" sp-on:click="onBackClick" />
        <div>{{state.headerTitle}}</div>
        <div class="back hole" />
    </div>
    <div class="body-panel" sp-on:scroll="onPageScroll">
        <div class="banner" />
        <div class="nickname">Hi，<span>{{state.userInfo.nickname}}</span>已为你生成</div>
        <div class="subtitle">{{self.pageTitle}}</div>

        <div class="panel">
            <div class="title">我的计划安排</div>
            <div class="plans">
                <div class="plan">
                    <div
                        class="badge {{(state.isVip?(state.vipReport.doneCount>=state.vipReport.totalCount):(state.normalReport.doneCount>=state.normalReport.totalCount))||'disable'}}" />
                    <div class="name">目标1</div>
                    <div class="tips">完成<br />{{state.isVip?'VIP精简题库练习':'全题库练习'}}</div>
                    <div class="data">
                        <span>{{state.isVip?state.vipReport.doneCount:state.normalReport.doneCount}}</span>/{{state.isVip?state.vipReport.totalCount:state.normalReport.totalCount}}
                    </div>
                </div>
                <div class="plan">
                    <div class="badge {{state.examReport.doubleSuccess||'disable'}}" />
                    <div class="name">目标2</div>
                    <div class="tips">模拟考试</div>
                    <div class="tips2">连续2次{{state.passScore}}分以上</div>
                    <div class="data">最高分&nbsp;<span>{{state.examReport.topScore}}分</span></div>
                </div>
                <div class="plan">
                    <div class="badge {{state.normalReport.doneCount&&state.errorReport.errorCount===0||'disable'}}" />
                    <div class="name">目标3</div>
                    <div class="tips">错题本中的错题<br />都做对</div>
                    <div class="data">错题&nbsp;<span>{{state.errorReport.errorCount}}</span></div>
                </div>
            </div>
        </div>
        <div class="panel">
            <div class="title">我的评估结果</div>
            <div class="progresses">
                <div class="progress">
                    <div class="circle">
                        <svg width="61px" height="61px" viewBox="0 0 61 61" version="1.1""
                            xmlns=" http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <defs>
                                <linearGradient x1="13.4871951%" y1="40.4000165%" x2="100%" y2="59.5999835%"
                                    id="linearGradient-1">
                                    <stop stop-color="#FF8174" offset="0%"></stop>
                                    <stop stop-color="#FF4A40" offset="100%"></stop>
                                </linearGradient>
                                <linearGradient x1="40.8717988%" y1="0%" x2="62.5%" y2="100%" id="linearGradient-2">
                                    <stop stop-color="#FFAE74" offset="0%"></stop>
                                    <stop stop-color="#FF8640" offset="100%"></stop>
                                </linearGradient>
                                <linearGradient x1="50%" y1="0%" x2="50%" y2="106.581328%" id="linearGradient-3">
                                    <stop stop-color="#0AD1FF" offset="0%"></stop>
                                    <stop stop-color="#44BCFF" offset="100%"></stop>
                                </linearGradient>
                            </defs>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="学习计划" transform="translate(-47.000000, -453.000000)" stroke-width="6">
                                    <g id="编组-3" transform="translate(15.000000, 395.000000)">
                                        <g id="编组-2" transform="translate(21.500000, 61.000000)">
                                            <g id="pgjg_jm" transform="translate(13.500000, 0.000000)">
                                                <circle id="椭圆形" stroke="#E9ECF0" cx="27.5" cy="27.5" r="27.5"></circle>
                                                <circle cx="27.5" cy="27.5" r="27.5"
                                                    stroke="url(#linearGradient-{{self.progressInfo.progress1.level}})"
                                                    stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-dasharray="{{55*Math.PI}},{{55*Math.PI}}"
                                                    stroke-dashoffset="{{55*Math.PI*(1-self.progressInfo.progress1.percent)}}"
                                                    fill="none" />
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                        <div class="text level-{{self.progressInfo.progress1.level}}">
                            {{self.progressInfo.progress1.text}}</div>
                    </div>
                    <div class="name">练习进度</div>
                    <div class="tips">完成度&nbsp;
                        <span
                            class="level-{{self.progressInfo.progress1.level}}">{{self.progressInfo.progress1.doneCount}}</span>/{{self.progressInfo.progress1.totalCount}}
                    </div>
                </div>
                <div class="progress">
                    <div class="circle">
                        <svg width="61px" height="61px" viewBox="0 0 61 61" version="1.1""
                            xmlns=" http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <defs>
                                <linearGradient x1="13.4871951%" y1="40.4000165%" x2="100%" y2="59.5999835%"
                                    id="linearGradient-1">
                                    <stop stop-color="#FF8174" offset="0%"></stop>
                                    <stop stop-color="#FF4A40" offset="100%"></stop>
                                </linearGradient>
                                <linearGradient x1="40.8717988%" y1="0%" x2="62.5%" y2="100%" id="linearGradient-2">
                                    <stop stop-color="#FFAE74" offset="0%"></stop>
                                    <stop stop-color="#FF8640" offset="100%"></stop>
                                </linearGradient>
                                <linearGradient x1="50%" y1="0%" x2="50%" y2="106.581328%" id="linearGradient-3">
                                    <stop stop-color="#0AD1FF" offset="0%"></stop>
                                    <stop stop-color="#44BCFF" offset="100%"></stop>
                                </linearGradient>
                            </defs>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="学习计划" transform="translate(-47.000000, -453.000000)" stroke-width="6">
                                    <g id="编组-3" transform="translate(15.000000, 395.000000)">
                                        <g id="编组-2" transform="translate(21.500000, 61.000000)">
                                            <g id="pgjg_jm" transform="translate(13.500000, 0.000000)">
                                                <circle id="椭圆形" stroke="#E9ECF0" cx="27.5" cy="27.5" r="27.5"></circle>
                                                <circle cx="27.5" cy="27.5" r="27.5"
                                                    stroke="url(#linearGradient-{{self.progressInfo.progress2.level}})"
                                                    stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-dasharray="{{55*Math.PI}},{{55*Math.PI}}"
                                                    stroke-dashoffset="{{55*Math.PI*(1-self.progressInfo.progress2.percent)}}"
                                                    fill="none" />
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                        <div class="text level-{{self.progressInfo.progress2.level}}">
                            {{self.progressInfo.progress2.text}}</div>
                    </div>
                    <div class="name">答题正确率</div>
                    <div class="tips">正确率&nbsp;<span
                            class="level-{{self.progressInfo.progress2.level}}">{{self.progressInfo.progress2.percentNum}}%</span>
                    </div>
                </div>
                <div class="progress {{URLCommon.isZigezheng?'hide':''}}">
                    <div class="circle">
                        <svg width="61px" height="61px" viewBox="0 0 61 61" version="1.1""
                            xmlns=" http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <defs>
                                <linearGradient x1="13.4871951%" y1="40.4000165%" x2="100%" y2="59.5999835%"
                                    id="linearGradient-1">
                                    <stop stop-color="#FF8174" offset="0%"></stop>
                                    <stop stop-color="#FF4A40" offset="100%"></stop>
                                </linearGradient>
                                <linearGradient x1="40.8717988%" y1="0%" x2="62.5%" y2="100%" id="linearGradient-2">
                                    <stop stop-color="#FFAE74" offset="0%"></stop>
                                    <stop stop-color="#FF8640" offset="100%"></stop>
                                </linearGradient>
                                <linearGradient x1="50%" y1="0%" x2="50%" y2="106.581328%" id="linearGradient-3">
                                    <stop stop-color="#0AD1FF" offset="0%"></stop>
                                    <stop stop-color="#44BCFF" offset="100%"></stop>
                                </linearGradient>
                            </defs>
                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="学习计划" transform="translate(-47.000000, -453.000000)" stroke-width="6">
                                    <g id="编组-3" transform="translate(15.000000, 395.000000)">
                                        <g id="编组-2" transform="translate(21.500000, 61.000000)">
                                            <g id="pgjg_jm" transform="translate(13.500000, 0.000000)">
                                                <circle id="椭圆形" stroke="#E9ECF0" cx="27.5" cy="27.5" r="27.5"></circle>
                                                <circle cx="27.5" cy="27.5" r="27.5"
                                                    stroke="url(#linearGradient-{{self.progressInfo.progress3.level}})"
                                                    stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-dasharray="{{55*Math.PI}},{{55*Math.PI}}"
                                                    stroke-dashoffset="{{55*Math.PI*(1-self.progressInfo.progress3.percent)}}"
                                                    fill="none" />
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                        <div class="text level-{{self.progressInfo.progress3.level}}">
                            {{self.progressInfo.progress3.text}}</div>
                    </div>
                    <div class="name">知识点掌握</div>
                    <div class="tips">掌握进度&nbsp;
                        <span
                            class="level-{{self.progressInfo.progress3.level}}">{{self.progressInfo.progress3.doneCount}}</span>/{{self.progressInfo.progress3.totalCount}}
                    </div>
                </div>
            </div>
        </div>
        <div class="panel">
            <div class="title">为了在约考前完成目标</div>
            <div class="desc">建议您使用{{self.pageTitle}}</div>
            <div class="actions">
                <div class="action" sp-on:click="goPractice" data-type="jingjian">
                    <div class="name">练VIP精简题库</div>
                    <div class="tips">高频考点，答题技巧，省时省力</div>
                </div>
                <div class="action" sp-on:click="goPractice" data-type="exam">
                    <div class="name">真实考场模拟考试</div>
                    <div class="tips">高仿真还原考场考试规则和操作</div>
                </div>
                <div class="action" sp-on:click="goPractice" data-type="mijuan">
                    <div class="name">{{self.mijuanText}}巩固弱点</div>
                    <div class="tips">揭秘高频易错试题，考前再巩固</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部购买按钮 -->
    <sp:if value='{{!state.isVip}}'>
        <div class="footer">
            <div class="button-container">
                <div class="buy-button">
                    <sp:each for='{{state.goodsInfoPool}}'>
                        <div sp-on:click="buttonBuy" data-tabIndex="{{$index}}"
                            data-fragment="{{$index==0?'底部吸底左侧按钮':'底部吸底右侧按钮'}}"
                            class="{{state.goodsInfoPool.length>=2&&$index===0?'goods-button':'goods-button01'}}   {{state.goodsInfoPool.length===1?'goods-button02':''}}">
                            <div class="goods-button-title">{{$value.name}} {{$value.payPrice}}元</div>
                            <div class="goods-button-desc">{{$value.validDays}}天有效期</div>
                            <sp:if
                                value='{{(state.goodsInfoPool.length>=2&&$index===1)||(state.goodsInfoPool.length==1)}}'>
                                <com:PriceTag class="passrate-label" goodsInfo="{{$value}}"
                                    comparePriceMap="{{state.comparePricePool}}" labelMap="{{state.labelPool}}" />
                            </sp:if>

                        </div>
                    </sp:each>
                </div>
                <div class="xieyi">
                    <com:readProtocol theme="kqfd-dialog" protocolText1="购买即表示您同意" />
                    <sp:if value='{{Platform.isIOS}}'>
                        <div class="pay-guide" sp-on:click="gotoPayGuide">支付教程 ></div>
                    </sp:if>
                </div>
            </div>
        </div>
    </sp:if>

    <com:payDialog />
    <com:buyButton />
</div>