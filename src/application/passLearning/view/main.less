body {
    background: transparent !important;
}

.back {
    position: fixed;
    left: 18px;
    top: 56px;
    width: 16px;
    height: 14px;
    background: url(../images/back.png);
    background-size: cover;
}

.page-step1 {
    background: url(../images/<EMAIL>), linear-gradient(180deg, #51c6ff, #c2e9ff 24%, #ffffff);
    background-size: 188px 140px, auto;
    background-position: top right, top left;
    background-repeat: no-repeat;
    position: relative;
    padding: 84px 15px 0;
    height: 100vh;
    display: flex;
    flex-direction: column;

    .panel {
        flex: 1;
        border-radius: 15px;
        background: #fff;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .title {
            margin-top: 40px;
            margin-left: 20px;
            font-size: 29px;
            font-weight: bold;
            color: #333;
            line-height: 41px;
        }

        .desc {
            margin-top: 2px;
            margin-left: 20px;
            font-size: 13px;
            color: #666;
            line-height: 18px;
        }

        .label {
            margin-top: 105px;
            margin-left: 20px;
            color: #333;
            font-size: 16px;
            line-height: 22px;
        }

        .select {
            margin: 20px auto;
            width: 285px;
            background: rgba(4, 165, 255, 0.10);
            border: 1px solid rgba(4, 165, 255, 0.10);
            border-radius: 8px;
            padding: 8px 0;
            text-align: center;

            .date {
                font-size: 17px;
                line-height: 24px;
                color: #333;
            }

            .tips {
                font-size: 11px;
                line-height: 16px;
            }

            .leftover {
                color: #04a5ff;
            }

            .due {
                color: #ff7647;
            }

            .today {
                color: #04a5ff;
            }
        }

        .flex1 {
            flex: 1;
        }

        .btn {
            margin: 0 auto 70px;
            width: 272px;
            height: 55px;
            background: #04a5ff;
            border-radius: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 17px;
            line-height: 24px;
            color: #fff;

            &.disable {
                background: #e8e8e8;
                color: #a0a0a0;
            }
        }
    }
}

.page-step2 {
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(180deg, #e9f8ff 0%, #e5f3ff 38%, #d4eeff 67%, #b3e9fe 95%);

    .title {
        margin-top: 140px;
        font-size: 17px;
        line-height: 24px;
        color: #576787;
        font-weight: bold;
        display: flex;
        
        span {
            display: inline-block;
            max-width: 140px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }

    .subtitle {
        margin-top: 9px;
        font-size: 29px;
        color: #2c3d68;
        line-height: 41px;
        font-weight: bold;
    }

    .anim {
        margin-top: 20px;
        margin-bottom: 55px;
        width: 335px;
        height: 249px;
        background: url(../images/<EMAIL>);
        background-size: cover;
        position: relative;
        overflow: hidden;

        @keyframes scanning {
            from {
                top: -50px;
            }

            to {
                top: 280px;
            }
        }

        .scanner {
            background: url(../images/<EMAIL>);
            background-size: cover;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 315px;
            height: 50px;
            animation: scanning 1.5s linear infinite;
        }
    }

    .progress-tips {
        font-size: 16px;
        line-height: 22px;
        color: #333;
    }

    .progress {
        margin-top: 15px;
        width: 215px;
        background: #f7fbff;
        border-radius: 5px;
        overflow: hidden;

        @keyframes progress-loading {
            from {
                width: 0%;
            }

            40% {
                width: 20%;
            }

            50% {
                width: 25%;
            }

            to {
                width: 100%;
            }
        }

        .inner {
            height: 10px;
            background: #04a5ff;
            border-radius: 5px;
            animation: progress-loading 5s ease-in;
        }
    }
}

.page-step3 {
    background: linear-gradient(180deg, #c4eaff, #eaf8ff);
    height: 100vh;
    display: flex;
    flex-direction: column;

    .header {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        height: 45px;
        padding-top: 30px;
        padding-left: 15px;
        padding-right: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        z-index: 10;
        box-sizing: content-box;
        font-size: 16px;
        text-align: center;

        .back {
            position: static;

            &.hole {
                background: none;
            }
        }

        &.header-white {
            background: white;
        }
    }

    .body-panel {
        flex: 1 0 0;
        overflow-y: auto;
        padding-bottom: 10px;

        .banner {
            background: url(../images/<EMAIL>) no-repeat;
            background-size: cover;
            width: 375px;
            height: 220px;
            margin-bottom: -220px;
        }

        .nickname {
            margin-top: 88px;
            margin-left: 22px;
            font-size: 16px;
            line-height: 22px;
            color: #4c588f;
            display: flex;

            span {
                display: inline-block;
                max-width: 120px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }
        }

        .subtitle {
            margin-top: 1px;
            margin-left: 22px;
            margin-bottom: 41px;
            font-size: 26px;
            line-height: 37px;
            color: #132357;
            font-weight: bolder;
        }
    }

    .panel {
        margin: 10px 15px 0;
        background: #ffffff;
        border-radius: 15px;

        .title {
            padding: 15px 15px 0;
            font-size: 19px;
            line-height: 26px;
            color: #132357;
            font-weight: bold;
        }

        .desc {
            margin-left: 15px;
            font-size: 13px;
            line-height: 18px;
            color: #3d5a85;
        }
    }

    .plans {
        padding: 20px;
        overflow-x: auto;
        white-space: nowrap;

        .plan {
            text-align: center;
            position: relative;
            display: inline-block;
            width: 100px;
            height: 112px;
            background: #fafdff;
            box-shadow: 0 0 0 3px #9af1ff;
            border-radius: 8px 21px 8px 8px;
        }

        .plan+.plan {
            margin-left: 17px;
        }

        .badge {
            position: absolute;
            top: -8px;
            left: 1px;
            width: 44px;
            font-size: 11px;
            line-height: 18px;
            background: #04a5ff;
            color: #ffffff;
            border-radius: 4px;
            text-align: center;

            &:after {
                content: '已达成';
            }

            &.disable {
                background: #8799ae;

                &:after {
                    content: '未达成';
                }
            }
        }

        .name {
            margin-top: 12px;
            font-size: 16px;
            line-height: 22px;
            color: #333;
            font-weight: bold;
        }

        .tips {
            margin-top: 3px;
            font-size: 13px;
            line-height: 18px;
            color: #333;
        }

        .tips2 {
            margin-top: 2px;
            font-size: 11px;
            line-height: 16px;
            color: #333;
        }

        .data {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            line-height: 24px;
            text-align: center;
            font-size: 13px;
            color: #333;
            font-weight: bold;
            background: linear-gradient(270deg, rgba(230, 252, 255, 0.7) 98%, rgba(184, 245, 255, 0.7));
            border-radius: 0 0 8px 8px;

            span {
                color: #04A5FF;
            }
        }
    }

    .progresses {
        display: flex;
        padding-top: 17px;
        padding-bottom: 25px;

        .level-1 {
            color: #FF4A40;
        }

        .level-2 {
            color: #FF7940;
        }

        .level-3 {
            color: #04A5FF
        }

        .progress {
            flex: 1 0 0;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .circle {
            margin-bottom: 7px;
            width: 61px;
            height: 61px;
            position: relative;

            svg {
                transform: rotate(-90deg);
            }

            .text {
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                font-size: 15px;
                font-weight: bold;
                white-space: nowrap;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .name {
            font-size: 14px;
            line-height: 20px;
            color: #132357;
        }

        .tips {
            font-size: 12px;
            line-height: 17px;
            color: #666;
            margin-top: 5px;
        }
    }

    .actions {
        padding: 0 15px 20px;

        .action {
            margin-top: 10px;
            background: linear-gradient(90deg, #f0f7ff 2%, #f7faff);
            border-radius: 8px;
            padding-left: 50px;
            position: relative;
            overflow: hidden;

            .name {
                margin-top: 14px;
                font-weight: bold;
                font-size: 16px;
                line-height: 22px;
                color: #0c0e44;
            }

            .tips {
                margin-top: 5px;
                margin-bottom: 15px;
                font-size: 13px;
                line-height: 18px;
                color: #3d5a85;
            }
        }

        .action:nth-child(1) {
            &:before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 34px;
                height: 74px;
                background: url(../images/<EMAIL>);
                background-size: cover;
            }

            &:after {
                content: '';
                position: absolute;
                right: 0;
                bottom: 0;
                width: 102/2px;
                height: 106/2px;
                background: url(../images/<EMAIL>);
                background-size: cover;
            }
        }

        .action:nth-child(2) {
            &:before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 34px;
                height: 74px;
                background: url(../images/<EMAIL>);
                background-size: cover;
            }

            &:after {
                content: '';
                position: absolute;
                right: 0;
                bottom: 0;
                width: 102/2px;
                height: 106/2px;
                background: url(../images/<EMAIL>);
                background-size: cover;
            }
        }

        .action:nth-child(3) {
            &:before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 34px;
                height: 74px;
                background: url(../images/<EMAIL>);
                background-size: cover;
            }

            &:after {
                content: '';
                position: absolute;
                right: 0;
                bottom: 0;
                width: 102/2px;
                height: 106/2px;
                background: url(../images/<EMAIL>);
                background-size: cover;
            }
        }
    }

    .footer {
        position: relative;
        z-index: 10;

        .button-container {
            padding-top: 10px;
            width: 100%;
            height: 113px;
            background: linear-gradient(180deg, #ffffff, #ffffff);
            border-radius: 10px 10px 0px 0px;
            box-shadow: 0px -3px 7px 0px rgba(208, 192, 192, 0.12);

            .buy-button {
                display: flex;
                align-items: center;
                justify-content: center;
                padding-bottom: 10px;

                .goods-button {
                    width: 160px;
                    height: 50px;
                    background: linear-gradient(113deg,
                            #353b4e 10%,
                            #1d222b 91%);
                    border-radius: 28px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                }

                .goods-button01 {
                    width: 185px;
                    height: 50px;
                    background: linear-gradient(315deg, #ff4a40, #ff7d76);
                    border-radius: 28px;
                    margin-left: 10px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                }

                .goods-button02 {
                    width: 355px;
                    height: 50px;
                    background: linear-gradient(315deg, #ff4a40, #ff7d76);
                    border-radius: 28px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                    margin-left: 0px;
                }

                .button-xuan-fu {
                    position: absolute;
                    top: -18px;
                    right: 0px;
                    width: 100px;
                    height: 22px;
                    background: linear-gradient(180deg, #ffee6d, #ffc806);
                    border-radius: 14px 14px 14px 0px;
                    font-size: 11px;
                    font-family: PingFangSC, PingFangSC-Medium;
                    font-weight: 500;
                    text-align: center;
                    color: #333330;
                    line-height: 22px;
                    text-align: center;
                }

                .goods-button-title {
                    font-size: 14px;
                    font-family: PingFangSC, PingFangSC-Semibold;
                    font-weight: 600;
                    color: #ffffff;
                    line-height: 20px;
                }

                .goods-button-desc {
                    font-size: 10px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    color: rgba(255, 255, 255, 0.8);
                    line-height: 14px;
                }

                .passrate-label {
                    top: -10px !important;

                    .tip {
                        display: inline-block;
                        height: 18px;
                        background: linear-gradient(110deg,
                                #ffd74f 3%,
                                #ffc634 86%);
                        border-radius: 0px 10px 0px 8px;
                        padding: 0px 8px;
                        font-size: 11px;
                        font-family: PingFangSC, PingFangSC-Regular;
                        font-weight: 400;
                        color: #8c2801;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transform: scale(1);
                    }

                    .time-tip {
                        display: inline-block;
                        height: 18px;
                        transform: scale(1);
                        background: linear-gradient(110deg,
                                #ffd74f 3%,
                                #ffc634 86%);
                        border-radius: 0px 10px 0px 8px;
                        padding: 0px 8px;
                        font-size: 11px;
                        color: #8c2801;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
            }

            .xieyi {
                padding-left: 10px;
                display: flex;
                justify-content: space-between;

                .pay-guide {
                    font-size: 12px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    color: #1dacf9;
                    line-height: 17px;
                    margin-right: 17px;
                }

                .coupon-pick {
                    height: 30px;
                    font-size: 12px;
                    line-height: 30px;
                    background: #ffffff;
                    box-sizing: border-box;
                    margin-top: 2px;
                    color: #741b01;
                    text-align: right;
                }
            }
        }
    }
}