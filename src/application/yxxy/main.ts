/*
 * ------------------------------------------------------------------
 * 优秀学员落地页
 * 如果说买了按次购买的就会跳转到购买成功页，如果说买了长期的vip，就会跳转到status.html页
 * ------------------------------------------------------------------
 */

import { ABTestKey, ABTestType, CarType, PayType, setPageName, URLCommon, Version } from ':common/env';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import PayDialog from ':component/payDialog/main';
import PersuadeDialog from ':component/persuadeDialog/main';
import ExpiredDialog from ':component/expiredDialog/main';
import { getGroupSessionInfo, GoodsInfo, GroupKey, TimesList } from ':store/goods';
import View from './view/main.html';
import { iosBuySuccess } from ':common/features/ios_pay';
import { webClose } from ':common/core';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackExit, trackPageLoad, trackPageShow } from ':common/stat';
import BaseVip from ':common/features/baseVip';
import { getCache } from ':common/core';
import { typeCode } from ':common/features/bottom';
import { BUYED_URL, openAuth } from ':common/navigate';
import { getHMT } from ':common/utils';
import { getAbtest, getPermission, StrategyType } from ':store/chores';
import { isHubei } from ':common/features/locate';
import { onPageShow } from ':common/features/page_status_switch';
import { getTabIndex } from ':common/features/cache';
import isNumber from 'lodash/isNumber';
import { pauseAllVideos, scrollTop } from ':common/features/dom';
import jump, { reload, replace } from ':common/features/jump';
interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    prevScrollTop: number,
    isendActivity: boolean,
    timeTips: string,
    strategy: StrategyType,
    isDefaluse: boolean,
    isHubei: boolean,
    checkPermission: any,
}
let timer;
let timer2;
// 标记是否展示过挽留弹窗
// let flag = false;
const pageNameMap = {
    [GroupKey.ChannelKe1]: 'VIP速成版页',
    [GroupKey.ChannelKe4]: 'VIP速成版页',
    [GroupKey.ChannelKe1Ke4Group]: '科1科4组合包页',
    [GroupKey.ChannelKe4Short]: '短时提分页',
    [GroupKey.HcChannelKe1]: 'VIP速成版页',
    [GroupKey.HcChannelKe4]: 'VIP速成版页',
    [GroupKey.HcChannelKe1Ke4Group]: '科1科4组合包页',
    [GroupKey.HcChannelKe4Short]: '短时提分页'
};
const qaKeyMap = {
    [GroupKey.ChannelKe1]: 'qaKey1',
    [GroupKey.ChannelKe4]: 'qaKey4',
    [GroupKey.ExcellentChannelKe1]: 'qaKey1',
    [GroupKey.ExcellentChannelKe4]: 'qaKey4',
    [GroupKey.HcChannelKe1]: 'qaKey1',
    [GroupKey.HcChannelKe4]: 'qaKey4',
    [GroupKey.HcChannelKe1Ke4Group]: 'qaKey1',
    [GroupKey.HcChannelKe4Short]: 'qaKey4'
};
export default class extends BaseVip {
    declare state: State
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog;
        persuadeDialog: PersuadeDialog,
        expiredDialog: ExpiredDialog
    };
    get getTitle() {
        return this.state.tabIndex === 0 ? '真实考场模拟' : this.nowGoodInfo.name;
    }
    get pageName() {
        return this.state.tabIndex === 0 ? '优秀学员真实考场页' : pageNameMap[this.nowGoodInfo.groupKey];
    }

    get qaKey() {
        return qaKeyMap[this.nowGoodInfo.groupKey] || 'qaKey1';
    }
    get nowLabelPool() {
        switch (this.nowGoodInfo.groupKey) {
            case GroupKey.ChannelKe1:
            case GroupKey.ChannelKe4:
                this.state.labelPool[this.nowGoodInfo.groupKey].label = '有效期内不限次数';
                break;
            case GroupKey.ChannelKe1Ke4Group:
            case GroupKey.HcChannelKe1Ke4Group:
                this.state.labelPool[this.nowGoodInfo.groupKey].label = '比分开买更划算';
                break;
            default:
                break;
        }
        return this.state.labelPool;
    }
    get getAbtestKeyByTiku() {
        if (URLCommon.tiku === CarType.TRUCK) {
            return ABTestKey.key15;
        } else if (URLCommon.tiku === CarType.CAR) {
            return ABTestKey.key13;
        }
        return '';
    }
    get headerAbtext() {
        const { strategy } = this.state;
        if (URLCommon.tiku === CarType.TRUCK) {
            return strategy[ABTestKey.key10];
        }
        return strategy[ABTestKey.key9];
    }
    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            goodsInfoPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            // 滚动距离
            prevScrollTop: 0,
            isendActivity: false,
            isDefaluse: false,
            timeTips: '',
            strategy: null,
            isHubei: true,
            checkPermission: {}
        };

    }
    async didMount() {
        const kemu = +URLCommon.kemu;
        const goodsInfoPool: GoodsInfo[] = [];
        // 如果有次数的权限就跳到次数成功的购买页
        if (await this.checkPermission()) {
            replace('./yxxySuccess.html');
            return;
        }
        // ABTestKey.key13,key15默认是c
        const strategy = {
            [this.getAbtestKeyByTiku]: ABTestType.B
        };
        // if (Version.bizVersion > 10) {
        //     const { strategy: strategyAbtest } = await getAbtest(URLCommon.tiku);
        //     strategy = {
        //         ...strategy,
        //         ...strategyAbtest
        //     };
        // }

        if (strategy[this.getAbtestKeyByTiku] === ABTestType.C) {
            this.setState({ tabIndex: 0, isDefaluse: true });
            await this.setTimer();
        }

        const { isendActivity } = this.state;
        switch (URLCommon.tiku) {
            case CarType.CAR:
                switch (strategy[this.getAbtestKeyByTiku]) {
                    case ABTestType.A:
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.ChannelKe1ExamNum3 : GroupKey.ChannelKe4ExamNum3
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.ChannelKe1 : GroupKey.ChannelKe4
                        } as GoodsInfo);
                        break;
                    case ABTestType.B:
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.ChannelKe1Alone : GroupKey.ChannelKe4Alone
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.ChannelKe1 : GroupKey.ChannelKe4
                        } as GoodsInfo);
                        break;
                    case ABTestType.C:
                        if (isendActivity) {
                            goodsInfoPool.push({
                                groupKey: kemu === 1 ? GroupKey.ExcellentChannelKe1 : GroupKey.ExcellentChannelKe4
                            } as GoodsInfo);
                        } else {
                            goodsInfoPool.push({
                                groupKey: kemu === 1 ? GroupKey.ChannelKe1 : GroupKey.ChannelKe4
                            } as GoodsInfo);
                        }
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.ChannelKe1Ke4Group : GroupKey.ChannelKe4Short
                        } as GoodsInfo);
                        break;
                    default:
                        break;
                }
                break;
            case CarType.TRUCK:
                switch (strategy[this.getAbtestKeyByTiku]) {
                    case ABTestType.A:
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.HcChannelKe1ExamNum3 : GroupKey.HcChannelKe4ExamNum3
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.HcChannelKe1 : GroupKey.HcChannelKe4
                        } as GoodsInfo);
                        break;
                    case ABTestType.B:
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.HcChannelKe1Alone : GroupKey.HcChannelKe4Alone
                        } as GoodsInfo);
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.HcChannelKe1 : GroupKey.HcChannelKe4
                        } as GoodsInfo);
                        break;
                    case ABTestType.C:
                        if (isendActivity) {
                            goodsInfoPool.push({
                                groupKey: kemu === 1 ? GroupKey.HcChannelExcellentKe1 : GroupKey.HcChannelExcellentKe4
                            } as GoodsInfo);
                        } else {
                            goodsInfoPool.push({
                                groupKey: kemu === 1 ? GroupKey.HcChannelKe1 : GroupKey.HcChannelKe4
                            } as GoodsInfo);
                        }
                        goodsInfoPool.push({
                            groupKey: kemu === 1 ? GroupKey.HcChannelKe1Ke4Group : GroupKey.HcChannelKe4Short
                        } as GoodsInfo);
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        this.setState({ strategy, goodsInfoPool });

        await this.getGoodInfo();
        // 先展示页面，再去请求无关的信息
        setTimeout(async () => {
            await this.getLabel();
            // await this.getComparePrice();

            this.setPageInfo();
        }, 60);

        setPageName(this.pageName);
        // 页面进出时长打点
        trackPageLoad();

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                // eslint-disable-next-line max-len
                if (TimesList.includes(this.nowGoodInfo.groupKey)) {
                    replace('./yxxySuccess.html');
                } else {
                    iosBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
                }

            }
        });
        // 判断是否是湖北
        isHubei().then(isHubei => {
            this.setState({
                isHubei
            });
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: this.nowGoodInfo.groupKey
        });

    }
    async checkPermission() {
        let permisstionKey;
        switch (URLCommon.tiku) {
            case CarType.CAR:
                permisstionKey = URLCommon.kemu === 1 ? 'kcmnnumK1' : 'kcmnnumK4';
                break;
            case CarType.TRUCK:
                permisstionKey = URLCommon.kemu === 1 ? 'kcmnnumhcK1' : 'kcmnnumhcK4';
                break;
            default:
                break;
        }
        const { hasPromission } = await getPermission(permisstionKey);
        if (hasPromission) {
            return true;
        }
        return false;
    }
    async setTimer() {
        const kemu = URLCommon.kemu;
        const tiku = URLCommon.tiku;
        // 要区分车型和科目
        const data = await getCache('jkExcellentUserDiscountTime_' + tiku + '_kemu' + kemu);
        const { endTime = 0 } = JSON.parse(data || '{}');
        clearInterval(timer2);
        let leftTime = +endTime - new Date().getTime();
        if (leftTime > 0) {
            this.setState({
                isendActivity: true
            });
            timer2 = window.setInterval(() => {
                if (leftTime > 0) {
                    this.setState({
                        timeTips: getHMT(leftTime)
                    });
                    leftTime -= 1000;
                } else {
                    reload();
                }
            }, 1000);
        }
    }
    setPageInfo() {
        this.setBuyBottom();
    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        const goodsListInfo = await getGroupSessionInfo({ groupKeys });

        goodsListInfo.forEach(async (goodInfo, index) => {

            // 如果第一个商品过期就弹出过期弹窗
            if (index === 0 && goodInfo.expired && this.state.isDefaluse) {
                this.children.expiredDialog.show({ time: goodInfo.expiredTime });
            }

            // 如果非次数的商品已购买就跳走
            if ((index === 1 && goodInfo.bought && !this.state.isDefaluse) || (index === 0 && goodInfo.bought && this.state.isDefaluse)) {
                jump.replace(BUYED_URL);
                return;
            }

            if (TimesList.includes(goodInfo.groupKey) || !goodInfo.bought) {
                newGoodsPool.push(goodInfo);
            }
        });

        // 如果当前的goodInfo不存在就跳转到第一个
        if (newGoodsPool.length <= tabIndex) {
            tabIndex = 0;
        }

        this.setState({
            tabIndex,
            goodsInfoPool: newGoodsPool
        });

        this.setPageInfo();

    }
    setBuyBottom(config?: { stat: any }) {
        const { tabIndex, goodsInfoPool, isendActivity } = this.state;
        const nowGoodInfo: GoodsInfo = goodsInfoPool[tabIndex];
        const fragmentName1 = '底部吸底按钮';
        let discount;
        let bottomType: typeCode = typeCode.type4;

        // 有活动时间并且在左边商品要展示优惠的价格
        if (tabIndex === 0 && isendActivity) {
            bottomType = typeCode.type5;
            discount = ((+nowGoodInfo.originalPrice * 100) - (+this.showPrice * 100)) / 100;
        }
        switch (bottomType) {
            case typeCode.type4:
                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '¥ ' + this.showPrice + ' 确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    fragmentName1,
                    ...config?.stat
                });
                break;
            case typeCode.type5:

                this.children.buyButton.setButtonConfig({
                    type: bottomType,
                    groupKey: nowGoodInfo.groupKey,
                    title: '确认协议并支付',
                    subtitle: '有效期' + nowGoodInfo.validDays + '天',
                    validDays: nowGoodInfo.validDays,
                    discount: `已立减${discount}元`,
                    price: this.showPrice,
                    originalPrice: '日常价￥' + nowGoodInfo.originalPrice,
                    fragmentName1,
                    ...config?.stat
                });
                break;
            default:
                break;
        }
    }
    goAuth = async (id) => {
        const { goodsInfoPool } = this.state;
        openAuth({
            groupKeys: goodsInfoPool.map(item => item.groupKey).join(','),
            groupKey: this.nowGoodInfo.groupKey,
            authId: id
        });
        await new Promise<void>(resolve => {
            onPageShow(resolve);
        });

        let tabIndex = await getTabIndex();

        tabIndex = isNumber(tabIndex) ? tabIndex : this.state.tabIndex;

        this.tabChangeCall(tabIndex);
    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        // 退出当前tab的打点
        this.leavePageCall();

        // 回到滚动的顶部
        scrollTop(document.querySelector('.page-yxxy .body-panel'));

        pauseAllVideos();
        this.setState({
            tabIndex
        }, () => {
            setPageName(this.pageName);
            // 页面进出时长打点
            trackPageShow();
            this.setPageInfo();

        });

    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            this.setState({
                prevScrollTop
            });
        }, 100);
    }
    payBtnCall = (e) => {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        this.onPayBtnCall({
            stat: {
                fragmentName1
            }
        });
    }
    onPay = async (config: { stat: PayStatProps }) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo?.couponCode,
            ...config.stat
        }, false).then(() => {
            console.log(this.nowGoodInfo.groupKey);
            // eslint-disable-next-line max-len
            if (TimesList.includes(this.nowGoodInfo.groupKey)) {
                replace('./yxxySuccess.html');
            } else {
                newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey });
            }
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(config.stat);
                },
                ...config.stat
            });
        });
    }
    pay = async (stat: PayStatProps) => {
        this.onPay({ stat });
    }
    // 退出页面的回调
    goBackPage() {
        webClose();
    }
    backCall = () => {
        this.goBackPage();
    }
    // 离开当前页面
    leavePageCall = () => {
        // 退出当前页面的打点
        setPageName(this.pageName);
        trackExit();
    }
}
