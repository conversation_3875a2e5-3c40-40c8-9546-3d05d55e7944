<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="K1K4" content=":application/yxxy/component/k1k4/main" />
<import
    name="CarKemu1and4"
    content=":application/car/component/CarKemu1and4/main"
/>
<import name="CarKemu14" content=":application/car/component/CarKemu14/main" />
<import name="CarShort" content=":application/car/component/CarShort/main" />
<import
    name="BusTruckShort"
    content=":application/khche/component/BusTruckShort/main"
/>
<import
    name="BusTruck14"
    content=":application/khche/component/BusTruck14/main"
/>
<import
    name="BusTruck1and4"
    content=":application/khche/component/BusTruck1and4/main"
/>
<div class="page-container page-yxxy">
    <div class="page-header">
        <com:header
            title="{{state.prevScrollTop > 200?self.getTitle: ' '}}"
            theme="black"
            endTheme="black"
            qaKey="{{self.qaKey}}"
            scrollTop="{{state.prevScrollTop}}"
            back="{{self.backCall}}"
        />
    </div>
    <div class="body-panel" sp-on:scroll="pageScroll">
        <div class="{{state.tabIndex === 0?'':'hide'}}">
            <com:K1K4
                isDefaluse="{{state.isDefaluse}}"
                strategy="{{state.strategy}}"
                timeTips="{{state.timeTips}}"
                goodsList="{{self.nowGoodInfo}}"
                originalPrice="{{self.nowGoodInfo.originalPrice}}"
                payPrice="{{self.nowGoodInfo.payPrice}}"
                goAuth="{{self.goAuth}}"
                isendActivity="{{state.isendActivity}}"
                payBtnCall="{{self.payBtnCall}}"
            />
        </div>

        <!-- 小车科目1或4 -->
        <!-- abtest a和b的场景，右边卖这个，所以既要判断tabindex又要判断groupKey -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.ChannelKe1).payPrice || self.getGroupKeyInfo(GroupKey.ChannelKe4).payPrice"
        >
            <div
                class="{{state.tabIndex === 1 && ((self.nowGoodInfo&&self.nowGoodInfo.groupKey) === GroupKey.ChannelKe1 || (self.nowGoodInfo&&self.nowGoodInfo.groupKey) === GroupKey.ChannelKe4)?'':'hide'}}"
            >
                <com:CarKemu14
                    currentIndex="{{state.tabIndex}}"
                    labelPool="{{self.nowLabelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    kemu="{{URLCommon.kemu}}"
                    tiku="{{URLCommon.tiku}}"
                    isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>
        <!-- 货车科目1或4 -->
        <!-- abtest a和b的场景，右边卖这个，所以既要判断tabindex又要判断groupKey -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.HcChannelKe1).payPrice || self.getGroupKeyInfo(GroupKey.HcChannelKe4).payPrice"
        >
            <div
                class="{{state.tabIndex === 1&&(self.nowGoodInfo.groupKey === GroupKey.HcChannelKe1 ||self.nowGoodInfo.groupKey === GroupKey.HcChannelKe4)?'':'hide'}}"
            >
                <com:BusTruck14
                    goodsInfo="{{self.nowGoodInfo}}"
                    headerAbTestType="{{self.headerAbtext}}"
                    labelPool="{{self.nowLabelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>
        <!-- 小车短时提分 -->
        <sp:if value="self.getGroupKeyInfo(GroupKey.ChannelKe4Short).payPrice">
            <div
                class="{{self.nowGoodInfo&&self.nowGoodInfo.groupKey === GroupKey.ChannelKe4Short?'':'hide'}}"
            >
                <com:CarShort
                    payPrice="{{self.showPrice}}"
                    labelPool="{{self.nowLabelPool}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>
        <!-- 货车短时提分 -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.HcChannelKe4Short).payPrice"
        >
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.HcChannelKe4Short?'':'hide'}}"
            >
                <com:BusTruckShort
                    payPrice="{{self.showPrice}}"
                    labelPool="{{self.nowLabelPool}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    isHubei="{{state.isHubei}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>

        <!-- 小车科一科四组合VIP -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.ChannelKe1Ke4Group).payPrice"
        >
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.ChannelKe1Ke4Group?'':'hide'}}"
            >
                <com:CarKemu1and4
                    currentIndex="{{state.tabIndex}}"
                    goodsList="{{state.goodsInfoPool}}"
                    labelPool="{{self.nowLabelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    kemu="{{URLCommon.kemu}}"
                    tiku="{{state.tiku}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>
        <!-- 货车科一科四组合VIP -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.HcChannelKe1Ke4Group).payPrice"
        >
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.HcChannelKe1Ke4Group?'':'hide'}}"
            >
                <com:BusTruck1and4
                    goodsInfo="{{self.nowGoodInfo}}"
                    labelPool="{{self.nowLabelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    payPrice="{{self.showPrice}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    tiku="{{state.tiku}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>
    </div>
    <div class="footer {{state.goodsInfoPool.length > 1?'':'hide'}}">
        <com:bottomTabs
            tabIndex="{{state.tabIndex}}"
            labelPool="{{self.nowLabelPool}}"
            comparePricePool="{{state.comparePricePool}}"
            goodsList="{{state.goodsInfoPool}}"
            tabChange="{{self.tabChangeCall}}"
        />
    </div>

    <com:buyButton />
    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
</div>
