/*
 * main
 *优秀学员考试模拟
 * name: xiaojia
 * date: 16/3/24
 */
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
interface State {
    played: boolean,
    showVideo: boolean
}

interface Props {
    goAuth?(any)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            played: false,
            showVideo: false
        };
        this.props = {

        };

    }
    async didMount() {
        setTimeout(() => {
            this.setState({
                showVideo: true
            }, () => {
                this.event.on('play', 'click', () => {
                    (document.getElementById('video') as HTMLVideoElement)
                        .play();
                    this.setState({
                        played: true

                    });
                });
            });
        }, 500);

    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
    willReceiveProps() {
        return true;
    }
}
