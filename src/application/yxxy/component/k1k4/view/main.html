<import name="style" content="./main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="Count" content=":component/count/main" />
<import name="PriceTag" content=":component/priceTag/main" />

<div class="panel-kemu1kemu4">
    <div
        class="page-head-banner {{props.isDefaluse?'defalut':''}} {{URLCommon.kemu==KemuType.Ke4?'k4-page-banner':''}} ">
    </div>
    <sp:if value="{{props.isendActivity}}">
        <div class="activity-buy-btn" sp-on:click="pay" data-fragment="主图">
            <div class="div1">
                <p class="p1">
                    <span class="sp1"></span>
                </p>
                <p class="p2">
                    <span class="sp3">
                        日常价
                        <i class="i1">&nbsp; ¥ &nbsp;</i>
                        <b class="b1">{{props.originalPrice}}</b>
                    </span>
                    <span class="sp4">
                        限时折后
                        <i class="i2">&nbsp; ¥ &nbsp;</i>
                        <b class="b2">{{props.payPrice || '--'}}</b>
                    </span>
                </p>
            </div>
            <div class="div2">
                <div class="p3">限时折扣</div>
                <div class="p4">{{props.timeTips}}</div>
            </div>
        </div>
    </sp:if>
    <sp:if value="{{!props.isDefaluse&&!props.isCheckPermission}}">
        <sp:if value='{{props.fromRouetrPage==="mnksPage"}}'>
            <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                <div class="buy-btn">
                    ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
                    '--'}}元开通{{props.goodsList.name}}
                    <p class="buy-btn-desc">
                        有效期{{props.goodsList.validDays}}天
                    </p>
                </div>
                <sp:if value='{{props.labelPool[props.groupKey].label}}'>
                    <div class="price-tag-qita">
                        {{props.labelPool[props.groupKey].label}}
                    </div>
                </sp:if>
               
            </div>
            <sp:else />
            <div sp-on:click="pay" class="buy-btn-box" data-fragment="主图">
                <div class="buy-btn">
                    ¥{{props.payPrice || props.payPrice === 0?props.payPrice :
                    '--'}}元开通{{props.goodsList.times}}次真实考场模拟
                    <p class="buy-btn-desc">
                        有效期{{props.goodsList.validDays}}天
                    </p>
                </div>
                <div class="price-tag-qita">
                    仅限使用{{props.goodsList.times}}次
                </div>
                <!-- <com:PriceTag class="noScale" goodsInfo="{{props.goodsInfo}}" comparePriceMap="{{props.comparePricePool}}"
                labelMap="{{props.labelPool}}" /> -->
            </div>
        </sp:if>

    </sp:if>
    <sp:if value="props.isDefaluse">
        <div class="step-box">
            <div class="step step1">
                <img src="http://exam-room.mc-cdn.cn/exam-room/2022/06/15/15/b18359c2c8ca4acc8496e109b4c01d1b.png"
                    alt="" />
            </div>
            <sp:if value="state.showVideo">
                <div class="media">
                    <video id="video" class="" webkit-playsinline="" playsinline="" x-webkit-airplay="allow"
                        x5-playsinline="" controls=""
                        src="{{Texts.TVHOSTMAP.maiche}}/2022/01/21/b8581d76ef9e46e5b72fad88a15d99ae.middle.mp4"
                        preload=""
                        poster="{{URLCommon.kemu==KemuType.Ke4?'http://exam-room.mc-cdn.cn/exam-room/2022/06/15/15/ebbb15fda6e843ef8e55da07b5479b53.png':'http://exam-room.mc-cdn.cn/exam-room/2022/06/15/11/d7c35faeb514454ba22b37adf15abe4f.png'}}"></video>
                    <div class="poster {{URLCommon.kemu==KemuType.Ke4?'poster-k4':''}} {{state.played ? 'hide': ''}}">
                        <div class="play-btn" ref="play"></div>
                    </div>
                </div>
            </sp:if>

            <div class="step step3">
                <sp:if value='{{URLCommon.tiku===CarType.TRUCK}}'>
                    <img
                        src="http://exam-room.mc-cdn.cn/exam-room/2022/07/28/10/9fd30cb964274998bfbf631224fa37d6.png" />
                    <sp:else />
                    <img
                        src="{{URLCommon.kemu==KemuType.Ke4?'http://exam-room.mc-cdn.cn/exam-room/2022/06/15/16/ba1a42f374f740c58608f958d0128b4d.png':'http://exam-room.mc-cdn.cn/exam-room/2022/06/15/13/42e0f267a9424868912dec9bd5e29744.png'}}" />
                </sp:if>

            </div>
            <div class="step step4">
                <img src="http://exam-room.mc-cdn.cn/exam-room/2022/06/15/13/a754fb03e430429b81316580f81e6446.png" />
            </div>
            <div class="step step5" sp-on:click="goAuth" data-uniqkey="bgbc">
                <img src="https://web-resource.mc-cdn.cn/web/vip/ban2.png" />
            </div>
        </div>
        <sp:else />
        <div class="step-box">
            <div class="step step1">
                <img src="http://exam-room.mc-cdn.cn/exam-room/2022/06/15/15/b18359c2c8ca4acc8496e109b4c01d1b.png"
                    alt="" />
            </div>
            <div class="img-dec">
                <img src="{{URLCommon.kemu==KemuType.Ke4?'http://exam-room.mc-cdn.cn/exam-room/2022/06/15/15/ebbb15fda6e843ef8e55da07b5479b53.png':'http://exam-room.mc-cdn.cn/exam-room/2022/06/15/11/d7c35faeb514454ba22b37adf15abe4f.png'}}"
                    alt="">
            </div>
        </div>
    </sp:if>
    <sp:if value="props.isDefaluse">
        <com:commonQuestion type="1" />
        <sp:else />
        <com:commonQuestion type="{{props.fromRouetrPage==='mnksPage'?'15':'11'}}" />
    </sp:if>
</div>