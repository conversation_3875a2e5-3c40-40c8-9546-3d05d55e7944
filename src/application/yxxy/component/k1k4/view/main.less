.panel-kemu1kemu4 {
    background-color: #F2F2F2;

    .page-head-banner {
        padding-top: 95px;
        height: 280px;
        position: relative;
        background-repeat: no-repeat;
        background-size: cover;
        background-color: #000;
        background-image: url(../images/<EMAIL>);

        &.k4-page-banner {
            background-image: url(../images/<EMAIL>)
        }

        &.defalut {
            background-image: url(../images/top-bg.png);

            &.k4-page-banner {
                background-image: url(../images/top-bg-k4.png)
            }
        }
    }

    // 活动按钮
    .activity-buy-btn {
        position: relative;
        z-index: 10;
        height: 70px;
        background: linear-gradient(90deg, #F95B38 0%, #E83E30 100%);
        display: flex;
        padding: 5px 10px;
        box-sizing: border-box;
        margin-bottom: 20px;
        margin-top: -10px;

        .div1 {
            flex: 1;

            .p1 {
                display: flex;
                align-items: center;
                padding-top: 4px;
                padding-left: 4px;
            }

            .sp1 {
                width: 78px;
                height: 19px;
                background: url(../images/1.png) no-repeat;
                background-size: 100% 100%;
            }

            .sp2 {
                font-size: 11px;
                color: #FFFFFF;
                line-height: 16px;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.3) 100%);
                border-radius: 8px;
                padding: 1px 5px 0 5px;
                transform: scale3d(0.9, 0.9, 0.9);
                margin-left: 5px;
            }

            .p2 {
                display: flex;
                align-items: center;
                padding-top: 5px;
                padding-left: 4px;

            }

            .sp3 {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);

                .i1 {
                    font-size: 12px;
                    color: rgba(255, 255, 255, 0.6);
                    transform: scale3d(0.9, 0.9, 0.9);
                }

                .b1 {
                    font-size: 16px;
                    font-weight: bold;
                    color: #ffffff;
                    transform: scale3d(0.9, 0.9, 0.9);
                }
            }

            .sp4 {
                background: linear-gradient(360deg, #FAB78A 0%, #FFDFC3 100%);
                border-radius: 13px;
                font-size: 12px;
                font-weight: bold;
                color: #B4440F;
                line-height: 17px;
                padding: 5px 10px;
                margin-left: 12px;

                .i2 {
                    font-size: 12px;
                    transform: scale3d(0.85, 0.85, 0.85);
                }

                .b2 {
                    font-size: 18px;
                }
            }
        }

        .div2 {
            width: 128px;
            height: 60px;
            background: url(../images/2.png) no-repeat;
            background-size: 100% 100%;
            box-sizing: border-box;
            padding: 10px 0 0 10px;

            .p3 {
                font-size: 15px;
                font-weight: bold;
                color: #6F2117;
                line-height: 21px;
                text-align: center;
            }

            .p4 {
                font-size: 15px;
                font-weight: bold;
                color: #6F2117;
                line-height: 21px;
                text-align: center;
            }

            .count-content {
                text-align: center;

                .count {
                    color: #6F2117;
                    font-size: 15px;
                    font-weight: 600;
                }
            }
        }
    }

    .buy-btn-box {
        position: relative;
        width: 343px;
        height: 49px;
        margin: 30px auto 15px;
        background: url(https://web-resource.mc-cdn.cn/web/vip/btn1.png) no-repeat center center/100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .buy-btn {
            font-size: 19px;
            color: white;
        }

        .buy-btn-desc {
            text-align: center;
            margin-top: 0.02rem;
            font-size: 0.12rem;
            -webkit-transform: scale(0.8);
            transform: scale(0.8);
            color: rgba(255, 255, 255, 0.7)
        }

        .price-tag-qita {
            position: absolute;
            right: 0px;
            top: -16px;
            text-align: center;
            padding: 0px 5px;
            height: 22px;
            line-height: 22px;
            background: linear-gradient(90deg, #FFD878 0%, #FFC400 100%);
            border-radius: 33px 33px 33px 2px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #6F2117;
             text-overflow: -o-ellipsis-lastline;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
        }

        .label {
            position: absolute;
            right: 0px;
            top: -7px;
            background: linear-gradient(360deg, #F9C39F 0%, #FEDEC7 100%);
            border-radius: 0 10px 0 8px;
            font-size: 12px;
            font-weight: 500;
            color: #622604;
            transform: scale3d(0.9, 0.9, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2px 9px;
            font-weight: 500;
        }

    }

    .media {
        width: 100%;
        margin-top: 20px;
        height: 194px;
        position: relative;
        overflow: hidden;

        video {
            width: 100%;
            height: 100%;
            border-radius: 18px;
            object-fit: fill;
        }

        .poster {
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            bottom: 0;
            z-index: 50;
            width: 100%;
            height: 100%;
            background: url(http://exam-room.mc-cdn.cn/exam-room/2022/06/15/11/d7c35faeb514454ba22b37adf15abe4f.png) no-repeat;
            background-size: 100% 100%;

            &.poster-k4 {
                background: url(http://exam-room.mc-cdn.cn/exam-room/2022/06/15/15/ebbb15fda6e843ef8e55da07b5479b53.png) no-repeat;
                background-size: 100% 100%;
            }

            &.poster1 {
                background: none;
                overflow: hidden;
                display: flex;

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .play-btn {
            position: absolute;
            top: 69px;
            left: 145px;
            z-index: 60;
            width: 56px;
            height: 56px;
            border-radius: 56px;
            background: url(../images/icon-bf.png) no-repeat center;
            background-size: 56px 56px;
        }
    }

    .step-box {
        margin-top: 30px;
        padding: 0 15px;

        img {
            display: block;
            width: 100%;
            margin: 0 auto;
        }

        .img-dec{
            margin-top: 10px;
        }
        .step1 {
            padding: 0 14px;
        }

        .step3 {
            margin-top: 37px;
        }

        .step4 {
            padding: 0 7px;
            margin-top: 39px;
        }

        .step5 {
            margin-top: 20px;
            margin-bottom: 10px;
        }
    }
}
