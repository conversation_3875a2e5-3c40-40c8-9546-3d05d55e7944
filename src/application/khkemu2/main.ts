/*
 * ------------------------------------------------------------------
 * 科目二3D购买弹窗
 * ------------------------------------------------------------------
 */

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { ABTestKey, ABTestType, CarType, PayType, setPageName, URLCommon, URLParams } from ':common/env';
import { comparePrice, ComparePriceInfo, getGroupSessionInfo, getSessionExtra, GoodsExtra, GoodsInfo, GroupKey } from ':store/goods';
import { ensureSiriusBound, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { trackGoPay } from ':common/stat';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { iosBuySuccess } from ':common/features/ios_pay';
import { showSetting } from ':common/features/embeded';
import { BUYED_URL } from ':common/navigate';
import { webClose } from ':common/core';
import { Coupons, batchGetBestCoupon, goodsInfoWithCoupon, couponWithHint } from ':common/features/coupon';
import PayTypeCom from ':component/payType/main';
import jump from ':common/features/jump';
import { getAbtest } from ':store/chores';

let fragmentName1 = URLParams.fragmentName1 || '未知片段';
let fragmentName2 = '支付弹窗';
let propsParams;

const groupKey = URLCommon.tiku === CarType.TRUCK ? GroupKey.HcChannelKe2 : GroupKey.KcChannelKe2;

const bundleGroupKey = URLCommon.tiku === CarType.TRUCK ? GroupKey.HcChannelKe2Ke3Group : GroupKey.KcChannelKe2Ke3Group;

interface State {
    tabIndex: number;
    goodsList: GoodsInfo[];
    coupons: Coupons;
    diffPrice: ComparePriceInfo;
    isKemuall: boolean;
    ke2Label: GoodsExtra;
    strategy?: Record<ABTestKey, ABTestType>
}

export default class Ke23D extends Application<State, {
    from?: string,
    fragmentName1?: string,
    fragmentName2?: string
}> {
    declare children: {
        buyButton: BuyButton;
        payType: PayTypeCom;
    };

    $constructor(o) {
        this.$super({
            target: o.target || document.body,
            name: module.id,
            view: View
        });
        this.state = {
            tabIndex: 0,
            goodsList: [],
            coupons: {},
            diffPrice: null,
            isKemuall: false,
            ke2Label: null
        };
        this.props = {};
    }

    get currentGoods() {
        return goodsInfoWithCoupon(this.state.goodsList[this.state.tabIndex], this.currentCoupon);
    }

    get currentCoupon() {
        return couponWithHint(this.state.coupons[this.state.goodsList[this.state.tabIndex]?.groupKey]);
    }

    /** 是否显示在h5页面中，还是单独的弹窗 */
    get isInH5Page() {
        return (this as any).target !== document.body;
    }

    getLabelTip($index: number) {
        if ($index === 0) {
            const title = '科二难点一网打尽';
            return title;
        }
        return '比分开买立省' + this.state.diffPrice.savePrice + '元';
    }

    async didMount() {
        this.windowResize();
        // ke2Exam 页面用到
        fragmentName1 = this.props.fragmentName1 || fragmentName1;
        fragmentName2 = this.props.fragmentName2 || fragmentName2;
        !this.isInH5Page && setPageName(URLParams.fromPage || '项目视频详情页');

        !this.isInH5Page && trackGoPay({
            groupKey,
            fragmentName1,
            payPathType: 0
        });
        // showSetting({
        //     iosH: 380,
        //     androidH: 534
        // });
        showSetting({
            iosH: 300,
            androidH: 430
        });
        this.event.on('close', 'click', () => {
            // 作为落地页的弹窗使用时，不直接关闭
            if (this.isInH5Page) {
                // 科二前置页关闭时需要知道关闭时选择的tab信息
                if (this.props.from !== 'ke2_exam') {
                    this.emit('close', null);
                } else {
                    this.emit('close', this.currentGoods);
                }
            } else {
                webClose();
            }
        });

        ensureSiriusBound({ groupKey, type: PayBoundType.GoLogin });

        this.setPayment();

        await this.fetchGoodsInfo();
        this.fetchCouponInfo();
        this.fetchLabelInfo();

    }

    async fetchGoodsInfo() {
        const groupKeys = [groupKey, bundleGroupKey];

        const [goodsInfo, bundleGoodsInfo] = await getGroupSessionInfo({
            groupKeys
        });

        console.log('URLCommon.tiku', URLCommon.tiku, 'groupKeys----', groupKeys, goodsInfo, bundleGoodsInfo);

        // 主商品已购买，直接跳走 （科二考场前置页面，不跳转已购页面）
        if (goodsInfo.bought && this.props.from !== 'ke2_exam') {
            jump.replace(BUYED_URL);
            return;
        }
        const goodsList = [goodsInfo];
        if ((bundleGoodsInfo && !bundleGoodsInfo.bought)) {
            goodsList.push(bundleGoodsInfo);

            const diffPrice = await comparePrice({ groupKey: bundleGroupKey, upgradeStrategyCode: bundleGoodsInfo.upgradeStrategyCode });

            this.setState({
                isKemuall: false,
                diffPrice
            });
        }

        this.setState({ goodsList });

        this.switchTab(this.state.tabIndex);
        // 作为落地页的弹窗使用时，告诉落地页主商品信息
        this.isInH5Page && this.emit('goodsInfo', goodsInfo);
    }

    async fetchCouponInfo() {
        // 获取优惠券信息
        const coupons = await batchGetBestCoupon(this.state.goodsList);
        this.setState({ coupons });
        // 从科二前置页进来，不希望他再次设置。
        if (this.props.from !== 'ke2_exam') {
            this.setPayment();
        }
    }

    async fetchLabelInfo() {
        // 获取label信息
        const ke2Label = await getSessionExtra({ groupKey });
        this.setState({ ke2Label });
    }

    private switchTab(tabIndex: number) {
        if (tabIndex !== this.state.tabIndex) {
            this.setState({ tabIndex });
            this.setPayment();
        }
    }

    /** 设置支付参数 */
    public setPayment(params?: { propFragmentName1?: string }) {
        // switch 切换tab时需要
        if (this.props.from === 'ke2_exam' && params) {
            propsParams = params;
        }
        this.children.buyButton.setPay({
            androidPay: this.pay.bind(this),
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => iosBuySuccess({ groupKey: this.currentGoods.groupKey }),
            isInDialog: true
        });
        if (this.currentGoods.inActivity) {
            this.children.buyButton.setButtonConfig({
                groupKey: this.currentGoods.groupKey,
                type: 6,
                title: '确认协议并支付',
                subtitle: '有效期' + this.currentGoods.validDays + '天',
                price: this.currentGoods.payPrice,
                validDays: this.currentGoods.validDays,
                originalPrice: `日常价￥${this.currentGoods.inActivity.preDiscountPrice}`,
                discount: `已立减${this.currentGoods.inActivity.discountedPrice}元`,
                fragmentName1: params?.propFragmentName1 || propsParams?.propFragmentName1 || fragmentName1,
                fragmentName2,
                actionName: this.isInH5Page ? '确认支付' : '去支付'
            });
        } else {
            this.children.buyButton.setButtonConfig({
                groupKey: this.currentGoods.groupKey,
                type: 1,
                title: '确认协议并支付',
                price: this.currentGoods.payPrice,
                validDays: this.currentGoods.validDays,
                fragmentName1: params?.propFragmentName1 || propsParams?.propFragmentName1 || fragmentName1,
                fragmentName2,
                actionName: this.isInH5Page ? '确认支付' : '去支付'
            });
        }
    }

    /** 发起支付 */
    public async pay(stat: PayStatProps) {
        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.currentGoods.groupKey,
            sessionIds: this.currentGoods.sessionIds,
            activityType: this.currentGoods.activityType,
            couponCode: this.currentCoupon.code,
            ...stat
        }, false).then(() => newBuySuccess({ groupKey: this.currentGoods.groupKey }, 2));
    }

    onTabClick(e) {
        const idx = +e.refTarget.getAttribute('data-idx');
        this.switchTab(idx);
    }
    windowResize() {
        const reCalc = function () {
            const dimensionWidth = Math.min(document.documentElement.clientWidth, 480);

            const rate = dimensionWidth / Package.build.style.baseWidth;

            const baseFontSize = 100 * rate;
            window.baseFontSize = baseFontSize;

            document.documentElement.style.fontSize = baseFontSize + 'px';
        };
        setTimeout(() => {
            reCalc();
        }, 500);
    }
}
