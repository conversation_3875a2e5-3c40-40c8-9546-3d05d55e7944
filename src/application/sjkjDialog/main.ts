/*
 * ------------------------------------------------------------------
 * 答题技巧
 * ------------------------------------------------------------------
 */

import { setPageName, URLParams } from ':common/env';
import { getAbtest } from ':store/chores';

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
const type = URLParams.type || 1;
const dataMap = {
    1: {
        fragmentName1: '速记口诀',
        title1: '速记口诀',
        subTitle1: '顺口溜巧记题，生动简单易懂'
    },
    2: {
        fragmentName1: '答题技巧',
        title1: '答题技巧',
        subTitle1: '顺口溜巧记题，生动简单易懂'
    },
    3: {
        fragmentName1: '精简题库',
        title1: '精简题库',
        subTitle1: '精选易考题，提炼高频考点'
    },
    4: {
        fragmentName1: '真实考场模拟',
        title1: '真实考场模拟',
        subTitle1: '深度体验正式考试操作，高仿真模拟'
    },
    5: {
        fragmentName1: '板书教学',
        title1: '板书教学',
        subTitle1: '宝典主播在线讲题'
    }
};

export default class extends Application {
    $constructor() {
        const fromPage = URLParams.fromPage || '速记口诀弹窗页';
        const fragmentName1 = URLParams.fragmentName1 || dataMap[type]?.fragmentName1 || '未知片段';

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        setPageName(fromPage);

        this.state = {
            fragmentName1,
            title1: dataMap[type]?.fragmentName1,
            subTitle1: dataMap[type]?.fragmentName1
        };

    }
    async didMount() {
        // const { strategy } = await getAbtest();
        
        // this.setState({
        //     strategy
        // });
    }
}
