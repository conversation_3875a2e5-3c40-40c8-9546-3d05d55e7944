<import name="style" content="./main" module="S" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />

<import name="CarKemu2" content=":application/kemu2/component/CarKemu2/main" />
<import
    name="TruckKemu2"
    content=":application/kemu2/component/TruckKemu2/main"
/>
<import
    name="CarKemuAll"
    content=":application/car/component/CarKemuAll/main"
/>
<import
    name="TruckKemuAll"
    content=":application/khche/component/TruckKemuAll/main"
/>
<import
    name="CarKemu2and3"
    content=":application/kemu2/component/CarKemu2and3/main"
/>

<div class="page-container :page-kemu2">
    <sp:if value="URLParams.pageType === 'dialog'">
        <div class=":close" sp-on:click="backCall"></div>
        <sp:else/>
        <div class=":header">
            <com:header
                title="{{state.prevScrollTop > 200?self.nowGoodInfo.name: ' '}}"
                scrollTop="{{state.prevScrollTop}}"
                theme="black"
                endTheme="black"
                qaKey="{{self.qaKey}}"
                back="{{self.backCall}}"
            />
        </div>
    </sp:if>

    <div class=":main" id="pageScroll" sp-on:scroll="pageScroll">
        <!-- 科目2 -->

        <div class="{{state.tabIndex===0?'':'hide'}}">
            <sp:if value="URLCommon.tiku === CarType.CAR">
                <com:CarKemu2
                    goodsInfo="{{state.goodsInfoPool[0]}}"
                    payPrice="{{self.showPrice}}"
                    payBtnCall="{{self.payBtnCall}}"
                    goAuth="{{self.goAuth}}"
                    labelMap="{{state.labelPool}}"
                    comparePriceMap="{{state.comparePricePool}}"
                />
                <sp:else />
                <com:TruckKemu2
                    goodsInfo="{{state.goodsInfoPool[0]}}"
                    payPrice="{{self.showPrice}}"
                    payBtnCall="{{self.payBtnCall}}"
                    goAuth="{{self.goAuth}}"
                    labelMap="{{state.labelPool}}"
                    comparePriceMap="{{state.comparePricePool}}"
                />
            </sp:if>
        </div>

        <!-- 小车全科 -->
        <sp:if value="self.getGroupKeyInfo(GroupKey.ChannelKemuAll).payPrice">
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.ChannelKemuAll?'':'hide'}}"
            >
                <com:CarKemuAll
                    tiku="{{URLCommon.tiku}}"
                    goodsList="{{state.goodsInfoPool}}"
                    goodsInfo="{{state.goodsInfoPool[1]}}"
                    labelPool="{{state.labelPool}}"
                    comparePricePool="{{state.comparePricePool}}"
                    currentIndex="{{state.tabIndex}}"
                    groupKey="{{self.nowGoodInfo.groupKey}}"
                    upgradeStrategyCode="{{self.nowGoodInfo.upgradeStrategyCode}}"
                    payPrice="{{self.showPrice}}"
                    goAuth="{{self.goAuth}}"
                    payBtnCall="{{self.payBtnCall}}"
                />
            </div>
        </sp:if>

        <!-- 货车全科 -->
        <sp:if value="self.getGroupKeyInfo(GroupKey.HcChannelKemuAll).payPrice">
            <div
                class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.HcChannelKemuAll?'':'hide'}}"
            >
                <com:TruckKemuAll
                    goodsInfo="{{self.getGroupKeyInfo(GroupKey.HcChannelKemuAll)}}"
                    comparePricePool="{{state.comparePricePool}}"
                    labelPool="{{state.labelPool}}"
                    payPrice="{{self.showPrice}}"
                    payBtnCall="{{self.payBtnCall}}"
                    goAuth="{{self.goAuth}}"
                />
            </div>
        </sp:if>

        <!-- 科二科三组合包 -->
        <sp:if
            value="self.getGroupKeyInfo(GroupKey.ChannelKe2Ke3Group).payPrice"
        >
            <div
                class="{{self.nowGoodInfo.groupKey === GroupKey.ChannelKe2Ke3Group?'':'hide'}}"
            >
                <com:CarKemu2and3
                    goodsList="{{state.goodsInfoPool}}"
                    tabIndex="{{state.tabIndex}}"
                    payPrice="{{self.showPrice}}"
                    goodsInfo="{{self.getGroupKeyInfo(GroupKey.ChannelKe2Ke3Group)}}"
                    comparePriceMap="{{state.comparePricePool}}"
                    payBtnCall="{{self.payBtnCall}}"
                    goAuth="{{self.goAuth}}"
                />
            </div>
        </sp:if>
    </div>

    <div class=":footer">
        <div class=" {{state.goodsInfoPool.length>1?'':'hide'}}">
            <com:bottomTabs
                comparePricePool="{{state.comparePricePool}}"
                labelPool="{{state.labelPool}}"
                tabIndex="{{state.tabIndex}}"
                goodsList="{{state.goodsInfoPool}}"
                tabChange="{{self.tabChangeCall}}"
            />
        </div>
        <com:buyButton>
            <div sp:slot="couponEntry">
                <div class="coupon-position-bottom" sp-on:click="goCoupon">
                    {{self.nowCouponInfo.couponCode?'已优惠' +
                    self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
                </div>
            </div>
        </com:buyButton>
    </div>

    <com:persuadeDialog>
        <div sp:slot="text-body">
            <div class=":persuade-content"></div>
        </div>
    </com:persuadeDialog>
    <com:payDialog />
    <com:expiredDialog />
</div>
