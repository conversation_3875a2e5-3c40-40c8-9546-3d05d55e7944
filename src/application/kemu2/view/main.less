.header {
    position: absolute;
    z-index: 1000;
    top: 0;
    left: 0;
    right: 0;
}

.page-kemu2 {
    position: relative;
    .close{
        position: absolute;
        z-index: 10;
        width: 30px;
        height: 30px;
        right: 15px;
        top: 15px;
        background: url(../images/close.png) no-repeat center center/cover;
    }
}

.main {
    flex: 1;
    height: 0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.footer {
    position: relative;
    z-index: 1;
    background: white;
    border-radius: 10px 10px 0px 0px;
}
.persuade-content{
    width: 100%;
    height:154px;
    position:relative;
    &:before{
        content: "";
        position:absolute;
        top: 0;
        left: -15px;
        width: 320px;
        height:100%;
        background: url(../images/13.png) no-repeat;
        background-size:320px 100%;
    }
    &:after {
        content: "";
        position:absolute;
        top: 0;
        left:-25px;
        right: 0;
        bottom: 0;
        width: 339px;
        height: 154px;
        background:url(../images/12.gif) no-repeat;
        background-size: cover;
    }
}