/*
 * ------------------------------------------------------------------
 * 科目二购买页
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GoodsExtra, GoodsInfo, GroupKey } from ':store/goods';

interface State {
    showVideo: boolean
}

interface Props {
    goodsInfo: GoodsInfo;
    payBtnCall: any;
    goAuth: any;
    comparePriceMap: Partial<Record<GroupKey, { diffPrice: string; allPrice: string }>>;
    labelMap: Partial<Record<GroupKey, GoodsExtra>>;
}

export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            showVideo: false
        };
    }

    willReceiveProps() {
        return true;
    }

    didMount() {
        setTimeout(() => {
            this.setState({
                showVideo: true
            });
        }, 500);
        this.event.on('pay-btn', 'click', (e) => {
            this.props.payBtnCall?.(e);
        });
    }
}
