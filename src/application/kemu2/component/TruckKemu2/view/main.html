<import name="style" content="./main" module="S" />
<import name="header" content=":component/header/main" />
<import name="tag" content=":component/tag/main" />
<import name="MyVideo" content=":component/myVideo/main" />
<import name="PriceTag" content=":component/priceTag/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />

<div class=":truck-page-kemu2">
    <div class=":sec1"></div>
    <div class=":wraper">
        <div class=":panel-wrapper">
            <div class=":panel">
                <div class=":panel-title" />
                <div class=":panel-icons">
                    <div class=":panel-icon">
                        <div class=":panel-img :panel-img1" />
                        <div class=":panel-desc">
                            <div>看视频</div>
                            <div>了解考试重点</div>
                        </div>
                    </div>
                    <div class=":panel-arrow"></div>
                    <div class=":panel-icon">
                        <div class=":panel-img :panel-img2" />
                        <div class=":panel-desc">
                            <div>练项目</div>
                            <div>3D模拟记点位</div>
                        </div>
                    </div>
                    <div class=":panel-arrow"></div>
                    <div class=":panel-icon">
                        <div class=":panel-img :panel-img3" />
                        <div class=":panel-desc">
                            <div>考场模拟</div>
                            <div>高仿真实战</div>
                        </div>
                    </div>
                </div>
                <div class=":panel-btn" ref="pay-btn" data-fragment="主图">
                    <span>{{props.payPrice || '--'}}元立即开通</span>
                    <com:PriceTag
                        class="{{S['panel-label']}}"
                        goodsInfo="{{props.goodsInfo}}"
                        comparePriceMap="{{props.comparePriceMap}}"
                        labelMap="{{props.labelMap}}"
                    />
                </div>
            </div>
        </div>

        <div class=":sec4"></div>

        <div class=":sec5">
            <div class=":step :step1"></div>
            <sp:if value="state.showVideo">
                <div class=":media :media1">
                    <com:MyVideo
                        src="{{Texts.TVHOSTMAP.maiche + '/2021/12/06/475374230f544b6eb914aa548f2fcdea.middle.mp4'}}"
                    />
                </div>
                <div class=":step :step2"></div>
                <div class=":media :media2">
                    <com:MyVideo
                        src="{{Texts.TVHOSTMAP.maiche + '/2021/12/06/4994344402404790a0db030903f9e071.middle.mp4'}}"
                    />
                </div>
                <div class=":step :step3"></div>
                <div class=":media :media3">
                    <com:MyVideo
                        src="{{Texts.TVHOSTMAP.maiche + '/2021/12/06/11573e1d79dc4dd2bf686bb39a6e8881.middle.mp4'}}"
                    />
                </div>
            </sp:if>
        </div>
    </div>
    <com:commonQuestion type="9" />
</div>
