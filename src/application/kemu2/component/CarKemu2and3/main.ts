/*
 * ------------------------------------------------------------------
 * 科目二购买页
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GoodsInfo, GroupKey } from ':store/goods';

interface Props {
    goodsList: GoodsInfo[];
    tabIndex: number;
    goodsInfo: GoodsInfo;
    comparePriceMap: Partial<Record<GroupKey, { diffPrice: string; allPrice: string }>>;
    payBtnCall: any;
    goAuth: any;
}

export default class extends Component<unknown, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });
    }

    willReceiveProps() {
        return true;
    }

    didMount() {
        this.event.on('icon', 'click', e => {
            const uniqkey = e.refTarget.getAttribute('data-uniqkey');
            this.props.goAuth?.(uniqkey);
        });
        this.event.on('pay-btn', 'click', e => {
            this.props.payBtnCall?.(e);
        });
        this.event.on('buchangIntro', 'click', (e) => {
            const uniqkey = e.refTarget.getAttribute('data-uniqkey');
            this.props.goAuth?.(uniqkey);
        });
    }
}
