.page {
    background: #f2f2f2;
    padding-bottom: 13px;
}

.banner {
    width: 375px;
    height: 430px;
    background: url("../images/banner.png");
    background-size: cover;
    position: relative;
}

.icon-list {
    position: absolute;
    left: 0;
    right: 0;
    top: 334px;
    display: flex;
    align-items: flex-start;
    justify-content: space-around;
    padding: 0 12px;
}

.icon {
    width: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 13px;
    color: #ffffff;
    line-height: 18px;
    text-align: center;

    div {
        margin-bottom: 5px;
        width: 38px;
        height: 36px;
    }

    &:nth-child(1) div {
        background: url("../images/vip_ke2.png");
        background-size: 100% 100%;
    }

    &:nth-child(2) div {
        background: url("../images/vip_buchang.png");
        background-size: 100% 100%;
    }

    &:nth-child(3) div {
        background: url("../images/vip_ke3.png");
        background-size: 100% 100%;
    }

    &:nth-child(4) div {
        background: url("../images/vip_dgmn.png");
        background-size: 100% 100%;
    }
}

.btn {
    position: relative;
    margin: 40/2px auto 0;
    width: 690/2px;
    height: 178/2px;
    background: url("../images/btn.png");
    background-size: cover;
    text-align: center;
    line-height: 80px;
    font-size: 19px;
    font-family: PingFangSC-Medium, PingFang SC, sans-serif;
    font-weight: 500;
    color: #ffffff;

    .label {
        right: 20px;
        top: 2px;
        position: absolute;
    }
}

.ke2 {
    margin: 0 auto 0;
    width: 690/2px;
    height: 794/2px;
    background: url("../images/ke2.png");
    background-size: cover;
}

.ke3 {
    margin: 80/2px auto 0;
    width: 690/2px;
    height: 824/2px;
    background: url("../images/ke3.png");
    background-size: cover;
}

.buchang {
    margin: 80/2px auto 0;
    width: 690/2px;
    height: 374/2px;
    background: url("../images/buchang.png");
    background-size: cover;
}
