<import name="style" content="./main" module="S" />
<import
    name="pageHeader"
    content=":application/car/component/page-header/main"
/>
<import name="commonQuestion" content=":component/commonQuestion/main" />
<import name="PriceTag" content=":component/priceTag/main" />

<div class=":page">
    <div class=":banner">
        <!-- 有活动图片或者手动设置为透明的时候需要变成透明的 -->
        <div class=":icon-list">
            <div class=":icon" ref="icon" data-uniqkey="k2vip">
                <div></div>
                <span>科二3DVIP</span>
            </div>
            <div class=":icon" ref="icon" data-uniqkey="bgbc">
                <div style></div>
                <span>科二考不过<br />补偿40元</span>
            </div>
            <div class=":icon" ref="icon" data-uniqkey="k3vip">
                <div style></div>
                <span>科三3DVIP</span>
            </div>
            <div class=":icon" ref="icon" data-uniqkey="dgmn">
                <div style></div>
                <span>灯光模拟</span>
            </div>
        </div>
    </div>
    <div class=":btn" ref="pay-btn" data-fragment="主图">
        {{props.payPrice || '--'}}元立即开通
        <com:PriceTag
            class="{{S['label']}}"
            goodsInfo="{{props.goodsInfo}}"
            comparePriceMap="{{props.comparePriceMap}}"
            labelMap="{{props.labelMap}}"
        />
    </div>
    <div class=":ke2"></div>
    <div class=":ke3"></div>
    <div class=":buchang" ref="buchangIntro"></div>

    <com:commonQuestion type="7" />
</div>
