<import name="style" content="./main" module="S" />
<import name="header" content=":component/header/main" />
<import name="tag" content=":component/tag/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="PriceTag" content=":component/priceTag/main" />
<import name="commonQuestion" content=":component/commonQuestion/main" />

<div class=":page">
    <div class=":sec1">
        <p class=":intro" ref="buchangIntro" data-uniqkey="bgbc">
            <span>补偿说明 ></span>
        </p>
    </div>
    <div class=":wraper">
        <div class=":panel-wrapper">
            <div class=":panel">
                <div class=":panel-title" />
                <div class=":panel-icons">
                    <div class=":panel-icon">
                        <div class=":panel-img" />
                        <div class=":panel-desc">
                            <div>看视频</div>
                            <div>了解考试重点</div>
                        </div>
                    </div>
                    <div class=":panel-arrow"></div>
                    <div class=":panel-icon">
                        <div class=":panel-img" />
                        <div class=":panel-desc">
                            <div>练项目</div>
                            <div>3D模拟记点位</div>
                        </div>
                    </div>
                    <div class=":panel-arrow"></div>
                    <div class=":panel-icon">
                        <div class=":panel-img" />
                        <div class=":panel-desc">
                            <div>考场模拟</div>
                            <div>高仿真实战</div>
                        </div>
                    </div>
                </div>
                <div class=":panel-btn" ref="pay-btn" data-fragment="主图">
                    <span>{{props.payPrice || '--'}}元立即开通</span>
                    <com:PriceTag
                        class="{{S['panel-label']}}"
                        goodsInfo="{{props.goodsInfo}}"
                        comparePriceMap="{{props.comparePriceMap}}"
                        labelMap="{{props.labelMap}}"
                    />
                </div>
            </div>
        </div>

        <div class=":sec4"></div>

        <div class=":sec5">
            <div class=":step :step1"></div>
            <sp:if value="state.showVideo">
                <div class=":media">
                    <video
                        id="video1"
                        class=":vhide {{state.vtype == 1 ? S['vshow1']: ''}}"
                        webkit-playsinline=""
                        playsinline=""
                        x-webkit-airplay="allow"
                        x5-playsinline=""
                        src="{{Texts.TVHOSTMAP.maiche}}/2021/07/15/6c99172c23494427991ea3741418a17c.middle.mp4"
                        preload=""
                        poster=""
                    ></video>
                    <video
                        id="video2"
                        class=":vhide {{state.vtype == 2 ? S['vshow2']: ''}}"
                        webkit-playsinline=""
                        playsinline=""
                        x-webkit-airplay="allow"
                        x5-playsinline=""
                        src="{{Texts.TVHOSTMAP.maiche}}/2021/07/15/f068b956f373497ab73f037373eacbcd.middle.mp4"
                        preload=""
                        poster=""
                    ></video>
                    <div class=":poster {{state.playing ? 'hide': ''}}"></div>
                    <div
                        ref="play"
                        class=":play-btn {{state.playing ? 'hide': ''}}"
                    ></div>
                </div>
            </sp:if>
            <div class=":vtype">
                <label
                    ref="vtype"
                    data-type="1"
                    class=":label {{state.vtype == 1 ? S['active'] : ''}}"
                    ><i>手动挡</i></label
                >
                <label
                    ref="vtype"
                    data-type="2"
                    class=":label {{state.vtype == 2 ? S['active'] : ''}}"
                    ><i>自动挡</i></label
                >
            </div>
            <div class=":step :step2"></div>
            <sp:if value="state.showVideo">
                <div class=":gif :gif1"></div>
            </sp:if>
           
            <div class=":step :step3"></div>
            <sp:if value="state.showVideo">
                <div class=":gif :gif2"></div>
            </sp:if>
        </div>

        <div class=":sec6"></div>

        <div class=":sec7">
            <p class=":intro" ref="buchangIntro" data-uniqkey="bgbc">
                <span>查看详细补偿条件和说明 ></span>
            </p>
        </div>
    </div>
    <com:commonQuestion type="9" />
</div>
