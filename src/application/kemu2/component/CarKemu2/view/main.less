.page {
    background: #231d1d;
}

.sec1 {
    height: 276px;
    background: url(../images/ke2/1.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
}

.intro {
    font-size: 12px;
    color: #ffffff;
    line-height: 16px;
    position: absolute;
    bottom: 55px;
    left: 15px;
    display: flex;

    span {
        border: 1px solid #ffffff;
        padding: 6px 8px 4px 8px;
        border-radius: 20px;
    }
}

.wraper {
    padding: 0 15px 0px 15px;
    margin-top: -26px;
    position: relative;
}

.panel-wrapper {
    width: 345px;
    padding: 10px;
    background: linear-gradient(180deg, #fad5b4 0%, #ffeee1 100%);
    box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.35);
    border-radius: 10px;
}

.panel {
    background: #ffffff;
    border-radius: 5px;
    overflow: hidden;

    &-title {
        margin: 4px auto 0;
        width: 284px;
        height: 61px;
        background: url("../images/title.png");
        background-size: cover;
    }

    &-icons {
        margin-top: 15px;
        display: flex;
        justify-content: space-between;
        padding: 0 30px;
    }

    &-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 56px;

        &:nth-of-type(1) .panel-img {
            background: url("../images/icon1.png");
            background-size: cover;
        }

        &:nth-of-type(3) .panel-img {
            background: url("../images/icon2.png");
            background-size: cover;
        }

        &:nth-of-type(5) .panel-img {
            background: url("../images/icon3.png");
            background-size: cover;
        }

    }

    &-arrow {
        margin-top: 20px;
        width: 11px;
        height: 15px;
        background: url("../images/arrow.png");
        background-size: cover;
    }

    &-img {
        width: 56px;
        height: 56px;
    }



    &-desc {
        text-align: center;
        white-space: nowrap;
        margin-top: 8px;
        font-size: 13px;
        font-family: PingFangSC-Medium, PingFang SC, sans-serif;
        font-weight: 500;
        color: #333333;
        line-height: 18px;

        div:first-child {
            color: rgba(243, 82, 53, 1);
        }
    }

    &-btn {
        position: relative;
        margin: 24px auto 31px;
        width: 202px;
        height: 49px;
        background: url("../images/btn.gif");
        background-size: cover;
        font-size: 19px;
        font-family: PingFangSC-Medium, PingFang SC, sans-serif;
        font-weight: 500;
        color: #ffffff;
        line-height: 49px;
        text-align: center;
    }

    &-label {
        position: absolute;
        right: -39px;
        top: -10px;
    }
}

.sec2 {
    background: linear-gradient(180deg, #fad5b4 0%, #ffeee1 100%);
    box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.35);
    border-radius: 10px;
    padding: 10px;
    position: relative;

    .box {
        background: #ffffff;
        padding: 5px 0 20px 0;

        .p1 {
            width: 284px;
            height: 59px;
            background: url(../images/ke2/2.png) no-repeat;
            background-size: 100% 100%;
            margin: 5px auto 0 auto;
        }

        .p2 {
            width: 295px;
            height: 38px;
            background: url(../images/ke2/3.png) no-repeat;
            background-size: 100% 100%;
            margin: 5px auto 0 auto;
        }

        .p3 {
            width: 295px;
            height: 38px;
            background: url(../images/ke2/4.png) no-repeat;
            background-size: 100% 100%;
            margin: 0 auto;
        }

        .p4 {
            width: 295px;
            height: 38px;
            background: url(../images/ke2/5.png) no-repeat;
            background-size: 100% 100%;
            margin: 0 auto;
        }

        .p5 {
            width: 305px;
            height: 49px;
            background: url(../images/ke2/6.png) no-repeat;
            background-size: 100% 100%;
            margin: 30px auto 0 auto;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 19px;
            font-weight: 500;
            color: #ffffff;
            line-height: 26px;
        }

        .arrow {
            width: 15px;
            height: 11px;
            background: url(../images/ke2/16.png) no-repeat;
            background-size: 100% 100%;
            margin: 7px auto;
        }
    }
}

.sec3 {
    margin-top: 30px;
}

.sec4 {
    width: 310px;
    height: 56px;
    background: url(../images/ke2/8.png) no-repeat;
    background-size: 100% 100%;
    margin: 30px auto 0 auto;
}

.sec5 {
    width: 345px;
    padding-top: 20px;
    padding-bottom: 25px;
    margin-top: 20px;
    background: #ffffff;
    box-shadow: 0px 4px 15px -1px rgba(236, 159, 102, 0.3);
    border-radius: 10px;

    .step {
        width: 100%;
        height: 67px;
    }

    .step1 {
        background: url(../images/ke2/12.png) no-repeat;
        background-size: 100% 100%;
    }

    .step2 {
        background: url(../images/ke2/13.png) no-repeat;
        background-size: 100% 100%;
    }

    .step3 {
        background: url(../images/ke2/14.png) no-repeat;
        background-size: 100% 100%;
    }

    .media {
        width: 305px;
        height: 177px;
        border-radius: 7px;
        margin: 20px auto 0 auto;
        position: relative;
        overflow: hidden;
        background: #ffffff;

        video {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }

        .poster {
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: url(../images/kemu2/ke2_zhibo.png) no-repeat;
            background-size: 100% 100%;
        }

        .play-btn {
            position: absolute;
            top: 59px;
            left: 126px;
            width: 52px;
            height: 52px;
            border-radius: 52px;
            background: url(../images/kemu2/10.png) no-repeat center center;
            background-size: 52px 52px;
        }
    }

    .vtype {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 15px 15px 25px 15px;

        .label {
            width: 134px;
            height: 34px;
            border-radius: 4px;
            border: 1px solid #c58256;
            display: flex;
            align-items: center;
            justify-content: center;

            i {
                font-size: 15px;
                color: #c27c4d;
                line-height: 33px;
            }

            &.active {
                background: linear-gradient(266deg, #fc845e 0%, #f84d30 100%);

                i {
                    color: #ffffff;
                }
            }
        }
    }

    .vhide {
        visibility: hidden;
        transform: translate3d(0, 100%, 0);
    }

    .vshow1 {
        visibility: visible;
        transform: translate3d(0, 0, 0);
    }

    .vshow2 {
        visibility: visible;
        transform: translate3d(0, -100%, 0);
    }

    .gif {
        width: 305px;
        height: 172.5px;
        border-radius: 7px;
    }

    .gif1 {
        background: url(../images/kemu2/ke2_shijing.gif) no-repeat;
        background-size: 100% 100%;
        margin: 25px auto;
    }

    .gif2 {
        background: url(../images/kemu2/ke2_zskc.gif) no-repeat;
        background-size: 100% 100%;
        margin: 25px auto 0 auto;
    }
}

.sec6 {
    width: 268px;
    height: 56px;
    background: url(../images/ke2/9.png) no-repeat;
    background-size: 100% 100%;
    margin: 30px auto 0 auto;
}

.sec7 {
    width: 345px;
    height: 107px;
    background: url(../images/ke2/10.png) no-repeat;
    background-size: 100% 100%;
    margin: 20px auto 0 auto;
    position: relative;

    .intro {
        position: absolute;
        left: 15px;
        bottom: 10px;
        font-size: 10px;
        color: #96490e;
        line-height: 14px;
        display: flex;
        background-color: #fdece1;
        border-radius: 20px;
        transform: scale3d(0.9, 0.9, 1);
        transform-origin: left;

        span {
            border: 1px solid #ffffff;
            padding: 6px 8px 4px 8px;
            border-radius: 20px;
        }
    }
}
