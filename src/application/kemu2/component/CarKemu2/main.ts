/*
 * ------------------------------------------------------------------
 * 科目二购买页
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { GoodsExtra, GoodsInfo, GroupKey } from ':store/goods';

interface State {
    playing: boolean;
    showVideo: boolean
    vtype: 1 | 2;
}

interface Props {
    goodsInfo: GoodsInfo;
    payBtnCall: any;
    goAuth: any;
    comparePriceMap: Partial<Record<GroupKey, { diffPrice: string; allPrice: string }>>;
    labelMap: Partial<Record<GroupKey, GoodsExtra>>;
}

export default class extends Component<State, Props> {

    $constructor() {
        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            playing: false,
            showVideo: false,
            vtype: 1
        };
    }

    willReceiveProps() {
        return true;
    }

    didMount() {
        setTimeout(() => {
            this.setState({
                showVideo: true
            }, () => {
                const video1 = (document.getElementById('video1') as HTMLVideoElement);
                const video2 = (document.getElementById('video2') as HTMLVideoElement);

                video1.addEventListener('pause', () => {
                    this.setState({
                        playing: false
                    });
                });
                video1.addEventListener('play', () => {
                    this.setState({
                        playing: true
                    });
                });

                this.event.on('pay-btn', 'click', (e) => {
                    this.props.payBtnCall?.(e);
                });
                this.event.on('play', 'click', () => {
                    video1.pause();
                    video2.pause();

                    (document.getElementById('video' + this.state.vtype) as HTMLVideoElement).play();
                    this.setState({
                        playing: true
                    });
                });
                this.event.on('vtype', 'click', (e) => {
                    const type = +e.refTarget.getAttribute('data-type') as 1 | 2;

                    video1.pause();
                    video2.pause();
                    this.setState({
                        vtype: type,
                        playing: false
                    });
                });
                this.event.on('buchangIntro', 'click', (e) => {
                    const uniqkey = e.refTarget.getAttribute('data-uniqkey');
                    this.props.goAuth?.(uniqkey);
                });
            });
        }, 500);
    }
}
