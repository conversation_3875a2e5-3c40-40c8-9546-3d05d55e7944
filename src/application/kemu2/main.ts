import { webClose } from ':common/core';
import { CarType, PayType, persuadeDialogAllow, Platform, setPageName, URLCommon, URLParams } from ':common/env';
import BaseVip from ':common/features/baseVip';
import { pauseAllVideos, scrollTop } from ':common/features/dom';
import { showSetting } from ':common/features/embeded';
import { iosBuySuccess, iosDialogBuySuccess } from ':common/features/ios_pay';
import jump from ':common/features/jump';
import { ensureSiriusBound, getDefaultPayType, newBuySuccess, PayBoundType, startSiriusPay } from ':common/features/pay';
import { onWebBack } from ':common/features/persuade';
import { BUYED_URL } from ':common/navigate';
import { trackExit, trackPageLoad, trackPageShow } from ':common/stat';
import { PayStatProps } from ':component/buyButton/main';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import View from './view/main.html';

const pageNameMap = {
    [GroupKey.ChannelKe2]: '科二速成VIP购买页',
    [GroupKey.HcChannelKe2]: '科二速成VIP购买页',
    [GroupKey.ChannelKe2Ke3Group]: '科二科三3DVIP页',
    [GroupKey.ChannelKemuAll]: '全科VIP页',
    [GroupKey.HcChannelKemuAll]: '全科VIP页'
};

interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[],
    couponPool: object,
    labelPool: object,
    comparePricePool: object,
    prevScrollTop: number
}
let timer;
// 标记是否展示过挽留弹窗
let flag = false;

export default class extends BaseVip {
    declare state: State
    get pageName() {
        const { tabIndex } = this.state;

        return tabIndex === 0 ? '科二速成VIP购买页' : '科二科三3DVIP页';
    }

    get qaKey() {
        return this.nowGoodInfo.groupKey === GroupKey.ChannelKemuAll || this.nowGoodInfo.groupKey === GroupKey.HcChannelKemuAll ? 'qaKey6' : 'qaKey1';
    }

    $constructor() {

        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 0,
            goodsInfoPool: [],
            couponPool: {},
            labelPool: {},
            comparePricePool: {},
            // 滚动距离
            prevScrollTop: 0
        };
    }
    async didMount() {
        const goodsInfoPool: GoodsInfo[] = [];

        this.windowResize();
        this.appEventProxy();
        switch (URLCommon.tiku) {
            case CarType.CAR:
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe2
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKe2Ke3Group
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.ChannelKemuAll
                } as GoodsInfo);
                break;
            case CarType.TRUCK:
                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKe2
                } as GoodsInfo);
                goodsInfoPool.push({
                    groupKey: GroupKey.HcChannelKemuAll
                } as GoodsInfo);
                break;
            default:
                break;
        }
        this.state.goodsInfoPool = goodsInfoPool;

        await this.getGoodInfo();

        setPageName(this.pageName);
        // 页面进出时长打点
        trackPageLoad();

        // 注册底部支付方法
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                iosDialogBuySuccess({ groupKey: this.nowGoodInfo.groupKey });
            }
        });

        // 未登录购买就去登录
        ensureSiriusBound({
            type: PayBoundType.GoStatusPage,
            groupKey: this.nowGoodInfo.groupKey
        });

    }
    appEventProxy() {

        if (URLParams.pageType === 'dialog') {
            showSetting({ androidH: 629, iosH: 483 });
        }
        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }
    async getGoodInfo() {
        let { tabIndex } = this.state;
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        console.log('000', performance.now());
        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {
            goodsListInfo.forEach((goodInfo, index) => {
                // 如果第一个商品过期就弹出过期弹窗
                if (index === 0 && goodInfo.expired) {
                    this.children.expiredDialog.show({ time: goodInfo.expiredTime });
                }
                // 如果第一个商品已购买就跳走
                if (index === 0 && goodInfo.bought) {
                    jump.replace(BUYED_URL);
                    return;
                }

                // 商品未购买才push
                if ((goodInfo.groupKey === GroupKey.ChannelKemuAll || goodInfo.groupKey === GroupKey.HcChannelKemuAll)) {
                    if (goodInfo.upgrade) {
                        if (newGoodsPool.length > 1) {
                            newGoodsPool.pop();
                        }
                        newGoodsPool.push(goodInfo);
                    }
                } else if (!goodInfo.bought) {
                    newGoodsPool.push(goodInfo);
                }
            });

            // 如果当前的goodInfo不存在就跳转到第一个
            if (newGoodsPool.length <= tabIndex) {
                tabIndex = 0;
            }

            console.log('111', performance.now());
            this.setState({
                tabIndex,
                goodsInfoPool: newGoodsPool
            }, function() {
                console.log('222', performance.now());
            });
            this.setPageInfo();
            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getCoupon();
                await this.getLabel();
                await this.getComparePrice();

                this.setPageInfo();
            }, 60);
        });

    }
    tabChangeCall = (tabIndex) => {
        if (tabIndex === this.state.tabIndex) {
            return;
        }
        // 退出当前tab的打点
        this.leavePageCall();

        // 回到滚动的顶部
        scrollTop(document.querySelector('#pageScroll'));

        // 暂停所有视频
        pauseAllVideos();

        this.setState({
            tabIndex
        }, () => {
            setPageName(this.pageName);

            trackPageShow();

            this.setPageInfo();

        });

    }
    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;
            this.setState({
                prevScrollTop
            });
        }, 300);
    }
    payBtnCall = (e) => {
        const fragmentName1 = e.refTarget.getAttribute('data-fragment');
        this.onPayBtnCall({
            stat: {
                fragmentName1
            }
        });
    }
    pay = async (stat: PayStatProps) => {
        this.onPay({
            stat: {
                ...stat
            }
        });
    }
    onPay = async (config: { stat: PayStatProps }) => {
        const { tabIndex, goodsInfoPool } = this.state;

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo?.couponCode,
            ...config.stat
        }, false).then(() => {
            newBuySuccess({ groupKey: goodsInfoPool[tabIndex].groupKey }, 2);
        }).catch(async () => {
            // console.log('支付错误', err);
            this.children.payDialog.show({
                groupKey: goodsInfoPool[tabIndex].groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(config.stat);
                },
                ...config.stat
            });
        });
    }
    // 退出页面的回调
    goBackPage() {
        const { tabIndex, goodsInfoPool, labelPool } = this.state;
        const nowGoodInfo = goodsInfoPool[tabIndex];

        if (persuadeDialogAllow && URLParams.pageType !== 'dialog' && !flag && Platform.isAndroid) {
            flag = true;
            this.children.persuadeDialog.show({
                goodsInfo: nowGoodInfo,
                groupKey: nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                title: `真的要放弃${this.nowGoodInfo.name}吗？`,
                txt1: '倒车入库看点位\n太重要了',
                txt2: '3D模拟挺有意思\n好用',
                txt3: '居然可以看到\n本地的考场',
                txt4: URLCommon.tiku === 'truck' ? '朋友推荐的说好用' : '第一次没考过\n真的补偿了40块钱',
                tag: {
                    text: labelPool[this.nowGoodInfo.groupKey]?.label
                }
            }).then(payType => {
                if (payType === false) {
                    this.leavePageCall();
                    webClose();
                }
                if (payType) {
                    this.pay({ fragmentName1: '挽留弹窗' });
                }
            });
        } else {
            webClose();
        }
    }
    backCall = () => {
        this.goBackPage();
    }
    // 离开当前页面
    leavePageCall = () => {
        // 退出当前页面的打点
        setPageName(pageNameMap[this.nowGoodInfo.groupKey]);
        trackExit();
    }
    windowResize() {
        const reCalc = function () {
            const dimensionWidth = Math.min(document.documentElement.clientWidth, 480);

            const rate = dimensionWidth / Package.build.style.baseWidth;

            const baseFontSize = 100 * rate;
            window.baseFontSize = baseFontSize;

            document.documentElement.style.fontSize = baseFontSize + 'px';
        };
        setTimeout(() => {
            reCalc();
        }, 500);
    }
}
