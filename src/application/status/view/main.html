<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="addBuy" content="../component/addBuy/main" />

<div class="page-container page-status">
    <div
        class="page-header {{(state.login && state.hasRecommend)?'':'nologin'}}"
    >
        <sp:if value="state.login && state.hasRecommend">
            <com:header title=" " theme="black" endTheme="black"> </com:header>
            <sp:else/>
            <com:header title=" " > </com:header>
        </sp:if>
        <div class="content">
            <div class="success-box">
                <span class="success-logo"></span>
                <span class="success-txt">开通成功</span>
            </div>

            <sp:if value="state.login">
                <div class="dec">
                    您的【{{state.boughtGood.name}}】已经开通成功，请尽情享用特权吧。
                </div>
                <sp:if value="URLParams.goUse">
                    <div sp-on:click="onGoUse" class="btn">{{URLParams.goUseTxt || '去使用'}}</div>
                    <sp:else />
                    <div sp-on:click="onViewVip" class="btn">查看我的VIP</div>
                </sp:if>
                <sp:else />
                <div class="dec">
                    您的【{{state.boughtGood.name}}】已经开通成功，您可以登录账号以便在其他设备享用权益
                </div>

                <div sp-on:click="onLogin" class="btn">去登录</div>
            </sp:if>
        </div>
    </div>
    <sp:if value="state.time">
        <div class="time-loading">
            <div class="time">{{state.time}}</div>
            <div class="dec">确认支付状态中，请勿点击或退出APP</div>
        </div>
    </sp:if>

    <!-- 有推荐或者升级才展示 -->
    <sp:if
        value="state.hasRecommend"
    >
        <com:addBuy buyedGood="{{state.boughtGood}}" />
    </sp:if>
</div>
