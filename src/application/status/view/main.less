.page-status {
    background-color: #fff;

    .page-header {
        height: 199px;
        background: url(../images/jiakao_pic_vip_bj.png) no-repeat center center/cover;
        position: relative;

        &.nologin {
            background: #fff;
            height: 250px;

            .content {
                color: #692204;

                .success-box{
                     .success-logo {
                         background: url(../images/vip_ygm_kt.png) no-repeat center center/cover;
                     }
                }
            }
        }

        .content {
            position: absolute;
            left: 50%;
            bottom: 20px;
            transform: translateX(-50%);
            text-align: center;
            color: white;

            .success-box {
                display: flex;
                justify-content: center;
                align-items: center;

                .success-logo {
                    width: 20px;
                    height: 20px;
                    background: url(../images/vip_ygm_kt_1.png) no-repeat center center/cover;
                }

                .success-txt {
                    margin-left: 3px;
                    font-size: 19px;
                    font-weight: bold;
                }
            }

            .dec {
                margin-top: 10px;
                font-size: 13px;
                width: 280px;
                line-height: 18px;
            }

            .btn {
                display: inline-block;
                width: fit-content;
                padding: 0 33px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 15px auto 0;
                font-size: 17px;
                background: linear-gradient(101deg, #f4cba8 9%, #e8b186 90%);
                border-radius: 20px;
                color: #692204;
            }
        }
    }

    .time-loading {
        position: fixed;
        z-index: 101;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: white;
        display: flex;
        align-items: center;
        flex-direction: column;

        .time {
            margin-top: 150px;
            width: 80px;
            height: 80px;
            border: 3px solid #999;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 32px;
        }

        .dec {
            font-size: 16px;
            margin-top: 20px;
            color: #666;

        }
    }
}
