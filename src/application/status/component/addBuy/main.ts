/*
 * ------------------------------------------------------------------
 * 小车科一科四
 * ------------------------------------------------------------------
 */

import { PayType, Platform } from ':common/env';
import { getGroupSessionInfo, getSessionExtra, GoodsInfo, GroupComparePrice, GroupKey } from ':store/goods';
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import PayDialog from ':component/payDialog/main';
import BuyButton, { PayStatProps } from ':component/buyButton/main';
import { getDefaultPayType, PayBoundType, startSiriusPay } from ':common/features/pay';
import { iosPay } from ':common/features/ios_pay';
import { trackDialogShow, trackGoPay } from ':common/stat';
import { replace } from ':common/features/jump';
import { STATUS_URL } from ':common/navigate';
import { checkReaded } from ':common/features/agreement';
import { Coupon, goodsInfoWithCoupon } from ':common/features/coupon';
import { saveCache } from ':common/core';
interface State {
    tabIndex: number,
    goodsInfoPool: GoodsInfo[]
    comparePricePool: any
    couponPool: any
    labelPool: any
}

interface Props {
    buyedGood: GoodsInfo
}

const fragmentName1 = '推荐加购';

export default class extends Component<State, Props> {
    declare children: {
        buyButton: BuyButton;
        payDialog: PayDialog
    };
    get nowGoodInfo() {
        const { tabIndex, goodsInfoPool } = this.state;
        return goodsInfoPool[tabIndex];
    }
    get nowCouponInfo() {
        const { couponPool } = this.state;
        return couponPool[this.nowGoodInfo.groupKey];
    }
    /**
     * 如果有优惠券的价格为0的就显示0
     * 如果没有优惠券的价格为0的就是没有请求回来数据，就展示--
    */
    get showPrice() {
        const { tabIndex, goodsInfoPool, couponPool } = this.state;
        const nowPayPrice = goodsInfoPool[tabIndex].payPrice;

        if (couponPool[this.nowGoodInfo.groupKey]?.couponCode) {
            const showPrice = goodsInfoWithCoupon(this.nowGoodInfo, { code: couponPool[this.nowGoodInfo.groupKey].couponCode, price: couponPool[this.nowGoodInfo.groupKey].priceCent } as Coupon).payPrice;
            return +showPrice > 0 ? String(showPrice) : '0';
        }

        return (nowPayPrice && +nowPayPrice > 0) ? String(nowPayPrice) : '--';
    }
    getGoodsDec(groupKey: GroupKey) {
        const { comparePricePool, goodsInfoPool } = this.state;

        if (comparePricePool[groupKey]) {
            const arr = [];
            comparePricePool[groupKey].upGroupItems.forEach(item => {
                arr.push(item.name);
            });

            return `${arr.join('+')}=${comparePricePool[groupKey].allPrice}元`;
        }

        return goodsInfoPool.filter(item => {

            return item.groupKey === groupKey;
        })[0]?.description;
    }
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tabIndex: 1,
            goodsInfoPool: [],
            comparePricePool: {},
            couponPool: {},
            labelPool: {}
        };

        this.props = {
            buyedGood: {} as GoodsInfo
        };

    }
    willMount() {
        const { buyedGood } = this.props;
        const { goodsInfoPool } = this.state;

        if (buyedGood.recommendGroupKey) {
            goodsInfoPool.push({
                groupKey: buyedGood.recommendGroupKey
            } as unknown as GoodsInfo);
        }

        buyedGood.recommendPurchaseGroupKeyList?.forEach(item => {
            goodsInfoPool.push({
                groupKey: item
            } as unknown as GoodsInfo);
        });

        this.setState({
            goodsInfoPool
        });

    }
    didMount() {
        trackDialogShow({
            fragmentName1
        });

        // 注册底部支付方法(这里不能删除，因为ios的购买成功回调方法在这里注册的)
        this.children.buyButton.setPay({
            androidPay: this.pay,
            intercepter: async () => {
                return false;
            },
            iosPaySuccess: () => {
                saveCache({
                    key: 'status-timeLoding',
                    value: ''
                });
                replace(STATUS_URL, {
                    boughtGroupKey: this.nowGoodInfo.groupKey
                });
            }
        });
        this.getGoodInfo();

    }
    async getGoodInfo() {
        const { goodsInfoPool } = this.state;
        const newGoodsPool = [];
        const groupKeys: GroupKey[] = [];

        goodsInfoPool.forEach(item => {
            groupKeys.push(item.groupKey);
        });

        await getGroupSessionInfo({ groupKeys }).then(async goodsListInfo => {

            goodsListInfo.forEach((goodInfo, index) => {
                newGoodsPool.push({
                    ...goodsInfoPool[index],
                    ...goodInfo
                });
            });

            this.setState({
                goodsInfoPool: newGoodsPool
            });
            console.log(newGoodsPool);
            // 先展示页面，再去请求无关的信息
            setTimeout(async () => {
                await this.getComparePrice();
                await this.getLabel();

            }, 60);
        });
    }
    async getComparePrice() {
        const { goodsInfoPool } = this.state;

        GroupComparePrice({ groupKeys: goodsInfoPool.map(item => item.groupKey).join(',') }).then(comparePricePool => {
            this.setState({ comparePricePool });
        });
    }
    async getLabel() {
        const { goodsInfoPool } = this.state;
        const promiseList = [];
        const labelPool = {};

        goodsInfoPool.forEach(item => {
            promiseList.push(getSessionExtra(item));
        });

        await Promise.all(promiseList).then(couponList => {
            couponList.forEach((item, index) => {
                labelPool[goodsInfoPool[index].groupKey] = item;
            });

            this.setState({ labelPool });
        });
    }
    
    pay = async (stat: PayStatProps) => {

        startSiriusPay({
            type: PayBoundType.GoLogin,
            groupKey: this.nowGoodInfo.groupKey,
            sessionIds: this.nowGoodInfo.sessionIds,
            activityType: this.nowGoodInfo.activityType,
            couponCode: this.nowCouponInfo?.couponCode,
            ...stat
        }, false).then(() => {
            saveCache({
                key: 'status-timeLoding',
                value: ''
            });
            replace(STATUS_URL, {
                boughtGroupKey: this.nowGoodInfo.groupKey
            });
        }).catch(async () => {
            this.children.payDialog.show({
                groupKey: this.nowGoodInfo.groupKey,
                payPrice: this.showPrice,
                onPay: () => {
                    this.pay(stat);
                },
                ...stat
            });
        });
    }
    payBtnCall = async (e) => {
        const tabIndex = e.refTarget.getAttribute('data-index');

        this.setState({
            tabIndex
        }, async () => {

            // 点击支付按钮打点
            trackGoPay({
                groupKey: this.nowGoodInfo.groupKey,
                fragmentName1
            });

            if (Platform.isIOS) {
                checkReaded(() => {
                    iosPay(this.nowGoodInfo.groupKey, {
                        fragmentName1
                    });
                });
            } else {
                // 由于底部按钮处有勾选协议判断，但是主图按钮没有，所以点击主图支付按钮直接调起支付。
                // todo：ios地址支付区域的勾选状态没有保存，需解决。H5的勾选协议组件需要改造

                this.children.payDialog.show({
                    groupKey: this.nowGoodInfo.groupKey,
                    payPrice: this.showPrice,
                    onPay: () => {
                        this.pay({ fragmentName1 });   
                    },
                    fragmentName1
                });
            }
        });       
    }
    willReceiveProps() {
        return true;
    }
}
