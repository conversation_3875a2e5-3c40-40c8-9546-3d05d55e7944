.component-add-buy {
    flex: 1;
    padding: 15px;
    overflow: auto;

    .title-box {
        .dec {
            font-size: 18px;
            font-weight: bold;
        }

        .dec1 {
            margin-top: 2px;
            font-size: 13px;
            line-height: 18px;
            color: #666;
        }
    }

    .buy-box {
        padding-top: 5px;

        .buy-item {
            margin-top: 15px;
            height: 105px;
            background: #FFFBF6;
            border-radius: 5px;
            border: 1px solid #F4C2A2;
            display: flex;
            flex-direction: column;
            position: relative;

            .top-info {
                flex: 1;
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 15px;

                .info {
                    .name {
                        font-size: 18px;
                        font-weight: bold;
                    }

                    .other-info {
                        margin-top: 10px;
                        display: flex;
                        align-items: flex-end;

                        .price-valid {
                            color: #692204;
                            font-size: 14px;

                            .unit {
                                color: #D00F1B;
                                font-size: 13px;
                                font-weight: bold;

                            }

                            .price {
                                color: #D00F1B;
                                font-size: 22px;
                                font-weight: bold;
                            }
                        }

                        .diff-price {
                            color: white;
                            font-size: 12px;
                            transform: scale(0.9);
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 18px;
                            padding: 0 7px;
                            background: rgba(219, 153, 39, 0.28);
                            border-radius: 8px;
                            color: #692204;

                            .price {
                                color: #D00F1B;
                            }
                        }
                    }
                }

                .buy-btn {
                    width: 90px;
                    height: 36px;
                    background: url(../images/button-tehuigoumai.png) no-repeat center center/cover;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-weight: bold;
                    color: #831C1C;
                    font-size: 14px;
                }
            }

            .bottom-dec {
                flex-shrink: 0;
                height: 24px;
                line-height: 24px;
                background: #FEF0E7;
                font-size: 12px;
                color: #692204;
                display: flex;
                justify-content: center;
                align-items: center;
                font-weight: 500;
            }

            .tips{
                position: absolute;
                right: -6px;
                top: 0;
                height: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 0 9px;
                background: linear-gradient(90deg, #FF7810 0%, #FE3C29 55%, #FE6164 100%);
                border-radius: 33px 33px 33px 2px;
                color: white;
                font-size: 12px;
                transform: translate(0,-60%) scale(0.9);
            }

            &:nth-of-type(1) {
                background: linear-gradient(131deg, #ffedd1 0%, #ffd791 100%);

                .top-info {
                    .info {
                        .name {
                            color: #501504;
                        }
                        .other-info{
                            .diff-price{
                                color: white;
                                background-color: #2E2E2E;
                                .price{
                                    color: #FFCB48;
                                }
                            }
                        }
                    }
                    .buy-btn{
                        background: url(../images/button-tehuigoumai1.png) no-repeat center center/cover;
                    }
                }

                .bottom-dec {
                    background: rgba(255, 255, 255, 0.38);
                }
            }
        }

        .protocol-box{
            margin-top: 15px;
        }
    }
}
