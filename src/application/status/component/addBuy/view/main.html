<import name="style" content="./main" />

<import name="payDialog" content=":component/payDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="readProtocol" content=":component/readProtocol/main" />
<import name="payType" content=":component/payType/main" />

<div class="component-add-buy">
    <div class="title-box">
        <div class="dec">超值特惠</div>
        <div class="dec1">离开这个页面，就不能享受超值特惠了哦！</div>
    </div>
    <div class="buy-box">
        <sp:each for="state.goodsInfoPool">
            <div class="buy-item">
                <div class="top-info">
                    <div class="info">
                        <div class="name">
                            {{$value.upgrade?'升级':''}}{{$value.name}}
                        </div>
                        <div class="other-info">
                            <div class="price-valid">
                                <span class="unit">￥</span>
                                <span class="price">{{$value.payPrice}}</span
                                >/{{$value.validDays}}天
                            </div>
                            <sp:if
                                value="$index === 0 && state.comparePricePool[$value.groupKey]"
                            >
                                <div class="diff-price">
                                    更划算，立省<span class="price"
                                        >{{state.comparePricePool[$value.groupKey].diffPrice}}</span
                                    >元
                                </div>
                                <sp:elseif
                                    value="state.comparePricePool[$value.groupKey]"
                                />
                                <div class="diff-price">
                                    立省<span class="price"
                                        >{{state.comparePricePool[$value.groupKey].diffPrice}}</span
                                    >元
                                </div>
                            </sp:if>
                        </div>
                    </div>
                    <div
                        sp-on:click="payBtnCall"
                        class="buy-btn"
                        data-index="{{$index}}"
                    >
                        特惠{{$value.upgrade?'升级':'开通'}}
                    </div>
                </div>
                <div class="bottom-dec">
                    {{self.getGoodsDec($value.groupKey)}}
                </div>
                <sp:if value="{{state.labelPool[$value.groupKey].label">
                    <div class="tips">
                        {{state.labelPool[$value.groupKey].label}}
                    </div>
                </sp:if>
            </div>
        </sp:each>
        <sp:if value="{{Platform.isIOS}}">
            <div class="protocol-box">
                <com:payType theme="horizontal" />
                <com:readProtocol />
            </div>
        </sp:if>
    </div>
    <com:payDialog />
    <com:buyButton />
</div>
