/*
 * ------------------------------------------------------------------
 * 购买成功，支付状态结果页
 * ------------------------------------------------------------------
 */

import { Platform, setPageName, URLParams } from ':common/env';
import { getGroupSessionInfo, GoodsInfo, GroupKey } from ':store/goods';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { getAuthToken, getCache, openVipWebView, saveCache, setStatusBarTheme, webClose } from ':common/core';
import { setEmbeddedHeight } from ':common/features/embeded';
import { MCProtocol } from '@simplex/simple-base';
import { login } from ':common/features/login';
import { BUYED_URL } from ':common/navigate';
import { onWebBack } from ':common/features/persuade';
import { trackPageLoad } from ':common/stat';
import { reload } from ':common/features/jump';

interface State {
    login: boolean,
    boughtGroupKey: GroupKey,
    boughtGood: GoodsInfo | unknown
    time: number
    hasRecommend: boolean
}

export default class extends Application<State> {
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            // 是否登录
            login: false,
            // 购买成功的groupKey
            boughtGroupKey: URLParams.boughtGroupKey as GroupKey,
            // 需要根据这个数据请求看看是否有加购和升级
            boughtGood: {},
            // 倒计时
            time: 5,
            hasRecommend: false
        };

    }
    async didMount() {

        this.windowResize();

        setPageName('VIP支付成功页');

        trackPageLoad();

        setStatusBarTheme('dark');

        this.appEventProxy();

        getAuthToken().then(authToken => {
            if (authToken) {
                this.setState({
                    login: true
                });
            }
        });

        if (await getCache('status-timeLoding')) {
            saveCache({
                key: 'status-timeLoding',
                value: ''
            });
            this.setState({
                time: 0
            });
            this.getBuyedInfo();
            return;
        } else {
            saveCache({
                key: 'status-timeLoding',
                value: 'true'
            });
            this.timing();
        }

    }
    // 倒计时
    async timing() {
        let { time } = this.state;

        setTimeout(() => {
            time--;

            this.setState({
                time
            }, () => {
                const { boughtGroupKey } = this.state;
                if (time) {
                    this.timing();
                } else if (boughtGroupKey) {
                    this.getBuyedInfo();
                }
            });
        }, 1000);

    }
    appEventProxy() {

        if (Platform.isIOS) {
            setEmbeddedHeight(0);
        }

        MCProtocol.Core.Web.setting({
            titleBar: false,
            toolbar: false,
            menu: false,
            button: false,
            orientation: 'portrait'
        });

        onWebBack(() => {
            this.goBackPage();
            return Promise.resolve();
        });

    }
    getBuyedInfo() {
        const { boughtGroupKey } = this.state;

        getGroupSessionInfo({ groupKeys: [boughtGroupKey], needRecommendGoods: true }).then(data => {
            // data.recommendGroupKey = GroupKey.ChannelKemuAll;
            // data.recommendPurchaseGroupKeyList = [GroupKey.ChannelKe4, GroupKey.ChannelKe4, GroupKey.ChannelKe4, GroupKey.ChannelKe4, GroupKey.ChannelKe4];
            if (!this.state.login) {
                data[0].recommendGroupKey = '' as any;
                data[0].recommendPurchaseGroupKeyList = [] as any;

            }
            this.setState({
                boughtGood: data[0],
                hasRecommend: !!(data[0].recommendGroupKey || (data[0].recommendPurchaseGroupKeyList && data[0].recommendPurchaseGroupKeyList.length))
            });
        });

    }
    // 这里的登录不能直接刷新页面，会弹出推荐，测试不肯(安卓，登录会直接把这个webView关了)
    async onLogin() {
        await login(false);
        this.setState({
            login: true
        });
    }
    onViewVip() {
        // 兼容双端参数不一致情况
        openVipWebView({
            url: BUYED_URL + '?iosH5Head=show'
        });
    }
    onGoUse() {
        if (URLParams.useUrl) {
            openVipWebView({
                url: decodeURIComponent(URLParams.useUrl)
            });
        } else {
            webClose();
        }
    }
    goBackPage() {
        webClose();
    }
    windowResize() {
        const resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize';
        const orientation = window.orientation;

        window.addEventListener(resizeEvt, () => {
            setTimeout(() => {
                if ((orientation === 90 || orientation === -90) && (window.orientation === 0 || window.orientation === 180)) {
                    reload();
                }
            }, 500);
        }, false);
    }
}
