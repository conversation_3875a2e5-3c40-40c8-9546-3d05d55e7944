/*
 * ------------------------------------------------------------------
 * 答题技巧落地页
 * ------------------------------------------------------------------
 */

import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { trackPageLoad } from ':common/stat';
import { setStatusBarTheme } from ':common/core';
import Header from ':component/header/main';
import { setPageName } from ':common/env';

interface State {
    prevScrollTop: number
}
let timer;

export default class extends Application<State> {
    declare children: {
        header: Header;
    };
    get pageName() {
        return '答题技巧页';
    }
    get pageTitle() {
        const title = '答题技巧';

        return title;
    }

    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            prevScrollTop: 0
        };

    }

    async didMount() {
        setPageName(this.pageName);
        // 页面进出时长打点
        trackPageLoad({
            payStatus: 1
        });

        setStatusBarTheme('dark');

        this.children.header.setScrollBg(0, '#EBF8FF');

    }

    pageScroll(e) {

        timer && clearTimeout(timer);

        timer = setTimeout(() => {
            const prevScrollTop = e.refTarget.scrollTop;

            this.setState({
                prevScrollTop
            });
        }, 10);
    }
}
