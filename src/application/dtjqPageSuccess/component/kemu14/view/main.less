.panel-dtjqPage-success {

    .content {

        .tab-box {
            display: flex;
            width: 100%;
            overflow-x: auto;
            position: sticky;
            z-index: 103;
            top: 0px;
            background-color: #EBF8FF;
            padding: 0 20px;
            .tab-item {
                flex-shrink: 0;
                padding: 11px 10px;
                font-size: 15px;
                color: #606C77;
                white-space: nowrap;
                line-height: 21px;

                .txt {
                    position: relative;
                    z-index: 1;
                }

                &.active {
                    color: #333;
                    font-weight: bold;
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        bottom: 7px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 44px;
                        height: 13px;
                        background: url(../images/active.png) no-repeat center center/cover;
                    }
                }
            }
        }

        .content-list {
            padding: 0 20px 30px;

            .content-item {
                padding: 15px 0;

                .myvideo-box {
                    height: 185px;
                    border-radius: 16px;
                    overflow: hidden;
                    margin-bottom: 13px;
                }

                .question-type-list {

                    .question-type-item {
                        margin-top: 15px;
                        background: #ffffff;
                        border-radius: 16px;
                        padding: 15px;
                        position: relative;


                        .question-item-head {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            position: relative;
                            margin-bottom: 10px;

                            &::before {
                                content: '';
                                position: absolute;
                                top: 50%;
                                left: -15px;
                                transform: translateY(-50%);
                                width: 3px;
                                height: 13px;
                                background: #6da3fe;
                                border-radius: 0px 2px 2px 0px;
                            }

                            .item-title {
                                max-width: 200px;
                                font-size: 15px;
                                font-weight: bold;
                                color: #494455;
                            }

                            .question-num {
                                border: 1px solid #0a63fe;
                                border-radius: 12px;
                                padding: 3px 10px;
                                font-size: 12px;
                                color: #0B64FE;

                                &::after {
                                    content: '';
                                    display: inline-block;
                                    width: 10px;
                                    height: 10px;
                                    background: url(../images/<EMAIL>) no-repeat center center/cover;
                                }
                            }
                        }


                        .mask-question-item {
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            background: linear-gradient(180deg, rgba(235, 248, 255, 0.00), #ebf8ff 50%, #ebf8ff);


                            .item-show {
                                position: absolute;
                                bottom: 0;
                                left: 0;
                                right: 0;

                                .open-title {
                                    color: #B4804B;
                                    font-size: 14px;
                                    text-align: center;
                                }

                                .open-auth {
                                    height: 82px;
                                    margin-top: 10px;
                                    background: url(../images/1.png) no-repeat center center/contain;
                                }
                            }

                        }
                    }
                }
            }
        }
    }
}
