/*
 * ------------------------------------------------------------------
 * 视频播放器
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import { MCProtocol } from '@simplex/simple-base';
import View from './view/main.html';
import { Platform } from ':common/env';

export default class MyVideo extends Component<any, any> {
    $video: HTMLVideoElement;
    timer: ReturnType<typeof setTimeout>
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
           
        };

    }
    willReceiveProps() {
        return true;
    }
}
