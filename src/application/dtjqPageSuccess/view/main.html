<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="Kemu14Panel" content=":application/dtjqPageSuccess/component/kemu14/main" />

<div class="page-container page-dtjq">
    <div class="page-header">
        <com:header title="{{self.pageTitle}}" finalBgColor="#EBF8FF" theme="white" endTheme="white"
            scrollTop="{{state.prevScrollTop}}">
            <div sp:slot="right"></div>
        </com:header>
    </div>

    <div class="body-panel-box">
        <div class="body-panel" sp-on:scroll="pageScroll">
            <!-- 科目1或4 -->
            <div class="panel-box">
                <com:Kemu14Panel />
            </div>
        </div>
    </div>
</div>
