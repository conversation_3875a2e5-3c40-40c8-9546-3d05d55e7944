img {
    width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

.play-container {
    width: 100%;
    height: auto;
    margin-bottom: 20px;
    position: relative;

    .play-button-img {
        width: 50px;
        height: 50px;
        position: absolute;
        top: 0px;
        left: 0px;
        bottom: 0px;
        right: 0px;
        margin: auto;

    }

}

.swiper-slide {
    position: relative;
    width: 100%;
    overflow: auto !important;
    height: auto;
}

.step-div {
    height: auto;
    padding-left: 15px;
    padding-right: 15px;

    &.step-div-qita {
        padding-top: 39px;
    }

}

.step-img {
    width: 57px;
    height: 18px;
    margin-bottom: 10px;
    margin-left: 0px
}

.link-weixin-title {
    width: 100%;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 21px;
    margin-bottom: 5px;

    &.link-weixin-title2 {
        margin-bottom: 35px;
    }

    &.link-weixin-title4 {
        margin-bottom: 39px;
    }

    &.link-weixin-title5 {
        margin-bottom: 18px;
    }

    span {
        color: #04A5FF
    }
}

.link-weixin {
    width: 300px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    background: #008AFF;
    border-radius: 22px;
    margin: 0 auto;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    margin-top: 10px;

    &.vip-course {
        display: block;
    }

    &.link-weixin2 {
        margin-top: 25px;
    }
}

.shanghua-div {
    text-align: center;
    width: 100%;
    margin-top: 10px;

    &.shanghua-div-02 {
        margin-top: 74px;
    }

    &.shanghua-div-03 {
        margin-top: 75px;
    }

    &.shanghua-div-04 {
        margin-top: 85px;
    }

    .shanghua-img {
        display: inline-block;
        width: 19px;
        height: 19px;
        background: url("../images/shangyi-icon.png") no-repeat center;
        background-size: 100% 100%;


    }
}

.question-weiti-qita {
    margin-top: 10px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 20px;
    text-align: center;
}

.fixed-bottom-div {
    position: absolute;
    bottom: 20px;
    left: 0px;
    right: 0px;
    margin: auto;

    &.fixed-bottom-div2 {
        bottom: 14px;

        .question-weiti-qita {
            margin-top: 1px;
        }
    }
}

.swiper-container-other {
    flex: 1;
    width: 100%;
    overflow: hidden;
    // padding-bottom: 10px;
    // overflow:hidden;

}

.swiper-pagination-bullet-active {
    background: #3D5AFF;
}

.page-container-playguide {
    height: 100%;
    display: flex;
    display: -webkit-flex;
    overflow: hidden;
    flex-direction: column;
    -webkit-flex-direction: column;
    background: #fff;
}

.header-conatiner {
    padding-top: calc(~ "0.001px + constant(safe-area-inset-top)") !important;
    /* 兼容 iOS < 11.2 */
    padding-top: calc(~ "0.001px + env(safe-area-inset-top)") !important;
    /* 兼容 iOS > 11.2 */
}

.header {
    display: flex;

    align-items: center;
    height: 50px;
    justify-content: space-around;
    // padding: 0 75px;
    width: 100%;

    .goback-img {
        height: 100%;
        width: 90px;
        display: flex;
        align-items: center;

        .img-div {
            width: 70px;
            height: 100%;
            display: flex;
            padding-left: 19px;
            align-items: center;
        }

        .img-span {
            display: inline-block;
            width: 16px;
            height: 14px;
            background: url("../images/goback.png") no-repeat center center;
            background-size: 100% 100%;

        }
    }

    .tab-qiehuan {
        flex: 1;
        display: flex;
        align-items: center;

        .nav {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 30px;

            &.active {
                span {
                    color: #333;
                }

                i {
                    background: #333;
                }
            }

            span {
                display: block;
                color: #999999;
                box-sizing: border-box;
                height: 47px;
                line-height: 40px;
                font-size: 17px;
                padding-top: 5px;
            }

            i {
                display: block;
                background: #fff;
                height: 3px;
                width: 20px;
            }


        }
    }


}