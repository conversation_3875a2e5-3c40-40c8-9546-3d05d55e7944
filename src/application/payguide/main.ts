/*
 * ------------------------------------------------------------------
 * ios支付引导页
 * ------------------------------------------------------------------
 */

import { Platform, URLParams } from ':common/env';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import Swiper from 'swiper';
import { MCProtocol } from '@simplex/simple-base';
import { setStatusBarTheme, webClose, openVipWebView, openWeb } from ':common/core';
import { HELP_VIP } from ':common/navigate';
import jump from ':common/features/jump';
interface State {
    payType: string,
    appName: string,
    fromPageType: string | number,
}
export default class extends Application<State> {
    swiper: any
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });
        this.state = {
            payType: URLParams.payType || 'weixin',
            appName: URLParams._appName || '',
            fromPageType: URLParams.fromPageType
        };

    }
    didMount() {
        setStatusBarTheme('dark');
        MCProtocol.Core.Web.setting({
            titleBar: false,
            toolbar: false,
            menu: false,
            button: false
        });
        document.onvisibilitychange = () => {

            if (document.visibilityState === 'visible') {
                MCProtocol.Core.Web.setting({
                    titleBar: false,
                    toolbar: false,
                    menu: false,
                    button: false
                });
            }
        };
        if (this.state.appName === 'jiakao3d' || Platform.isXueTang) {
            const eleEle = document.querySelectorAll('.vip-course');
            for (let i = 0; i < eleEle.length; i++) {
                (eleEle[i] as HTMLElement).style.display = 'none';
            }

        }
        this.setSwiper();
        this.event.on('paytype', 'click', (e) => {
            const payType = e.refTarget.getAttribute('data-nav');
            this.setState({
                payType: payType
            });
            if (payType === 'weixin') {
                this.swiper.slideToLoop(0, 0, false);
            } else {
                this.swiper.slideToLoop(5, 0, false);
            }
        });
        this.event.on('weixinVideo', 'click', (e) => {
            const payType = e.refTarget.getAttribute('data-payType');
            jump.navigateTo('./payguidevideo.html', { payType });
        });
        this.event.on('bindWeixinorZFB', 'click', () => {
            openWeb({
                url: 'http://app.nav.mucang.cn/open?scheme=https%3A%2F%2Fapps.apple.com%2Faccount%2Fbilling'
            });
        });
        this.event.on('gotoBuy', 'click', () => {
            // 直播课那边直接传fromPageType == 1，直接关闭，避免跳转返回多级的问题
            if (+this.state.fromPageType === 1) {
                MCProtocol.Core.Web.close();
            } else {
                openVipWebView({
                    url: 'https://laofuzi.kakamobi.com/jkbd-vip/index/index.html',
                    style: 0
                });
            }
        });
        this.event.on('help', 'click', () => {
            jump.navigateTo(HELP_VIP);
        });
        this.event.on('goback', 'click', () => {
            webClose();
        });
    }
    setSwiper() {
        setTimeout(() => {
            const $dom = this.getDOMNode().swiper as HTMLElement;
            this.swiper = new Swiper($dom, {
                initialSlide: 0,
                direction: 'vertical',
                slidesPerView: 1,
                spaceBetween: 0,
                on: {
                    slideChangeTransitionEnd: (swiper) => {
                        const index = swiper.realIndex;
                        if (index >= 5) {
                            this.setState({
                                payType: 'alipay'
                            });
                        } else {
                            this.setState({
                                payType: 'weixin'
                            });
                        }

                    }
                }
            });
            let startScroll: number;
            let touchStart: number;
            let touchCurrent: number;
            this.swiper.slides.on('touchstart', function (e: any) {
                startScroll = Math.floor(this.scrollTop);
                touchStart = e.targetTouches[0].pageY;
            }, true);
            this.swiper.slides.on('touchmove', function (e: any) {
                touchCurrent = e.targetTouches[0].pageY;
                const touchesDiff = touchCurrent - touchStart;
                const onlyScrolling =
                    (this.scrollHeight > this.offsetHeight) &&
                    (
                        (touchesDiff < 0 && startScroll === 0) ||
                        (touchesDiff > 0 && startScroll === (this.scrollHeight - this.offsetHeight)) ||
                        (startScroll > 0 && startScroll < (this.scrollHeight - this.offsetHeight))
                    );
                if (onlyScrolling) {
                    e.stopPropagation();
                }
            }, true);

        }, 500);
    }
}
