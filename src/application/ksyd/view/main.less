.page-ksyd {
    overflow-y: auto;
    background: #fff;

    >* {
        flex-shrink: 0;
    }

    h3 {
        font-size: 20px;
        line-height: 25px;
        color: #333;
        padding: 25px 0 10px 20px;
    }

    h4 {
        font-size: 15px;
        line-height: 20px;
        color: #6E6E6E;
        padding: 15px 20px 5px 20px;
    }

    p {
        position: relative;
        padding: 10px 20px 5px 20px;
        color: #6E6E6E;
    }

    p span {
        display: block;
        font-size: 16px;
        padding-left: 37px;
        line-height: 28px;
    }

    p b {
        position: absolute;
        top: 10px;
        left: 20px;
        display: block;
        width: 23px;
        height: 23px;
        border-radius: 100%;
        background: #E8E8E8;
        color: #000;
        font-size: 12px;
        text-align: center;
        line-height: 24px;
        margin-top: 3px;
    }
}
