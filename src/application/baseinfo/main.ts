/*
 * ------------------------------------------------------------------
 * 基础信息
 * ------------------------------------------------------------------
 */

import { setPageName, URLParams } from ':common/env';
import { getUserPayOutInfo } from ':store/user';
import Texts from ':common/features/texts';
import { Application } from '@simplex/simple-core';
import View from './view/main.html';
import { trackPageShow } from ':common/stat';
import { gethelpPage, getQuestionDetail, getQuestionList } from ':store/baseinfo';
import { setStatusBarTheme } from ':common/core';
interface State {
    tiku: string,
    currentKemu: number | string,
    kemuString: string,
    kemuList: any[],
    questionList: any[]
}
export default class extends Application<State> {
    $constructor() {
        this.$super({
            target: document.body,
            name: module.id,
            view: View
        });

        this.state = {
            tiku: URLParams.carStyle || 'car',
            currentKemu: 1,
            kemuString: '',
            kemuList: [],
            questionList: []
        };
    }
    didMount() {
        setStatusBarTheme('dark');
        setPageName('补偿基本信息页');
        trackPageShow();
        this.renderPage();
        this.getCommonRequestion();
    }
    async getDetailInfo(itemList) {
        const questionDetailList = itemList;
        const promiseList = [];
        questionDetailList.forEach((item) =>
            promiseList.push(getQuestionDetail({ questionId: item.id }))
        );
        const questionPool = {};
        return Promise.all(promiseList).then((goodsList) => {
            goodsList.forEach((item, index) => {
                questionPool[questionDetailList[index].id] = item;
            });
            return questionPool;
        });
    }
    async getCommonRequestion() {
        const data = await gethelpPage();
        // 线上的补偿信息id是4
        let labelId = '';
        data.labelList && data.labelList.forEach((key) => {
            if (key.id === 4) {
                labelId = key.id;
            }
        });
        const question = await getQuestionList({ labelId: labelId });
        const questionList = [];
        const questionPool = await this.getDetailInfo(question.itemList || []);
        for (const key in questionPool) {
            questionList.push(questionPool[key]);
        }
        this.setState({ questionList });
    }
    async renderPage() {
        const data = await getUserPayOutInfo({ tiku: this.state.tiku });
        const kemuList = data.itemList;
        let kemuString = '';
        kemuList && kemuList.forEach((res) => {
            kemuString += Texts.kemuTxt[res.kemu] + '/';
        });
        kemuString = kemuString.substring(0, kemuString.length - 1);
        this.setState({
            kemuString: kemuString,
            kemuList: kemuList
        });
    }
}
