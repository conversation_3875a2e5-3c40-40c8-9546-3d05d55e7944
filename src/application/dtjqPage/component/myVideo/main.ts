/*
 * ------------------------------------------------------------------
 * 视频播放器
 * ------------------------------------------------------------------
 */

import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import MyVideo from ':component/myVideo/main';

export default class extends Component<any, any> {
    declare children: {
        myVideo: MyVideo
    }
    timer: ReturnType<typeof setTimeout>
    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            showBuyMask: false
        };

    }
    onPlayAgain() {
        this.children.myVideo.play();
        this.setState({
            showBuyMask: false
        });
    }
    endPlay = () => {
        this.setState({
            showBuyMask: true
        });

        this.props.openPayDialog && this.props.openPayDialog('试看视频');
    }
    onPay() {
        this.props.openPayDialog && this.props.openPayDialog('试看视频');
    }
    willReceiveProps(nextProps) {
        if (nextProps.src !== this.props.src) {
            this.setState({
                showBuyMask: false
            });
        }
        return true;
    }
}
