.my-video {
    width: 100%;
    height: 100%;
    position: relative;

    video {
        width: 1px;
        height: 1px;
        object-fit: fill;

        &.show-video {
            width: 100%;
            height: 100%;
        }
    }

    .pay-box {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.85);
        z-index: 1;
        padding: 17px 26px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .pay-title {
            font-size: 15px;
            color: #FFD3B4;
        }

        .pay-dec {
            margin-top: 2px;
            font-size: 12px;
            transform: scale(0.9);
            color: #FFD3B4;
            opacity: 0.9;
        }

        .auth-box {
            margin-top: 10px;
            width: 283px;
            height: 54px;
            background: url(../images/2.png) no-repeat center center/cover;
        }

        .active-box {
            margin-top: 12px;
            display: flex;
            justify-content: space-between;
            width: 100%;

            .again {
                width: 94px;
                height: 36px;
                border: 1px solid;
                border-radius: 18px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 13px;
                color: #FFD3B4;

                &::before {
                    content: '';
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    background: url(../images/3.png) no-repeat center center/cover;
                    margin-right: 4px;
                }
            }

            .buy-btn {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 179px;
                height: 36px;
                font-size: 14px;
                color: #000000;
                background: linear-gradient(0deg, #f9c39f, #fedec7);
                border-radius: 18px;
            }
        }
    }
}

.hide {
    display: none;
}
