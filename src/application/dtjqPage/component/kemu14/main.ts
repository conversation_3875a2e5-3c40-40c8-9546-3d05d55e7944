/*
 * main
 *
 * name: xiaojia
 * date: 16/3/24
 */
import { Component } from '@simplex/simple-core';
import View from './view/main.html';
import { openWeb } from ':common/core';
import { pauseAllVideos } from ':common/features/dom';
import { getArtfulGroupDetail, getArtfulGroupList } from ':store/chores';

interface State {
    currentIndex: number
    tabList: any[]
    tabContent: any
    // 计算头部定位的距离
    stickyTop: number
}
interface Props {
    goAuth?(any)
    openPayDialog?(fragment1: string)
    payBtnCall?(e: Event)
}

export default class extends Component<State, Props> {

    $constructor() {

        this.$super({
            name: module.id,
            view: View
        });

        this.state = {
            currentIndex: 0,
            tabList: [],
            tabContent: {},
            stickyTop: 0
        };
        this.props = {
        };

    }
    didMount() {
        const top = document.querySelector('.page-dtjq>.page-header').clientHeight;

        this.setState({
            stickyTop: top
        });

        this.getTabList().then(() => {
            this.getTabContent();
        });
    }
    onSwitchIndex(e) {
        const { currentIndex } = this.state;
        const index = +e.refTarget.getAttribute('data-index');

        if (index !== currentIndex) {
            pauseAllVideos();
            this.setState({
                currentIndex: index
            });
            this.getTabContent();
            this.props.openPayDialog('切换分类');
        }
    }
    getTabList() {
        return new Promise<void>((resolve) => {
            getArtfulGroupList().then(data => {
                this.setState({
                    tabList: data.itemList
                });
                resolve();
            });

        });

    }
    getTabContent() {
        const { currentIndex, tabList } = this.state;

        getArtfulGroupDetail({
            artfulGroupId: tabList[currentIndex].artfulGroupId
        }).then(data => {
            this.setState({
                tabContent: data
            });
        });

    }
    onOpenPayDialog(e) {
        const { tabContent } = this.state;
        const index = e.refTarget.getAttribute('data-index');

        if (tabContent.artfulItemList[index].questionIdList?.length) {
            openWeb({
                url: `http://jiakao.nav.mucang.cn/commonPractice?indexKey=dtjqPage_${tabContent.artfulItemList[index].artfulId}&ids=${tabContent.artfulItemList[index].questionIdList.join(',')}`
            });
        } else {
            this.props.openPayDialog('查看更多');
        }
    }
    pay(e) {
        this.props.payBtnCall && this.props.payBtnCall(e);
    }
    willReceiveProps() {
       
        return true;
    }
    goAuth(e) {
        const uniqkey = e.refTarget.getAttribute('data-uniqkey');

        this.props.goAuth && this.props.goAuth(uniqkey);
    }
}
