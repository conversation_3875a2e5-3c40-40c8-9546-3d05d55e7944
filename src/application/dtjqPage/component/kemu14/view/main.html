<import name="style" content="./main" />
<import name="myVideo" content="../../myVideo/main" />

<div class="panel-dtjqPage">
    <div class="header-bg"></div>
    <div class="content">
        <div class="tab-box" style="top: {{state.stickyTop - 1}}px">
            <sp:each for="state.tabList">
                <div class="tab-item {{state.currentIndex === $index?'active':''}}" data-index="{{$index}}"
                    sp-on:click="onSwitchIndex"><span class="txt">{{$value.artfulGroupName}}</span></div>
            </sp:each>
        </div>

        <div class="content-list">
            <div class="content-item">
                <sp:if value="!!state.tabContent.videoData.middleUrl">
                    <div class="myvideo-box">
                        <com:myVideo openPayDialog="{{props.openPayDialog}}"
                            src="{{state.tabContent.videoData.middleUrl}}"
                            poster="{{state.tabContent.videoData.coverImage}}"
                            showVideo="true" />
                    </div>
                </sp:if>
                <div class="question-type-list">
                    <sp:each for="state.tabContent.artfulItemList">
                        <div class="question-type-item {{$index === 2?'last':''}}" sp-on:click="onOpenPayDialog" data-index="{{$index}}">
                            <div class="question-item-head">
                                <div class="item-title">{{$value.artfulName}}</div>
                                <div class="question-num">可学会{{$value.questionCount}}题</div>
                            </div>
                            <sp:if value="$value.description">
                                <div class="quetion-content">
                                    {{#$value.description}}
                                </div>
                            </sp:if>
                            <sp:if value="$index === 2">
                                <div class="mask-question-item">
                                    <div class="item-show">
                                        <div class="open-title">解锁全部答题技巧，尊享VIP四大权益</div>
                                        <div class="open-auth"></div>
                                    </div>
                                </div>
                            </sp:if>
                        </div>
                    </sp:each>
                </div>
            </div>
        </div>
    </div>
</div>
