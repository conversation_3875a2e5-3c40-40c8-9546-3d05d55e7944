<import name="style" content="./main" />

<import name="header" content=":component/header/main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="buyButton" content=":component/buyButton/main" />
<import name="bottomTabs" content=":component/bottomTabs/main" />
<import name="payDialog" content=":component/payDialog/main" />
<import name="CarShort" content=":application/car/component/CarShort/main" />
<import name="persuadeDialog" content=":component/persuadeDialog/main" />
<import name="BuyDialog" content=":component/buyDialog/main" />
<import name="Kemu14Panel" content=":application/dtjqPage/component/kemu14/main" />
<import name="CarKemuAll" content=":application/car/component/CarKemuAll/main" />
<import name="CarKemu1and4" content=":application/car/component/CarKemu1and4/main" />
<import name="MotoKemu1and4" content=":application/moto/component/MotoKemu1and4/main" />

<import name="Score12Short" content=":application/score12buy/component/score12Short/main" />
<import name="BusTruck1and4" content=":application/khche/component/BusTruck1and4/main" />
<div class="page-container page-dtjq">
    <div class="page-header">
        <com:header title="{{state.prevScrollTop > 100 ? self.pageTitle : ' '}}" finalBgColor="#EBF8FF" theme="black"
            endTheme="white"
            qaKey="{{self.qaKey}}" back="{{self.backCall}}" scrollTop="{{state.prevScrollTop}}">
            <div sp:slot="right"></div>
        </com:header>
    </div>

    <div class="body-panel-box">
        <div class="body-panel" sp-on:scroll="pageScroll">
            <!-- 科目1或4 -->
            <div class="panel-box {{state.tabIndex == 0?'':'hide'}}">
                <com:Kemu14Panel goAuth="{{self.goAuth}}" payBtnCall="{{self.payBtnCall}}"
                    openPayDialog="{{self.openPayDialog}}" />
            </div>

            <!-- 小车短时提分 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.ChannelKe4Short).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKe4Short).showPage) || (self.getGroupKeyInfo(GroupKey.GcChannelKe4Short).payPrice && self.getGroupKeyInfo(GroupKey.GcChannelKe4Short).showPage)">
                <div
                    class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.ChannelKe4Short || self.nowGoodInfo.groupKey === GroupKey.GcChannelKe4Short?'':'hide'}}">
                    <com:CarShort payPrice="{{self.showPrice}}" labelPool="{{state.labelPool}}"
                        groupKey="{{self.nowGoodInfo.groupKey}}" isHubei="{{state.isHubei}}"
                        payBtnCall="{{self.payBtnCall}}" goAuth="{{self.goAuth}}" />
                </div>
            </sp:if>

            <!-- 小车全科 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.ChannelKemuAll).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKemuAll).showPage)">
                <div class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.ChannelKemuAll?'':'hide'}}">
                    <com:CarKemuAll tiku="{{state.tiku}}" showActiveBtn="{{true}}"
                        goodsInfo="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll)}}"
                        comparePricePool="{{state.comparePricePool}}"
                        groupKey="{{self.getGroupKeyInfo(GroupKey.ChannelKemuAll).groupKey}}"
                        payPrice="{{self.showPrice}}" goAuth="{{self.goAuth}}" payBtnCall="{{self.payBtnCall}}" />
                </div>
            </sp:if>

            <!-- 小车科一科四组合包 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.ChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKe1Ke4Group).showPage)">
                <div class="{{self.nowGoodInfo.groupKey === GroupKey.ChannelKe1Ke4Group?'':'hide'}}">
                    <com:CarKemu1and4 currentIndex="{{state.tabIndex}}" goodsList="{{state.goodsInfoPool}}"
                        labelPool="{{state.labelPool}}" comparePricePool="{{state.comparePricePool}}"
                        payPrice="{{self.showPrice}}" groupKey="{{self.nowGoodInfo.groupKey}}" kemu="{{state.kemu}}"
                        tiku="{{state.tiku}}" goAuth="{{self.goAuth}}" payBtnCall="{{self.payBtnCall}}" />
                </div>
            </sp:if>
            <!-- 客货车科一科四组合包 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.HcChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.HcChannelKe1Ke4Group).showPage)||(self.getGroupKeyInfo(GroupKey.KcChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.KcChannelKe1Ke4Group).showPage)">
                <div
                    class="{{self.nowGoodInfo.groupKey === GroupKey.HcChannelKe1Ke4Group||self.nowGoodInfo.groupKey === GroupKey.KcChannelKe1Ke4Group?'':'hide'}}">
                    <com:BusTruck1and4 goodsInfo="{{self.nowGoodInfo}}" labelPool="{{state.labelPool}}"
                        comparePricePool="{{state.comparePricePool}}" payPrice="{{self.showPrice}}"
                        groupKey="{{self.nowGoodInfo.groupKey}}" goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}" />
                </div>
            </sp:if>
            <!-- 货车全科 -->
            <!-- <sp:if value="self.getGroupKeyInfo(GroupKey.HcChannelKemuAll).payPrice">
                <div class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.HcChannelKemuAll?'':'hide'}}">
                    <com:TruckKemuAll goodsInfo="{{self.getGroupKeyInfo(GroupKey.HcChannelKemuAll)}}"
                        comparePricePool="{{state.comparePricePool}}" labelPool="{{state.labelPool}}" tiku="{{state.tiku}}"
                        payPrice="{{self.showPrice}}" payBtnCall="{{self.payBtnCall}}" goAuth="{{self.goAuth}}" />
                </div>
            </sp:if> -->

            <!-- 客车全科 -->
            <!-- <sp:if value="self.getGroupKeyInfo(GroupKey.KcChannelKemuAll).payPrice">
                <div class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.KcChannelKemuAll?'':'hide'}}">
                    <com:BusKemuAll goodsInfo="{{self.getGroupKeyInfo(GroupKey.KcChannelKemuAll)}}"
                        comparePricePool="{{state.comparePricePool}}" labelPool="{{state.labelPool}}"
                        payPrice="{{self.showPrice}}" payBtnCall="{{self.payBtnCall}}" goAuth="{{self.goAuth}}" />
                </div>
            </sp:if> -->

            <!-- moto的科一科四组合包 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.MotoChannelKe1Ke4Group).payPrice && self.getGroupKeyInfo(GroupKey.MotoChannelKe1Ke4Group).showPage)">
                <div class="{{self.nowGoodInfo.groupKey === GroupKey.MotoChannelKe1Ke4Group?'':'hide'}}">
                    <com:MotoKemu1and4 goodsInfo="{{self.nowGoodInfo}}" payPrice="{{self.showPrice}}"
                        labelPool="{{state.labelPool}}" groupKey="{{self.nowGoodInfo.groupKey}}"
                        isHubei="{{state.isHubei}}" goAuth="{{self.goAuth}}" payBtnCall="{{self.payBtnCall}}" />
                </div>
            </sp:if>

            <!-- 扣满12分短时提分 -->
            <sp:if
                value="(self.getGroupKeyInfo(GroupKey.ChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.ChannelKou12Short).showPage) || (self.getGroupKeyInfo(GroupKey.HcChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.HcChannelKou12Short).showPage) || (self.getGroupKeyInfo(GroupKey.KcChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.KcChannelKou12Short).showPage) || (self.getGroupKeyInfo(GroupKey.MotoChannelKou12Short).payPrice && self.getGroupKeyInfo(GroupKey.MotoChannelKou12Short).showPage)">
                <div
                    class="panel-box {{self.nowGoodInfo.groupKey === GroupKey.ChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.HcChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.KcChannelKou12Short || self.nowGoodInfo.groupKey === GroupKey.MotoChannelKou12Short?'':'hide'}}">
                    <com:Score12Short goodsInfo="{{self.nowGoodInfo}}" labelPool="{{state.labelPool}}"
                        isHubei="{{state.isHubei}}" comparePricePool="{{state.comparePricePool}}"
                        payPrice="{{self.showPrice}}" groupKey="{{self.nowGoodInfo.groupKey}}" goAuth="{{self.goAuth}}"
                        payBtnCall="{{self.payBtnCall}}" />
                </div>
            </sp:if>
        </div>
    </div>

    <div class="footer-box">
        <div class="footer {{state.goodsInfoPool.length > 1?'':'hide'}}">
            <com:bottomTabs tabIndex="{{state.tabIndex}}" labelPool="{{state.labelPool}}"
                comparePricePool="{{state.comparePricePool}}" goodsList="{{state.goodsInfoPool}}"
                tabChange="{{self.tabChangeCall}}" />
        </div>

        <com:buyButton>
            <div sp:slot="couponEntry" class="go_coupon coupon-position-bottom" sp-on:click="goCoupon">
                {{self.nowCouponInfo.couponCode?'已优惠' +
                self.nowCouponInfo.priceCent + '元>':'领取优惠券'}}
            </div>
        </com:buyButton>
    </div>

    <sp:if value="state.showBuyDialog">
        <div class="showLookAllBuy-mask">
            <div class="showLookAllBuy-box" key="{{state.buyModalFragmentName1}}">
                <com:BuyDialog fragmentName1="{{state.buyModalFragmentName1}}" type="component" title1="答题技巧"
                    subTitle1="小车答题技巧"
                    close="{{self.closePayModal}}}" />
            </div>
        </div>
    </sp:if>

    <com:persuadeDialog />
    <com:payDialog />
    <com:expiredDialog />
</div>
