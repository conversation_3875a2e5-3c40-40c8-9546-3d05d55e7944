<import name="style" content="./main" />
<import name="expiredDialog" content=":component/expiredDialog/main" />
<import name="loading" content=":application/buyed/components/loading/main" />
<import name="indexPage" content=":application/kqfdDialog/component/index/main" />


<div class="page-container page-kqfd-dialog">
    <div class="body-panel">
        <com:indexPage expireTimeString="{{state.expireTimeString}}" hasPromission="{{state.hasPromission}}"
            goodsInfoPool="{{state.goodsInfoPool}}"></com:indexPage>
    </div>

    <com:loading />
    <com:expiredDialog />
</div>