.kqfd-index-dialog {
    .index-top {
        height: 437px;
        background: url(../images/top_new.png) no-repeat center;
        background-size: 100%;
        position: relative;
         margin-top: -0.4rem;
        .index-top-time {
            position: absolute;
            top: 277px;
            height: 35px;
            display: flex;
            align-items: center;
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Semibold;
            font-weight: 600;
            color: #111b30;
            padding-left: 65px;
        }
    }
    .index-buy-button {
        width: 318px;
        height: 55px;
        background: url(../images/buy-button.png) no-repeat center;
        background-size: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: -10px auto 0px auto;
        z-index: 1;
        position: relative;
        .buy-title {
            font-size: 17px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            color: #ffffff;
            line-height: 24px;
            margin-top: -5px;
        }
        .buy-desc {
            font-size: 10px;
            font-family: Ping<PERSON>angSC, PingFangSC-Regular;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.8);
            line-height: 14px;
        }
    }
    .history-bg {
        width: 299px;
        height: 49px;
        margin: 19px auto 12px auto;
        background: url(../images/wangqijingcai.png) no-repeat center;
        background-size: 100%;
    }
    .history-video-container {
        width: 351px;
        height: 126px;
        margin: 0px auto 13px auto;
        background: url(../images/history01.png) no-repeat center;
        background-size: 100%;
        position: relative;
        padding-left: 10px;
        &.history2 {
            margin: 0px auto 0px auto;
            background: url(../images/history02.png) no-repeat center;
            background-size: 100%;
        }
        .history-title {
            font-size: 13px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            color: #111b30;
            line-height: 18px;
            position: absolute;
            bottom: 9px;
            display: flex;
            .left {
            }
            .right {
                margin-left: 13px;
                &.right2 {
                    margin-left: 8px;
                }
            }
        }
    }

    .mbw-bg {
        height: 473px;
        margin: 22px 15px 0px 15px;
        background: url(../images/jxzb_mbw.png) no-repeat center;
        background-size: 100%;
        &.mbw-bg2 {
            margin-top: 19px;
        }
    }
    .mashangkaoshi-bg {
        width: 334px;
        height: 50px;
        margin: 29px auto 14px auto;
        background: url(../images/mashangkaoshi.png) no-repeat center;
        background-size: 100%;
    }
    .mashangkaoshi-container {
        width: 345px;
        height: 130px;
        margin: 0px auto;
        background: url(../images/mashang_bg.png) no-repeat center;
        background-size: 100%;
    }
    .zhibo-time-container {
        margin-top: 29px;
        .time-title {
            width: 299px;
            height: 49px;
            background: url(../images/zhibo-time.png) no-repeat center;
            background-size: 100%;
            margin: 0 auto 38px auto;
        }
        .time-tabs {
            position: relative;

            background: #fffacc;
            border: 1px solid #111b30;
            border-radius: 13px;
            margin: 0px 15px 40px 15px;
            padding: 35px 8px 15px 8px;
            position: relative;
            &.time-tabs2 {
                margin-bottom: 0px;
            }

            .tabs-step1 {
                .top {
                    width: 330px;
                    // display: flex;
                    // justify-content: space-between;
                    // align-items: flex-end;
                    // background: url(../images/time-top-bg.png) no-repeat top;
                    // background-size: 100%;
                    position: relative;
                    height: 37px;
                    z-index: 2;
                    .left {
                        width: 66px;
                        height: 37px;
                        line-height: 37px;
                        text-align: center;
                        background: #00afff;
                        border: 1px solid #000000;
                        border-radius: 4px 16px 0px 0px;

                        font-size: 15px;
                        font-family: PingFangSC, PingFangSC-Semibold;
                        font-weight: 600;

                        color: #ffffff;
                        overflow: hidden;
                        position: absolute;
                        z-index: 2;
                        left: 0px;
                        top: 0px;
                    }
                    .right {
                        width: calc(330px - 61px);
                        height: 32px;
                        font-size: 16px;
                        font-family: PingFangSC, PingFangSC-Semibold;
                        font-weight: 600;
                        color: #111b30;
                        line-height: 32px;
                        padding-left: 15px;
                        background: linear-gradient(to left, #bafe62, #fffc56);
                        border: 1px solid #111b30;
                        border-left: none;
                        border-radius: 0px 10px 0px 0px;
                        position: absolute;
                        left: 61px;
                        bottom: 0px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        .right-time {
                            margin-left: 5px;
                        }
                        .zhizho1 {
                            display: inline-block;
                            width: 55px;
                            height: 22px;
                            line-height: 22px;
                            background: #fe674f;
                            border: 1px solid #111b30;
                            border-radius: 4px;
                            font-size: 13px;
                            font-family: PingFangSC, PingFangSC-Semibold;
                            font-weight: 600;
                            text-align: center;
                            color: #ffffff;
                            margin-right: 5px;
                        }
                        .zhizho2 {
                            width: 65px;
                            height: 22px;
                            line-height: 22px;
                            background: #fef04c;
                            border: 1px solid #111b30;
                            border-radius: 4px;
                            font-size: 13px;
                            font-family: PingFangSC, PingFangSC-Semibold;
                            font-weight: 600;
                            text-align: center;
                            color: #170a0a;
                            margin-right: 5px;
                        }
                        .zhizho3 {
                            width: 55px;
                            height: 22px;
                            background: #00afff;
                            border: 1px solid #000000;
                            border-radius: 4px;
                            font-size: 13px;
                            font-family: PingFangSC, PingFangSC-Semibold;
                            font-weight: 600;
                            text-align: center;
                            color: #ffffff;
                            line-height: 22px;
                            margin-right: 5px;
                        }
                    }
                }
                .center {
                    width: 330px;
                    background: #f2f8fd;
                    border: 1px solid #111b30;
                    border-top: none;
                    border-radius: 0px 0px 10px 10px;
                    font-size: 15px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #111b30;
                    line-height: 21px;
                    padding: 15px 16px 15px 10px;
                    margin-bottom: 10px;
                }
            }
            .tabs-icon-left {
                position: absolute;
                left: 56px;
                top: -9px;
                width: 35px;
                height: 17px;
                background: url(../images/time-left.png) no-repeat center;
                background-size: 100%;
            }
            .tabs-icon-center {
                width: 125px;
                height: 35px;
                background: linear-gradient(to left, #bafe62, #fffc56);
                border: 1px solid #111b30;
                border-radius: 8px;
                font-size: 18px;
                font-family: PingFangSC, PingFangSC-Semibold;
                font-weight: 600;
                text-align: center;
                color: #111b30;
                line-height: 35px;
                position: absolute;
                left: 50%;
                top: -18px;
                transform: translate3d(-50%, 0, 0);
                overflow: hidden;
            }
            .tabs-icon-right {
                position: absolute;
                right: 56px;
                top: -9px;
                width: 35px;
                height: 17px;
                background: url(../images/time-right.png) no-repeat center;
                background-size: 100%;
            }
            &:last-child {
                margin-bottom: 0px;
            }
        }
        .time-more {
            width: 345px;
            height: 44px;
            background: #f2f8fd;
            border: 1px solid #111b30;
            border-radius: 8px;
            font-size: 15px;
            font-family: AlibabaPuHuiTi_2_55_Regular,
                AlibabaPuHuiTi_2_55_Regular-Regular;
            font-weight: 400;
            text-align: center;
            color: #111b30;
            line-height: 44px;
            margin: 15px auto 0px auto;
        }
    }
    .zhibo-fuli-container {
        margin-top: 29px;
        .zhibo-title {
            width: 299px;
            height: 51px;
            background: url(../images/zhobo-right.png) no-repeat center;
            background-size: 100%;
            margin: 0 auto;
        }
        .fuli-tabs {
            margin-top: 18px;
            display: flex;
            justify-content: center;

            .fuli-item {
                width: 169px;
                height: 201px;
                background: url(../images/fuli1.png) no-repeat center;
                background-size: 100%;
                margin-right: 9px;
                position: relative;
                padding: 0px 10px;
                &.fuli-item1 {
                    background: url(../images/fuli2.png) no-repeat center;
                    background-size: 100%;
                    margin-right: 0px;
                    text-align: center;
                    padding: 0px 8px 0px 10px;
                    &.qita {
                        background: url(../images/fuli2-yilinqu.png) no-repeat
                            center;
                        background-size: 100%;
                    }
                }
                .has-icon {
                    width: 56px;
                    height: 56px;
                    background: url(../images/yilingqu.png) no-repeat center;
                    background-size: 100%;
                    position: absolute;
                    top: -4.5px;
                    right: -5px;
                }
                .item-title-container {
                    position: absolute;
                    bottom: 14px;
                    &.container2 {
                        bottom: 11px;
                    }
                }
                .item-title {
                    font-size: 13px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    color: #111b30;
                    line-height: 17px;
                    margin-top: 4px;
                    display: flex;
                    align-items: baseline;
                    padding-right: 10px;
                    &:last-child {
                        font-size: 12px;
                    }
                    &.item-title2 {
                        font-size: 10px;
                        color: #666666;
                        margin-top: 0px;
                        .has-title {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            width: 100%;
                        }
                    }
                    &:first-child {
                        margin-top: 0px;
                        line-height: 18px;
                    }

                    .item-title-icon {
                        display: inline-block;
                        width: 6px;
                        height: 6px;
                        background: #fff200;
                        border: 1px solid #111b30;
                        border-radius: 50%;
                        margin-right: 5px;
                        vertical-align: top;
                    }
                }
                .go-study {
                    width: 60px;
                    height: 24px;
                    background: #fe674f;
                    border: 1px solid #111b30;
                    border-radius: 4px;
                    font-size: 13px;
                    font-family: PingFangSC, PingFangSC-Semibold;
                    font-weight: 600;
                    text-align: center;
                    color: #ffffff;
                    line-height: 24px;
                }
                .item-container {
                    margin-right: 15px;
                    margin-top: 56px;
                    display: inline-block;
                    &.item-container2 {
                        margin-right: 0px;
                    }
                    .item-icon-fuli {
                        width: 54px;
                        height: 59px;
                        background: url(../images/fuli-01.png) no-repeat center;
                        background-size: 100%;
                        &.fuli2 {
                            background: url(../images/fuli-02.png) no-repeat
                                center;
                            background-size: 100%;
                        }
                    }
                    .item-icon-title {
                        font-size: 13px;
                        font-family: PingFangSC, PingFangSC-Regular;
                        font-weight: 400;
                        text-align: center;
                        color: #111b30;
                        line-height: 18px;
                        margin-top: 15px;
                    }
                }
            }
        }
    }
    .zhibo-liuyan-container {
        margin-top: 29px;
        .liuyan-title {
            width: 299px;
            height: 51px;
            background: url(../images/liuyan.png) no-repeat center;
            background-size: 100%;
            margin: 0 auto;
        }
        .liuyan-item {
            min-height: 145px;
            background: #fffacc;
            border: 1px solid #111b30;
            border-radius: 8px;
            margin: 14px 15px 0px 15px;
            padding: 20px 13px;
            .liuyan-item-title {
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular,
                    AlibabaPuHuiTi_2_55_Regular-Regular;
                font-weight: 400;
                color: #111b30;
                line-height: 20px;
                margin-bottom: 10px;
                &::before {
                    content: "";
                    display: inline-block;
                    width: 6px;
                    height: 6px;
                    background: #fff200;
                    border: 1px solid #111b30;
                    border-radius: 50%;
                    margin-right: 7px;
                }
            }
            .liuyan-button {
                width: 250px;
                height: 40px;
                background: linear-gradient(to left, #bafe62, #fffc56);
                border: 1px solid #111b30;
                border-radius: 21px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 15px;
                font-family: AlibabaPuHuiTi_2_65_Medium,
                    AlibabaPuHuiTi_2_65_Medium-Regular;
                font-weight: 400;
                color: #111b30;
                margin: 15px auto 0px auto;
            }
        }
    }
    .zhibo-common-question {
        margin-top: 29px;
        .common-question-title {
            width: 299px;
            height: 54px;
            background: url(../images/common-question.png) no-repeat center;
            background-size: 100%;
            margin: 0 auto;
        }
    }
}
