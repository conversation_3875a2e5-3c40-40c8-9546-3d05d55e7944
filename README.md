# vip项目开发文档

## 项目的背景

要在app内售卖商品，使用h5更加灵活

## 项目的部署

生产环境：

| [https://laofuzi.kakamobi.com/jkbd-vip/index/](https://laofuzi.kakamobi.com/jkbd-vip/index/) |
| ----------------------------------------------------------------------------------------- |

测试环境：

| [https://laofuzi.ttt.kakamobi.com/jkbd-vip/index/](https://ttt.kakamobi.com/laofuzi.ttt.kakamobi.com/jkbd-vip/index/) |
| ------------------------------------------------------------------------------------------------------------------ |

## 项目发布问题

页面加载不出来但是打包没问题： 运维带宽超出，导致资源回溯不到

## 项目的运行环境

驾考相关的app，需要用vipWebView打开

## 项目的逻辑视图

**vip项目结构**

![](https://web-resource.mc-cdn.cn/web/md-image/1741069300681.jpg!1000x0)

**vip页面展示流程图**

![](https://web-resource.mc-cdn.cn/web/md-image/1741069356804.jpg!1000x0)

| 1.  判断主商品是否购买，如果是可以复购的商品是依旧停留在当前页面并可以售卖当前商品      2.  跳转到已购买页，部分页面有属于自己的单独已购买页（例如：扣满12分）      3.  优先展示默认的tab， 延迟500ms后展示其他tab |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |

**vip购买流程图**

![](https://web-resource.mc-cdn.cn/web/md-image/1741069388587.jpg!1000x0)

| 1.  ios有历史问题，如果h5IsSquirrel为false的情况，调用的是MCProtocol.Vip.BuyGoods,由客户端下单，所以必须保持squ和sir商品保持一致。      2.  ios下单的时候，可能同一个商品在不同的位置售卖价格不同，所以由客户端去请求详情是不对的，在部分页面需要我们自己把详情传给客户端（例如：新人专享）      3.  部分页面是购买完成关闭当前页面，部分页面是跳转支付状态页，部分页面是购买完成刷新当前页面      4.  这里绑定订单是延迟1.5s刷新页面（由于服务端的履约慢的问题，及时刷新可能拿不到权益），如果客户在这1.5s内又进行了操作（例如：点击支付，跳转页面），可能会出现意料之外的问题（例如：跳转走了之后，前面一个页面的刷新会调用webClose，ios会把当前最高层级的webview关闭） |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |

**公共逻辑**

1. 展示部分： baseFontSize在半截弹窗的情况下，最大宽度按照480来算，落地页不限制宽度(原因是要兼容pad，落地页不能限制宽度，而半截弹窗要限制高度)
2. 有部分页面会有打开横竖屏切换，我们收不到resize的变化，导致设置宽高比不对（由客户端保证webview的稳定性）
3. 由于ios基础库兼容性问题，在ios地域设置为法国的情况下设置宽高比失效，导致半截弹窗变成了全屏
4. 由于子页面也会售卖，所以当子页面购买完成，父页面也需要关掉，由于子页面也能登录，所以当子页面登录后，父页面也需要去刷新权益

![](https://web-resource.mc-cdn.cn/web/md-image/1741069409738.jpg!1000x0)

1. 页面隐藏的时候需要暂停所有video
2. 由于大部分页面都是黑色，所以状态栏设置成了白色，需要黑色状态栏的页面请单独设置
3. 打点里面会有数据来源于接口，所以接口失败打点会造成死循环
4. 由于客户端传过来的kemuStyle是公共里面取的，但是如果在科目1的情况下去推荐跳转科目2的页面，带过来的kemuStyle是1，然后我们传给接口的会是1，返回的数据都很可能是不对
5. ios  webview的onpageShow在被别的页面覆盖的时候也会触发（已修复8.25.0）
6. Ios alert会弹窗到所有webview最上面，哪怕被其他webview覆盖的时候（已修复8.25.0）
7. 安卓可能会存在http2协议注册不上，我们会调用http协议，但是客户端必须接受data里面带itemList字段，需要服务端处理
8. 打点是按照协议的show和hide去打进入和消失点的，ios处于预休眠状态的时候不会触发hide，会导致打点的viewId对接不上（已修复8.26.0）
9. 千万要注意，同一个商品，在不同ios的app中，后端返回的appId不同，记得要产品配置，否则ios支付会失败

**对接新商业化**

| 新商业化是采用字段映射的方式去做的（调用的方式不变，根据abtest去使用不同的接口，return的值保持一致） |
| ---------------------------------------------------------------------------------------------------- |

| 1.  由于新接口的参数变化太大，但是都来源于商品信息，所以用全局变量globalGoodsInfo存储了商品信息，调用优惠券，label展示，比价接口前一定要先调用detail接口      2.  获取活动配置图的时候一定要注意，由于现在的展示都放到接口的uiconfig里面了，所以新人专享的图片信息返回的是新人专享的接口，一定要先调用详情接口，再调用活动图      3.  订单状态查询的时候，以前content和orderNo字段是一致的，现在是不一致的。所以调用ios支付协议的时候，需要传不同的字段      4.  getUserCoupons接口由于squirrel接受的参数不同，所以新商品中的sessionIds被我赋值成了GroupKey\[\]      5.  尤其需要注意现在路线的权益需要通过list-data接口获取，并不能通过detail接口获取 |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |

**页面显示优化**

1. 只显示当前的tab，延迟显示其余隐藏的tab
2. 延迟展示video
3. 碎片化打包，尽可能的少打页面不用的代码到公共文件（如：city文件）
4. 压缩图片

**页面打点整理**

1. 匹配不上viewId，因为离开页面的时候未打点，所以有进入点没有离开点，安卓会触发onbeforeunload

| 1.  页面reload       *   登录成功      *   绑定成功      *   所有未购买页上有已购买状态的页面（例：kqfd.html,buyed.html，yxxy.html）      *   status.html的横竖屏变化      *   pageShow，登录状态变化（可能是在下几级页面登录的）      *   半截弹窗（buyDialog）在作为组件的时候      *   活动倒计时完的时候      *   ios在loacation.href回来的时候      2.  页面href      *   页面上的点击跳转，需要先打离开点      3.  页面replace      *   有权益直接跳转 |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |

1. 其他

| ios非活跃的webview也会触发hide，8.26.0修复 |
| ------------------------------------------ |

**技术整理**

1. 所有的购买页都可以继承于baseVip。

| 优势：大部分代码不需要重复工作，修改统一逻辑也方便。 弊端：因为各个页面的特殊性导致，基本每个页面都要重写方法，排查bug的时候极其困难，需要十分熟练，改动需要考虑到所有页面的场景。 |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |

![](https://web-resource.mc-cdn.cn/web/md-image/1741069452461.jpg!1000x0)

1. 由于ios底部按钮是原生的，所以做了一个组件buyButton去统一规划。整个点击逻辑都在这个组件里面。

| 原生按钮的层级和webview平级，所以可能会发生跳转页面，底部按钮还在的情况，这个在jump模块统一处理了，所以任何页面级别的跳转一定要用jump模块，不要直接location.href和replace； |
| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |

1. 由于一个页面会有多个组件，每个组件内都可能会在onpageshow做一些事，所有把客户端的onpageshow做了一些封装，将所有的注册事件放到数组中，触发会将数组中的方法执行一次
2. 部分组件中使用事件广播机制，由于广播会导致可控性变差，所以将广播控制在一个组件内（例：阅读协议组件），所以需要把所有功能相同的做到一个组件中，只是样式不同的话，用type去区分

注： 如果可以的话，请推动vip做成配置页面