# VIP售卖页文档
https://alidocs.dingtalk.com/i/nodes/2Amq4vjg89RDNEwLf4ykovEZW3kdP0wQ


# 已购买页项目文档

## 项目背景

用户购买权益之后，成为vip，在此页面集合了用户的大部分权益，方便用户通过这个页面直接使用权益，和通过用户已购买的权益推荐其他商品

## 项目的术语定义

vip：指的是用户购买科一等等商品后，成为了已购买用户

权益：是指用户能使用的哪些功能，比如，购买科一之后可以使用精简500题的做题页，

商品：是指驾考宝典卖的虚拟商品，购买商品后才能在app上使用某些功能

## 项目的部署

生产环境：[https://laofuzi.kakamobi.com/jkbd-vip/index/buyed.html](https://laofuzi.kakamobi.com/jkbd-vip/index/buyed.html)

测试环境：[https://laofuzi.ttt.kakamobi.com/jkbd-vip/index/buyed.html](https://laofuzi.kakamobi.com/jkbd-vip/index/buyed.html)

git地址：[https://git.mucang.cn/jiakaobaodian-webfront/jiakaobaodian-vip](https://git.mucang.cn/jiakaobaodian-webfront/jiakaobaodian-vip)

## 项目的运行环境

运行在驾考宝典以及驾考宝典系列app上

## 项目的编码规范

文件名小驼峰命名     ts规范    esline规范    共用方法提取   location跳转方法已封装   url绝对地址  url归纳等

## 项目的技术选型

simplex(公司自有的框架)    ts     webpack

## 项目的逻辑视图

1，ui展示

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4j6OJ11Rgvk6q3p8/img/dffcccb8-abed-4852-887b-188f7cbc153a.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4j6OJ11Rgvk6q3p8/img/98145d72-46a2-4104-a030-55d20c7fb065.png)

2，文件名含义

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/2M9qPBZ2akAkl015/img/04774023-8798-447c-9352-041dbedee483.png)

3，主要文件对应ui

    ![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4j6OJ11Rgvk6q3p8/img/960fe273-f4b6-4b81-bad4-832ac8e4e819.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4j6OJ11Rgvk6q3p8/img/0dc4c86a-c739-4dc7-88cc-7523e3626b3b.png)

4，主要模块的功能描述

    1)徽章卡片模块(index模块)： 

        接口：api/open/vip-badge/vip-badges.htm

        数据：返回徽章所有数据

        卡片高亮逻辑流程图：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/2M9qPBZ2akAkl015/img/2676297d-cee4-48f9-88cd-5f0709e03f95.png)

    2)会员专属优惠推荐套餐(packageRecommend模块)：

         根基当前的高亮的科目去查询推荐的商品

   3)学习步骤(studyStep模块)

        根据当前高亮的科目来的，此模块每一步的文案和跳转协议都是写死的，只有数据和状态(已完成未完成)是通过协议从客户端获取的，对应的协议跳转地址和协议获取数据从代码里面查看 

    4）其他增值权益(right模块)：

         根据当前高亮的科目从接口获取

## 项目的注意事项

   1，MCProtocol.Core.Web.setting  //低版本的(具体版本号不清楚了)的已购买页ios的头部是ios自己的，设置这个为了避免ios低版本出现两个头部

   2，MCProtocol\['jiakao-global'\].fixVipPermissions()  //版本(bizVersion>=8)，解决问题是：已有vip权益，比如科一，但是学习步骤的点击去学习，去到客户端做题页却显示没有权益的问题

   3，onWebBack()   //解决其他页面进入已购买页，其他页面拦截了物理返回键，导致在已购买页物理返回键点击无效

    4，http://jiakao.nav.mucang.cn/switch科目?科目=     //低于版本8.49.0，初始页面进入并对应高亮当前卡片徽章，当点击其他卡片徽章数据时，对应的科目变了，但是客户端还是初始进入的科目，导致学习步骤进入到对应的做题页，对应的做题页数据不对，所以此协议通知客户端科目变了，让客户端切换本地的科目。8.49.0版本改了全局科目的概念，点击去学习，协议带上对应的科目

    5，memberRecommend/main.js

         ![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4j6OJ11Rgvk6q3p8/img/73d18352-b11e-43a2-932c-71c815800c42.png)

会员优选课程卡片会展示没有解锁的价格，misc获取会员课程的价格与后期的squirrel商品的价格对应不上，因此通过channelCode去squirrel拿价格

## 项目的参考资料

    以前老的已购买页