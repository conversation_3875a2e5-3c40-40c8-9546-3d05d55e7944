/* eslint-disable spaced-comment */
/// <reference types="@types/node/" />
/// <reference types="@simplex-types/simple-core/" />

declare namespace Package {
    const hosts: { [key: string]: string };
    const build: {
        style: {
            baseWidth: number
        }
    };
}

declare module '*.html' {
    const value: any;
    export default value;
}

declare module '*.less' {
    const value: any;
    export default value;
}

declare module '*.png' {
    const value: any;
    export default value;
}

declare module '*.pag' {
    const value: any;
    export default value;
}

declare const module: { id: string };

interface Window {
    /*
     ｜ 登录相关回调 
     */

    loginSuccess(): void;

    /*
     ｜ 购买相关回调
     */
    iosBuySuccess(groupKey): void;
    buySuccess(): void;
    buyCancel(): void;
    buyFailed(): void;

    /*
     | 导航相关回调 
     */

    onWebviewClose: () => void

    /*
     | 环境相关
     */

    mucang: any;

    /** 网页根元素的字体大小 */
    baseFontSize: number;

    /** pag动画库 */
    pagInit: any;

    getCDNCityData: (cityData: any) => void,
    /** 域名映射，在script中单独引用<https://web-resource.mc-cdn.cn/web/vAssets.js> */
    vAssets: any,
    mucangStat: any,
    mcAdSdk: any,
    // eslint-disable-next-line camelcase
    mc_onLoadRewardSuccess: any,
    // eslint-disable-next-line camelcase
    mc_onRewardArrived: any,
    // eslint-disable-next-line camelcase
    mc_onRewardAbandon: any
}

type AnyFunc = (...args: any[]) => any;
